< {%
    request.variables.set("webhookSecret", "hlwhsec_hTD1fZYPoQL4OHfli9XtVGJxoCF9MHL851HSFoUqwh0")
%}
POST http://localhost:8080/api/integrations/highlevel/webhook?api_key={{webhookSecret}}
Content-Type: application/json

< ../../applications/monolith/src/test/resources/highlevel-webhook-limelight.json
###

#Test via ngrok (invalid body)

< {%
    request.variables.set("webhookSecret", "hlwhsec_l55cx-_NlTZFf03hpBfLp8RHyLbVRAxdL987bZdoQgg")
%}
POST https://sparrow-inspired-suddenly.ngrok-free.app/api/integrations/highlevel/webhook?api_key={{webhookSecret}}
Content-Type: application/json

{"hello":"darlin"}
###


< {%
    request.variables.set("webhookSecret", "hlwhsec_l55cx-_NlTZFf03hpBfLp8RHyLbVRAxdL987bZdoQgg")
%}
POST http://localhost:5173/api/integrations/highlevel/webhook?api_key=hlwhsec_l55cx-_NlTZFf03hpBfLp8RHyLbVRAxdL987bZdoQgg
Content-Type: application/json

< ../../applications/monolith/src/test/resources/highlevel-webhook-limelight.json
###


< {%
    request.variables.set("webhookSecret", "2835-22ace09cd7f63447")
%}
POST https://siv-integrations.sivconverts.com/api/integrations/highlevel/webhook?api_key=sfwhsec_-7-rxeqH57hG6gySaIg1pMgbp16AD3GuyafSsUq7jAc
Content-Type: application/json

< ../../applications/monolith/src/test/resources/highlevel-webhook-limelight.json
###

< {%
    request.variables.set("agencyAuthToken", "pit-4f8d9466-6d71-41fa-a733-a234882236b8")
%}

POST https://services.leadconnectorhq.com/oauth/locationToken HTTP/1.1
Version: 2021-07-28
Content-Type: application/x-www-form-urlencoded
Accept: application/json
Host: services.leadconnectorhq.com
Authorization: Bearer {{agencyAuthToken}}

companyId=&locationId=hRTYoTTfvX1zO4fAK9kv

####
< {%
    request.variables.set("subaccountPrivateIntegrationAuthToken", "pit-dccf262b-439d-41ab-adf7-d5c7fe0344c3")
%}

GET /contacts/?locationId=ve9EPM428h8vShlRW1KT&startAfter=1&query=Billy&limit=10 HTTP/1.1
Authorization: Bearer {{subaccountPrivateIntegrationAuthToken}}
Version: 2021-07-28
Accept: application/json
Host: services.leadconnectorhq.com
