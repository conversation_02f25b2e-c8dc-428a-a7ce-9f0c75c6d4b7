#V0 prompt for generating form builder components

Now we're going to make the components adhere to my app's theming system.

1. Use these CSS custom properties for theming, overriding ShadCN's default theme variables:
- Primary color: var(--ef-primary)
- Background color: var(--ef-background)
- Text color: var(--ef-text)
- Border color: var(--ef-border)
- Input border radius: var(--ef-input-radius)
- Button border radius: var(--ef-button-radius)
- Spacing/padding: var(--ef-spacing)
- Button text color: var(--ef-button-text)
- Font family: var(--ef-font)
- Field/input background: var(--ef-field-background)
- Input text color: var(--ef-input-text)
- Form border radius: var(--ef-form-radius)
- Placeholder color: var(--ef-placeholder)

2. Component-specific requirements:
- All form inputs should use var(--ef-field-background) for their background
- Buttons should use var(--ef-primary) for background and var(--ef-button-text) for text
- Form elements should respect var(--ef-input-radius) for border-radius
- Buttons should use var(--ef-button-radius) for border-radius
- All text inputs and textareas should use var(--ef-input-text) for text color
- Placeholders should use var(--ef-placeholder) for color

3. Component states:
- Focus states should use var(--ef-primary) for outlines/rings
- Hover states on buttons should adjust opacity to 0.9
- Error states should preserve the current error styling

5. Additional considerations:
- Ensure all interactive elements have proper focus states
- Maintain ARIA attributes and accessibility features
- Preserve the current form validation system
- Keep the responsive design aspects

The goal is to create a ShadCN implementation that perfectly matches the current theming system while providing enhanced component functionality and accessibility.