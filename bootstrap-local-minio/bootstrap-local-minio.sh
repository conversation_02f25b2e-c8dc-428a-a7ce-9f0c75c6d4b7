#!/bin/sh
set -e

echo "starting minio server in the background..."
minio server /data --console-address=":9001" &
minio_pid=$!

# Wait for minio to be ready using mc ping
wait_for_minio() {
    timeout=30
    counter=0
    echo "waiting for minio to start up.."

    until (mc alias set local http://127.0.0.1:9000 minioadmin minioadmin > /dev/null 2>&1) && (mc ping local --count 1 > /dev/null 2>&1)
    do
        sleep 1
        counter=$((counter + 1))
        if [ $counter -ge $timeout ]; then
            echo "timeout waiting for minio to start"
            exit 1
        fi
    done
    echo "...minio server is up"
}

wait_for_minio

echo "creating buckets for dev and test envs.."
mc mb --ignore-existing "local/dev-lead-form-file-uploads"
mc mb --ignore-existing "local/test-lead-form-file-uploads"
echo "...done creating buckets"
echo "listing all buckets:"
mc ls local

echo "bringing minio server into foreground (wait on the pid)"
wait $minio_pid
