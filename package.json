{"name": "siv-monorepo", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">=20", "pnpm": ">=9.1.4"}, "packageManager": "pnpm@9.1.4", "scripts": {"check-types": "tsc --noEmit", "build:deps": "rm -rf dist/node_modules && mkdir -p dist && cp package.json dist/ && cd dist && pnpm install --prod", "test": "turbo run test", "test:e2e": "pnpm --filter=@siv/e2e-tests test", "update-all-deps": "tsx scripts/update-all-deps.ts", "track-deps": "tsx scripts/track-deps.ts", "prepare": "husky install", "postinstall": "pnpm prepare", "dev": "node scripts/dev.js", "list": "node scripts/dev.js list", "generate": "node scripts/dev.js generate", "parse-prd": "node scripts/dev.js parse-prd"}, "workspaces": ["applications/*", "packages/*"], "devDependencies": {"@types/node": "^22.14.0", "husky": "^9.0.11", "tsx": "^4.7.1", "turbo": "^2.5.0", "vitest": "^3.1.1"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "boxen": "^8.0.1", "chalk": "^4.1.2", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "fastmcp": "^1.20.5", "figlet": "^1.8.0", "fuse.js": "^7.0.0", "gradient-string": "^3.0.0", "helmet": "^8.1.0", "inquirer": "^12.5.0", "jsonwebtoken": "^9.0.2", "lru-cache": "^10.2.0", "openai": "^4.89.0", "ora": "^8.2.0"}}