# Lead Assignment Rules Overlap Detection System

## Overview Flow Diagram

```mermaid
flowchart TD
    A[User Triggers Validation] --> B[checkForValidationIssues]
    B --> C[findOverlappingAssignments]
    
    C --> D{Assignment Strategy?}
    D -->|team| E[Check Team Overlaps]
    D -->|individual| F[Check Individual Overlaps]
    
    E --> G[For each team pair]
    F --> H[For each individual pair]
    
    G --> I[doRulesOverlap]
    H --> J[Get Effective Criteria<br/>with Team Inheritance]
    J --> I
    
    I --> K[Phase 1: Baseline Intersection]
    K --> L[Phase 2: Exception Analysis]
    L --> M[Phase 3: Test Scenario Generation]
    M --> N[Return Overlap Result]
    
    N --> O[Collect All Overlaps]
    O --> P[Update UI State]
    P --> Q[Show Validation Panel]
```

## Core Algorithm: doRulesOverlap

```mermaid
flowchart TD
    A[doRulesOverlap<br/>rule1, rule2, activeCriteria] --> B[Get Active Criteria Types]
    
    B --> C[Phase 1: Baseline Intersection]
    C --> D[For each active criteria type]
    
    D --> E{Check Criteria Values}
    E -->|Both null| F[No Overlap - Early Exit]
    E -->|One null| F
    E -->|Both valid| G[Calculate Intersection]
    
    G --> H{Intersection Type?}
    H -->|Both "Any"| I[Intersection = "Any"]
    H -->|One "Any"| J[Intersection = Other's Values]
    H -->|Both Specific| K[Intersection = Common Values]
    
    K --> L{Common Values Exist?}
    L -->|No| F
    L -->|Yes| M[Continue to Next Criteria]
    
    I --> M
    J --> M
    M --> N{More Criteria?}
    N -->|Yes| D
    N -->|No| O[Phase 2: Exception Analysis]
    
    O --> P[Collect Exception Values]
    P --> Q[Phase 3: Test Scenarios]
    Q --> R[Generate Cartesian Product]
    R --> S[Test Each Scenario]
    S --> T[Check if Both Rules Accept]
    T --> U[Return Final Overlap Result]
    
    F --> V[Return No Overlap]
```

## Criteria Intersection Logic

```mermaid
flowchart TD
    A[Two Rules with Criteria] --> B{Rule 1 Criteria}
    
    B -->|null| C[No Overlap]
    B -->|"Any" empty array| D{Rule 2 Criteria}
    B -->|Specific values| E{Rule 2 Criteria}
    
    D -->|null| C
    D -->|"Any" empty array| F[Intersection = "Any"<br/>Both accept all values]
    D -->|Specific values| G[Intersection = Rule 2's values<br/>Rule 1 accepts all, Rule 2 filters]
    
    E -->|null| C
    E -->|"Any" empty array| H[Intersection = Rule 1's values<br/>Rule 2 accepts all, Rule 1 filters]
    E -->|Specific values| I[Intersection = Common values<br/>Both rules must have overlap]
    
    I --> J{Common Values?}
    J -->|Yes| K[Criteria Overlap]
    J -->|No| C
    
    F --> K
    G --> K
    H --> K
    K --> L[Continue to Next Criteria Type]
```

## Team Inheritance System

```mermaid
flowchart TD
    A[Individual Assignment Rule] --> B[getEffectiveCriteria]
    
    B --> C[For each criteria type]
    C --> D{Individual has value?}
    
    D -->|Yes| E[Use Individual's Value<br/>Override team setting]
    D -->|No| F{Individual in team?}
    
    F -->|No| G[Use null<br/>No assignment rule]
    F -->|Yes| H{Team has value?}
    
    H -->|Yes| I[Inherit Team's Value<br/>Team rule applies]
    H -->|No| G
    
    E --> J[Add to Effective Criteria]
    I --> J
    G --> J
    
    J --> K{More criteria types?}
    K -->|Yes| C
    K -->|No| L[Return Effective Criteria]
    
    L --> M[Use in Overlap Detection]
```

## Exception Handling Flow

```mermaid
flowchart TD
    A[Rules Have Baseline Overlap] --> B[Collect Exception Values]
    
    B --> C[From Rule 1 Exceptions]
    C --> D[From Rule 2 Exceptions]
    D --> E[From All Possible Values Registry]
    
    E --> F[Generate Test Scenarios<br/>Cartesian Product]
    F --> G[For each test scenario]
    
    G --> H[evaluateLeadAgainstCriteria<br/>Rule 1]
    H --> I[evaluateLeadAgainstCriteria<br/>Rule 2]
    
    I --> J[evaluateRuleExceptions<br/>Rule 1]
    J --> K[evaluateRuleExceptions<br/>Rule 2]
    
    K --> L{Both rules accept<br/>this scenario?}
    L -->|Yes| M[Found Overlap!<br/>Return true]
    L -->|No| N{More scenarios?}
    
    N -->|Yes| G
    N -->|No| O[No Overlap Found<br/>Return false]
```

## Main Validation Trigger Flow

```mermaid
flowchart TD
    A[User Action] --> B{Trigger Type}
    
    B -->|Add/Update Individual| C[updateIndividual]
    B -->|Add/Update Team| D[updateTeam]
    B -->|Change Criteria| E[handleCriteriaSave]
    B -->|Manual Validation| F[handleCheckIssues]
    
    C --> G[checkForValidationIssues]
    D --> G
    E --> G
    F --> H[runValidation]
    H --> G
    
    G --> I[findOverlappingAssignments]
    I --> J[Update State]
    
    J --> K[setOverlappingRules]
    J --> L[setOverlappingIndividuals]
    J --> M[setOverlappingTeams]
    J --> N[setIndividualsWithBlankCriteria]
    
    K --> O[UI Updates]
    L --> O
    M --> O
    N --> O
    
    O --> P[Show Validation Panel<br/>if issues found]
    O --> Q[Highlight Overlapping Entities<br/>in Assignment Table]
```

## Performance Optimization: Sampling

```mermaid
flowchart TD
    A[Large Rule Set] --> B[Calculate Complexity]
    B --> C{Scenarios > Threshold?}
    
    C -->|No| D[Exhaustive Check<br/>Test all scenarios]
    C -->|Yes| E[Sampling Mode<br/>Test subset of scenarios]
    
    E --> F[Set isSampling = true]
    F --> G[Random Sample Selection]
    G --> H[Test Sample Scenarios]
    
    D --> I[Test All Scenarios]
    
    H --> J[Return Results + Warning]
    I --> K[Return Definitive Results]
    
    J --> L[Show Sampling Warning<br/>to User]
    K --> M[Show Complete Results]
```

## Key Data Structures

### OverlapDetails Interface
```typescript
interface OverlapDetails {
  overlaps: boolean;                    // Final overlap determination
  criteriaDetails: Array<{
    type: CriteriaType;                 // geography, roomCount, etc.
    rule1Values: string[];              // Rule 1's values for this criteria
    rule2Values: string[];              // Rule 2's values for this criteria
    isOverlapping: boolean;             // Does this criteria type overlap?
    isExceptedOut?: boolean;            // Prevented by exceptions?
    relevantExceptions?: Array<{
      entityId: string;
      exceptionIds: string[];
      explanation: string;
    }>;
  }>;
  isSampling?: boolean;                 // Used sampling vs exhaustive check
}
```

### Criteria Value Types
```typescript
type CriterionValue = 
  | { type: "Any" }                     // Matches all values (empty array)
  | { type: "Specific", values: string[] }  // Matches specific values
  | null                                // No rule set (individuals: no match, teams: no filter)
```

## Algorithm Complexity

- **Best Case**: O(n) - Early exit when criteria are mutually exclusive
- **Average Case**: O(n × m) - Where n = number of entities, m = average criteria complexity  
- **Worst Case**: O(n × 2^k) - Where k = number of active criteria types with many values
- **Sampling Threshold**: Switches to sampling when scenario count > 10,000

## Key Features

1. **Early Exit Optimization**: Stops checking as soon as mutual exclusion is found
2. **Team Inheritance**: Individuals inherit criteria from their teams when not explicitly set
3. **Exception Handling**: Complex exception rules can prevent overlaps even when baseline criteria overlap
4. **Sampling for Performance**: Handles large rule sets without blocking the UI
5. **Detailed Reporting**: Provides specific information about which criteria types cause overlaps
6. **Real-time Validation**: Automatically runs when rules are modified 