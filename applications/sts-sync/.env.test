ENVIRONMENT="test"
NODE_ENV="test"
INNGEST_DEV="1"
INNGEST_BASE_URL="http://host.docker.internal:8288"
INNGEST_SERVE_PATH="/api/inngest"
INNGEST_SERVE_HOST="http://host.docker.internal:3001"
INNGEST_LOG_LEVEL="debug"
INNGEST_EVENT_KEY="test_key"
INNGEST_SIGNING_KEY="test_signing_key"
S3_ACCESS_KEY="minioadmin"
S3_SECRET_KEY="minioadmin"
S3_ENDPOINT=http://localhost:9000
S3_LEAD_FORM_FILE_UPLOAD_BUCKET=test-lead-form-file-uploads
S3_FORCE_PATH_STYLE=true
PACT_BROKER_URL=https://pact-broker.qjxczm22k1618.us-west-2.cs.amazonlightsail.com/
PACT_BROKER_USERNAME=pactbroker
# PACT_BROKER_PASSWORD should be set in .env.test.local (see .env.test.local.example)
SIVWEB_INTERNAL_API_BASE_URL=http://localhost:8888
SIVWEB_INTERNAL_API_SECRET=test-secret
