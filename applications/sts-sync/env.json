{"StsFileSyncFunction": {"NODE_ENV": "development", "ENVIRONMENT": "development", "INNGEST_DEV": "1", "INNGEST_EVENT_KEY": "dev_dummy_event_key", "INNGEST_SIGNING_KEY": "dev_dummy_signing_key", "INNGEST_ENV": "development", "INNGEST_BASE_URL": "http://host.docker.internal:8288", "INNGEST_SERVE_PATH": "/api/inngest", "INNGEST_SERVE_HOST": "http://host.docker.internal:3001", "INNGEST_LOG_LEVEL": "debug", "S3_ENDPOINT": "http://host.docker.internal:9000", "S3_ACCESS_KEY": "minioadmin", "S3_SECRET_KEY": "minioadmin", "S3_FORCE_PATH_STYLE": "true", "S3_LEAD_FORM_FILE_UPLOAD_BUCKET": "dev-lead-form-file-uploads", "SIVWEB_INTERNAL_API_BASE_URL": "http://host.docker.internal:5173", "SIVWEB_INTERNAL_API_SECRET": "siv-web-internal-api-secret-development"}}