import {InternalApiClient, LeadSyncLog, StsSettings} from './internalApiClient';
import {beforeEach, describe, expect, test} from "vitest";
import {createHttpPactProvider, PACT_MOCK_BASE_URL, pactMatchers, PARTICIPANT_NAMES} from '@siv/pact-helpers';

const provider = createHttpPactProvider({
    consumerName: PARTICIPANT_NAMES.STS_SYNC_WORKFLOW,
    providerName: PARTICIPANT_NAMES.SIV_WEB
});

const {like, uuid} = pactMatchers.http;

describe('InternalApiClient Pact Tests', () => {
    let internalApiClient: InternalApiClient;
    const mockBaseUrl = PACT_MOCK_BASE_URL;
    const mockApiSecret = 'test-secret';

    beforeEach(() => {
        internalApiClient = new InternalApiClient(mockBaseUrl, mockApiSecret);
    });

    describe('STS Settings', () => {
        const propertyId = '123e4567-e89b-12d3-a456-************';
        const expectedResponse: StsSettings = {
            stsUsername: 'test-username',
            apiKey: 'test-api-key',
            rfpFormId: 'test-rfp-form-id',
        };

        test('successfully retrieves STS settings', async () => {
            provider
                .given('property exists with STS settings')
                .uponReceiving('a request to get STS settings')
                .withRequest({
                    method: 'GET',
                    path: `/internal/api/properties/${propertyId}/sts-settings`,
                    headers: {
                        'X-Api-Key': mockApiSecret,
                    },
                })
                .willRespondWith({
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like(expectedResponse),
                });

            await provider.executeTest(async (mockServer) => {
                const result = await internalApiClient.fetchStsSettings(propertyId);
                expect(result.isOk()).toBe(true);
                if (result.isOk()) {
                    expect(result.value).toEqual(expectedResponse);
                }
            });
        });

        test('handles 404 when STS settings not found', async () => {
            provider
                .given('property exists but has no STS settings')
                .uponReceiving('a request to get non-existent STS settings')
                .withRequest({
                    method: 'GET',
                    path: `/internal/api/properties/${propertyId}/sts-settings`,
                    headers: {
                        'X-Api-Key': mockApiSecret,
                    },
                })
                .willRespondWith({
                    status: 404,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like({
                        errorType: 'not_found_error',
                        message: `No STS integration settings found for property ${propertyId}`,
                    }),
                });

            await provider.executeTest(async () => {
                try {
                    const result = await internalApiClient.fetchStsSettings(propertyId);
                    expect(true).toBe(false); // Should not reach here
                } catch (error) {
                    expect(error instanceof Error).toBe(true);
                    if (error instanceof Error) {
                        expect(error.message).toBe('STS settings not found for property');
                    }
                }
            });
        });

        // Note: Authorization tests are removed as recommended in the Pact documentation.
        // They are better tested in separate integration tests.
    });

    describe('Lead Sync Logs', () => {
        const leadId = '123e4567-e89b-12d3-a456-************';
        const integrationType = 'sts';

        const expectedResponseItem = {
            id: uuid('123e4567-e89b-12d3-a456-************'),
            leadId: uuid(leadId),
            integrationType,
            integrationLeadId: '12345',
            syncedAt: like('2023-01-01T00:00:00.000Z'),
        };

        const expectedResponse = {
            data: [expectedResponseItem],
        };

        test('successfully retrieves lead sync logs', async () => {
            // For the test, create a simpler version of the expected response without matchers
            const simplifiedResponseItem: LeadSyncLog = {
                id: '123e4567-e89b-12d3-a456-************',
                leadId: '123e4567-e89b-12d3-a456-************',
                integrationType,
                integrationLeadId: '12345',
                syncedAt: '2023-01-01T00:00:00.000Z',
            };

            const apiResponseData = {
                data: [simplifiedResponseItem]
            };

            await provider
                .given('lead exists with sync logs')
                .uponReceiving('a request to get lead sync logs')
                .withRequest({
                    method: 'GET',
                    path: `/internal/api/leads/${leadId}/sync-log`,
                    query: {
                        integrationType,
                    },
                    headers: {
                        'X-Api-Key': mockApiSecret,
                    },
                })
                .willRespondWith({
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like(apiResponseData),
                });

            await provider.executeTest(async () => {
                const result = await internalApiClient.fetchLeadSyncLogs(leadId, integrationType);
                expect(result.isOk()).toBe(true);
                if (result.isOk()) {
                    expect(result.value).toEqual([simplifiedResponseItem]);
                }
            });
        });

        test('handles 404 when lead not found', async () => {
            await provider
                .given('lead does not exist')
                .uponReceiving('a request to get sync logs for non-existent lead')
                .withRequest({
                    method: 'GET',
                    path: `/internal/api/leads/${leadId}/sync-log`,
                    query: {
                        integrationType,
                    },
                    headers: {
                        'X-Api-Key': mockApiSecret,
                    },
                })
                .willRespondWith({
                    status: 404,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like({
                        errorType: 'not_found_error',
                        message: `Lead with id ${leadId} not found`,
                    }),
                });

            await provider.executeTest(async () => {
                try {
                    const result = await internalApiClient.fetchLeadSyncLogs(leadId, integrationType);
                    expect(true).toBe(false); // Should not reach here
                } catch (error) {
                    expect(error instanceof Error).toBe(true);
                    if (error instanceof Error) {
                        expect(error.message).toBe(`Lead with id ${leadId} not found`);
                    }
                }
            });
        });
    });

    describe('Create Lead Sync Log', () => {
        const leadId = '123e4567-e89b-12d3-a456-************';
        const integrationType = 'sts';
        const integrationLeadId = '12345';

        const createParams = {
            leadId,
            integrationType,
            integrationLeadId,
        };

        const expectedResponse: LeadSyncLog = {
            id: '987e6543-e21b-12d3-a456-************',
            leadId,
            integrationType,
            integrationLeadId,
            syncedAt: '2023-01-01T00:00:00.000Z',
        };

        test('successfully creates lead sync log', async () => {
            await provider
                .given('lead exists')
                .uponReceiving('a request to create a lead sync log')
                .withRequest({
                    method: 'POST',
                    path: `/internal/api/leads/${leadId}/sync-log`,
                    headers: {
                        'X-Api-Key': mockApiSecret,
                        'Content-Type': 'application/json',
                    },
                    body: {
                        integrationType,
                        integrationLeadId,
                    },
                })
                .willRespondWith({
                    status: 201,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like(expectedResponse),
                });

            await provider.executeTest(async () => {
                const result = await internalApiClient.createLeadSyncLog(createParams);
                expect(result.isOk()).toBe(true);
                if (result.isOk()) {
                    expect(result.value).toEqual(expectedResponse);
                }
            });
        });

        test('handles 404 when lead not found', async () => {
            await provider
                .given('lead does not exist')
                .uponReceiving('a request to create a sync log for non-existent lead')
                .withRequest({
                    method: 'POST',
                    path: `/internal/api/leads/${leadId}/sync-log`,
                    headers: {
                        'X-Api-Key': mockApiSecret,
                        'Content-Type': 'application/json',
                    },
                    body: {
                        integrationType,
                        integrationLeadId,
                    },
                })
                .willRespondWith({
                    status: 404,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: like({
                        errorType: 'not_found_error',
                        message: `Lead with id ${leadId} not found`,
                    }),
                });

            await provider.executeTest(async () => {
                try {
                    const result = await internalApiClient.createLeadSyncLog(createParams);
                    expect(true).toBe(false); // Should not reach here
                } catch (error) {
                    expect(error instanceof Error).toBe(true);
                    if (error instanceof Error) {
                        expect(error.message).toBe(`Lead with id ${leadId} not found`);
                    }
                }
            });
        });
    });
});
