import { Result, ok, err } from 'neverthrow';

export const STS_INTEGRATION_BASE_URL = 'https://stscloud.com';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB limit

export interface StsCreateLeadApiRequestDto {
    external_id: string;
    user_email: string | null;
    customer_contact_name: string;
    customer_contact_email: string;
    customer_contact_phone: string;
    start_date: string | null;
    end_date: string | null;
    number_of_attendees: string | null;
    title: string;
    customer_name: string | null;
    customer_contact_title: string | null;
    guest_room_requirements: string | null;
    description: string | null;
    alternate_dates: string | null;
    function_space_requirements: string | null;
    wedding_budget: string | null;
    file?: string; // Base64 encoded content
    fileName?: string; // Example: mydocument.pdf
    fileContentType?: string; // Example: application/pdf
}

export interface StsCreateLeadApiRequest {
    bodyDto: StsCreateLeadApiRequestDto;
    query: StsRequestQuery;
}

export interface StsRequestQuery {
    username: string;
    key: string;
    rfp_form_id: string;
}

export type StsError = {
    type: 'api_error' | 'unknown_error' | 'parse_error' | 'validation_error';
    message: string;
    statusCode?: number;
    response?: any;
}

export function encodeFileToBase64(content: Buffer): Result<string, StsError> {
    try {
        // Check file size
        if (content.length > MAX_FILE_SIZE) {
            return err({
                type: 'validation_error',
                message: `File size (${content.length} bytes) exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`
            });
        }

        // Convert to Base64
        const base64Content = content.toString('base64');
        return ok(base64Content);
    } catch (error) {
        return err({
            type: 'unknown_error',
            message: 'Failed to encode file content to Base64',
            response: error
        });
    }
}

export class StsApiClient {
    constructor(
        private readonly baseUrl: string = STS_INTEGRATION_BASE_URL,
        private readonly fetch: typeof global.fetch = global.fetch
    ) {}

    async createLead(
        request: StsCreateLeadApiRequest
    ): Promise<Result<{stsLeadId: number}, StsError>> {
        const bodyDto = request.bodyDto;

        const queryParams = new URLSearchParams();
        Object.entries(request.query).forEach(([key, value]) => {
            queryParams.append(key, value);
        });
        const queryString = queryParams.toString();
        const fullUrl = `${this.baseUrl}/interfaces/lead_capture/leads?${queryString}`;
        console.log('[StsApiClient.createLead] Fetching URL:', fullUrl); // DEBUGGING LINE

        try {
            const response = await this.fetch(fullUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify(bodyDto),
            });

            let responseData: Record<string, any> | null = null;
            try {
                responseData = await response.json();
            } catch (e) {
                return err({
                    type: 'parse_error',
                    message: 'Server returned invalid JSON response',
                    statusCode: response.status,
                    response: { error: 'Invalid JSON response' }
                });
            }

            // Check for error in response body even if status is 2xx
            if (responseData?.error) {
                return err({
                    type: 'api_error',
                    message: responseData.error,
                    statusCode: response.status,
                    response: responseData
                });
            }

            // Handle non-2xx responses
            if (!response.ok) {
                return err({
                    type: 'api_error',
                    message: response.statusText,
                    statusCode: response.status,
                    response: responseData
                });
            }

            return ok({stsLeadId: responseData!.id});
        } catch (error) {
            return err({
                type: 'unknown_error',
                message: error instanceof Error ? error.message : 'Network error occurred',
                response: error
            });
        }
    }
} 
