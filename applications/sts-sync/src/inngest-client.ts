import {EventSchemas, Inngest} from 'inngest';
import {env} from './env';
import {logger} from './logger';
import {LeadEventsSchema} from "@/events";
import {Lead} from "@siv/lead-types";

export type LeadAssignedLeadData = {
    id: string,
    assignedSalesRepEmail: string | null,
    firstName: string | null,
    lastName: string | null,
    email: string | null,
    phone: string | null,
    startDate: string | null
    endDate: string | null
    guestCount: number | null,
    propertyId: string,
    company: string | null,
    roomCount: number | null,
    eventType: Lead["eventType"],
    eventNeeds: string[] | null,
    eventName: string | null,
    eventDescription: string | null
    flexibleDates: boolean | null,
    mealCount: number | null,
    budget: number | null
}
export type LeadAssignedLeadWithFilesData = LeadAssignedLeadData & { files?: LeadFile[] }


type LeadFile = {
    s3Key: string,
    fileName: string,
    contentType: string,
}

export interface LeadAssignedEvent {
    name: 'lead.assigned';
    data: {
        leadId: string;
        leadData: LeadAssignedLeadData & { files?: LeadFile[] };
    };
    user?: {
        email?: string;
    };
}

export function createStsInngestClient() {
    logger.debug('Creating Inngest client');

    // Configuration options for the Inngest client
    return new Inngest({
        id: 'sts-sync',
        eventKey: env.INNGEST_EVENT_KEY,
        schemas: new EventSchemas().fromRecord<LeadEventsSchema>(),
        // Explicitly set these values to ensure proper initialization
        isDev: process.env.INNGEST_DEV === "1",
    });
}

export const inngestClient = createStsInngestClient();

export type StsInngestClient = ReturnType<typeof createStsInngestClient>;
