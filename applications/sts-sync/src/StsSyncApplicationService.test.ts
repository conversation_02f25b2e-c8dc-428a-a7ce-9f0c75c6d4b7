import {afterEach, beforeEach, describe, expect, it} from 'vitest';
import {StsSyncApplicationService} from './StsSyncApplicationService';
import {mock, MockProxy} from 'vitest-mock-extended';
import {StsApiClient} from './stsClient';
import {err, ok} from 'neverthrow';
import {mapLeadToStsRequest} from "./lead-to-sts-request-mapper";
import {s3Client} from "./s3Client";
import {DeleteObjectCommand, PutObjectCommand} from "@aws-sdk/client-s3";
import {env} from "./env";
import {InternalApiClient, LeadSyncLog} from "./internalApiClient";
import {LeadAssignedLeadData} from "./inngest-client";

const encoder = new TextEncoder();
const decoder = new TextDecoder();
const stringToUint8Array = (str: string) => encoder.encode(str);
const uint8ArrayToString = (arr: Uint8Array) => decoder.decode(arr);

const testBucket = env.S3_LEAD_FORM_FILE_UPLOAD_BUCKET;
const testFileKey = 'submitted/test-file.txt';
const testFileContent = 'hello from S3!';

async function deleteTestFile(key: string) {
    try {
        await s3Client.send(new DeleteObjectCommand({Bucket: testBucket, Key: key}));
    } catch {
    }
}

const createTestLead = (overrides = {}): LeadAssignedLeadData => ({
    id: 'test-id',
    propertyId: "abc-123-prop",
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '************',
    assignedSalesRepEmail: '<EMAIL>',
    eventType: 'corporate_meeting',
    startDate: null,
    endDate: null,
    guestCount: null,
    company: null,
    roomCount: null,
    eventNeeds: null,
    eventName: null,
    eventDescription: null,
    flexibleDates: null,
    mealCount: null,
    budget: null,
    ...overrides
});

describe('StsSyncApplicationService', () => {

    let service: StsSyncApplicationService;
    let stsClient: MockProxy<StsApiClient>;
    let internalApiClient: MockProxy<InternalApiClient>;

    beforeEach(() => {
        stsClient = mock<StsApiClient>();
        internalApiClient = mock<InternalApiClient>();
        service = new StsSyncApplicationService(stsClient, internalApiClient);
    });

    describe('fetchFileFromS3', () => {

        beforeEach(async () => {
            // Upload a test file to S3
            await s3Client.send(new PutObjectCommand({
                Bucket: testBucket,
                Key: testFileKey,
                Body: stringToUint8Array(testFileContent),
                ContentType: 'text/plain',
            }));
        });

        afterEach(async () => {
            await deleteTestFile(testFileKey);
        });

        it('returns Ok with file content for an existing file', async () => {
            const result = await service.fetchFileFromS3(testFileKey);
            expect(result.isOk()).toBe(true);
            if (result.isOk()) {
                const content = uint8ArrayToString(result.value as Uint8Array);
                expect(content).toBe(testFileContent);
            }
        });

        it('returns Err for a non-existent file', async () => {
            const nonExistentKey = 'submitted/does-not-exist-' + Date.now() + '.txt';
            const result = await service.fetchFileFromS3(nonExistentKey);
            expect(result.isErr()).toBe(true);
            if (result.isErr()) {
                expect(result.error.message.toLowerCase()).toContain('not found');
            }
        });
    })

    describe('first-time sync', () => {
        it('successfully syncs a lead to STS and logs the sync', async () => {
            const lead = createTestLead({
                id: '123-abc',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>'
            });
            
            const stsSettings = {
                stsUsername: 'test-username',
                apiKey: 'test-apikey',
                rfpFormId: 'test-form-id'
            };

            internalApiClient.fetchLeadSyncLogs.mockResolvedValue(ok([]));

            const stsLeadId = 12345;
            stsClient.createLead.mockResolvedValue(ok({
                stsLeadId
            }));
            
            internalApiClient.createLeadSyncLog.mockResolvedValue(ok({
                id: 'sync-log-123',
                leadId: lead.id,
                integrationType: 'sts',
                integrationLeadId: stsLeadId.toString(),
                syncedAt: new Date().toISOString()
            }));

            const result = await service.syncLead({
                lead,
                stsSettings
            });

            expect(result.isOk()).toBe(true);
            expect(result.isOk() && result.value.stsIntegrationConfigured).toBe(true);
            if (result.isOk() && result.value.stsIntegrationConfigured) {
                expect(result.value.stsLeadId).toBe(stsLeadId.toString());
            }

            expect(internalApiClient.createLeadSyncLog).toHaveBeenCalledTimes(1);
            expect(internalApiClient.createLeadSyncLog).toHaveBeenCalledWith({
                leadId: lead.id,
                integrationType: 'sts',
                integrationLeadId: stsLeadId.toString(),
            });

            expect(stsClient.createLead).toHaveBeenCalledTimes(1);
            expect(stsClient.createLead).toHaveBeenCalledWith({
                bodyDto: mapLeadToStsRequest(lead),
                query: {
                    username: stsSettings.stsUsername,
                    key: stsSettings.apiKey,
                    rfp_form_id: stsSettings.rfpFormId
                }
            });
        });

        it('returns error when lead sync logs API returns error', async () => {
            const lead = createTestLead({
                id: '123-abc'
            });
            
            const stsSettings = {
                stsUsername: 'test-username',
                apiKey: 'test-apikey',
                rfpFormId: 'test-form-id'
            };

            internalApiClient.fetchLeadSyncLogs.mockResolvedValue(err(new Error('Lead not found')));

            const result = await service.syncLead({
                lead,
                stsSettings
            });

            expect(result.isErr()).toBe(true);
            if (result.isErr()) {
                expect(result.error.message).toBe('Lead not found');
            }

            expect(stsClient.createLead).not.toHaveBeenCalled();
            expect(internalApiClient.createLeadSyncLog).not.toHaveBeenCalled();
        });

        it('does not log sync when STS API call fails', async () => {
            const lead = createTestLead({
                id: '123-abc'
            });
            
            const stsSettings = {
                stsUsername: 'test-username',
                apiKey: 'test-apikey',
                rfpFormId: 'test-form-id'
            };

            internalApiClient.fetchLeadSyncLogs.mockResolvedValue(ok([]));

            stsClient.createLead.mockResolvedValue(err({
                type: 'api_error',
                message: 'API Error',
                statusCode: 400
            }));

            const result = await service.syncLead({
                lead,
                stsSettings
            });

            expect(result.isErr()).toBe(true);

            expect(internalApiClient.createLeadSyncLog).not.toHaveBeenCalled();
        });
    });

    describe('idempotency behavior', () => {
        it('returns existing STS ID without resyncing if lead was already synced', async () => {
            // Mock data
            const lead = createTestLead({
                id: '123-abc'
            });
            
            const stsSettings = {
                stsUsername: 'test-username',
                apiKey: 'test-apikey',
                rfpFormId: 'test-form-id'
            };
            
            const existingStsLeadId = "12345";

            const existingSyncLog: LeadSyncLog = {
                id: 'sync-log-123',
                leadId: lead.id,
                integrationType: 'sts',
                integrationLeadId: existingStsLeadId,
                syncedAt: new Date().toISOString()
            };
            
            internalApiClient.fetchLeadSyncLogs.mockResolvedValue(ok([existingSyncLog]));

            const result = await service.syncLead({
                lead,
                stsSettings
            });

            expect(result.isOk()).toBe(true);
            expect(result.isOk() && result.value.stsIntegrationConfigured).toBe(true);
            if (result.isOk() && result.value.stsIntegrationConfigured) {
                expect(result.value.stsLeadId).toBe(existingStsLeadId);
            }

            expect(stsClient.createLead).not.toHaveBeenCalled();
            expect(internalApiClient.createLeadSyncLog).not.toHaveBeenCalled();
        });

        it('returns most recent sync ID when multiple sync records exist', async () => {
            const lead = createTestLead({
                id: '123-abc'
            });
            
            const stsSettings = {
                stsUsername: 'test-username',
                apiKey: 'test-apikey',
                rfpFormId: 'test-form-id'
            };
            
            const oldStsLeadId = "12345";
            const newStsLeadId = "67890";

            const syncLogs: LeadSyncLog[] = [
                {
                    id: 'sync-log-456',
                    leadId: lead.id,
                    integrationType: 'sts',
                    integrationLeadId: newStsLeadId,
                    syncedAt: new Date('2024-01-02').toISOString()
                },
                {
                    id: 'sync-log-123',
                    leadId: lead.id,
                    integrationType: 'sts',
                    integrationLeadId: oldStsLeadId,
                    syncedAt: new Date('2024-01-01').toISOString()
                }
            ];
            
            internalApiClient.fetchLeadSyncLogs.mockResolvedValue(ok(syncLogs));

            const result = await service.syncLead({
                lead,
                stsSettings
            });

            expect(result.isOk()).toBe(true);
            if (result.isOk()) {
                expect(result.value.stsIntegrationConfigured).toEqual(true)
                if(result.value.stsIntegrationConfigured){
                    expect(result.value.stsLeadId).toBe(newStsLeadId);
                }
            }

            expect(stsClient.createLead).not.toHaveBeenCalled();
            expect(internalApiClient.createLeadSyncLog).not.toHaveBeenCalled();
        });
    });
}); 
