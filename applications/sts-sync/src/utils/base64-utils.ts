import { Result, ok, err } from 'neverthrow';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB limit

export interface Base64EncodeError {
  message: string;
  cause?: unknown;
}

/**
 * Encodes a Buffer or Uint8Array to a Base64 string, with size validation
 * @param content The file content to encode
 * @returns Result containing either the Base64 encoded string or an error
 */
export function encodeFileContentToBase64(content: Buffer | Uint8Array): Result<string, Base64EncodeError> {
  try {
    // Check file size
    if (content.length > MAX_FILE_SIZE) {
      return err({
        message: `File size (${content.length} bytes) exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`,
      });
    }

    // Convert to Base64
    const base64Content = Buffer.from(content).toString('base64');
    return ok(base64Content);
  } catch (error) {
    return err({
      message: 'Failed to encode file content to Base64',
      cause: error,
    });
  }
} 