import { describe, it, expect } from 'vitest';
import { encodeFileContentToBase64 } from './base64-utils';

describe('base64-utils', () => {
  describe('encodeFileContentToBase64', () => {
    it('should successfully encode a Buffer to Base64', () => {
      const content = Buffer.from('test file content');
      const result = encodeFileContentToBase64(content);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe('dGVzdCBmaWxlIGNvbnRlbnQ=');
      }
    });

    it('should successfully encode a Uint8Array to Base64', () => {
      const content = new Uint8Array([116, 101, 115, 116]); // "test" in ASCII
      const result = encodeFileContentToBase64(content);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe('dGVzdA==');
      }
    });

    it('should return an error for files exceeding size limit', () => {
      // Create a buffer larger than 10MB
      const content = Buffer.alloc(11 * 1024 * 1024);
      const result = encodeFileContentToBase64(content);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('exceeds maximum allowed size');
      }
    });
  });
}); 