// Enable source maps for stack traces
import 'source-map-support/register';
import {Hono} from "hono";
import {serve as serveInngest} from "inngest/hono";
import {handle} from "hono/aws-lambda";
import {createStsSyncFunction} from "./functions/sts-sync-workflow";
import {inngestClient} from "./inngest-client";
import {env, validateRequiredEnvVars} from "./env";
import {logger} from "./logger";
import {StsSyncApplicationService} from "@/StsSyncApplicationService";
import {StsApiClient} from "@/stsClient";
import {InternalApiClient} from "@/internalApiClient";

const app = new Hono();

try {
    validateRequiredEnvVars();
    
    logger.info("Initializing STS File Sync Lambda", {
        environment: env.ENVIRONMENT
    });

    const stsApiClient = new StsApiClient()
    const internalApiClient = new InternalApiClient()
    const applicationService = new StsSyncApplicationService(stsApiClient, internalApiClient);

    const stsFileSyncFunction = createStsSyncFunction(inngestClient, applicationService);

    const inngestPath = env.INNGEST_SERVE_PATH;
    app.use(inngestPath, serveInngest({
        client: inngestClient,
        functions: [stsFileSyncFunction]
    }));

} catch (error) {
    logger.error("Lambda initialization failed", error as Error);
    
    // Add a special route to show the initialization error
    app.all('/api/*', (c) => {
        return c.json(
            { error: 'Lambda initialization failed', message: (error as Error).message },
            500
        );
    });
}

// Health check endpoint
app.get('/health', (c) => {
    logger.debug('Health check requested');
    return c.json({status: 'ok', service: 'sts-sync'});
});

// Add a catch-all route for debugging
app.all('*', (c) => {
    logger.warn('Received request for non-existent route', {
        method: c.req.method,
        path: c.req.path,
    });
    return c.json(
        {error: 'Not found', message: 'The requested endpoint does not exist'},
        404
    );
});

// Export the Lambda handler
export const handler = handle(app);
