import { env } from './env';
import { Result, ok, err } from 'neverthrow';
import logger from "@/logger";

export interface StsSettings {
  stsUsername: string;
  apiKey: string;
  rfpFormId: string;
}

export interface LeadSyncLog {
  id: string;
  leadId: string;
  integrationType: string;
  integrationLeadId: string;
  syncedAt: string;
}

export interface CreateLeadSyncLogParams {
  leadId: string;
  integrationType: string;
  integrationLeadId: string;
}

export class InternalApiClient {
  private baseUrl: string;
  private apiSecret: string;

  constructor(baseUrl: string = env.SIVWEB_INTERNAL_API_BASE_URL, apiSecret: string = env.SIVWEB_INTERNAL_API_SECRET) {
    this.baseUrl = baseUrl;
    this.apiSecret = apiSecret;
  }

  private async makeRequest<T = unknown>(url: string, method: string, body?: T): Promise<Result<Response, Error>> {
    try {
      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': this.apiSecret,
        },
        ...(body ? { body: JSON.stringify(body) } : {}),
      });
      
      return ok(res);
    } catch (e) {
      return err(e instanceof Error ? e : new Error(`Unknown error making ${method} request to ${url}`));
    }
  }

  async fetchStsSettings(propertyId: string): Promise<Result<StsSettings, Error>> {
    const url = `${this.baseUrl}/internal/api/properties/${propertyId}/sts-settings`;
    logger.info(`about to request ${url}, propertyId=${propertyId}`)

    const response = await this.makeRequest(url, 'GET');
    
    return response.asyncMap(async (res) => {
      if (res.status === 200) {
        const data = await res.json();
        return data as StsSettings;
      } else if (res.status === 404) {
        throw new Error('STS settings not found for property');
      } else if (res.status === 401 || res.status === 403) {
        throw new Error('Unauthorized to access internal API');
      } else {
        const text = await res.text();
        throw new Error(`Internal API error (${res.status}): ${text}`);
      }
    });
  }

  async fetchLeadSyncLogs(leadId: string, integrationType: string): Promise<Result<LeadSyncLog[], Error>> {
    const url = `${this.baseUrl}/internal/api/leads/${leadId}/sync-log?integrationType=${integrationType}`;

    const response = await this.makeRequest(url, 'GET');

    return response.asyncMap(async (res) => {
      if (res.status === 200) {
        const data = await res.json();
        return data.data as LeadSyncLog[];
      } else if (res.status === 404) {
        throw new Error(`Lead with id ${leadId} not found`);
      } else if (res.status === 401 || res.status === 403) {
        throw new Error('Unauthorized to access internal API');
      } else {
        const text = await res.text();
        throw new Error(`Internal API error (${res.status}): ${text}`);
      }
    });
  }

  async createLeadSyncLog(params: CreateLeadSyncLogParams): Promise<Result<LeadSyncLog, Error>> {
    const url = `${this.baseUrl}/internal/api/leads/${params.leadId}/sync-log`;

    interface LeadSyncLogBody {
      integrationType: string;
      integrationLeadId: string;
    }

    const response = await this.makeRequest<LeadSyncLogBody>(url, 'POST', {
      integrationType: params.integrationType,
      integrationLeadId: params.integrationLeadId,
    });

    return response.asyncMap(async (res) => {
      if (res.status === 201) {
        const data = await res.json();
        return data as LeadSyncLog;
      } else if (res.status === 404) {
        throw new Error(`Lead with id ${params.leadId} not found`);
      } else if (res.status === 401 || res.status === 403) {
        throw new Error('Unauthorized to access internal API');
      } else {
        const text = await res.text();
        throw new Error(`Internal API error (${res.status}): ${text}`);
      }
    });
  }
}