// Helper to access environment variables with type safety

export const env = {
  // General
  NODE_ENV: process.env.NODE_ENV || 'development',
  ENVIRONMENT: process.env.ENVIRONMENT || 'development',
  
  // S3
  S3_LEAD_FORM_FILE_UPLOAD_BUCKET: process.env.S3_LEAD_FORM_FILE_UPLOAD_BUCKET || '',
  S3_ENDPOINT: process.env.S3_ENDPOINT || '', 
  S3_ACCESS_KEY: process.env.S3_ACCESS_KEY || '',
  S3_SECRET_KEY: process.env.S3_SECRET_KEY || '',
  S3_FORCE_PATH_STYLE: process.env.S3_FORCE_PATH_STYLE || 'false',
  
  // Inngest
  INNGEST_DEV: process.env.INNGEST_DEV || '1',
  INNGEST_EVENT_KEY: process.env.INNGEST_EVENT_KEY || '',
  INNGEST_SIGNING_KEY: process.env.INNGEST_SIGNING_KEY || '',
  INNGEST_ENV: process.env.INNGEST_ENV || 'development',
  INNGEST_BASE_URL: process.env.INNGEST_BASE_URL || 'https://api.inngest.com/',
  INNGEST_SERVE_PATH: process.env.INNGEST_SERVE_PATH || '/api/inngest',

  // SivWeb Internal API
  SIVWEB_INTERNAL_API_BASE_URL: process.env.SIVWEB_INTERNAL_API_BASE_URL || '',
  SIVWEB_INTERNAL_API_SECRET: process.env.SIVWEB_INTERNAL_API_SECRET || '',
};

// Helper function to validate required environment variables
export function validateRequiredEnvVars() {
  const requiredVars = [
    'S3_LEAD_FORM_FILE_UPLOAD_BUCKET',
    'INNGEST_EVENT_KEY',
    'INNGEST_SIGNING_KEY',
    'SIVWEB_INTERNAL_API_BASE_URL',
    'SIVWEB_INTERNAL_API_SECRET'
  ];
  
  const missingVars = requiredVars.filter(varName => !env[varName as keyof typeof env]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
} 
