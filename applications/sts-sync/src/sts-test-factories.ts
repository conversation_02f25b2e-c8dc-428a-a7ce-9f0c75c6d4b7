import {Factory} from 'fishery'
import {randomUUID} from 'crypto'
import {StsCreateLeadApiRequest} from "@/stsClient";
import {StsSettings} from "@/internalApiClient";

export const stsCreateLeadRequestFactory = Factory.define<StsCreateLeadApiRequest>(() => ({
    query: {
        username: 'siv_demo',
        key: '2835-22ace09cd7f63447',
        rfp_form_id: 'b609993f',
    },
    bodyDto: {
        title: 'Test Event',
        external_id: randomUUID(),
        user_email: '<EMAIL>',
        start_date: '2029-03-15',
        end_date: '2029-03-17',
        number_of_attendees: '110',
        customer_contact_name: '<PERSON>',
        customer_contact_email: '<EMAIL>',
        customer_contact_phone: '+17208389771',
        customer_name: 'Test Company',
        customer_contact_title: 'Manager',
        guest_room_requirements: '50 rooms',
        description: 'Test description',
        alternate_dates: null,
        function_space_requirements: null,
        wedding_budget: '80000'
    }
}))

export const stsIntegrationSettingsFactory = Factory.define<StsSettings>(() => {
        return {
            propertyId: randomUUID(),
            stsUsername: `test-sts-username-${randomUUID()}`,
            apiKey: `test-api-key-${randomUUID()}`,
            rfpFormId: `rfp-form-id-${randomUUID()}`
        }
    }
)
