import {err, ok, Result} from "neverthrow";
import {mapLeadToStsRequest} from "@/lead-to-sts-request-mapper";
import {InternalApiClient, StsSettings} from "@/internalApiClient";
import {env} from "@/env";
import {GetObjectCommand} from "@aws-sdk/client-s3";
import {s3Client} from "@/s3Client";
import {LeadAssignedLeadData} from "@/inngest-client";
import {StsApiClient} from "@/stsClient";
import logger from "@/logger";

type StsSyncError = { message: string };

type SuccessResult = { stsLeadId: string, stsIntegrationConfigured: true } | { stsIntegrationConfigured: false };

export class StsSyncApplicationService {
    constructor(private readonly stsApiClient: StsApiClient,
                private readonly internalApiClient: InternalApiClient) {
    }

    async fetchStsSettings(propertyId: string) {
        return this.internalApiClient.fetchStsSettings(propertyId);
    }

    async fetchFileFromS3(s3Key: string) {
        try {
            const bucket = env.S3_LEAD_FORM_FILE_UPLOAD_BUCKET;
            if (!bucket) {
                return err(new Error('S3_LEAD_FORM_FILE_UPLOAD_BUCKET env var not set'));
            }
            const command = new GetObjectCommand({
                Bucket: bucket,
                Key: s3Key,
            });
            const response = await s3Client.send(command);
            if (!response.Body) {
                return err(new Error('No file body returned from S3'));
            }
// Read the stream into a Buffer
            const stream = response.Body;
            const chunks: Uint8Array[] = [];
            for await (const chunk of stream as any) {
                chunks.push(chunk);
            }
            const buffer = Buffer.concat(chunks);
            return ok(buffer);
        } catch (e: any) {
            if (e?.name === 'NoSuchKey' || e?.$metadata?.httpStatusCode === 404) {
                return err(new Error(`File not found: ${s3Key}`));
            }
            return err(new Error(e?.message || 'Unknown S3 error'));
        }
    }

    async syncLead(params: {
        lead: LeadAssignedLeadData,
        stsSettings: StsSettings,
        fileContent?: Buffer
    }): Promise<Result<SuccessResult, StsSyncError>> {
        const leadId = params.lead.id;

        // Check if we've already synced this lead to STS
        const syncLogsResult = await this.internalApiClient.fetchLeadSyncLogs(leadId, 'sts');

        if (syncLogsResult.isErr()) {
            if (syncLogsResult.error.message.includes('not found')) {
                return err({message: 'Lead not found'});
            }
            return err({message: syncLogsResult.error.message});
        }

        const syncLogs = syncLogsResult.value;

        if (syncLogs.length > 0) {
            return ok({stsLeadId: syncLogs[0].integrationLeadId, stsIntegrationConfigured: true});
            // If we can't parse the ID, continue with creating a new sync
        }

        const integrationSettingsRecord = params.stsSettings;
        const leadDomainObj = params.lead;
        const bodyDto = mapLeadToStsRequest(leadDomainObj, params.fileContent);
        const result = await this.stsApiClient.createLead({
            bodyDto: bodyDto,
            query: {
                username: integrationSettingsRecord.stsUsername,
                key: integrationSettingsRecord.apiKey,
                rfp_form_id: integrationSettingsRecord.rfpFormId,
            }
        });

        return result.match(async (result) => {
            const syncLogResult = await this.internalApiClient.createLeadSyncLog({
                leadId: leadId,
                integrationType: 'sts',
                integrationLeadId: result.stsLeadId.toString(),
            });

            if (syncLogResult.isErr()) {
                logger.error(syncLogResult.error, "logging lead sync failed")
                // Even if logging fails, we return success as the sync was successful
                return ok({stsLeadId: result.stsLeadId.toString(), stsIntegrationConfigured: true});
            }

            return ok({stsLeadId: result.stsLeadId.toString(), stsIntegrationConfigured: true});
        }, (error) => {
            return err({message: error.message});
        });
    }
}
