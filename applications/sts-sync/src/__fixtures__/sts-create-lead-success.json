[{"scope": "https://stscloud.com:443", "method": "POST", "path": "/interfaces/lead_capture/leads?username=siv_demo&key=2835-22ace09cd7f63447&rfp_form_id=b609993f", "body": {"title": "Test Event", "external_id": "abc123456", "user_email": "<EMAIL>", "start_date": "2029-03-15", "end_date": "2029-03-17", "number_of_attendees": "110", "customer_contact_name": "<PERSON>", "customer_contact_email": "<EMAIL>", "customer_contact_phone": "+17208389771", "customer_name": "Test Company", "customer_contact_title": "Manager", "guest_room_requirements": "50 rooms", "description": "Test description", "alternate_dates": null, "function_space_requirements": null, "wedding_budget": "80000"}, "status": 200, "response": {"success": true, "id": 223901}, "rawHeaders": {"cache-control": "no-store", "connection": "keep-alive", "content-type": "application/json; charset=utf-8", "date": "Fri, 25 Apr 2025 05:39:06 GMT", "etag": "W/\"14ec6562d05e696bb0516cf6180ff340\"", "expires": "Fri, 01 Jan 1990 00:00:00 GMT", "pragma": "no-cache", "server": "nginx/1.26.2 + Phusion Passenger(R) 6.0.24", "status": "200 OK", "strict-transport-security": "max-age=63072000; includeSubDomains", "transfer-encoding": "chunked", "vary": "Accept, Origin", "x-frame-options": "ALLOWALL", "x-powered-by": "Phusion Passenger(R) 6.0.24", "x-request-id": "141848b6-e8e4-441e-9f4f-d861dda600e0", "x-runtime": "0.296179"}, "responseIsBinary": false}]