import {formatEventType, generateOpportunityName, titleCase} from "@siv/lead-sync-field-helpers";
import {StsCreateLeadApiRequestDto} from "@/stsClient";
import {LeadAssignedLeadData, LeadAssignedLeadWithFilesData} from "@/inngest-client";

function formatEventNeeds(needs: string[] | undefined | null): string | null {
    if (!needs?.length) return null;
    return needs
        .map(need => need
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ')
        )
        .join(', ');
}

function buildDescription(lead: LeadAssignedLeadData ): string {
    const parts: string[] = [];
    
    // Only add event type if it exists
    if (lead.eventType) {
        parts.push(titleCase(formatEventType(lead.eventType)));
    }

    if (lead.eventName) {
        parts.push(`\nEvent Name: ${lead.eventName}`);
    }
    
    // Add event description if available
    if (lead.eventDescription) {
        parts.push(`\nDescription: ${lead.eventDescription}`);
    }

    // Add flexible dates info
    if (lead.flexibleDates) {
        parts.push('\nDates are flexible.');
    }

    if(lead.roomCount !==null){
        parts.push(`\nNumber of Rooms Requested: ${lead.roomCount}`);
    }

    if(lead.mealCount !==null){
        parts.push(`\nNumber of Meals Requested: ${lead.mealCount}`);
    }
    
    return parts.join('\n');
}

export function mapLeadToStsRequest(lead: LeadAssignedLeadWithFilesData, fileContent?: Buffer): StsCreateLeadApiRequestDto {
    const baseDto: StsCreateLeadApiRequestDto = {
        external_id: lead.id,
        user_email: lead.assignedSalesRepEmail || null,
        customer_contact_name: `${lead.firstName || ''} ${lead.lastName || ''}`.trim(),
        customer_contact_email: lead.email || '<EMAIL>',
        customer_contact_phone: lead.phone || 'unknown',
        start_date: lead.startDate ?? null,
        end_date: lead.endDate ?? null,
        number_of_attendees: lead.guestCount?.toString() || null,
        title: generateOpportunityName(lead),
        customer_name: lead.company || null,
        customer_contact_title: null,
        guest_room_requirements: lead.roomCount?.toString() || null,
        description: buildDescription(lead),
        alternate_dates: null,
        function_space_requirements: formatEventNeeds(lead.eventNeeds),
        wedding_budget: lead.budget?.toString() || null,
    };

    if (fileContent && lead.files && lead.files.length > 0) {
        // Assuming the first file in the array is the one being processed.
        // And that lead.files[0] has fileName and contentType properties.
        const firstFile = lead.files[0];
        return {
            ...baseDto,
            file: fileContent.toString('base64'),
            fileName: firstFile.fileName,       // Populate from lead data
            fileContentType: firstFile.contentType  // Populate from lead data
        };
    } else if (fileContent) {
        // If fileContent is provided but lead.files is not, we can only send the content.
        // This might lead to the API error if metadata is strictly required.
        return {
            ...baseDto,
            file: fileContent.toString('base64'),
        };
    }

    return baseDto;
}
