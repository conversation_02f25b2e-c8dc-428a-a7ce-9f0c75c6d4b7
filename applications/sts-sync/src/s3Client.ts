import { S3Client } from '@aws-sdk/client-s3';
import { env } from './env';
import { logger } from './logger';

/**
 * Create an S3 client instance
 * This function centralizes S3 client creation, making it easier to 
 * manage region settings or add other configuration as needed
 */
export function createS3Client(): S3Client {
  logger.debug('Creating S3 client');
  
  const config: any = {
    region: 'us-west-2',  // Default region
  };
  
  // If we have a local endpoint configured (for development/testing)
  if (env.S3_ENDPOINT) {
    logger.debug('Using custom S3 endpoint', { endpoint: env.S3_ENDPOINT });
    config.endpoint = env.S3_ENDPOINT;
    
    // Add credentials if provided
    if (env.S3_ACCESS_KEY && env.S3_SECRET_KEY) {
      config.credentials = {
        accessKeyId: env.S3_ACCESS_KEY,
        secretAccessKey: env.S3_SECRET_KEY
      };
    }
    
    // Enable path style addressing if requested
    if (env.S3_FORCE_PATH_STYLE === 'true') {
      config.forcePathStyle = true;
    }
  }
  
  return new S3Client(config);
}

// Default export of the S3 client
export const s3Client = createS3Client(); 