import {describe, expect, it} from 'vitest';
import {mapLeadToStsRequest} from './lead-to-sts-request-mapper';
import {Lead} from '@siv/lead-types';
import {LeadAssignedLeadWithFilesData} from "@/inngest-client";

describe('mapLeadToStsRequest', () => {
    it('should map a complete lead to STS request format', () => {
        const lead: LeadAssignedLeadWithFilesData = {
            id: 'test-lead-123',
            firstName: '<PERSON>',
            lastName: 'Doe',
            email: '<EMAIL>',
            eventName: "A real cool event!",
            phone: '************',
            startDate: '2024-05-15',
            endDate: '2024-05-17',
            guestCount: 100,
            company: 'Acme Corp',
            roomCount: 50,
            mealCount: 75,
            eventNeeds: ['CATERING', 'MEETING_SPACE'],
            budget: 25000,
            assignedSalesRepEmail: '<EMAIL>',
            flexibleDates: true,
            eventType: 'corporate_meeting',
            eventDescription: "Annual offsite meeting",
            propertyId: 'property-123'
        };

        const result = mapLeadToStsRequest(lead);

        expect(result).toEqual({
            external_id: 'test-lead-123',
            user_email: '<EMAIL>',
            customer_contact_name: 'John Doe',
            customer_contact_email: '<EMAIL>',
            customer_contact_phone: '************',
            start_date: '2024-05-15',
            end_date: '2024-05-17',
            number_of_attendees: '100',
            title: 'A real cool event! - Corporate Meetings',
            customer_name: 'Acme Corp',
            customer_contact_title: null,
            guest_room_requirements: '50',
            description: 'Corporate Meetings\n\nEvent Name: A real cool event!\n\nDescription: Annual offsite meeting\n\nDates are flexible.\n\nNumber of Rooms Requested: 50\n\nNumber of Meals Requested: 75',
            alternate_dates: null,
            function_space_requirements: 'Catering, Meeting Space',
            wedding_budget: '25000'
        });
    });

    // Helper function to create lead objects for tests
    const createLead = (partialLead: Partial<LeadAssignedLeadWithFilesData> = {}): LeadAssignedLeadWithFilesData => {
        return {
            id: 'test-id',
            propertyId: 'property-123',
            
            // Default values
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            phone: '************',
            company: 'Test Company',
            eventType: 'corporate_meeting',
            assignedSalesRepEmail: '<EMAIL>',
            startDate: '2024-06-01',
            endDate: '2024-06-03',
            
            // Nullable fields with defaults
            eventName: null,
            budget: null,
            eventDescription: null,
            eventNeeds: null,
            flexibleDates: null,
            guestCount: null,
            roomCount: null,
            mealCount: null,
            
            // Override with partial data
            ...partialLead
        };
    };

    it('should handle minimal lead data with only required fields', () => {
        const lead: LeadAssignedLeadWithFilesData = {
            eventName: null,
            budget: null,
            company: null,
            email: null,
            eventDescription: null,
            eventNeeds: null,
            firstName: null,
            flexibleDates: null,
            guestCount: null,
            lastName: null,
            phone: null,
            roomCount: null,
            mealCount: null,
            id: 'test-lead-456',
            propertyId: 'property-123',
            assignedSalesRepEmail: '<EMAIL>',
            eventType: 'wedding',
            startDate: null,
            endDate: null
        };

        const result = mapLeadToStsRequest(lead);

        expect(result).toEqual({
            external_id: 'test-lead-456',
            user_email: '<EMAIL>',
            customer_contact_name: '',
            customer_contact_email: '<EMAIL>',
            customer_contact_phone: 'unknown',
            start_date: null,
            end_date: null,
            number_of_attendees: null,
            title: 'Wedding',
            customer_name: null,
            customer_contact_title: null,
            guest_room_requirements: null,
            description: 'Wedding',
            alternate_dates: null,
            function_space_requirements: null,
            wedding_budget: null
        });
    });

    it('should handle lead with partial name information', () => {
        const lead = createLead({
            firstName: 'Jane',
            lastName: null,
        });

        const result = mapLeadToStsRequest(lead);

        expect(result.customer_contact_name).toBe('Jane');

        const leadWithLastNameOnly = createLead({
            firstName: null,
            lastName: 'Smith',
        });

        const result2 = mapLeadToStsRequest(leadWithLastNameOnly);

        expect(result2.customer_contact_name).toBe('Smith');
    });

    it('should handle numeric fields correctly', () => {
        const lead = createLead({
            guestCount: 0,
            roomCount: 0,
            mealCount: 0,
            budget: 0,
        });

        const result = mapLeadToStsRequest(lead);

        expect(result.number_of_attendees).toBe('0');
        expect(result.guest_room_requirements).toBe('0');
        expect(result.wedding_budget).toBe('0');
        expect(result.description).toContain('Number of Rooms Requested: 0');
        expect(result.description).toContain('Number of Meals Requested: 0');
    });

    it('should handle empty arrays for eventNeeds', () => {
        const lead = createLead({
            eventNeeds: [],
        });

        const result = mapLeadToStsRequest(lead);

        expect(result.function_space_requirements).toBe(null);
    });

    it('should handle missing event type', () => {
        const lead = createLead({
            company: "Test Company",
            firstName: "Paula",
            lastName: "Dean",
            eventDescription: null,
            eventName: null,
            flexibleDates: null,
            eventType: null,
            roomCount: null,
            mealCount: null
        });

        const result = mapLeadToStsRequest(lead);

        expect(result.title).toBe('Paula Dean - Event');
        expect(result.description).toBe('');
    });

    it('should include room and meal counts in description when present and exclude when not present', () => {
        const leadWithCounts = createLead({
            roomCount: 25,
            mealCount: 30,
            eventType: 'corporate_meeting',
            eventDescription: null,
            flexibleDates: null,
        });

        const resultWithCounts = mapLeadToStsRequest(leadWithCounts);
        expect(resultWithCounts.description).toContain('Number of Rooms Requested: 25');
        expect(resultWithCounts.description).toContain('Number of Meals Requested: 30');

        const leadWithoutCounts = createLead({
            roomCount: null,
            mealCount: null,
            eventType: 'corporate_meeting',
            eventDescription: null,
            flexibleDates: null,
        });

        const resultWithoutCounts = mapLeadToStsRequest(leadWithoutCounts);
        expect(resultWithoutCounts.description).not.toContain('Number of Rooms Requested');
        expect(resultWithoutCounts.description).not.toContain('Number of Meals Requested');
    });

    it('should default email and phone to "unknown" when not provided', () => {
        const leadWithoutContactInfo = createLead({
            email: null,
            phone: null,
        });

        const result = mapLeadToStsRequest(leadWithoutContactInfo);

        expect(result.customer_contact_email).toBe('<EMAIL>');
        expect(result.customer_contact_phone).toBe('unknown');
    });

    it('should format description with all fields in correct order', () => {
        const lead = createLead({
            eventType: 'wedding',
            eventName: "My most wonderful wedding!",
            eventDescription: 'Beautiful garden wedding',
            flexibleDates: true,
            roomCount: 15,
            mealCount: 100
        });

        const result = mapLeadToStsRequest(lead);
        const description = result.description || '';
        const descriptionLines = description.split('\n');

        // Check order of sections
        expect(descriptionLines[0]).toBe('Wedding');
        expect(descriptionLines[2]).toBe('Event Name: My most wonderful wedding!');
        expect(descriptionLines[4]).toBe('Description: Beautiful garden wedding');
        expect(descriptionLines[6]).toBe('Dates are flexible.');
        expect(descriptionLines[8]).toBe('Number of Rooms Requested: 15');
        expect(descriptionLines[10]).toBe('Number of Meals Requested: 100');
    });

    it('should handle event needs formatting correctly', () => {
        const lead = createLead({
            eventNeeds: ['MEETING_SPACE', 'CATERING']
        });

        const result = mapLeadToStsRequest(lead);
        expect(result.function_space_requirements).toBe('Meeting Space, Catering');
    });

    it('should correctly encode file buffer to base64 string', () => {
        const lead = createLead();
        
        // Create a simple buffer with known content
        const fileBuffer = Buffer.from('This is a test file content');
        
        // Expected base64 encoded string
        const expectedBase64 = 'VGhpcyBpcyBhIHRlc3QgZmlsZSBjb250ZW50';
        
        const result = mapLeadToStsRequest(lead, fileBuffer);
        
        // Assert that the file content was correctly encoded
        expect(result.file).toBe(expectedBase64);
    });
    
    it('should handle undefined file content', () => {
        const lead = createLead();
        
        const result = mapLeadToStsRequest(lead);
        
        // When no file content is provided, fileContentBase64 should be undefined
        expect(result.file).toBeUndefined();
    });
}); 
