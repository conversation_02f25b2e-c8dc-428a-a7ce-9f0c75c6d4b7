import { describe, it, expect, beforeEach } from 'vitest';
import { InngestTestEngine } from '@inngest/test';
import { mock, MockProxy } from 'vitest-mock-extended';
import { ok, err } from 'neverthrow';
import { createStsSyncFunction } from './sts-sync-workflow';
import { createStsInngestClient } from '../inngest-client';
import {StsSyncApplicationService} from "@/StsSyncApplicationService";
import {LeadAssignedEventData} from "@/events";

type Jsonify<T> =
    T extends Date ? string :
        T extends object ? { [K in keyof T]: Jsonify<T[K]> } :
            T;

// Minimal event data for lead.assigned
const propertyId = 'property-456';
const leadId = 'lead-123';
const files = [
  {
    s3Key: 'submitted/file1.pdf',
    keyWithoutStatusPrefix: 'file1.pdf',
    fileName: 'file1.pdf',
    contentType: 'application/pdf',
    size: 1024
  }
];

const eventData = {
  leadId,
  leadData: {
    id: leadId,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '************',
    propertyId,
    status: 'NEW',
    createdAt: '2023-04-20T14:30:00Z',
    files
  },
};

describe('STS File Sync Workflow', () => {
  let appService: MockProxy<StsSyncApplicationService>;
  let testEngine: InngestTestEngine;

  beforeEach(() => {
    appService = mock<StsSyncApplicationService>();
    const inngest = createStsInngestClient();
    const fn = createStsSyncFunction(inngest, appService);
    testEngine = new InngestTestEngine({ function: fn });
    
    // Default file fetching mock - return empty buffer for most tests
    // Individual tests can override this as needed
    const emptyBuffer = Buffer.from('');
    appService.fetchFileFromS3.mockResolvedValue(ok(emptyBuffer));
  });

  it('successfully fetches STS settings and completes workflow', async () => {
    appService.fetchStsSettings.mockResolvedValue(
      ok({ stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' })
    );
    appService.syncLead.mockResolvedValue(
      ok({ stsLeadId: "42", stsIntegrationConfigured: true })
    );

    const { result } = await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    expect(result).toMatchObject({
      status: 'success',
      message: `Fetched STS settings for property ${propertyId}`,
      leadId,
      propertyId,
      filesCount: files.length,
      stsSettings: { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' },
      error: undefined
    });
    expect(appService.fetchStsSettings).toHaveBeenCalledWith(propertyId);
  });

  it('handles error when STS settings fetch fails', async () => {
    appService.fetchStsSettings.mockResolvedValue(
      err(new Error('not found'))
    );

    // With InngestTestEngine, the error is caught and included in the result
    const { error } = await testEngine.execute({
      events: [{name: 'lead.assigned', data: eventData}]
    });

    expect((error as Error).message).toBe('Failed to fetch STS settings: not found');
    expect(appService.fetchStsSettings).toHaveBeenCalledWith(propertyId);
  });

  it('handles events with no files gracefully', async () => {
    appService.fetchStsSettings.mockResolvedValue(
      ok({ stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' })
    );
    appService.syncLead.mockResolvedValue(
      ok({ stsLeadId: "42", stsIntegrationConfigured: true })
    );

    const noFilesLeadData = {...eventData.leadData, files: []}
    const noFilesEventData : Jsonify<LeadAssignedEventData> = { ...eventData, leadData: noFilesLeadData };
    const { result } = await testEngine.execute({
      events: [{ name: 'lead.assigned', data: noFilesEventData }]
    });
    expect(result).toMatchObject({
      status: 'success',
      filesCount: 0
    });
  });

  it('calls syncLead with correct arguments when STS settings are fetched', async () => {
    const stsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    const emptyBuffer = Buffer.from('');
    
    appService.fetchStsSettings.mockResolvedValue(ok(stsSettings));
    appService.syncLead.mockResolvedValue(ok({ stsLeadId: "123", stsIntegrationConfigured: true }));

    await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    expect(appService.syncLead).toHaveBeenCalledWith({
      lead: eventData.leadData,
      stsSettings,
      fileContent: emptyBuffer
    });
  });

  it('throws error if syncLead returns Err', async () => {
    const stsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    appService.fetchStsSettings.mockResolvedValue(ok(stsSettings));
    appService.syncLead.mockResolvedValue(err({ message: 'sync failed' }));

    const { error } = await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    expect((error as Error).message).toBe('Failing sync-to-sts step: sync failed');
  });

  it('includes fields from syncLead result in workflow result', async () => {
    const stsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    appService.fetchStsSettings.mockResolvedValue(ok(stsSettings));
    appService.syncLead.mockResolvedValue(ok({ stsLeadId: "999", stsIntegrationConfigured: true, extraField: 'extra' }));

    const { result } = await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    expect(result).toMatchObject({
      status: 'success',
      stsLeadId: "999",
      stsIntegrationConfigured: true,
      extraField: 'extra'
    });
  });

  it('fetches file from S3 and passes it to syncLead when files are present', async () => {
    // Setup
    const stsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    const fileBuffer = Buffer.from('test file content');
    
    // Mock the fetchStsSettings response
    appService.fetchStsSettings.mockResolvedValue(ok(stsSettings));
    
    // Override the default mock for this test
    appService.fetchFileFromS3.mockResolvedValue(ok(fileBuffer));
    
    // Mock the syncLead response
    appService.syncLead.mockResolvedValue(ok({ stsLeadId: "42", stsIntegrationConfigured: true }));

    // Execute the workflow
    await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    // Verify fetchFileFromS3 was called with the correct S3 key
    expect(appService.fetchFileFromS3).toHaveBeenCalledWith('submitted/file1.pdf');
    
    // Verify syncLead was called with the lead data, sts settings, and file buffer
    expect(appService.syncLead).toHaveBeenCalledWith({
      lead: eventData.leadData,
      stsSettings,
      fileContent: fileBuffer
    });
  });

  it('throws an exception when file fetch from S3 fails', async () => {
    // Setup
    const stsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    
    // Mock the fetchStsSettings response
    appService.fetchStsSettings.mockResolvedValue(ok(stsSettings));
    
    // Mock fetchFileFromS3 to return an error
    const fetchError = new Error('File not found');
    appService.fetchFileFromS3.mockResolvedValue(err(fetchError));

    // Execute the workflow and expect it to throw
    const { error } = await testEngine.execute({
      events: [{ name: 'lead.assigned', data: eventData }]
    });

    // Verify fetchFileFromS3 was called
    expect(appService.fetchFileFromS3).toHaveBeenCalledWith('submitted/file1.pdf');
    
    // Verify syncLead was NOT called since workflow should fail before reaching it
    expect(appService.syncLead).not.toHaveBeenCalled();
    
    // Verify the error message indicates file fetch failure
    expect((error as Error).message).toContain('Failed to fetch file from S3');
  });
}); 
