import {LeadAssignedEvent, StsInngestClient} from "../inngest-client";
import {logger} from "../logger";
import {StsSyncApplicationService} from "@/StsSyncApplicationService";


/**
 * Creates an Inngest function that triggers on lead.assigned events with files
 * and handles the STS file sync workflow
 */
export const createStsSyncFunction = (
    inngest: StsInngestClient,
    applicationService: StsSyncApplicationService
) => {
    return inngest.createFunction(
        {id: "sts-sync"},
        {
            event: "lead.assigned",
        },
        async ({event, step}: { event: { data: LeadAssignedEvent["data"] }, step: any }) => {
            const files = event.data.leadData.files ?? [];
            const filesCount = files.length;
            const propertyId = event.data.leadData.propertyId;
            logger.info(
                "Processing lead.assigned event for STS file sync",
                {
                    leadId: event.data.leadId,
                    propertyId: propertyId,
                    filesCount
                }
            );


            // Use the injected applicationService
            const stsSettingsResult = await applicationService.fetchStsSettings(propertyId);
            if (stsSettingsResult.isOk()) {
                logger.info("Fetched STS settings", stsSettingsResult.value);
                
                // Fetch file from S3 if available
                let fileContent : Buffer | undefined;
                if (filesCount > 0) {
                    const s3Key = files[0].s3Key; // Use the first file
                    const fileResult = await applicationService.fetchFileFromS3(s3Key);
                    
                    if (fileResult.isErr()) {
                        logger.error(`Failed to fetch file from S3: ${fileResult.error.message}`, { s3Key });
                        // Throw an error to fail the workflow when file fetch fails
                        throw new Error(`Failed to fetch file from S3: ${fileResult.error.message}`);
                    } else {
                        fileContent = fileResult.value;
                        logger.info(`Successfully fetched file from S3`, { s3Key, size: fileContent.length });
                    }
                }
                
                const result = await applicationService.syncLead({
                    lead: event.data.leadData,
                    stsSettings: stsSettingsResult.value,
                    fileContent
                });

                // Await the result of .match() and return the plain object
                return await result.match(
                    async (syncResult) => {
                        return {
                            status: "success",
                            message: `Fetched STS settings for property ${propertyId}`,
                            leadId: event.data.leadId,
                            propertyId: propertyId,
                            filesCount,
                            stsSettings: stsSettingsResult.value,
                            error: undefined,
                            ...syncResult // include any additional fields from syncLead
                        };
                    },
                    async (error) => {
                        // Throw an actual error object for Inngest to catch
                        throw new Error(`Failing sync-to-sts step: ${error.message}`, { cause: error });
                    }
                );
            } else {
                logger.error("Failed to fetch STS settings", stsSettingsResult.error);
                // Throw an error to allow Inngest to retry
                throw new Error(`Failed to fetch STS settings: ${stsSettingsResult.error.message}`, { cause: stsSettingsResult.error });
            }
        }
    );
}; 
