import {StsSyncApplicationService} from '@/StsSyncApplicationService';
import {createStsSyncFunction} from './sts-sync-workflow';
import {createStsInngestClient} from '@/inngest-client';
import {mock} from 'vitest-mock-extended';
import {LeadAssignedEvent, LeadAssignedEventData} from '@/events';
import {InngestTestEngine} from '@inngest/test';
import {beforeEach, describe, expect, it} from 'vitest';
import {ok} from 'neverthrow';
import {Lead} from "@siv/lead-types";
import {
  createMessagePactProvider,
  pactMatchers,
  PARTICIPANT_NAMES,
  REGEX
} from '@siv/pact-helpers';
import {MessageConsumerPact} from "@pact-foundation/pact";
import {randomUUID} from 'crypto';

const { like, eachLike, term, boolean, integer } = pactMatchers.message;

const CONSUMER_NAME = PARTICIPANT_NAMES.STS_SYNC_WORKFLOW;
const PROVIDER_NAME = PARTICIPANT_NAMES.SIV_WEB_EVENT_PROVIDER;

type LeadFile = LeadAssignedEventData["leadData"]["files"][number];
describe('STS Sync Workflow - Event Contract Tests', () => {
  let messagePact: MessageConsumerPact;
  let appService: ReturnType<typeof mock<StsSyncApplicationService>>;
  let testEngine: InngestTestEngine;

  beforeEach(() => {
    messagePact = createMessagePactProvider({
      consumerName: CONSUMER_NAME,
      providerName: PROVIDER_NAME
    });

    // Mock the application service
    appService = mock<StsSyncApplicationService>();
    const mockStsSettings = { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' };
    appService.fetchStsSettings.mockResolvedValue(ok(mockStsSettings));
    appService.fetchFileFromS3.mockResolvedValue(ok(Buffer.from('test content')));
    appService.syncLead.mockResolvedValue(ok({
      stsLeadId: "42",
      stsIntegrationConfigured: true
    }));

    const inngest = createStsInngestClient();
    const fn = createStsSyncFunction(inngest, appService);
    testEngine = new InngestTestEngine({ function: fn });
  });

  const leadAssignedEventName : LeadAssignedEvent["name"] = 'lead.assigned';
  
  it('should process a valid lead.assigned event with nullable fields', () => {
    // Create type-safe example data that matches the LeadAssignedEventData type
    const propertyId = 'property-456';
    const leadId = 'lead-123';

    // Use PACT matchers to enforce type validation while allowing dynamic values
    return messagePact
      .expectsToReceive('a lead.assigned event')
      .withMetadata({
        name: leadAssignedEventName
      })
      .withContent({
        leadId: like(leadId),
        leadData: {
          id: like(leadId),
          firstName: like('John'),
          lastName: like('Doe'),
          email: term({
            generate: '<EMAIL>',
            matcher: REGEX.EMAIL
          }),
          phone: like('************'),
          propertyId: like(propertyId),
          eventType: term({
            generate: 'wedding',
            matcher: REGEX.EVENT_TYPE
          }),
          startDate: null,
          endDate: null,
          assignedSalesRepEmail: null,
          guestCount: null,
          company: null,
          roomCount: null,
          eventNeeds: null,
          eventName: null,
          eventDescription: null,
          flexibleDates: null,
          mealCount: null,
          budget: null,
          createdAt: term({
            generate: '2023-04-20T14:30:00Z',
            matcher: REGEX.ISO_DATE
          }),
          updatedAt: null,
          marketingConsent: boolean(false),
          assignedAt: null,
          country: null,
          city: null,
          state: null,
          postalCode: null,
          source: like('form_submission'),
          sourceId: like('source-123'),
          highlevelContactId: null,
          highlevelOpportunityId: null,
          files: [{
            s3Key: like('submitted/file1.pdf'),
            s3KeyWithoutStatusPrefix: like('file1.pdf'),
            fileName: like('file1.pdf'),
            contentType: like('application/pdf'),
            id: like('file-123'),
            createdAt: term({
              generate: '2023-04-20T14:30:00Z',
              matcher: REGEX.ISO_DATE
            }),
            updatedAt: null,
            leadId: like(leadId)
          }]
        }
      })
      .verify(async (message) => {
        const metadata = message.metadata ?? {};
        if (typeof metadata.name !== 'string') {
          throw new Error('Event name must be provided in metadata and must be a string');
        }
        const eventName = metadata.name;
        const eventData = message.contents as unknown as LeadAssignedEventData;

        // For Inngest, we need to pass the event in the format it expects
        const result = await testEngine.execute({
          events: [
            {
              name: eventName,
              data: eventData
            }
          ]
        });

        expect(result.result).toMatchObject({
          status: 'success',
          leadId: leadId,
          propertyId: propertyId,
          filesCount: 1,
          stsSettings: { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' }
        });

        expect(appService.fetchStsSettings).toHaveBeenCalledWith(propertyId);
        expect(appService.fetchFileFromS3).toHaveBeenCalledWith('submitted/file1.pdf');

        expect(appService.syncLead).toHaveBeenCalledWith({
          lead: eventData?.leadData,
          stsSettings: { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' },
          fileContent: Buffer.from('test content')
        });
      });
  });

  it('should process a valid lead.assigned event with populated fields', () => {
    const propertyId = 'property-456';
    const leadId = 'lead-123';

    return messagePact
      .expectsToReceive('a lead.assigned event with populated fields')
      .withMetadata({
        name: leadAssignedEventName
      })
      .withContent({
        leadId: like(leadId),
        leadData: {
          id: like(leadId),
          firstName: like('John'),
          lastName: like('Doe'),
          email: term({
            generate: '<EMAIL>',
            matcher: REGEX.EMAIL
          }),
          phone: like('************'),
          propertyId: like(propertyId),
          eventType: term({
            generate: 'wedding',
            matcher: REGEX.EVENT_TYPE
          }),
          startDate: term({
            generate: '2023-06-15T00:00:00Z',
            matcher: REGEX.ISO_DATE
          }),
          endDate: term({
            generate: '2023-06-17T00:00:00Z',
            matcher: REGEX.ISO_DATE
          }),
          assignedSalesRepEmail: term({
            generate: '<EMAIL>',
            matcher: REGEX.EMAIL
          }),
          guestCount: integer(150),
          company: like('Example Corp'),
          roomCount: integer(75),
          eventNeeds: eachLike(term({
            generate: 'CATERING',
            matcher: '^[A-Z_]+$'
          })),
          eventName: like('Smith-Johnson Wedding'),
          eventDescription: like('Outdoor ceremony with indoor reception'),
          flexibleDates: boolean(false),
          mealCount: integer(3),
          budget: integer(25000),
          createdAt: term({
            generate: '2023-04-20T14:30:00Z',
            matcher: REGEX.ISO_DATE
          }),
          updatedAt: null,
          marketingConsent: boolean(false),
          assignedAt: null,
          country: null,
          city: like('Austin'),
          state: like('TX'),
          postalCode: like('78701'),
          source: like('form_submission'),
          sourceId: like('source-123'),
          highlevelContactId: null,
          highlevelOpportunityId: null,
          files: eachLike({
            s3Key: like('submitted/file1.pdf'),
            s3KeyWithoutStatusPrefix: like('file1.pdf'),
            fileName: like('file1.pdf'),
            contentType: like('application/pdf'),
            id: like('file-123'),
            createdAt: term({
              generate: '2023-04-20T14:30:00Z',
              matcher: REGEX.ISO_DATE
            }),
            updatedAt: null,
            leadId: like(leadId)
          }, { min: 1 }) // At least one file is required
        }
      })
      .verify(async (message) => {
        const metadata = message.metadata ?? {};
        if (typeof metadata.name !== 'string') {
          throw new Error('Event name must be provided in metadata and must be a string');
        }
        const eventName = metadata.name;
        const eventData = message.contents as unknown as LeadAssignedEventData;

        // For Inngest, we need to pass the event in the format it expects
        const result = await testEngine.execute({
          events: [
            {
              name: eventName,
              data: eventData
            }
          ]
        });

        // Verify the function processed the event correctly
        expect(result.result).toMatchObject({
          status: 'success',
          leadId: leadId,
          propertyId: propertyId,
          filesCount: 1,
          stsSettings: { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' }
        });

        // Verify service calls were called with the correct parameters
        expect(appService.fetchStsSettings).toHaveBeenCalledWith(propertyId);
        expect(appService.fetchFileFromS3).toHaveBeenCalledWith('submitted/file1.pdf');

        // Verify syncLead was called with the correct parameters
        expect(appService.syncLead).toHaveBeenCalledWith({
          lead: eventData.leadData,
          stsSettings: { stsUsername: 'user', apiKey: 'key', rfpFormId: 'rfp' },
          fileContent: Buffer.from('test content')
        });
      });
  });
});
