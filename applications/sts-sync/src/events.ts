import {Lead} from "@siv/lead-types";

export type LeadAssignedEvent = {
    name: 'lead.assigned';
    id: string;
    data: LeadAssignedEventData;
}

type BaseEventData = {
    leadId: string;
}


export type LeadAssignedEventData = {
    leadId: string;
    // Full Lead domain object data
    leadData: Lead & {
        files: Array<{
            s3Key: string;
            keyWithoutStatusPrefix: string;
            fileName: string;
            contentType: string;
        }>
    };
}

export type LeadEventTypes = LeadAssignedEvent

// Complete event type definitions derived from existing types
export type LeadEventsSchema = {
    [K in LeadEventTypes['name']]: Extract<LeadEventTypes, { name: K }>
}
