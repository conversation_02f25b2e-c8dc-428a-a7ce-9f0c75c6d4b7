import {defineConfig} from "vitest/config";
import { resolve } from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
    plugins: [
        tsconfigPaths({
            loose: true
        })
    ],
    test: {
        environment: 'node',
        environmentOptions: {
            // Setup environment variables for tests
            env: {
                NODE_ENV: 'test'
            }
        },
        // Isolate each test file for consistent behavior
        isolate: true,
        // Increase timeout for tests
        testTimeout: 10000,
        // Mock global objects
        globals: true,
        // Ensure clean state between tests
        restoreMocks: true,
        setupFiles: ['vitest.setup.ts']
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, './src')
        }
    }
}); 
