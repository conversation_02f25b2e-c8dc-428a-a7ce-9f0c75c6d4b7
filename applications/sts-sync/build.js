#!/usr/bin/env node
import { build } from 'esbuild';
import { mkdirSync, existsSync, rmSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

// Get dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Clean the dist directory if it exists
const distDir = join(__dirname, 'dist');
if (existsSync(distDir)) {
  console.log('Cleaning dist directory...');
  rmSync(distDir, { recursive: true, force: true });
}

// Create a fresh dist directory
mkdirSync(distDir, { recursive: true });

async function bundle() {
  try {
    // Bundle the Lambda code
    const result = await build({
      entryPoints: ['src/index.ts'],
      bundle: true,
      minify: true,
      sourcemap: true,
      // Include sources in source maps for better debugging in production
      sourcesContent: true,
      platform: 'node',
      target: 'node20',
      outfile: 'dist/index.js',
      format: 'esm',
      banner: { 
        js: `// eslint-disable-next-line
import { createRequire } from "module"; 
const require = createRequire(import.meta.url);`
      },
      // Only mark AWS SDK as external, bundle everything else
      external: [
        '@aws-sdk/*',
      ],
      logLevel: 'info',
      // Enable tree shaking to reduce package size
      treeShaking: true,
      // Add a plugin to resolve workspace dependencies
      plugins: [
        {
          name: 'resolve-workspace-deps',
          setup(build) {
            // Custom resolver for @siv/highlevel-client
            build.onResolve({ filter: /^@siv\/highlevel-client$/ }, args => {
              // Resolve to the actual file
              return {
                path: join(__dirname, '../../packages/highlevel-client/dist/index.js')
              };
            });
          }
        }
      ]
    });

    console.log('Build complete:', result);
    
    // Create a small package.json for Lambda
    const lambdaPackageJson = {
      "name": "sts-sync-lambda",
      "version": "0.1.0",
      "type": "module",
      "private": true,
      "main": "index.js"
    };
    
    // Write the minimal package.json to dist
    const fs = await import('fs/promises');
    await fs.writeFile(
      join(__dirname, 'dist', 'package.json'),
      JSON.stringify(lambdaPackageJson, null, 2)
    );

    console.log('Successfully bundled Lambda function!');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

bundle(); 
