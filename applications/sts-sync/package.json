{"name": "sts-sync", "version": "1.0.0", "description": "Lambda to sync files from S3 to STS CRM on lead assigned events", "main": "dist/index.js", "type": "module", "scripts": {"build": "node build.js", "build:watch": "node build-watch.js", "check-types": "tsc --noEmit", "sam:start": "sam local start-api -t template.yaml --env-vars env.json --port=3001", "dev": "concurrently \"pnpm build:watch\" \"pnpm sam:start\"", "invoke": "pnpm build && sam local invoke -t template.yaml StsFileSyncFunction --event events/lead-assigned-event.json --env-vars env.json", "package": "cd ../.. && pnpm turbo run build --filter=sts-sync... && rm -rf deployment/lambda-packages/sts-sync && mkdir -p deployment/lambda-packages/sts-sync && cp -r applications/sts-sync/dist/* deployment/lambda-packages/sts-sync/ && cd deployment/lambda-packages/sts-sync && zip -q -r function.zip .", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "pact:test": "vitest run src/**/*.pact.test.ts", "pact:test:consumer": "vitest run $(pnpm dlx glob-cli '**/*.consumer.pact.test.ts')", "pact:test:provider": "vitest run $(pnpm dlx glob-cli '**/*.provider.pact.test.ts')", "pact:publish": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/publish-pacts.ts sts-sync-workflow", "pact:can-i-deploy": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/track-deployment check --env staging --participant sts-sync-workflow", "pact:record-deployment": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/track-deployment record --env staging --participant sts-sync-workflow"}, "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "@aws-sdk/client-servicediscovery": "^3.777.0", "@siv/lead-sync-field-helpers": "workspace:*", "@siv/lead-types": "workspace:*", "@siv/pact-helpers": "workspace:*", "aws-lambda": "^1.0.7", "dotenv-flow": "^4.1.0", "hono": "^4.7.5", "inngest": "^3.34.1", "neverthrow": "8.2.0", "node-fetch": "^3.3.2", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "source-map-support": "^0.5.21", "zod": "^3.24.1"}, "devDependencies": {"@inngest/test": "^0.1.5", "@pact-foundation/pact": "^15.0.1", "@pact-foundation/pact-cli": "^16.0.7", "@pact-foundation/pact-node": "^10.18.0", "@types/aws-lambda": "^8.10.148", "@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.2", "concurrently": "^8.2.2", "esbuild": "^0.25.2", "fishery": "^2.2.3", "nock": "^14.0.2", "nodemon": "^3.1.9", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.1", "vitest-mock-extended": "^2.0.2"}}