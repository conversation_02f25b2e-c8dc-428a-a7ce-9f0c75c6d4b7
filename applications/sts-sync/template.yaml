AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: STS File Sync Lambda function for syncing lead files to STS CRM

Globals:
  Function:
    Timeout: 30
    Runtime: nodejs18.x
    MemorySize: 256
    Environment:
      Variables:
        NODE_ENV: development
        ENVIRONMENT: development
        INNGEST_DEV: 1
        INNGEST_SERVE_PATH: /api/inngest
        S3_LEAD_FORM_FILE_UPLOAD_BUCKET: !Ref LeadFormFileUploadBucket
        SIVWEB_INTERNAL_API_BASE_URL: 'http://localhost:8081'
        SIVWEB_INTERNAL_API_SECRET: 'placeholder-secret'

Resources:
  # Define the Lambda function
  StsSyncFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./dist
      Handler: index.handler
      Runtime: nodejs20.x
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          ENVIRONMENT: development
          NODE_ENV: development
          INNGEST_SERVE_PATH: /api/inngest
          INNGEST_LOG_LEVEL: debug
          S3_ENDPOINT: http://host.docker.internal:9000
          S3_ACCESS_KEY: minioadmin
          S3_SECRET_KEY: minioadmin
          S3_FORCE_PATH_STYLE: true
          S3_LEAD_FORM_FILE_UPLOAD_BUCKET: dev-lead-form-file-uploads
          INNGEST_EVENT_KEY: test_key
          INNGEST_SIGNING_KEY: test_signing_key
          INNGEST_DEV: "1"
          INNGEST_BASE_URL: http://host.docker.internal:8288
          INNGEST_SERVE_HOST: http://host.docker.internal:3001
          SIVWEB_INTERNAL_API_SECRET: "siv-web-internal-api-secret-development"
          SIVWEB_INTERNAL_API_BASE_URL: "http://host.docker.internal:5173"
      Events:
        # For local testing only, we'll add a direct HTTP endpoint
        ApiEvent:
          Type: HttpApi
          Properties:
            Path: /sync
            Method: POST
        # Health check endpoint
        HealthCheck:
          Type: HttpApi
          Properties:
            Path: /health
            Method: GET
        # Inngest webhook endpoint
        InngestWebhook:
          Type: HttpApi
          Properties:
            Path: /api/inngest
            Method: ANY

  # Define the S3 bucket for testing
  LeadFormFileUploadBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "lead-form-file-uploads-${AWS::StackName}"

Outputs:
  StsFileSyncFunction:
    Description: "STS File Sync Lambda Function ARN"
    Value: !GetAtt StsFileSyncFunction.Arn

  StsFileSyncApi:
    Description: "API Gateway endpoint URL for STS File Sync Lambda"
    Value: !Sub "https://${ServerlessHttpApi}.execute-api.${AWS::Region}.amazonaws.com/"

  StsFileSyncFunctionRole:
    Description: "Implicit IAM Role created for STS File Sync Lambda"
    Value: !GetAtt StsFileSyncFunctionRole.Arn 
