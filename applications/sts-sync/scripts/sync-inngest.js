#!/usr/bin/env node
import { exec } from 'child_process';
import { promisify } from 'util';
import fetch from 'node-fetch';

const execAsync = promisify(exec);

// Get the Lambda function URL
const getLambdaUrl = async () => {
  console.log('Retrieving Lambda Function URL...');
  
  const STACK_NAME = 'sts-sync-app';
  
  try {
    // Display current AWS configuration
    try {
      console.log('Checking current AWS profile and region...');
      const { stdout: awsConfig } = await execAsync('aws configure list');
      console.log('AWS Configuration:');
      console.log(awsConfig);
      
      const { stdout: identity } = await execAsync('aws sts get-caller-identity');
      console.log('AWS Identity:');
      console.log(identity);
    } catch (configError) {
      console.warn('Unable to retrieve AWS configuration:', configError.message);
    }
    
    // List all CloudFormation stacks to help with debugging
    console.log('Listing available CloudFormation stacks...');
    try {
      const { stdout: stacks } = await execAsync('aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE | jq -r ".StackSummaries[].StackName"');
      console.log('Available stacks:');
      console.log(stacks || 'No stacks found');
      
      // Check if our stack is in the list
      const stackList = stacks.split('\n').map(s => s.trim()).filter(Boolean);
      const stackExists = stackList.includes(STACK_NAME);
      console.log(`Stack '${STACK_NAME}' found in available stacks: ${stackExists ? 'YES' : 'NO'}`);
      
    } catch (listError) {
      console.warn('Unable to list CloudFormation stacks:', listError.message);
    }
    
    // First check if the stack exists
    console.log(`Checking if stack '${STACK_NAME}' exists...`);
    try {
      await execAsync(`aws cloudformation describe-stacks --stack-name "${STACK_NAME}"`);
      console.log(`Stack '${STACK_NAME}' exists.`);
    } catch (error) {
      console.error(`Stack '${STACK_NAME}' not found. Have you deployed the Lambda function with 'pnpm deploy:cdk' yet?`);
      console.log('Possible issues:');
      console.log('1. The stack might be in a different AWS region or account');
      console.log('2. The stack might have a different name');
      console.log('3. You may need to specify AWS_REGION or AWS_PROFILE environment variables');
      throw new Error(`Stack '${STACK_NAME}' not found. Run 'pnpm deploy:cdk' first to deploy the Lambda function or check AWS region/profile.`);
    }
    
    // Get the Lambda URL from the stack outputs
    console.log('Getting Lambda URL from stack outputs...');
    const { stdout } = await execAsync(`aws cloudformation describe-stacks --stack-name "${STACK_NAME}" | jq -r '.Stacks[0].Outputs[] | select(.OutputKey=="LambdaFunctionUrl") | .OutputValue'`);
    
    const lambdaUrl = stdout.trim();
    
    if (!lambdaUrl) {
      console.error(`Lambda Function URL not found in stack '${STACK_NAME}'. Stack outputs:`);
      const { stdout: outputs } = await execAsync(`aws cloudformation describe-stacks --stack-name "${STACK_NAME}" | jq '.Stacks[0].Outputs'`);
      console.log(outputs);
      throw new Error('Lambda Function URL output not found in CloudFormation stack. Check the stack template.');
    }
    
    console.log(`Lambda Function URL: ${lambdaUrl}`);
    return lambdaUrl;
  } catch (error) {
    console.error('Error retrieving Lambda URL:', error);
    
    // Check AWS credentials
    try {
      console.log('Checking AWS credentials...');
      await execAsync('aws sts get-caller-identity');
      console.log('AWS credentials are valid.');
    } catch (credError) {
      console.error('AWS credentials issue detected. Make sure you have valid AWS credentials configured.');
    }
    
    throw error;
  }
};

// Sync Inngest functions
const syncInngest = async (lambdaUrl) => {
  console.log('Syncing Inngest function definitions...');
  
  try {
    if(lambdaUrl.endsWith('/')) {
      console.log('Lambda URL ends with a slash, removing it so we construct a proper url for the ingest sync...');
      lambdaUrl = lambdaUrl.slice(0, -1);
    }
    const lambdaInggestUrl = `${lambdaUrl}/api/inngest`;
    console.log(`Sending PUT request to ${lambdaInggestUrl}`);
    const response = await fetch(lambdaInggestUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to sync Inngest functions: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.text();
    console.log('Inngest sync response:', data);
    console.log('Inngest sync complete.');
  } catch (error) {
    console.error('Error syncing Inngest functions:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    console.log('Starting Inngest sync process...');
    const lambdaUrl = await getLambdaUrl();
    await syncInngest(lambdaUrl);
    console.log('Inngest sync completed successfully!');
  } catch (error) {
    console.error('Error syncing Inngest functions:', error);
    console.log('\nTroubleshooting steps:');
    console.log('1. Make sure you\'ve deployed the Lambda function with "pnpm deploy:cdk"');
    console.log('2. Check that your AWS credentials are valid');
    console.log('3. Verify that the CloudFormation stack has a LambdaFunctionUrl output');
    console.log('4. Try setting AWS_REGION environment variable, e.g.:');
    console.log('   AWS_REGION=us-west-2 pnpm deploy:sync-inngest');
    process.exit(1);
  }
};

main(); 
