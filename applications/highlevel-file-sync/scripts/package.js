#!/usr/bin/env node
import { exec } from 'child_process';
import { promisify } from 'util';
import { mkdirSync, rmSync, copyFileSync, existsSync, readdirSync } from 'fs';
import { join, resolve, dirname } from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const execAsync = promisify(exec);

// Get dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const appRoot = resolve(__dirname, '..');
const projectRoot = resolve(appRoot, '..', '..');

// Create the directories we need
const createDirectories = () => {
  console.log('Creating directories...');
  
  // Clean up any previous temp directories
  if (existsSync(join(appRoot, 'package-temp'))) {
    rmSync(join(appRoot, 'package-temp'), { recursive: true, force: true });
  }
  
  // Create deployment directory
  mkdirSync(join(projectRoot, 'deployment', 'lambda-packages', 'highlevel-file-sync'), { recursive: true });
  
  // Create temp directory for packaging
  mkdirSync(join(appRoot, 'package-temp'));
};

// Pack the application using pnpm
const packApp = async () => {
  console.log('Packing application...');
  
  try {
    // First create the tarball directly in the app directory
    console.log('Running pnpm pack command...');
    await execAsync('pnpm pack', { cwd: appRoot });
    
    // Move the created tarball to the package-temp directory
    const appFiles = readdirSync(appRoot);
    const tarballFile = appFiles.find(file => file.endsWith('.tgz'));
    
    if (!tarballFile) {
      throw new Error('Failed to create tarball in the app directory');
    }
    
    console.log(`Found tarball: ${tarballFile}`);
    
    // Move the tarball to the package-temp directory
    await execAsync(`mv ${tarballFile} package-temp/`, { cwd: appRoot });
    
    // Navigate to the package-temp directory
    process.chdir(join(appRoot, 'package-temp'));
    
    // List the directory contents to see what was created
    console.log('Directory contents after moving tarball:');
    const { stdout: dirContents } = await execAsync('ls -la', { cwd: join(appRoot, 'package-temp') });
    console.log(dirContents);
    
    // Extract the tarball
    console.log(`Extracting ${tarballFile}...`);
    await execAsync(`tar -xzf ${tarballFile}`, { cwd: join(appRoot, 'package-temp') });
    
    // Check if extraction was successful
    const extractedFiles = readdirSync(join(appRoot, 'package-temp'));
    console.log('After extraction, directory contains:', extractedFiles);
    
    // Verify the package directory exists
    if (!extractedFiles.includes('package')) {
      // Try to handle the case where the tarball might have a different structure
      // Typically the tarball creates a directory called "package"
      // But sometimes it might create a directory with the package name
      const possiblePackageDir = extractedFiles.find(file => 
        file !== tarballFile && fs.statSync(join(appRoot, 'package-temp', file)).isDirectory()
      );
      
      if (possiblePackageDir) {
        console.log(`Found alternative package directory: ${possiblePackageDir}, renaming to "package"`);
        await execAsync(`mv ${possiblePackageDir} package`, { cwd: join(appRoot, 'package-temp') });
      } else {
        throw new Error('Failed to extract package directory from tarball');
      }
    }
    
    // Remove the tarball
    rmSync(join(appRoot, 'package-temp', tarballFile));
  } catch (error) {
    console.error('Error during packing:', error);
    throw error;
  }
};

// Install production dependencies
const installDeps = async () => {
  console.log('Installing production dependencies...');
  
  // Navigate to the package directory (inside the extracted tarball)
  process.chdir(join(appRoot, 'package-temp', 'package'));
  
  // Install production dependencies
  await execAsync('pnpm install --prod --no-frozen-lockfile');
};

// Create the Lambda zip package
const createZip = async () => {
  console.log('Creating Lambda package zip...');
  
  // Navigate to the package-temp directory
  process.chdir(join(appRoot, 'package-temp'));
  
  // Zip everything in the package directory
  await execAsync('zip -q -r ../lambda-package.zip package/');
  
  // Move the zip to the deployment directory
  copyFileSync(
    join(appRoot, 'lambda-package.zip'),
    join(projectRoot, 'deployment', 'lambda-packages', 'highlevel-file-sync', 'function.zip')
  );
  
  console.log(`Lambda package created at: ${join(projectRoot, 'deployment', 'lambda-packages', 'highlevel-file-sync', 'function.zip')}`);
};

// Clean up
const cleanup = () => {
  console.log('Cleaning up...');
  
  // Remove temp directories
  rmSync(join(appRoot, 'package-temp'), { recursive: true, force: true });
  rmSync(join(appRoot, 'lambda-package.zip'), { force: true });
};

// Main function
const main = async () => {
  try {
    createDirectories();
    await packApp();
    await installDeps();
    await createZip();
    cleanup();
    console.log('Package created successfully!');
  } catch (error) {
    console.error('Error creating package:', error);
    process.exit(1);
  }
};

main(); 