// Enable source maps for stack traces
import 'source-map-support/register';

import {Hono} from "hono";
import {serve as serveInngest} from "inngest/hono";
import {handle} from "hono/aws-lambda";
import {createSyncFileToHighLevelFunction} from "./functions/highlevel-file-sync-workflow";
import {createHighlevelSyncInngestClient} from "./inngest-client";
import {HighlevelFileSyncApplicationService} from "./HighlevelFileSyncApplicationService";
import {s3Client} from "./s3Client";
import {HighLevelClient} from "@siv/highlevel-client";

// Explicitly declare process to satisfy TypeScript
declare const process: {
  env: {
    INNGEST_SERVE_PATH?: string;
    [key: string]: string | undefined;
  }
};

const inngest = createHighlevelSyncInngestClient();

const highlevelFileSyncApplicationService = new HighlevelFileSyncApplicationService(s3Client, new HighLevelClient());
const syncFileToHighLevelFn = createSyncFileToHighLevelFunction(inngest, highlevelFileSyncApplicationService);

const app = new Hono();

const inngestPath = process.env.INNGEST_SERVE_PATH || "/api/inngest";
app.use(inngestPath, serveInngest({ 
  client: inngest, 
  functions: [syncFileToHighLevelFn]
}));

export const handler = handle(app);
