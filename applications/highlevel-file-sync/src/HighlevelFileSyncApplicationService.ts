import {GetObjectCommand, GetObjectCommandOutput, S3Client} from "@aws-sdk/client-s3";
import {LeadSyncedToHighlevelEventPayload} from "./highlevel-sync-event";
import {
    FileAttachment,
    FileMeta,
    HighLevelClient,
    HighLevelUploadFileResponse
} from "../../../packages/highlevel-client/dist/highlevel-client";
import {err, ok, Result} from "neverthrow";
import {env} from "./env";
import logger from "./logger";
import {undefined} from "zod";

export class HighlevelFileSyncApplicationService {
    constructor(
        private readonly s3Client: S3Client = s3Client,
        private readonly highLevelClient: HighLevelClient
    ) {
    }

    async updateHighlevelOpportunityWithUploadedFiles(
        {uploadResponse, eventData}: {
            uploadResponse: HighLevelUploadFileResponse,
            eventData: LeadSyncedToHighlevelEventPayload
        }
    ): Promise<Result<{ opportunityId: string }, { message: string; cause?: unknown }>> {
        try {
            logger.info({
                opportunityId: eventData.highLevelOpportunityId,
                uploadedFiles: uploadResponse.uploadedFiles
            }, "Updating opportunity attachments in HighLevel");

            // Convert the uploadResponse into FileAttachment[]
            const fileAttachments: FileAttachment[] = Object.entries(uploadResponse.uploadedFiles).map(([fileName, url]) => {
                // Find matching metadata for this file
                const fileMeta = uploadResponse.meta.find(m => m.originalname === fileName);

                return {
                    fileName,
                    url,
                    contentType: fileMeta?.mimetype || 'application/octet-stream',
                    size: fileMeta?.size || 0
                };
            });

            const result = await this.highLevelClient.updateOpportunityAttachments({
                uploadResponse: uploadResponse,
                opportunityId: eventData.highLevelOpportunityId,
                authToken: eventData.highLevelAuthToken,
                uploadedFiles: fileAttachments
            });

            if (result.isErr()) {
                return err({
                    message: `Failed to update opportunity attachments in HighLevel: ${result._unsafeUnwrapErr().message}`,
                    cause: result._unsafeUnwrapErr()
                });
            }

            return ok({
                opportunityId: result.value.opportunityId
            });
        } catch (error) {
            return err({
                message: `Unexpected error updating opportunity attachments: ${error instanceof Error ? error.message : String(error)}`,
                cause: error
            });
        }
    }

    async uploadFilesToHighlevel(
        data: LeadSyncedToHighlevelEventPayload
    ): Promise<Result<HighLevelUploadFileResponse, { message: string; cause?: unknown }>> {
        try {
            const downloadPromises = data.fileUploads.map(async (fileUpload) => {
                console.log(`Downloading file ${fileUpload.fileName} from S3: ${JSON.stringify(fileUpload)}`);
                try {
                    // Create the GetObject command
                    const command = new GetObjectCommand({
                        Bucket: env.S3_LEAD_FORM_FILE_UPLOAD_BUCKET, // Use bucket from environment
                        Key: fileUpload.s3Key
                    });

                    // Send the command to get the file
                    const response: GetObjectCommandOutput = await this.s3Client.send(command);

                    const fileData = await response.Body?.transformToByteArray()

                    return {
                        fileName: fileUpload.fileName,
                        file: Buffer.from(fileData!),
                        contentType: fileUpload.contentType
                    };
                } catch (error) {
                    logger.error({error, fileUploads: data.fileUploads}, "error downloading files from S3");
                    throw new Error(`Failed to download file ${fileUpload.fileName} from S3: ${error instanceof Error ? error.message : String(error)}`);
                }
            });

            // Wait for all downloads to complete
            const filesToUpload = await Promise.all(downloadPromises);

            const uploadResult = await this.highLevelClient.uploadFilesToOpportunity({
                opportunityId: data.highLevelOpportunityId,
                authToken: data.highLevelAuthToken,
                locationId: data.highLevelLocationId,
                filesToUpload
            });

            if (uploadResult.isErr()) {
                logger.error(uploadResult._unsafeUnwrapErr(), "Failed to upload files to HighLevel");
                return err({
                    message: `Failed to upload files to HighLevel: ${uploadResult._unsafeUnwrapErr().message}`,
                    cause: uploadResult._unsafeUnwrapErr()
                });
            }

            return ok(uploadResult.value);
        } catch (error) {
            return err({
                message: `Error processing files: ${error instanceof Error ? error.message : String(error)}`,
                cause: error
            });
        }
    }
}
