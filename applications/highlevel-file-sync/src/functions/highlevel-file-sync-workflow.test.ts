import { vi, describe, it, expect, beforeEach } from 'vitest';
import { createSyncFileToHighLevelFunction } from './highlevel-file-sync-workflow';
import { LeadSyncedToHighlevelEventPayload } from '../highlevel-sync-event';
import { ok, err } from 'neverthrow';

// Mock the application service
vi.mock('../HighlevelFileSyncApplicationService');

describe('highlevel-file-sync-workflow', () => {
    // Test data
    const mockEventData: LeadSyncedToHighlevelEventPayload = {
        leadId: 'lead-123',
        highLevelContactId: 'contact-123',
        highLevelOpportunityId: 'opportunity-123',
        highLevelAuthToken: 'auth-token-123',
        highLevelLocationId: 'location-123',
        fileUploads: [
            {
                fileId: 'file-id-1',
                s3Key: 'uploads/approved/file1.pdf',
                keyWithoutStatusPrefix: 'uploads/file1.pdf',
                fileName: 'file1.pdf',
                contentType: 'application/pdf'
            }
        ]
    };

    // Mock implementations
    let mockInngest: any;
    let mockApplicationService: any;
    let mockStep: any;
    
    beforeEach(() => {
        // Reset all mocks
        vi.resetAllMocks();
        
        // Create mocks
        mockStep = {
            run: vi.fn().mockImplementation((name, fn) => fn())
        };
        
        mockInngest = {
            createFunction: vi.fn().mockReturnValue(() => {})
        };
        
        mockApplicationService = {
            uploadFilesToHighlevel: vi.fn().mockResolvedValue(ok({
                uploadedFiles: {
                    'file1.pdf': 'https://example.com/file1.pdf'
                },
                meta: [{
                    fieldname: 'field1',
                    originalname: 'file1.pdf',
                    encoding: '7bit',
                    mimetype: 'application/pdf',
                    size: 1024,
                    url: 'https://example.com/file1.pdf'
                }]
            })),
            updateHighlevelOpportunityWithUploadedFiles: vi.fn().mockResolvedValue(ok({
                opportunityId: 'opportunity-123'
            }))
        };
    });
    
    it('should create an Inngest function', () => {
        createSyncFileToHighLevelFunction(mockInngest, mockApplicationService);
        
        expect(mockInngest.createFunction).toHaveBeenCalledWith(
            {id: "sync-file-to-highlevel"},
            {event: "lead.synced-to-highlevel"},
            expect.any(Function)
        );
    });
    
    it('should process files successfully', async () => {
        // Create the function
        const syncFunction = createSyncFileToHighLevelFunction(mockInngest, mockApplicationService);
        
        // Get the handler function
        const handler = mockInngest.createFunction.mock.calls[0][2];
        
        // Call the handler with mock event and step
        const result = await handler({
            event: {
                data: mockEventData
            },
            step: mockStep
        });
        
        // Check that the application service was called with the correct data
        expect(mockApplicationService.uploadFilesToHighlevel).toHaveBeenCalledWith(mockEventData);
        
        expect(mockApplicationService.updateHighlevelOpportunityWithUploadedFiles).toHaveBeenCalledWith({
            eventData: mockEventData,
            uploadResponse: {
                uploadedFiles: {
                    'file1.pdf': 'https://example.com/file1.pdf'
                },
                meta: [{
                    fieldname: 'field1',
                    originalname: 'file1.pdf',
                    encoding: '7bit',
                    mimetype: 'application/pdf',
                    size: 1024,
                    url: 'https://example.com/file1.pdf'
                }]
            }
        });
        
        // Check the result
        expect(result).toEqual({
            status: "success",
            message: "Successfully synced 1 files",
            filesUploaded: 1,
            data: mockEventData
        });
    });
    
    it('should skip processing when no files are provided', async () => {
        // Create test data with no files
        const noFilesEventData = {
            ...mockEventData,
            fileUploads: []
        };
        
        // Create the function
        createSyncFileToHighLevelFunction(mockInngest, mockApplicationService);
        
        // Get the handler function
        const handler = mockInngest.createFunction.mock.calls[0][2];
        
        // Call the handler with mock event and step
        const result = await handler({
            event: {
                data: noFilesEventData
            },
            step: mockStep
        });
        
        // Check that the application service was not called
        expect(mockApplicationService.uploadFilesToHighlevel).not.toHaveBeenCalled();
        expect(mockApplicationService.updateHighlevelOpportunityWithUploadedFiles).not.toHaveBeenCalled();
        
        // Check the result
        expect(result).toEqual({
            status: "success",
            message: "No files to sync",
            data: noFilesEventData
        });
    });
    
    it('should handle file upload errors', async () => {
        // Mock the upload to fail
        mockApplicationService.uploadFilesToHighlevel.mockResolvedValue(err({
            message: 'Upload failed',
            cause: new Error('S3 error')
        }));
        
        // Create the function
        createSyncFileToHighLevelFunction(mockInngest, mockApplicationService);
        
        // Get the handler function
        const handler = mockInngest.createFunction.mock.calls[0][2];
        
        // Expect the function to throw
        await expect(async () => {
            await handler({
                event: {
                    data: mockEventData
                },
                step: mockStep
            });
        }).rejects.toThrow('Failed to upload files to HighLevel');
        
        // Check that only the upload was called
        expect(mockApplicationService.uploadFilesToHighlevel).toHaveBeenCalled();
        expect(mockApplicationService.updateHighlevelOpportunityWithUploadedFiles).not.toHaveBeenCalled();
    });
    
    it('should handle opportunity update errors', async () => {
        // Mock the update to fail
        mockApplicationService.updateHighlevelOpportunityWithUploadedFiles.mockResolvedValue(err({
            message: 'Update failed',
            cause: new Error('HighLevel API error')
        }));
        
        // Create the function
        createSyncFileToHighLevelFunction(mockInngest, mockApplicationService);
        
        // Get the handler function
        const handler = mockInngest.createFunction.mock.calls[0][2];
        
        // Expect the function to throw
        await expect(async () => {
            await handler({
                event: {
                    data: mockEventData
                },
                step: mockStep
            });
        }).rejects.toThrow('Failed to update opportunity attachments');
        
        // Check that both methods were called
        expect(mockApplicationService.uploadFilesToHighlevel).toHaveBeenCalled();
        expect(mockApplicationService.updateHighlevelOpportunityWithUploadedFiles).toHaveBeenCalled();
    });
}); 
