import {HighlevelSyncInngestClient} from "../inngest-client";
import {HighlevelFileSyncApplicationService} from "../HighlevelFileSyncApplicationService";

export const createSyncFileToHighLevelFunction = (
    inngest: HighlevelSyncInngestClient,
    applicationService: HighlevelFileSyncApplicationService
) => {
    return inngest.createFunction(
        {id: "sync-file-to-highlevel"},
        {event: "lead.synced-to-highlevel"},
        async ({event, step}) => {
            // Log the event data
            console.log("Processing file sync request", {
                opportunityId: event.data.highLevelOpportunityId,
                files: JSON.stringify(event.data.fileUploads)
            });

            // Skip if no files to sync
            if (!event.data.fileUploads || event.data.fileUploads.length === 0) {
                console.log('No files to sync, skipping');
                return {
                    status: "success",
                    message: "No files to sync",
                    data: event.data
                };
            }

            // Upload files to HighLevel
            const uploadResult = await step.run("upload-files-to-highlevel", async () => {
                const result = await applicationService.uploadFilesToHighlevel(event.data);

                if (result.isErr()) {
                    console.error('Failed to upload files to HighLevel:', JSON.stringify(result.error));
                    throw new Error(`Failed to upload files to HighLevel: ${result.error.message}`);
                }

                return result.value;
            });

            // Update opportunity with uploaded files
            await step.run('update-opportunity-attachments', async () => {
                const result = await applicationService.updateHighlevelOpportunityWithUploadedFiles({
                    uploadResponse: uploadResult,
                    eventData: event.data
                });

                if (result.isErr()) {
                    console.error('Failed to update opportunity attachments:', result.error);
                    throw new Error(`Failed to update opportunity attachments: ${result.error.message}`);
                }

                return result.value;
            });

            return {
                status: "success",
                message: `Successfully synced ${Object.keys(uploadResult.uploadedFiles).length} files`,
                filesUploaded: Object.keys(uploadResult.uploadedFiles).length,
                data: event.data
            };
        }
    );
};
