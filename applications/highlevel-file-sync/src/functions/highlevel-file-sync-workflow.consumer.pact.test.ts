import {mock} from 'vitest-mock-extended';
import {beforeEach, describe, expect, it} from 'vitest';
import {InngestTestEngine} from '@inngest/test';
import {ok} from 'neverthrow';
import {createMessagePactProvider, pactMatchers, PARTICIPANT_NAMES} from '@siv/pact-helpers';
import {MessageConsumerPact} from '@pact-foundation/pact';
import {createSyncFileToHighLevelFunction} from './highlevel-file-sync-workflow';
import {HighlevelFileSyncApplicationService} from '@/HighlevelFileSyncApplicationService';
import {createHighlevelSyncInngestClient} from '../inngest-client';
import {LeadSyncedToHighlevelEventPayload} from '../highlevel-sync-event';

// Import PACT matching helpers
const {like, eachLike, term, boolean, integer} = pactMatchers.message;

// PACT setup constants
const CONSUMER_NAME = PARTICIPANT_NAMES.HIGH_LEVEL_FILE_SYNC_WORKFLOW;
const PROVIDER_NAME = PARTICIPANT_NAMES.SIV_WEB_EVENT_PROVIDER;

describe('HighLevel File Sync Workflow - Event Contract Tests', () => {
    let messagePact: MessageConsumerPact;
    let appService: ReturnType<typeof mock<HighlevelFileSyncApplicationService>>;
    let testEngine: InngestTestEngine;

    beforeEach(() => {
        messagePact = createMessagePactProvider({
            consumerName: CONSUMER_NAME,
            providerName: PROVIDER_NAME
        });

        // Mock the application service
        appService = mock<HighlevelFileSyncApplicationService>();

        // Mock successful file upload
        appService.uploadFilesToHighlevel.mockResolvedValue(
            ok({
                uploadedFiles: {
                    'file1.pdf': 'https://highlevel.com/files/file1.pdf'
                },
                meta: [
                    {
                        fieldname: 'file',
                        originalname: 'file1.pdf',
                        encoding: '7bit',
                        mimetype: 'application/pdf',
                        size: 1024,
                        url: 'https://highlevel.com/files/file1.pdf'
                    }
                ]
            })
        );

        // Mock successful opportunity update
        appService.updateHighlevelOpportunityWithUploadedFiles.mockResolvedValue(
            ok({
                opportunityId: 'highlevel-opportunity-123'
            })
        );

        // Create a real Inngest client for testing
        const inngest = createHighlevelSyncInngestClient();

        // Create the function and test engine
        const fn = createSyncFileToHighLevelFunction(inngest, appService);
        testEngine = new InngestTestEngine({function: fn});
    });

    const eventName = 'lead.synced-to-highlevel';

    it('should process a valid lead.synced-to-highlevel event with file uploads', () => {
        // Create type-safe example data
        const leadId = '123e4567-e89b-12d3-a456-426614174000';
        const highLevelContactId = 'highlevel-contact-456';
        const highLevelOpportunityId = 'highlevel-opportunity-123';
        const highLevelAuthToken = 'highlevel-auth-token-789';
        const highLevelLocationId = 'highlevel-location-321';

        // Create a type-safe file upload structure
        const fileUpload = {
            fileId: 'file-123',
            s3Key: 'submitted/file1.pdf',
            keyWithoutStatusPrefix: 'file1.pdf',
            fileName: 'file1.pdf',
            contentType: 'application/pdf'
        };

        // Create a type-safe event data object
        const eventData: LeadSyncedToHighlevelEventPayload = {
            leadId,
            highLevelContactId,
            highLevelOpportunityId,
            highLevelAuthToken,
            highLevelLocationId,
            fileUploads: [fileUpload]
        };

        // Use PACT matchers to enforce type validation while allowing dynamic values
        return messagePact
            .expectsToReceive('lead.synced-to-highlevel')
            .withMetadata({
                name: eventName
            })
            .withContent({
                leadId: like(leadId),
                highLevelContactId: like(highLevelContactId),
                highLevelOpportunityId: like(highLevelOpportunityId),
                highLevelAuthToken: like(highLevelAuthToken),
                highLevelLocationId: like(highLevelLocationId),
                fileUploads: eachLike({
                    fileId: like(fileUpload.fileId),
                    s3Key: like(fileUpload.s3Key),
                    keyWithoutStatusPrefix: like(fileUpload.keyWithoutStatusPrefix),
                    fileName: like(fileUpload.fileName),
                    contentType: like(fileUpload.contentType)
                }, {min: 1})
            })
            .verify(async (message) => {
                const metadata = message.metadata ?? {};
                if (typeof metadata.name !== 'string') {
                    throw new Error('Event name must be provided in metadata and must be a string');
                }
                const eventName = metadata.name;
                const eventData = message.contents;

                const result = await testEngine.execute({
                    events: [
                        {
                            name: eventName,
                            data: eventData
                        }
                    ]
                });

                // Verify the function processed the event correctly
                expect(result.result).toMatchObject({
                    status: 'success',
                    filesUploaded: 1,
                    data: {
                        leadId,
                        highLevelOpportunityId,
                        fileUploads: expect.arrayContaining([
                            expect.objectContaining({
                                fileName: fileUpload.fileName
                            })
                        ])
                    }
                });

                // Verify service calls were made with the correct parameters
                expect(appService.uploadFilesToHighlevel).toHaveBeenCalledWith(
                    expect.objectContaining({
                        leadId,
                        highLevelOpportunityId,
                        highLevelAuthToken,
                        highLevelLocationId,
                        fileUploads: expect.arrayContaining([
                            expect.objectContaining({
                                fileName: fileUpload.fileName,
                                s3Key: fileUpload.s3Key
                            })
                        ])
                    })
                );

                // Verify the update opportunity call
                expect(appService.updateHighlevelOpportunityWithUploadedFiles).toHaveBeenCalled();
            });
    });

    it('should handle a lead.synced-to-highlevel event with no files to sync', () => {
        // Create type-safe example data
        const leadId = '123e4567-e89b-12d3-a456-426614174000';
        const highLevelContactId = 'highlevel-contact-456';
        const highLevelOpportunityId = 'highlevel-opportunity-123';
        const highLevelAuthToken = 'highlevel-auth-token-789';
        const highLevelLocationId = 'highlevel-location-321';

        // Create a type-safe event data object with empty fileUploads
        const eventData: LeadSyncedToHighlevelEventPayload = {
            leadId,
            highLevelContactId,
            highLevelOpportunityId,
            highLevelAuthToken,
            highLevelLocationId,
            fileUploads: []
        };

        // Use PACT matchers to enforce type validation while allowing dynamic values
        return messagePact
            .expectsToReceive('a lead.synced-to-highlevel event with no files')
            .withMetadata({
                name: eventName,
            })
            .withContent({
                leadId: like(leadId),
                highLevelContactId: like(highLevelContactId),
                highLevelOpportunityId: like(highLevelOpportunityId),
                highLevelAuthToken: like(highLevelAuthToken),
                highLevelLocationId: like(highLevelLocationId),
                fileUploads: [] // Empty array for no files
            })
            .verify(async (message) => {
                const metadata = message.metadata ?? {};
                const eventName = typeof metadata.name === 'string' ? metadata.name : '';
                const eventData = message.contents;
                const result = await testEngine.execute({
                    events: [
                        {
                            name: eventName,
                            data: eventData
                        }
                    ]
                });

                // Verify the function processed the event correctly for no files case
                expect(result.result).toMatchObject({
                    status: 'success',
                    message: 'No files to sync',
                    data: {
                        leadId,
                        highLevelOpportunityId,
                        fileUploads: []
                    }
                });

                // Verify service calls were NOT made for the no-files case
                expect(appService.uploadFilesToHighlevel).not.toHaveBeenCalled();
                expect(appService.updateHighlevelOpportunityWithUploadedFiles).not.toHaveBeenCalled();
            });
    });
});
