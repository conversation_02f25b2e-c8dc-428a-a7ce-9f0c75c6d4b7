import {S3Client} from "@aws-sdk/client-s3";
import {env} from "./env";

export const s3Client = new S3Client({
    endpoint: env.S3_ENDPOINT ?? undefined, // No endpoint for AWS S3; Local dev MinIO uses an endpoint
    region: "us-west-2",
    credentials: env.S3_ACCESS_KEY && env.S3_SECRET_KEY ?
        {
            accessKeyId: env.S3_ACCESS_KEY!,
            secretAccessKey: env.S3_SECRET_KEY!,
        } : undefined, // AWS SDK will automatically use IAM roles in production
    // This property is required by createPresignedPost
    systemClockOffset: 0,
    // ForcePathStyle makes S3 client treat bucket name as a path component
    // rather than as a subdomain, which is required when working with MinIO locally
    forcePathStyle: env.S3_FORCE_PATH_STYLE,
});
