//Duplicated for now from the lead events.ts file, but TODO: pull these event schemas into a package
export type LeadEventTypes = LeadSyncedToHighlevelEvent;

export type LeadEventsSchema = {
    [K in LeadEventTypes['name']]: Extract<LeadEventTypes, { name: K }>
}

type BaseEventData = {
    leadId: string;
}

export type LeadSyncedToHighlevelEventPayload = BaseEventData & {
    highLevelContactId: string;
    highLevelOpportunityId: string;
    // HighLevel authentication details
    highLevelAuthToken: string;
    highLevelLocationId: string;
    fileUploads: Array<{
        fileId: string;
        s3Key: string;
        keyWithoutStatusPrefix: string;
        fileName: string;
        contentType: string;
    }>;
};


export interface LeadSyncedToHighlevelEvent {
    name: 'lead.synced-to-highlevel';
    payload: LeadSyncedToHighlevelEventPayload;
}
