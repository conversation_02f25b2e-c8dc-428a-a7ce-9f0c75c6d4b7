import {EventSchemas, Inngest} from "inngest";
import {LeadEventsSchema} from "./highlevel-sync-event";


export function createHighlevelSyncInngestClient() {
    return new Inngest({
        id: "highlevel-file-sync",
        schemas: new EventSchemas().fromRecord<LeadEventsSchema>(),
        // Explicitly set these values to ensure proper initialization
        eventKey: process.env.INNGEST_EVENT_KEY,
        isDev: process.env.INNGEST_DEV === "1" || process.env.NODE_ENV !== "production",
    });
}

export type HighlevelSyncInngestClient = ReturnType<typeof createHighlevelSyncInngestClient>;
