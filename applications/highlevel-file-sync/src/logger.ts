import pino from 'pino'

const COLOR = {
    GREEN: `\x1b[32m`,
    RED: `\x1b[31m`,
    WHITE: `\x1b[37m`,
    YELLOW: `\x1b[33m`,
    CYAN: `\x1b[36m`,
}

const LEVEL_COLORS = {
    FATAL: COLOR.RED,
    ERROR: COLOR.RED,
    WARN: COLOR.YELLOW,
    INFO: COLOR.GREEN,
    DEBUG: COLOR.GREEN,
    TRACE: COLOR.GREEN,
}

const isBrowser = typeof window !== 'undefined'
// Check if we're running in AWS Lambda
const isLambda = !!process.env.AWS_LAMBDA_FUNCTION_NAME

// Node.js config
const nodeConfig = {
    formatters: {
        level: (label: string) => {
            return { level: label.toUpperCase() }
        },
        bindings: () => {
            return {}
        },
        log: (object: Record<string, unknown>) => {
            const { msg, ...rest } = object
            return Object.keys(rest).length ? { msg, ...rest } : { msg }
        }
    },
    timestamp: () => `,"time":"${new Date(Date.now()).toISOString()}"`,
    messageKey: 'msg',
    // Add Datadog trace correlation fields
    mixin: () => {
        return {
            // These fields will be automatically populated by dd-trace when logInjection is true
            dd: {
                trace_id: undefined,
                span_id: undefined,
                service: process.env.DD_SERVICE,
                env: process.env.DD_ENV,
                version: process.env.DD_VERSION
            }
        }
    }
}

const browserConfig = {
    browser: {
        write: (logObj: any) => {
            const { level, msg, time, ...rest } = logObj

            const levelUppercased = level.toUpperCase()
            const timeFormatted = new Date(time).toLocaleTimeString('en-US', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                fractionalSecondDigits: 3
            })

            const LEVEL_COLOR = LEVEL_COLORS[levelUppercased as keyof typeof LEVEL_COLORS] || COLOR.WHITE
            
            // Format: [time] LEVEL message {context}
            console.log(
                `[${timeFormatted}] ${LEVEL_COLOR}${levelUppercased}${COLOR.WHITE} ${msg}`,
                Object.keys(rest).length ? rest : ''
            )
        },
        formatters: {
            level: (label: string) => {
                return { level: label }
            },
        },
    }
}

let loggerConfig;

if (isBrowser) {
    // Browser configuration
    loggerConfig = browserConfig;
} else if (isLambda || process.env.LOGGER_JSON === 'true') {
    // In Lambda or when JSON logging is explicitly requested, use the node config without pretty printing
    loggerConfig = { level: 'info', ...nodeConfig };
} else {
    // In local development, use pretty printing
    loggerConfig = {
        level: 'info',
        transport: {
            target: 'pino-pretty'
        }
    };
}

const logger = isBrowser ? pino(browserConfig) : pino(loggerConfig);

export default logger 