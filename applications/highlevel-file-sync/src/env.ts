import {z} from 'zod';
import dotenvFlow from 'dotenv-flow';

dotenvFlow.config({silent: true});

// Define environment schema
const envSchema = z.object({
    S3_ENDPOINT: z.string().optional(), //just used for local dev/testing to point to minio
    S3_ACCESS_KEY: z.string().optional(), //just used for local dev/testing to authenticate with minio; in prod we use IAM
    S3_SECRET_KEY: z.string().optional(), //just used for local dev/testing to authenticate with minio; in prod we use IAM
    S3_FORCE_PATH_STYLE: z.preprocess( //used in local development/testing so buckets are represented as paths not subdomains
        // Convert string 'true'/'false' to boolean
        (val) => val === 'true' || val === true,
        z.boolean().default(false)),
    S3_LEAD_FORM_FILE_UPLOAD_BUCKET: z.string(),
});

// Parse and export environment variables
export const env = envSchema.parse(process.env);
