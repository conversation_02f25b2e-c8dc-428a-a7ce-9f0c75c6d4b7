{"name": "highlevel-file-sync", "version": "0.1.0", "description": "Lambda function to sync files from S3 to HighLevel CRM", "main": "dist/index.js", "type": "module", "scripts": {"build": "node build.js", "build:watch": "node build-watch.js", "check-types": "tsc --noEmit", "sam:start": "sam local start-api -t template.yaml --env-vars env.json", "dev": "concurrently \"pnpm build:watch\" \"pnpm sam:start\"", "invoke": "pnpm build && sam local invoke -t template.yaml HighlevelFileSyncFunction --event events/api-event.json --env-vars env.json", "predeploy": "cd ../../packages/highlevel-client && pnpm run build && cd ../../applications/highlevel-file-sync && pnpm build", "deploy": "serverless deploy", "test": "vitest run", "pact:test:consumer": "vitest run $(pnpm dlx glob-cli '**/*.consumer.pact.test.ts')", "pact:test:provider": "vitest run $(pnpm dlx glob-cli '**/*.provider.pact.test.ts')", "pact:test": "vitest run src/**/*.pact.test.ts", "pact:publish": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/publish-pacts.ts highlevel-file-sync-workflow", "pact:can-i-deploy": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/track-deployment check --env staging --participant highlevel-file-sync-workflow", "pact:record-deployment": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/track-deployment record --env staging --participant highlevel-file-sync-workflow", "package": "cd ../.. && pnpm turbo run build --filter=highlevel-file-sync... && rm -rf deployment/lambda-packages/highlevel-file-sync && mkdir -p deployment/lambda-packages/highlevel-file-sync && cp -r applications/highlevel-file-sync/dist/* deployment/lambda-packages/highlevel-file-sync/ && cd deployment/lambda-packages/highlevel-file-sync && zip -q -r function.zip .", "deploy:cdk": "pnpm package && cd ../../deployment && pnpm run cdk deploy highlevel-file-sync-app --require-approval never", "deploy:sync-inngest": "node scripts/sync-inngest.js", "deploy:all": "pnpm run package && cd ../../deployment && pnpm run cdk deploy highlevel-file-sync-app --require-approval never && cd ../applications/highlevel-file-sync && pnpm run deploy:sync-inngest", "track-deps": "tsx ../../scripts/track-deps.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "@siv/highlevel-client": "workspace:*", "@siv/pact-helpers": "workspace:*", "aws-lambda": "^1.0.7", "debug": "^4.3.4", "dotenv-flow": "^4.1.0", "hono": "^4.7.5", "inngest": "^3.34.1", "ms": "^2.1.3", "neverthrow": "8.2.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "source-map-support": "^0.5.21", "zod": "^3.24.1"}, "devDependencies": {"@inngest/test": "^0.1.5", "@pact-foundation/pact": "^15.0.1", "@pact-foundation/pact-cli": "^16.0.7", "@pact-foundation/pact-node": "^10.18.0", "@types/aws-lambda": "^8.10.148", "@types/node": "^22.14.0", "aws-cdk-lib": "^2.187.0", "concurrently": "^8.2.2", "constructs": "^10.4.2", "esbuild": "^0.25.2", "nock": "^14.0.2", "node-fetch": "^3.3.2", "nodemon": "^3.1.9", "typescript": "^5.8.3", "vitest": "^3.1.1", "vitest-mock-extended": "^2.0.2"}}