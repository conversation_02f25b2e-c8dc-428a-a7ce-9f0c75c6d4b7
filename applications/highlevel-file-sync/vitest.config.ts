import {defineConfig} from "vitest/config";

export default defineConfig({
    test: {
        environment: 'node',
        environmentOptions: {
            // Setup environment variables for tests
            env: {
                NODE_ENV: 'test'
            }
        },
        // Isolate each test file for consistent behavior
        isolate: true,
        // Increase timeout for tests
        testTimeout: 10000,
        // Mock global objects
        globals: true,
        // Ensure clean state between tests
        restoreMocks: true,
        setupFiles: ['vitest.setup.ts']
    }
}); 
