ENVIRONMENT="test"
NODE_ENV="test"
INNGEST_DEV="1"
INNGEST_BASE_URL="http://host.docker.internal:8288"
INNGEST_SERVE_PATH="/api/inngest"
INNGEST_SERVE_HOST="http://host.docker.internal:3000"
INNGEST_LOG_LEVEL="debug"
INNGEST_EVENT_KEY="test_key"
INNGEST_SIGNING_KEY="test_signing_key"
S3_ACCESS_KEY="minioadmin"
S3_SECRET_KEY="minioadmin"
S3_ENDPOINT=http://localhost:9000
S3_LEAD_FORM_FILE_UPLOAD_BUCKET=test-lead-form-file-uploads
S3_FORCE_PATH_STYLE=true
