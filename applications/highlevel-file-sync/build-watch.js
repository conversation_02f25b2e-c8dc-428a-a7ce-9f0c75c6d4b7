#!/usr/bin/env node
import { context } from 'esbuild';
import { copyFileSync, mkdirSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

// Get dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Ensure the dist directory exists
const distDir = join(__dirname, 'dist');
if (!existsSync(distDir)) {
  mkdirSync(distDir, { recursive: true });
}

async function bundleWatch() {
  try {
    // Create a build context
    const ctx = await context({
      entryPoints: ['src/index.ts'],
      bundle: true,
      minify: false,
      sourcemap: true,
      // Include sources in source maps for better debugging
      sourcesContent: true, 
      platform: 'node',
      target: 'node20',
      outfile: 'dist/index.js',
      format: 'esm',
      banner: { js: '// eslint-disable-next-line\nimport { createRequire } from "module"; const require = createRequire(import.meta.url);' },
      external: [
        '@aws-sdk/*',
        'pino-pretty',
      ],
      logLevel: 'info',
    });

    // First build and copy package.json
    await ctx.rebuild();
    
    // Copy the package.json to dist for node_modules resolution
    copyFileSync(
      join(__dirname, 'package.json'),
      join(__dirname, 'dist', 'package.json')
    );
    
    // Then start watching
    await ctx.watch();
    
    console.log('Build started in watch mode...');
    console.log('Watching for changes...');
    
    // Copy package.json on file change using filesystem watcher
    const fs = await import('fs/promises');
    const { watch } = await import('fs');
    
    // Create a file watcher for the src directory
    const watcher = watch(join(__dirname, 'src'), { recursive: true });
    
    // When files change, copy package.json to ensure it's always there
    watcher.on('change', async (_, filename) => {
      console.log(`File changed: ${filename}`);
      console.log(`Rebuilt at ${new Date().toLocaleTimeString()}`);
      
      try {
        await fs.copyFile(
          join(__dirname, 'package.json'),
          join(__dirname, 'dist', 'package.json')
        );
      } catch (err) {
        console.error('Failed to copy package.json:', err);
      }
    });
    
    // Keep process alive
    process.stdin.on('close', () => {
      watcher.close();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

bundleWatch(); 