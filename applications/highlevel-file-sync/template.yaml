AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: HighLevel File Sync Lambda for testing locally with SAM

Globals:
  Api:
    Cors:
      AllowMethods: "'*'"
      AllowHeaders: "'*'"
      AllowOrigin: "'*'"
  Function:
    Timeout: 30
    MemorySize: 256
    Architectures:
      - x86_64
    Runtime: nodejs20.x
    Environment:
      Variables:
        NODE_OPTIONS: '--enable-source-maps'

Resources:
  HighlevelFileSyncFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./dist
      Handler: index.handler
      Runtime: nodejs20.x
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          ENVIRONMENT: development
          NODE_ENV: development
          INNGEST_SERVE_PATH: /api/inngest
          INNGEST_LOG_LEVEL: debug
          S3_ENDPOINT: http://host.docker.internal:9000
          S3_ACCESS_KEY: minioadmin
          S3_SECRET_KEY: minioadmin
          S3_FORCE_PATH_STYLE: true
          S3_LEAD_FORM_FILE_UPLOAD_BUCKET: dev-lead-form-file-uploads
          INNGEST_EVENT_KEY: test_key
          INNGEST_SIGNING_KEY: test_signing_key
          INNGEST_DEV: "1"
          INNGEST_BASE_URL: http://localhost:8288
          INNGEST_SERVE_HOST: http://host.docker.internal:3000
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /api/inngest
            Method: any

Parameters:
  InngestEventKey:
    Type: String
    Default: test_key
    Description: Inngest Event Key for authentication
  InngestSigningKey:
    Type: String
    Default: test_signing_key
    Description: Inngest Signing Key for webhook verification
  InngestDev:
    Type: String
    Default: "1"
    Description: Enable Inngest development mode
  InngestBaseUrl:
    Type: String
    Default: http://localhost:8288
    Description: Inngest development server URL
  InngestServeHost:
    Type: String
    Default: http://localhost:3000
    Description: Host where the Inngest serve handler is deployed

Outputs:
  HighlevelFileSyncFunction:
    Description: "Highlevel File Sync Lambda Function ARN"
    Value: !GetAtt HighlevelFileSyncFunction.Arn
  ApiEndpoint:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/api/inngest" 
