# Dependencies
node_modules/
.pnpm-store/

# TypeScript compiled output
dist/
*.tsbuildinfo
dist-deploy/

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs and editors
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# AWS SAM
.aws-sam/
samconfig.toml

# Testing
coverage/
.nyc_output/

# Environment
.env
.env.*.local
!.env.example

# OS
.DS_Store


/.esbuild/.build/src/functions/highlevel-file-sync-workflow.test.ts
/.esbuild/.build/src/functions/highlevel-file-sync-workflow.ts
/.esbuild/.build/src/env.ts
/.esbuild/.build/src/highlevel-sync-event.ts
/.esbuild/.build/src/HighlevelFileSyncApplicationService.test.ts
/.esbuild/.build/src/HighlevelFileSyncApplicationService.ts
/.esbuild/.build/src/index.js
/.esbuild/.build/src/index.js.map
/.esbuild/.build/src/index.ts
/.esbuild/.build/src/inngest-client.ts
/.esbuild/.build/src/s3Client.ts
/.esbuild/.build/package.json
/.esbuild/
/package-temp/
/pacts/highlevel-file-sync-workflow-siv-web.json
/pacts/highlevel-file-sync-workflow-siv-web-event-provider.json
