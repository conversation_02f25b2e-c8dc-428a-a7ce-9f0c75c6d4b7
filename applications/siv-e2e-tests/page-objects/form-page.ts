import { Page, Locator, expect } from '@playwright/test';

/**
 * Page object for the form submission page
 * Uses direct selectors based on data-testid attributes
 */
export class FormPage {
  readonly page: Page;

  // Form field locators using direct data-testid selectors
  readonly eventTypeField: Locator;
  readonly firstNameField: Locator;
  readonly lastNameField: Locator;
  readonly companyField: Locator;
  readonly emailField: Locator;
  readonly phoneField: Locator;
  readonly cityField: Locator;
  readonly countryField: Locator;
  readonly stateField: Locator;
  readonly postalCodeField: Locator;
  readonly guestCountField: Locator;
  readonly roomCountField: Locator;
  readonly budgetField: Locator;
  readonly startDateField: Locator;
  readonly endDateField: Locator;
  readonly eventDescriptionField: Locator;
  readonly flexibleDatesField: Locator;
  readonly eventNeedsField: Locator;
  readonly marketingConsentField: Locator;
  readonly submitButton: Locator;
  readonly successMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Initialize all field locators with direct selectors
    this.eventTypeField = page.locator('[data-testid="input-EVENT_TYPE"]');
    this.firstNameField = page.locator('[data-testid="input-FIRST_NAME"]');
    this.lastNameField = page.locator('[data-testid="input-LAST_NAME"]');
    this.companyField = page.locator('[data-testid="input-COMPANY"]');
    this.emailField = page.locator('[data-testid="input-EMAIL"]');
    this.phoneField = page.locator('[data-testid="input-PHONE"]');
    this.cityField = page.locator('[data-testid="input-CITY"]');
    this.countryField = page.locator('[data-testid="input-COUNTRY"]');
    this.stateField = page.locator('[data-testid="input-STATE"]');
    this.postalCodeField = page.locator('[data-testid="input-POSTAL_CODE"]');
    this.guestCountField = page.locator('[data-testid="input-GUEST_COUNT"]');
    this.roomCountField = page.locator('[data-testid="input-ROOM_COUNT"]');
    this.budgetField = page.locator('[data-testid="input-BUDGET"]');
    
    // For date fields, we need to target the input within the field container
    this.startDateField = page.locator('[data-testid="field-START_DATE"] input');
    this.endDateField = page.locator('[data-testid="field-END_DATE"] input');
    
    this.eventDescriptionField = page.locator('[data-testid="input-EVENT_DESCRIPTION"]');
    this.flexibleDatesField = page.locator('[data-testid="input-FLEXIBLE_DATES"]');
    
    // For event needs, we use the exact ID selector
    this.eventNeedsField = page.locator('#EVENT_NEEDS');
    
    // For marketing consent, we use the name attribute as it's the most direct selector
    this.marketingConsentField = page.locator('[name="MARKETING_CONSENT"]');
    
    // Submit button and success message
    this.submitButton = page.locator('button[type="submit"]');
    this.successMessage = page.locator('[data-testid="submit-success-message"]');
  }

  /**
   * Navigate to a specific form by ID
   */
  async navigateToForm(formId: string) {
    await this.page.goto(`/forms/${formId}`);
    
    // Wait for the form to be fully loaded by checking for the phone field's country selector
    await this.page.locator('[data-testid="field-PHONE"] .iti__country-container').waitFor({
      state: 'visible',
      timeout: 5000
    });
  }

  /**
   * Fill out the form with the provided data
   * Uses direct selectors for all fields
   */
  async fillForm(formData: {
    eventType: string;
    firstName: string;
    lastName: string;
    company: string;
    email: string;
    phone: string;
    country: string;
    city: string;
    state: string;
    postalCode: string;
    guestCount: string;
    roomCount: string;
    budget: string;
    startDate: string;
    endDate: string;
    eventDescription: string;
    flexibleDates: boolean;
    eventNeeds: string[];
    marketingConsent: boolean;
  }) {
    // Select event type
    await this.eventTypeField.selectOption(formData.eventType);

    // Fill contact information fields
    await this.firstNameField.fill(formData.firstName);
    await this.lastNameField.fill(formData.lastName);
    await this.companyField.fill(formData.company);
    await this.emailField.fill(formData.email);
    
    // Phone field requires special handling due to the international input component
    await this.phoneField.click();
    await this.phoneField.fill(formData.phone);
    
    // Fill location information
    await this.cityField.fill(formData.city);
    
    await this.countryField.click();
    await this.page.locator(`[data-testid="COUNTRY-option-${formData.country}"]`).click();

    await this.stateField.click();
    await this.page.locator(`[data-testid="STATE-option-${formData.state}"]`).click();

    await this.postalCodeField.fill(formData.postalCode);

    // Fill event details
    await this.guestCountField.fill(formData.guestCount);
    await this.roomCountField.fill(formData.roomCount);
    
    // Budget field
    await expect(this.budgetField).toBeVisible({ timeout: 5000 });
    await this.budgetField.fill(formData.budget);
    
    // Fill date fields
    await expect(this.startDateField).toBeVisible();
    await expect(this.endDateField).toBeVisible();
    await this.startDateField.fill(formData.startDate);
    await this.endDateField.fill(formData.endDate);
    
    // Fill event description
    await this.eventDescriptionField.fill(formData.eventDescription);

    // Handle checkboxes
    if (formData.flexibleDates) {
      await this.flexibleDatesField.check();
    }
    
    // Handle event needs (multiple selection)
    for (const need of formData.eventNeeds) {
      await this.page.check(`#EVENT_NEEDS-${need}`);
    }
    
    // Handle marketing consent
    if (formData.marketingConsent) {
      await this.marketingConsentField.check();
    }
  }

  /**
   * Submit the form and verify success
   */
  async submitForm() {
    await expect(this.submitButton).toBeVisible();
    await this.submitButton.click();

    // Verify submission success with a reasonable timeout
    await expect(this.successMessage).toBeVisible({timeout: 10000});
  }

  /**
   * Log all available form fields to the console for debugging
   */
  async logAvailableFormFields() {
    console.log('Available form fields:');
    const fieldCount = await this.page.locator('.ef-field').count();
    
    for (let i = 0; i < fieldCount; i++) {
      const field = this.page.locator('.ef-field').nth(i);
      const fieldId = await field.getAttribute('data-testid');
      console.log(`Field ${i}: ${fieldId}`);
    }
  }
}
