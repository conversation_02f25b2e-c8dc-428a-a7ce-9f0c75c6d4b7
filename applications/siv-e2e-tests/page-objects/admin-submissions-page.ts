import { Locator, Page, expect } from '@playwright/test';

export class AdminSubmissionsPage {
  readonly page: Page;
  readonly submissionsTable: Locator;
  readonly submissionRows: Locator;

  constructor(page: Page) {
    this.page = page;
    // Use data-test-id-group attribute to locate submissions
    this.submissionsTable = page.locator('[data-test-id-group="form-submission"]');
    this.submissionRows = this.submissionsTable;
  }

  async navigateToSubmissions(propertyId?: string, formId?: string) {
    let url = '/admin/submissions';
    if (propertyId && formId) {
      url = `/admin/properties/${propertyId}/forms/${formId}/submissions`;
    }
    await this.page.goto(url);
    
    // Wait for the page to load and check for submissions
    try {
      // First wait for the page to be ready
      await this.page.waitForLoadState('networkidle');
      
      // Check if we have any submissions
      const count = await this.page.locator('[data-test-id-group="form-submission"]').count();
      console.log(`Found ${count} submissions on the page`);
      
      if (count === 0) {
        throw new Error('No submissions found on the page');
      }
      
      // Wait for the first submission to be visible without using expect
      await this.page.locator('[data-test-id-group="form-submission"]').first().waitFor({ 
state: 'visible', timeout: 10000 });
      
      // Success - we found at least one submission
      console.log('Successfully loaded submissions page');
    } catch (error) {
      console.error('Error while waiting for submissions:', error);
      throw error;
    }
  }

  async findSubmissionByEmail(email: string) {
    const count = await this.submissionRows.count();
    console.log(`Found ${count} submissions on the page`);
    
    for (let i = 0; i < count; i++) {
      const submission = this.submissionRows.nth(i);
      const emailElement = submission.locator('[data-testid="field-value-EMAIL"]');
      if (await emailElement.isVisible()) {
        const rowEmail = await emailElement.textContent();
        console.log(`Submission ${i} email: ${rowEmail}`);
        
        if (rowEmail?.trim() === email) {
          return submission;
        }
      }
    }
    throw new Error(`Submission with email ${email} not found`);
  }

  async clickViewDetailsForSubmission(email: string) {
    const submission = await this.findSubmissionByEmail(email);
    await submission.locator('a:has-text("View Details")').click();
  }

  async verifySubmissionField(submission: Locator, fieldName: string, expectedValue: string) {
    // The field is directly accessible in the submission
    const fieldElement = submission.locator(`[data-testid="field-value-${fieldName}"]`);
    await expect(fieldElement).toBeVisible();
    
    const fieldValue = await fieldElement.textContent() || '';
    expect(fieldValue.trim()).toContain(expectedValue);
  }
  
  async verifySubmissionFields(email: string, formData: FormSubmissionData) {
    // Find the submission by email
    const submission = await this.findSubmissionByEmail(email);
    
    // Verify contact information
    await this.verifySubmissionField(submission, 'FIRST_NAME', formData.firstName);
    await this.verifySubmissionField(submission, 'LAST_NAME', formData.lastName);
    await this.verifySubmissionField(submission, 'COMPANY', formData.company);
    await this.verifySubmissionField(submission, 'EMAIL', formData.email);
    
    // Check that the phone number contains the last 4 digits
    const phoneText = await submission.locator('[data-testid="field-value-PHONE"]').textContent();
    expect(phoneText).toContain(formData.phone.substring(formData.phone.length - 4));
    
    // Verify address fields
    await this.verifySubmissionField(submission, 'COUNTRY', formData.country);
    await this.verifySubmissionField(submission, 'CITY', formData.city);
    await this.verifySubmissionField(submission, 'STATE', formData.state);
    await this.verifySubmissionField(submission, 'POSTAL_CODE', formData.postalCode);
    
    // Verify event details
    await this.verifySubmissionField(submission, 'GUEST_COUNT', formData.guestCount);
    await this.verifySubmissionField(submission, 'ROOM_COUNT', formData.roomCount);
    await this.verifySubmissionField(submission, 'BUDGET', formData.budget);
    await this.verifySubmissionField(submission, 'EVENT_DESCRIPTION', formData.eventDescription);
    
    return submission;
  }
  
  async verifyDateFields(submission: Locator, startDate: string, endDate: string) {
    // Extract date components for verification
    const startYear = startDate.split('-')[0];
    const startMonth = startDate.split('-')[1];
    const startDay = startDate.split('-')[2];
    const endYear = endDate.split('-')[0];
    const endMonth = endDate.split('-')[1];
    const endDay = endDate.split('-')[2];
    
    // Get the displayed date text
    const startDateText = await submission.locator('[data-testid="field-value-START_DATE"]').textContent();
    const endDateText = await submission.locator('[data-testid="field-value-END_DATE"]').textContent();
    
    // Verify the date components are present in the displayed text
    // This is more robust than exact string matching since the date format may vary by locale
    expect(startDateText).toContain(parseInt(startYear).toString());
    expect(startDateText).toContain(parseInt(startMonth).toString()); // Remove leading zero if present
    expect(startDateText).toContain(parseInt(startDay).toString()); // Remove leading zero if present
    
    expect(endDateText).toContain(parseInt(endYear).toString());
    expect(endDateText).toContain(parseInt(endMonth).toString());
    expect(endDateText).toContain(parseInt(endDay).toString());
  }
  
  async verifyCheckboxFields(submission: Locator, flexibleDates: boolean, eventNeeds: string[]) {
    // Verify checkboxes
    await this.verifySubmissionField(submission, 'FLEXIBLE_DATES', flexibleDates.toString());
    
    // Verify event needs (multi-select)
    const eventNeedsText = await submission.locator('[data-testid="field-value-EVENT_NEEDS"]').textContent();
    for (const need of eventNeeds) {
      expect(eventNeedsText).toContain(need);
    }
  }
  
  async verifyMarketingConsent(submission: Locator, marketingConsent: boolean) {
    // Verify marketing consent
    await this.verifySubmissionField(submission, 'MARKETING_CONSENT', marketingConsent.toString());
  }
  
  async verifyEventType(submission: Locator, eventType: string) {
    // Verify the EVENT_TYPE field
    const eventTypeElement = submission.locator('[data-testid="field-value-EVENT_TYPE"]');
    await expect(eventTypeElement).toBeVisible();
    // Note: The displayed value might be transformed (e.g., to snake_case)
    const displayedValue = await eventTypeElement.textContent() || '';
    // Convert spaces to underscores and make lowercase for comparison
    const expectedValue = eventType.toLowerCase().replace(/ /g, '_');
    expect(displayedValue.toLowerCase()).toContain(expectedValue);
  }
  
  async logAllSubmissions() {
    const count = await this.submissionRows.count();
    console.log(`Found ${count} submissions on the page`);
    
    for (let i = 0; i < count; i++) {
      const emailElement = this.submissionRows.nth(i).locator('[data-testid="field-value-EMAIL"]');
      if (await emailElement.isVisible()) {
        console.log(`Submission ${i} email: ${await emailElement.textContent()}`);
      }
    }
  }
}

export interface FormSubmissionData {
  firstName: string;
  lastName: string;
  company: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  state: string;
  postalCode: string;
  guestCount: string;
  roomCount: string;
  budget: string;
  eventDescription: string;
  startDate?: string;
  endDate?: string;
  flexibleDates?: boolean;
  eventNeeds?: string[];
  marketingConsent?: boolean;
  eventType?: string;
}
