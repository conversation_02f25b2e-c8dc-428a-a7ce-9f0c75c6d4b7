import {defineConfig, devices} from '@playwright/test';
import path from 'path';
import dotenvFlow from 'dotenv-flow';

// Load environment variables from .env.e2e
dotenvFlow.config({
    node_env: process.env.NODE_ENV || 'development',
    path: path.resolve(__dirname),
});

export default defineConfig({
    testDir: './tests',
    fullyParallel: true,
    forbidOnly: !!process.env.CI,
    retries: process.env.CI ? 2 : 0,
    workers: process.env.CI ? 1 : undefined,
    reporter: [
        ['html'],
        ['junit', { outputFile: process.env.JUNIT_OUTPUT_FILE || 'test-results/junit.xml' }]
    ],
    // Support for test tags
    grep: process.env.TEST_TAG ? new RegExp(`@${process.env.TEST_TAG}`) : undefined,
    use: {
        baseURL: process.env.BASE_URL || 'http://localhost:5173',
        trace: 'on-first-retry',
        screenshot: 'only-on-failure',
        headless: !!process.env.CI || !!process.env.E2E_HEADLESS,
    },

    // Start the web server for the tests
    webServer: process.env.LOCAL_MODE !== "true" ? undefined : {
        command: 'cd ../siv-web && NODE_ENV=development pnpm dev',
        url: process.env.BASE_URL || 'http://localhost:5173',
        reuseExistingServer: !process.env.PLAYWRIGHT_SKIP_REUSE_EXISTING_SERVER,
        stdout: 'pipe',
        stderr: 'pipe',
        timeout: 20000, // 10 seconds to start
    },
    projects: [
        {
            name: 'setup clerk',
            testMatch: /global\.setup\.ts/,
        },
        {
            name: 'chromium',
            use: {...devices['Desktop Chrome']},
            dependencies: ['setup clerk'],
        },
        // {
        //   name: 'firefox',
        //   use: { ...devices['Desktop Firefox'] },
        // },
        // {
        //   name: 'webkit',
        //   use: { ...devices['Desktop Safari'] },
        // },
    ],
});
