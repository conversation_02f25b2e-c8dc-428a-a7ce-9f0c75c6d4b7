import axios from 'axios';
import { loginAsSivAdminAndGetSessionToken } from './test-auth';
import { BrowserContext } from '@playwright/test';

/**
 * Creates or updates a form for testing purposes using the upsert endpoint
 * @param propertyId The property ID to create/update the form for
 * @param formData The form data to upsert
 * @param authToken Optional authentication token (session cookie value). If provided, will include in request.
 * @returns The created or updated form
 */
export async function upsertTestForm(propertyId: string, formData: any, authToken: string) {
  // Ensure the propertyId is set in the form data
  const formWithProperty = {
    ...formData,
    propertyId
  };
  
  // Get the base URL from environment or use localhost default
  const baseUrl = process.env.BASE_URL || 'http://localhost:5173';
  
  try {
    // Set up request headers with content type
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`
    };
    
    // Make the API call to the upsert endpoint
    // In non-production environments (LOCAL_MODE=true), authentication may not be required
    // In production environments (LOCAL_MODE=false), authentication is required
    const response = await axios.post(
      `${baseUrl}/admin/properties/${propertyId}/forms/upsert`,
      formWithProperty,
      { headers }
    );
    
    // Check if the response contains data and has a successful status code
    if (!response.data || response.status !== 200) {
      throw new Error(`Form upsert failed with status ${response.status}: ${JSON.stringify(response.data)}`);
    }
    
    // Check if the response data has the expected form structure
    if (!response.data.id) {
      throw new Error(`Invalid form response: ${JSON.stringify(response.data)}`);
    }
    
    return response.data;
  } catch (error: unknown) {
    // Enhance error message with request details for better debugging
    if (axios.isAxiosError(error) && error.response) {
      // The request was made and the server responded with a status code outside of 2xx range
      throw new Error(
        `Form upsert failed with status ${error.response.status}: ${
          JSON.stringify(error.response.data)
        }. Form data: ${JSON.stringify(formWithProperty)}`
      );
    } else if (axios.isAxiosError(error) && error.request) {
      // The request was made but no response was received
      throw new Error(`Form upsert request failed, no response received. Form data: ${JSON.stringify(formWithProperty)}`);
    } else {
      // Something happened in setting up the request
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Form upsert error: ${errorMessage}. Form data: ${JSON.stringify(formWithProperty)}`);
    }
  }
}

