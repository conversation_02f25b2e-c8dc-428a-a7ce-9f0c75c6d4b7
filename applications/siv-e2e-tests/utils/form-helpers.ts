/**
 * Form submission helper utilities
 */
import { Page, expect } from '@playwright/test';

/**
 * Generate unique test data for form submissions
 */
export function generateTestData() {
  const timestamp = new Date().getTime();
  return {
    firstName: `Playwright${timestamp}`,
    lastName: 'Test',
    email: `playwright.test.${timestamp}@example.com`,
    phone: `555${timestamp.toString().slice(-7)}`,
    message: `Automated test message ${timestamp}`
  };
}

/**
 * Fill out a standard lead form with the provided data
 */
export async function fillLeadForm(page: Page, data: {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message?: string;
}) {
  await page.fill('[name="firstName"]', data.firstName);
  await page.fill('[name="lastName"]', data.lastName);
  await page.fill('[name="email"]', data.email);
  await page.fill('[name="phone"]', data.phone);
  
  if (data.message) {
    await page.fill('[name="message"]', data.message);
  }
}

/**
 * Submit a form and verify success
 */
export async function submitFormAndVerifySuccess(page: Page) {
  // Submit the form
  await page.click('button[type="submit"]');
  
  // Verify submission success
  await expect(page.locator('.success-message')).toBeVisible({ timeout: 10000 });
}

/**
 * Submit a form and expect validation errors
 */
export async function submitFormAndExpectValidationErrors(page: Page) {
  // Submit the form
  await page.click('button[type="submit"]');
  
  // Verify validation errors appear
  await expect(page.locator('.error-message')).toBeVisible({ timeout: 5000 });
}

/**
 * Verify form field validation error
 */
export async function verifyFieldValidationError(page: Page, fieldName: string) {
  // Check for field-specific error message
  const fieldError = page.locator(`[name="${fieldName}"] ~ .field-error, [name="${fieldName}"] + .field-error`);
  await expect(fieldError).toBeVisible({ timeout: 5000 });
}

/**
 * Navigate to STS and verify a lead exists
 */
export async function verifyLeadInSTS(page: Page, {
  username,
  password,
  leadName
}: {
  username: string;
  password: string;
  leadName: string;
}) {
  // Log in to STS
  await page.goto('https://proposalpath.com/login');
  await page.fill('#username', username);
  await page.fill('#password', password);
  await page.click('input[type="submit"]');

  // Navigate and search
  await page.click('text=Prospects');
  await page.fill('.search-input', leadName);
  await page.press('.search-input', 'Enter');

  // Verify lead exists
  await expect(page.locator(`text=${leadName}`)).toBeVisible({ timeout: 10000 });
}
