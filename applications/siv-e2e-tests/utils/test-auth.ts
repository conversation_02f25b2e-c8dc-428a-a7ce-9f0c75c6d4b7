/**
 * Utility for test authentication in e2e tests
 */
import {Page, BrowserContext} from '@playwright/test';
import {env} from './env';
import {clerk} from "@clerk/testing/playwright";
import path from 'path';

const TEST_AUTH_EMAIL = env.TEST_AUTH_EMAIL;
const TEST_AUTH_PASSWORD = env.TEST_AUTH_PASSWORD;

export const SIV_ADMIN_AUTH_FILE_PATH = path.join(__dirname, '..', '.clerk', 'siv-admin.json');
export const CUSTOMER_USER_AUTH_FILE_PATH = path.join(__dirname, '..', '.clerk', 'customer-user.json');

/**
 * Logs in using Clerk authentication
 * This is more efficient than using the UI when you just need authentication
 * @returns The authentication token string
 */
export async function loginAsSivAdminAndGetSessionToken(context: BrowserContext): Promise<string> {
    if (!TEST_AUTH_EMAIL || !TEST_AUTH_PASSWORD) {
        throw new Error('Test authentication credentials are not configured. Make sure TEST_AUTH_EMAIL and TEST_AUTH_PASSWORD are set in the environment.');
    }

    // Create a new page for Clerk authentication
    const page = await context.newPage();

    try {
        // Navigate to the app
        await page.goto('/admin');

        // Sign in using Clerk
        await clerk.signIn({
            page,
            signInParams: {
                strategy: 'password',
                identifier: TEST_AUTH_EMAIL,
                password: TEST_AUTH_PASSWORD,
            },
        });

        // Navigate to admin area to ensure login worked
        await page.goto('/admin');
        await page.waitForLoadState('networkidle');

        // Save the auth state to a file

        await page.context().storageState({path: SIV_ADMIN_AUTH_FILE_PATH});

        // Extract the auth token

        // @ts-ignore
        const sessionToken = await page.evaluate(async () => Clerk.session.getToken())
        if (!sessionToken) {
            throw new Error('Authentication token not found in cookies after login');
        }

        // Close the temporary page since we only needed it for the sign in request
        await page.close();

        return sessionToken;
    } catch (error) {
        // Make sure to close the page even if there's an error
        await page.close();
        throw error;
    }
}
/**
 * Logs in using Clerk authentication
 * This is more efficient than using the UI when you just need authentication
 * @returns The authentication token string
 */
export async function loginAsCustomerUser(context: BrowserContext): Promise<void> {
    if (!TEST_AUTH_EMAIL || !TEST_AUTH_PASSWORD) {
        throw new Error('Test authentication credentials are not configured. Make sure TEST_AUTH_EMAIL and TEST_AUTH_PASSWORD are set in the environment.');
    }

    // Create a new page for Clerk authentication
    const page = await context.newPage();

    try {
        // Navigate to the app
        await page.goto('/app/sales-deployment-onboarding');

        // Sign in using Clerk
        await clerk.signIn({
            page,
            signInParams: {
                strategy: 'password',
                identifier: TEST_AUTH_EMAIL,
                password: TEST_AUTH_PASSWORD,
            },
        });

        await page.goto('/app/sales-deployment-onboarding');
        await page.waitForLoadState('networkidle');

        // Save the auth state to a file

        await page.context().storageState({path: CUSTOMER_USER_AUTH_FILE_PATH});

        await page.close();

    } catch (error) {
        // Make sure to close the page even if there's an error
        await page.close();
        throw error;
    }
}
