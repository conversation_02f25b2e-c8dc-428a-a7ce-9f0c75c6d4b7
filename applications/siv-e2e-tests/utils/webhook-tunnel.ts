import ngrok from "@ngrok/ngrok";
import { env } from "./env";

export async function setupWebhookTunnelIfLocal() {
    if (env.LOCAL_MODE) {
        const port = env.PORT || 5173;
        const ngrokTunnel = await ngrok.forward({
            addr: port,
            authtoken_from_env: true,
            //domain from ngrok account; if we add more engineers and they want to run
            //tests concurrently, they should use their own ngrok domain and highlevel subaccount
            domain: "sivdev.ngrok.app"
        });
        const url = ngrokTunnel.url();
        if(url === null) {
            throw new Error(`Failed to establish ngrok tunnel to local port ${port}; ngrok listener url is null`);
        }

        return {url: url}
    }

    return {url: env.BASE_URL}
}
