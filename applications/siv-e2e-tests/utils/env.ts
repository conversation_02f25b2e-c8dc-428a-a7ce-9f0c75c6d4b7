/**
 * Environment utilities for E2E testing
 * Provides type-safe access to environment variables with validation
 */
import {z} from 'zod';
import dotenvFlow from 'dotenv-flow';

// Load environment variables before anything else
dotenvFlow.config();

// Define environment schema
const envSchema = z.object({
  PORT: z.preprocess((it)=>typeof it ==="string" ? parseInt(it) : 8080, z.number()),
  // Base URL for the application
  BASE_URL: z.string().url().default('http://localhost:5173'),

  PROPERTY_ID: z.string().min(5),
  // HighLevel API token

  // STS credentials
  STS_USERNAME: z.string(),
  STS_PASSWORD: z.string(),

  // Test auth credentials
  TEST_AUTH_EMAIL: z.string().email().optional(),
  TEST_AUTH_PASSWORD: z.string().optional(),
  
  // CI environment
  CI: z.preprocess(
    (val) => val === 'true' || val === true,
    z.boolean().default(false)
  ),
  
  // Local mode
  LOCAL_MODE: z.preprocess(
    (val) => val === 'true' || val === true,
    z.boolean().default(false)
  ),
  
  // Node environment
  NODE_ENV: z.enum(['development', 'staging', 'test', 'production', 'e2e']).default('development')
});

// Parse and export environment variables
export const env = envSchema.parse(process.env);


/**
 * Gets the STS credentials
 */
export function getSTSCredentials(): { username: string; password: string } {
  return {
    username: env.STS_USERNAME,
    password: env.STS_PASSWORD,
  };
}
