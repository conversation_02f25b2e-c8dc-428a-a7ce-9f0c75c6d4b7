import { test, expect } from '@playwright/test'

// Test data structure with contact information
const testContacts = [
  {
    eventType: 'corporate_meeting',
    eventName: 'Q1 2025 Planning Session',
    firstName: 'David Test',
    lastName: '<PERSON>',
    phone: '+17208389772',
    email: '<EMAIL>'
  },
  {
    eventType: 'wedding',
    eventName: 'Smith-Johnson Wedding',
    firstName: 'Mike Test',
    lastName: 'Medsker',
    phone: '+19704716722',
    email: '<EMAIL>'
  }
]

test.describe('Form Submission Tests', () => {
  test('should submit forms in parallel with different contact information', async ({ browser }) => {
    // Create separate browser contexts and pages for parallel submissions
    const contexts = await Promise.all(
      testContacts.map(() => browser.newContext())
    )
    
    const pages = await Promise.all(
      contexts.map(context => context.newPage())
    )

    try {
      // Submit all forms in parallel
      await Promise.all(
        testContacts.map(async (contact, index) => {
          const page = pages[index]
          
          // Navigate to the form page
          await page.goto('https://admin.sivconverts.com/forms/7fb79c00-46ac-494e-b649-e14f40e7cc0a')
          
          // Wait for form container to be visible
          await page.waitForSelector('[data-testid="form-container"]')

          // Fill in Event Type (select dropdown)
          await page.selectOption('[data-testid="input-EVENT_TYPE"]', contact.eventType)

          // Fill in Event Name
          await page.fill('[data-testid="input-EVENT_NAME"]', contact.eventName)

          // Fill in First Name
          await page.fill('[data-testid="input-FIRST_NAME"]', contact.firstName)

          // Fill in Phone Number
          // First click on the phone input to ensure it's focused
          await page.click('[data-testid="input-PHONE"]')
          await page.fill('[data-testid="input-PHONE"]', contact.phone)

          // Check the marketing consent checkbox
          await page.check('[data-testid="input-MARKETING_CONSENT"]')

          // Submit the form
          await page.click('button[type="submit"]')

          // Wait for form submission to complete
          // You might need to adjust this based on what happens after submission
          // For example, wait for a success message or navigation to a new page
          await page.waitForTimeout(1000)
        })
      )
      
      console.log(`Successfully submitted ${testContacts.length} forms in parallel`)
    } finally {
      // Clean up contexts
      await Promise.all(contexts.map(context => context.close()))
    }
  })
})
