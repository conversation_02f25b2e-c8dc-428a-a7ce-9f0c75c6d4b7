import {test} from '@playwright/test';
import {setupTestFixtures, TestFixtures} from '../fixtures/setup-fixtures';
import {getSTSCredentials} from '../utils/env';
import {setupWebhookTunnelIfLocal} from "../utils/webhook-tunnel";
import {loginAsSivAdminAndGetSessionToken, SIV_ADMIN_AUTH_FILE_PATH} from '../utils/test-auth';
import {FormPage} from '../page-objects/form-page';
import {AdminSubmissionsPage} from '../page-objects/admin-submissions-page';
import {StsPage} from '../page-objects/sts-page';


/**
 * Smoke test for form submission flow
 * This test runs after deployment to validate the full integration
 *
 * @tag smoke - Post-deployment validation tests
 */
test.describe('Form Submission Flow', {
    tag: ['@smoke', "@admin"],
}, () => {
    let fixtures: TestFixtures;
    let authToken: string;

    test.beforeAll(async ({browser}) => {
        // Login once at the beginning of the test suite
        const authContext = await browser.newContext();
        authToken = await loginAsSivAdminAndGetSessionToken(authContext);
        await authContext.close(); // Close the context after getting the token

        // Set up fixtures with the auth token
        fixtures = await setupTestFixtures(authToken);
        await setupWebhookTunnelIfLocal();
    });

    test.afterAll(async () => {
        // Clean up test fixtures
        if (fixtures) {
            console.log('Test fixtures cleaned up successfully');
        }
    });

    //TODO: repeat the test with new and THE SAME contact info (email/phone number)
    // this will catch GHL workflow configuration issues
    test('should submit form and verify lead in STS', async ({browser}, testInfo) => {
        testInfo.setTimeout(80000);
        // Create contexts for different parts of the test
        const formContext = await browser.newContext();
        const formPage = await formContext.newPage();
        const formSubmissionPage = new FormPage(formPage);

        // Generate unique test data
        const uuid = crypto.randomUUID();
        const formData = {
            eventType: 'Social or Sport Club',
            firstName: 'Jimmy',
            lastName: `Test-${uuid}`,
            company: `Test Company ${uuid}`,
            email: `jimmydean.${uuid}@example.com`,
            phone: '7208379662',
            city: 'Denver',
            state: 'CO',
            country: 'US',
            postalCode: '80202',
            guestCount: '150',
            roomCount: '25',
            budget: '15000',
            startDate: '2065-06-18', // YYYY-MM-DD format for input fields
            endDate: '2065-06-23',
            eventDescription: 'This is an automated test submission with comprehensive fields',
            flexibleDates: true,
            eventNeeds: ['CATERING', 'GUESTROOMS'],
            marketingConsent: true
        };

        await formSubmissionPage.navigateToForm(fixtures.form.id);

        // Log available form fields for debugging
        await formSubmissionPage.logAvailableFormFields();

        // Fill out the form using the page object method
        await formSubmissionPage.fillForm(formData);

        // Submit the form using the page object method
        await formSubmissionPage.submitForm();

        // Create admin context using the saved auth state
        const adminContext = await browser.newContext({
            storageState: SIV_ADMIN_AUTH_FILE_PATH
        });
        const adminPage = await adminContext.newPage();
        const adminSubmissionsPage = new AdminSubmissionsPage(adminPage);
        await adminSubmissionsPage.navigateToSubmissions(fixtures.propertyId, fixtures.form.id);
        await adminPage.waitForTimeout(2000); // Give the page a moment to load
        console.log(`Looking for submission with email: ${formData.email}`);

        await adminSubmissionsPage.logAllSubmissions();
        const targetSubmission = await adminSubmissionsPage.verifySubmissionFields(formData.email, formData);
        await adminSubmissionsPage.verifyDateFields(targetSubmission, formData.startDate, formData.endDate);
        await adminSubmissionsPage.verifyCheckboxFields(targetSubmission, formData.flexibleDates, formData.eventNeeds);
        await adminSubmissionsPage.verifyMarketingConsent(targetSubmission, formData.marketingConsent);
        await adminSubmissionsPage.verifyEventType(targetSubmission, formData.eventType);

        await formPage.waitForTimeout(10000);

        //TODO: verify via high level api
        // new HighLevelClient()

        // Verify in STS UI
        const stsContext = await browser.newContext();

        // Get STS credentials
        const {username, password} = getSTSCredentials();

        // Create STS page and log in
        const stsPage = new StsPage(await stsContext.newPage());
        await stsPage.login(username, password);
        await stsPage.navigateToLeads();

        // Find the lead row that contains the test name in the contact column and click on it
        const leadRow = await stsPage.findLeadByName(formData.firstName, formData.lastName);
        await stsPage.clickOnLead(leadRow);

        // Verify contact information in the detail page
        await stsPage.verifyContactInformation(
            formData.firstName,
            formData.lastName,
            formData.email,
            formData.company,
            formData.phone
        );

        await stsPage.verifyDates(formData.startDate, formData.endDate);
        await stsPage.verifyAttendees(formData.guestCount);
        await stsPage.verifyFunctionSpace(['Catering', 'Guestrooms']);
        await stsPage.verifyDescription(formData.eventDescription);

        // Close contexts
        await formContext.close();
        await stsContext.close();
    });
});
