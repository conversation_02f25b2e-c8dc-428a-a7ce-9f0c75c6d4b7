import {test as setup} from '@playwright/test';
import {clerkSetup} from '@clerk/testing/playwright';

setup('login admin user with clerk', {
    tag: ["@e2e", "@smoke", "@admin"]
}, async ({}) => {
    await clerkSetup({
        secretKey: process.env.SIV_ADMIN_CLERK_SECRET_KEY,
        publishableKey: process.env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY
    });
});

setup('login customer user with clerk', {
    tag: ["@e2e", "@smoke", "@customer"]
}, async ({}) => {
    await clerkSetup({
        secretKey: process.env.USER_FACING_SIV_CLERK_SECRET_KEY,
        publishableKey: process.env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY
    });
});

