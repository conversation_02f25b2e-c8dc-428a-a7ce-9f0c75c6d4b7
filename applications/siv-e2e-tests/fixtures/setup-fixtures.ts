/**
 * Main fixture setup for E2E testing
 * Combines all fixtures to set up a complete test environment
 */
import {Form, FieldIdentifier} from "siv-web/src/modules/forms/builder/types";
import {env} from "../utils";
import {upsertTestForm} from "../utils/test-form-setup";
import {createTestFieldList} from "siv-web/src/modules/admin/forms/new-form-test-factories";

// Define a more flexible field type for our test fixtures
type TestFormField = {
    type: FieldIdentifier;
    label: string;
    required: boolean;
    width?: 'full' | 'half';
    rowBreakAfter?: boolean;
    checkboxText?: string; // For CHECKBOX fields
    options?: Array<{ value: string; label: string }>; // For CHECKBOX_GROUP fields
};

// Test fixture result interface
export interface TestFixtures {
    propertyId: string
    form: Form;
}

/**
 * Sets up all test fixtures needed for E2E tests
 * Creates a property, configures integrations, and creates a form
 * @param authToken Optional authentication token to use for API requests
 */
export async function setupTestFixtures(authToken: string): Promise<TestFixtures> {
    // Create a deterministic ID for the form
    const formId = "750dec47-b92a-4bd0-b41c-9c5decd199c8";
    const formName = 'E2E Test Form';

    // Use the upsertTestForm utility to create or update the form with the API
    // Pass the auth token if provided, otherwise it will use the LOCAL_MODE setting
    const formObj = await upsertTestForm(env.PROPERTY_ID, {
        id: formId, // Include the ID for upserting
        name: formName,
        description: 'E2E Test Form for automated testing',
        propertyId: env.PROPERTY_ID,
        theme: {
            name: 'Default Theme',
            primaryColor: '#000000',
            backgroundColor: '#ffffff',
            textColor: '#000000',
            borderColor: '#e2e8f0',
            inputBorderRadius: '0.375rem',
            buttonBorderRadius: '0.375rem',
            padding: '1.5rem',
            buttonTextColor: '#ffffff',
            buttonAlignment: 'right',
            font: 'system-ui, sans-serif',
            fieldBackgroundColor: '#ffffff',
            inputTextColor: '#000000',
            formBorderRadius: '0.5rem',
            placeholderColor: '#6b7280'
        },
        fields: createTestFieldList([
            {
                type: "EVENT_TYPE",
                label: 'Event Type',
                required: true,
            },
            {
                type: 'FIRST_NAME',
                label: 'First Name',
                required: true,
            },
            {
                type: 'LAST_NAME',
                label: 'Last Name',
                required: true,
            },
            {
                type: 'COMPANY',
                label: 'Company Name',
                required: false,
            },
            {
                type: 'EMAIL',
                label: 'Email',
                required: true,
            },
            {
                type: 'PHONE',
                label: 'Phone Number',
                required: true,
            },
            {
                type: 'CITY',
                label: 'City',
                required: true,
            },
            {
                type: 'COUNTRY',
                label: 'Country',
                required: true,
            },            {
                type: 'STATE',
                label: 'State',
                required: true,
            },
            {
                type: 'POSTAL_CODE',
                label: 'Postal Code',
                required: true,
            },
            {
                type: 'GUEST_COUNT',
                label: 'Number of Guests',
                required: true,
            },
            {
                type: 'ROOM_COUNT',
                label: 'Number of Rooms Needed',
                required: false,
            },
            {
                type: 'BUDGET',
                label: 'Estimated Budget',
                required: false,
            },
            {
                type: 'START_DATE',
                label: 'Preferred Start Date',
                required: true,
            },
            {
                type: 'END_DATE',
                label: 'Preferred End Date',
                required: true,
            },
            {
                type: 'EVENT_DESCRIPTION',
                label: 'Message',
                required: false,
            },
            {
                type: 'FLEXIBLE_DATES',
                label: 'Are your dates flexible?',
                required: false,
                checkboxText: 'Yes, my dates are flexible',
            },
            {
                type: 'EVENT_NEEDS',
                label: 'Event Needs',
                required: false,
                options: [
                    {value: 'CATERING', label: 'Catering'},
                    {value: 'GUESTROOMS', label: 'Guest Rooms'},
                    {value: 'AUDIOVISUAL', label: 'Audio/Visual'},
                    {value: 'PARKING', label: 'Parking'}
                ],
            },
            {
                type: 'MARKETING_CONSENT',
                label: 'Marketing Consent',
                required: true,
                checkboxText: 'I agree to receive marketing communications',
            }
        ]),
        redirectUrl: null,
        allowedDomains: []
    }, authToken);

    console.log(`Created form with ID: ${formObj.id}`);

    return {propertyId: env.PROPERTY_ID, form: formObj};
}
