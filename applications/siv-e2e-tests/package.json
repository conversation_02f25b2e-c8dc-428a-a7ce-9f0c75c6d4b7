{"name": "@siv/e2e-tests", "version": "0.1.0", "private": true, "scripts": {"test": "playwright test", "test:smoke": "cross-env TEST_TAG=smoke playwright test --grep @smoke", "test:e2e": "cross-env NODE_ENV=development TEST_TAG=e2e playwright test --grep @e2e", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "track-deps": "tsx ../../scripts/track-deps.ts"}, "dependencies": {"@types/uuid": "^10.0.0", "axios": "^1.7.9", "dotenv-flow": "^4.1.0", "siv-web": "workspace:*", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@clerk/testing": "^1.8.0", "@ngrok/ngrok": "^1.4.1", "@playwright/test": "^1.40.0", "@types/node": "^20.10.0", "cross-env": "^7.0.3", "typescript": "^5.8.3"}}