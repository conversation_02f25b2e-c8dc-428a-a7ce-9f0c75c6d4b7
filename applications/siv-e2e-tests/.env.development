# E2E Test Environment Configuration
#Secrets and things that could vary based on smoke test vs just running local
#should be defined in either .env.e2e.local file or env vars
BASE_URL=http://localhost:5173
PORT=5173
# Integration Credentials
STS_USERNAME=siv_demo
STS_PASSWORD="define me in .env.development.local or env var"
LOCAL_MODE=true #change this for smoke tests running against staging; in local mode we set up an ngrok tunnel

# Test Auth Credentials; should match whatever the server settings are
# in either staging or development
TEST_AUTH_EMAIL=<EMAIL>
TEST_AUTH_PASSWORD=somesecretuser123!
SIV_ADMIN_CLERK_PUBLISHABLE_KEY=pk_test_dG91Y2hpbmctaWJleC01Ni5jbGVyay5hY2NvdW50cy5kZXYk
SIV_ADMIN_CLERK_SECRET_KEY=sk_test_CfXHGHksR5E83P3PxTsfK2Kp08hyNR2cKZWLTcgojm
CLERK_PUBLISHABLE_KEY=pk_test_dGlnaHQta29kaWFrLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
USER_FACING_SIV_CLERK_PUBLISHABLE_KEY=pk_test_dGlnaHQta29kaWFrLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
USER_FACING_SIV_CLERK_SECRET_KEY=sk_test_V8UnXlPOJuGq0gF6w2EylZeyctYUCRZZcOAjlVTlI4
