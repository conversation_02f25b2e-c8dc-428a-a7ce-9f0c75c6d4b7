#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to publish pacts for a specific participant to the pact broker 
 * This fixes the issue where the regular publish-pacts script looks in the wrong location
 */

import { execSync } from 'child_process';
import * as dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { Publisher, PublisherOptions } from '@pact-foundation/pact-node';
import fs from 'fs';

// Get the current file's directory
// @ts-ignore
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Determine monorepo root (go up two directories)
const monoRepoRoot = path.resolve(__dirname, '../../');

// Load environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, './.env.test') });

// Get Git user information
const getGitUserInfo = (): {email: string; username: string} => {
  try {
    const email = execSync('git config user.email').toString().trim();
    const username = execSync('git config user.name').toString().trim();
    return { email, username };
  } catch (error) {
    console.warn('Could not retrieve git user info. Using placeholder values.');
    return { email: 'unknown-email', username: 'unknown-user' };
  }
};


// Get version info based on git state
const getVersionInfo = (): { version: string; isDraft: boolean; username: string } => {
  // Check if there are uncommitted changes
  const status = execSync('git status --porcelain').toString();
  const hasUncommittedChanges = status.trim().length > 0;

  // Get the actual commit hash (used for committed changes)
  const commit = execSync('git rev-parse HEAD').toString().trim();

  // Get user info for draft versions
  const { username } = getGitUserInfo();
  const safeUsername = username.toLowerCase().replace(/\s+/g, '-');

  if (!hasUncommittedChanges) {
    // For committed changes, use the commit hash as version
    return {
      version: commit,
      isDraft: false,
      username: safeUsername
    };
  } else {
    // For drafts, use a consistent name that will be overwritten each time
    // Format: "draft-username" - this means each user has only ONE draft version at a time
    return {
      version: `draft-${safeUsername}`,
      isDraft: true,
      username: safeUsername
    };
  }
};

const getGitBranch = (): string => {
  return execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
};

const publishPacts = async (participantName: string) => {
  const versionInfo = getVersionInfo();
  const gitBranch = getGitBranch();
  
  // Destructure the version info
  const { version, isDraft, username: safeUsername } = versionInfo;
  
  // Get broker details from environment variables
  const brokerBaseUrl = process.env.PACT_BROKER_URL;
  const brokerUsername = process.env.PACT_BROKER_USERNAME;
  const brokerPassword = process.env.PACT_BROKER_PASSWORD;
  
  if (!brokerBaseUrl || !brokerUsername || !brokerPassword) {
    console.error('Error: Pact broker details not found in environment variables!');
    console.error('Make sure PACT_BROKER_URL, PACT_BROKER_USERNAME, and PACT_BROKER_PASSWORD are set.');
    process.exit(1);
  }

  // Path to the pacts directory in the monorepo root
  const pactsRootPath = path.resolve(monoRepoRoot, 'pacts');

  // Create provider directory if needed
  const participantPath = path.resolve(pactsRootPath, participantName);
  if (!fs.existsSync(participantPath)) {
    console.warn(`Creating provider directory at ${participantPath}`);
    fs.mkdirSync(participantPath, { recursive: true });
  }

  // Function to find all pact files for this participant across subdirectories
  const findPactFilesForParticipant = () => {
    console.log(`Finding pact files for participant: ${participantName}`);

    // For simplicity, just use the direct participant directory first
    if (fs.existsSync(participantPath)) {
      const directPactFiles = fs.readdirSync(participantPath)
        .filter(file => file.endsWith('.json'))
        .map(file => path.resolve(participantPath, file));

      if (directPactFiles.length > 0) {
        console.log(`Found ${directPactFiles.length} pact files in ${participantPath}`);
        return directPactFiles;
      }
    }

    // If not found in direct directory, look for specific files in consumer directories
    console.log(`No pacts found in direct directory, checking consumer directories`);

    // Keep track of processed files to avoid duplicates
    const processedFilePaths = new Set();
    const relevantPactFiles = [];

    const subdirs = fs.readdirSync(pactsRootPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    for (const subdir of subdirs) {
      // Skip the participant's own directory as we already processed it
      if (subdir === participantName) continue;

      const subdirPath = path.resolve(pactsRootPath, subdir);
      if (fs.existsSync(subdirPath)) {
        const files = fs.readdirSync(subdirPath)
          .filter(file => file.endsWith('.json') &&
                        (file.includes(`-${participantName}.json`) ||
                         file.includes(`-${participantName}-`)))
          .map(file => path.resolve(subdirPath, file));

        for (const file of files) {
          if (!processedFilePaths.has(file)) {
            processedFilePaths.add(file);
            relevantPactFiles.push(file);
          }
        }
      }
    }

    console.log(`Found ${relevantPactFiles.length} relevant pact files in consumer directories`);
    return relevantPactFiles;
  };

  // Get all pact files for this participant
  const pactFilePaths = findPactFilesForParticipant();

  if (pactFilePaths.length === 0) {
    // If no pact files exist, we'll create a provider version without pacts
    console.warn(`No pact files found for participant: ${participantName}`);
    console.warn('Creating a provider version without pacts.');

    try {
      // Create the version in the broker
      const result = execSync(
        `./node_modules/.bin/pact-broker create-version-tag --pacticipant=${participantName} --version=${version} --tag=${gitBranch} --broker-base-url=${brokerBaseUrl} --broker-username=${brokerUsername} --broker-password=${brokerPassword}`
      ).toString();

      console.log(`Provider version created: ${version}`);
      console.log(`With tag: ${gitBranch}`);
      console.log(`✅ Provider version successfully registered in the broker!`);

      // Exit successfully - we've created a provider version without pacts
      process.exit(0);
    } catch (error) {
      console.error('Error creating provider version:', error);
      process.exit(1);
    }
  }
  
  // Set up publisher options
  const options: PublisherOptions = {
    pactFilesOrDirs: pactFilePaths,
    pactBroker: brokerBaseUrl,
    pactBrokerUsername: brokerUsername,
    pactBrokerPassword: brokerPassword,
    consumerVersion: version,

    // Add tags - always include branch name
    // For drafts, add both generic 'draft' tag and user-specific draft tag
    tags: isDraft
      ? [gitBranch, 'draft', `${safeUsername}-draft`]
      : [gitBranch],
    branch: gitBranch
  };
  
  try {
    console.log(`Publishing pacts for ${participantName} to broker...`);
    console.log(`Options: ${JSON.stringify(options, (key, value) => 
      key === 'pactBrokerPassword' ? '********' : value, 2)}`);
    
    const publisher = new Publisher(options);
    const results = await publisher.publish();
    
    // Print success message
    console.log(`\nPact contracts for ${participantName} published successfully!`);

    if (isDraft) {
      console.log('\n⚠️  WARNING: This is a DRAFT version with local uncommitted changes! ⚠️');
      console.log(`This draft is tagged with your username: "${safeUsername}-draft"`);
      console.log('This version is for testing purposes only and should not be used for verification in CI/CD pipelines.');
    }

    console.log('Published pacts:');
    results.forEach((file : any) => console.log(`- ${file}`));

  } catch (error) {
    console.error('Error publishing pacts:', error);
    process.exit(1);
  }
};

// Get the participant name from command line arguments
const participantName = process.argv[2];
if (!participantName) {
  console.error('Error: Participant name is required');
  console.error('Usage: publish-participant-pacts.ts <participant-name>');
  process.exit(1);
}

// Execute the main function
publishPacts(participantName).catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
