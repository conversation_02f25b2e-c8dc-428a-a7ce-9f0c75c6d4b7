module.exports = {
  // ... existing config ...
  rules: {
    // ... existing rules ...
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'default',
        format: ['camelCase'],
      },
      {
        selector: 'variable',
        format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
      },
      {
        // Enforce Page suffix for page components
        selector: 'variable',
        filter: {
          regex: '.*Page$',
          match: true
        },
        format: ['PascalCase'],
        custom: {
          regex: '^[A-Z][a-zA-Z]*Page$',
          match: true,
          message: 'Page component names must match their file names exactly and end with "Page"'
        }
      }
    ],
  },

  overrides: [
    {
      files: ['**/*Page.tsx'],
      rules: {
        'naming-convention': [
          'error',
          {
            selector: 'default',
            format: ['PascalCase'],
            custom: {
              regex: '^[A-Z][a-zA-Z]*Page$',
              match: true,
              message: 'Page component file names must match their component names exactly and end with "Page"'
            }
          }
        ]
      }
    }
  ],

  extends: ['plugin:storybook/recommended']
} 