{"compilerOptions": {"noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "experimentalDecorators": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "composite": true, "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["vite/client"], "jsx": "react-jsx", "jsxImportSource": "react", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "#drizzle/*": ["./src/drizzle/*"]}}}