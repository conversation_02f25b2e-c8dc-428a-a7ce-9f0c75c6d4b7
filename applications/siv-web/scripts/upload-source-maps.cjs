#!/usr/bin/env node

/**
 * Script to upload source maps to Datadog for better error tracking
 * This should be run after the build process in CI/CD
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if required environment variables are set
const requiredEnvVars = ['DATADOG_API_KEY', 'DD_SERVICE', 'DD_ENV', 'DD_VERSION'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '));
  console.error('Please set these environment variables before running this script.');
  process.exit(1);
}

const service = process.env.DD_SERVICE;
const env = process.env.DD_ENV;
const version = process.env.DD_VERSION;

console.log(`Uploading source maps to Datadog...`);
console.log(`Service: ${service}`);
console.log(`Environment: ${env}`);
console.log(`Version: ${version}`);

// Check if dist directory exists
const distPath = path.join(__dirname, '..', 'dist/client');
if (!fs.existsSync(distPath)) {
  console.error('dist directory does not exist. Please run the build first.');
  process.exit(1);
}

try {
  let command = `npx -p @datadog/datadog-ci@latest datadog-ci sourcemaps upload ${distPath} --service="${service}" --release-version="${version}" --minified-path-prefix="/"`;
  
  command += ` --repository-url="${process.env.SEMAPHORE_GIT_URL}"`;

  // Upload all source maps from the dist directory
  console.log('\nUploading source maps...');
  execSync(command, { 
    stdio: 'inherit',
    env: { ...process.env, DATADOG_SITE: 'datadoghq.com' }
  });

  console.log('\nSource maps uploaded successfully!');
} catch (error) {
  console.error('Failed to upload source maps:', error.message);
  process.exit(1);
}
