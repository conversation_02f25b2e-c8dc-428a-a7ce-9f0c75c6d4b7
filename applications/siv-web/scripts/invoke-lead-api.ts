#!/usr/bin/env node

import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
const envPath = path.resolve(process.cwd(), '.env.invoke-lead-api');
dotenv.config({path: envPath});

interface LeadData {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    eventType: string;
    startDate: string;
    endDate: string;
    guestCount: number;
    roomCount: number;
    eventName: string;
    eventNeeds?: string[];
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    company?: string;
    budget?: number;
    flexibleDates?: boolean;
    eventDescription?: string;
    marketingConsent?: boolean;
}

// Configuration from environment variables
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5173';
const API_KEY = process.env.API_KEY || '';
const PROPERTY_ID = process.env.PROPERTY_ID || '';
const CONCURRENT_MODE = process.env.CONCURRENT_MODE === 'true';
const DELAY_SECONDS = parseInt(process.env.DELAY_SECONDS || '1', 10);

// Configure phone numbers in standard format here
type Contact = { phone: string, email: string, firstName: string }
const num = 2
const CONTACTS = [
    {phone: `+17208389772`, email: `david+demo${num}@sivconverts.com`, firstName: `David Test..${num}`, lastName: "Julia"},
    {phone: `+19704716722`, email: `mike+demo${num}@sivconverts.com`, firstName: `Mike Test..${num}`, lastName: "Medsker"}
]

// Helper to generate a future date
function getFutureDate(daysFromNow: number): string {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date.toISOString().split('T')[0];
}

// Create lead data for each phone number
function createLeadData(contactInfo: Contact, index: number): LeadData {
    return {
        firstName: contactInfo.firstName,
        lastName: `Lead${index + 1}`,
        email: contactInfo.email,
        phone: contactInfo.phone,
        eventType: 'wedding',
        startDate: getFutureDate(90 + index * 7), // Stagger dates
        endDate: getFutureDate(93 + index * 7),
        guestCount: 150 + index * 10,
        roomCount: 45 + index * 5,
        eventName: `${contactInfo.firstName} - Test Wedding ${index + 1}`,
        eventNeeds: ['WEDDING_CEREMONY', 'WEDDING_RECEPTION'],
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94103',
        country: 'US',
        company: `Test Company ${index + 1}`,
        budget: 25000 + index * 5000,
        flexibleDates: index % 2 === 0, // Alternate flexible dates
        eventDescription: `Looking for a beautiful venue for test wedding ${index + 1}`,
        marketingConsent: true
    };
}

// Submit a single lead
async function submitLead(leadData: LeadData): Promise<{ leadData: LeadData; leadId?: string }> {
    const url = `${API_BASE_URL}/properties/${PROPERTY_ID}/leads`;

    try {
        console.log(`Submitting lead for ${leadData.firstName} ${leadData.lastName} (${leadData.phone})...`, leadData);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY,
                'X-API-Version': '2025-04-14'
            },
            body: JSON.stringify(leadData)
        });

        const responseText = await response.text();
        let responseData: any;

        try {
            responseData = JSON.parse(responseText);
        } catch (parseError) {
            console.error(`❌ Error: Failed to parse response for ${leadData.firstName} ${leadData.lastName}`);
            console.error(`   Status: ${response.status}`);
            console.error(`   Response preview: ${responseText.substring(0, 200)}...`);
            throw new Error(`Failed to parse response: ${responseText.substring(0, 200)}...`);
        }

        if (response.ok) {
            console.log(`✅ Success: Lead created for ${leadData.firstName} ${leadData.lastName}`);
            console.log(`   Lead ID: ${responseData.lead?.id || 'N/A'}`);
            return {
                leadData,
                leadId: responseData.lead?.id
            };
        } else {
            console.error(`❌ Error: Failed to create lead for ${leadData.firstName} ${leadData.lastName}`);
            console.error(`   Status: ${response.status}`);
            console.error(`   Error Type: ${responseData.errorType}`);
            console.error(`   Message: ${responseData.message}`);
            if (responseData.errors) {
                console.error(`   Validation Errors:`, responseData.errors);
            }
            throw new Error(`${response.status}: ${responseData.errorType}: ${responseData.message}`);
        }
    } catch (error) {
        console.error(`❌ Network Error: Failed to submit lead for ${leadData.firstName} ${leadData.lastName}`);
        console.error(`   Error: ${error instanceof Error ? error.message : error}`);
        throw error;
    }
}

// Main function
async function main() {
    console.log('SIV Lead API Test Script');
    console.log('=======================\n');
    console.log(`Environment file: ${envPath}`);
    console.log(`API URL: ${API_BASE_URL}`);
    console.log(`Property ID: ${PROPERTY_ID}`);
    console.log(`Number of leads to submit: ${CONTACTS.length}`);
    console.log(`Mode: ${CONCURRENT_MODE ? 'Concurrent' : 'Sequential'}`);
    if (!CONCURRENT_MODE) {
        console.log(`Delay between submissions: ${DELAY_SECONDS} seconds`);
    }
    console.log();

    // Check if API key and property ID are configured
    if (!API_KEY || !PROPERTY_ID) {
        console.error('❌ Error: Missing required environment variables.');
        console.error('   Please create a .env.invoke-lead-api file with:');
        console.error('   API_KEY=your-api-key-here');
        console.error('   PROPERTY_ID=your-property-id-here');
        console.error('   API_BASE_URL=http://localhost:5173 (optional)');
        console.error('   CONCURRENT_MODE=true (optional, default: false)');
        console.error('   DELAY_SECONDS=15 (optional, default: 15)');
        process.exit(1);
    }

    // Create all lead data
    const allLeadData = CONTACTS.map((contact, index) =>
        createLeadData(contact, index)
    );

    function waitNSeconds(seconds: number) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(true);
            }, seconds*1000);
        });
    }

    let results: Array<{ status: 'fulfilled' | 'rejected'; leadData: LeadData; value?: any; reason?: any }> = [];

    if (CONCURRENT_MODE) {
        console.log('Submitting leads concurrently...\n');
        
        // Submit all leads concurrently
        const promises = allLeadData.map((leadData, index) => {
            console.log(`[${index + 1}/${allLeadData.length}] Submitting ${leadData.firstName} ${leadData.lastName}...`);
            return submitLead(leadData);
        });
        
        const settledResults = await Promise.allSettled(promises);
        results = settledResults.map((result, index) => ({
            status: result.status,
            leadData: allLeadData[index],
            ...(result.status === 'fulfilled' ? { value: result.value } : { reason: result.reason })
        }));
        
        results.forEach(res => console.log(`result: ${JSON.stringify(res)}`))
    } else {
        console.log(`Submitting leads sequentially with ${DELAY_SECONDS} second delay between each...\n`);
        
        // Submit all leads with delay between each
        for (let i = 0; i < allLeadData.length; i++) {
            const leadData = allLeadData[i];
            console.log(`\n[${i + 1}/${allLeadData.length}] Submitting ${leadData.firstName} ${leadData.lastName}...`);
            
            try {
                const value = await submitLead(leadData);
                results.push({
                    status: 'fulfilled',
                    leadData,
                    value
                });
            } catch (error) {
                results.push({
                    status: 'rejected',
                    leadData,
                    reason: error
                });
            }
            
            // Wait before next submission (except for the last one)
            if (i < allLeadData.length - 1) {
                console.log(`⏳ Waiting ${DELAY_SECONDS} seconds before next submission...`);
                await waitNSeconds(DELAY_SECONDS);
            }
        }
    }

    // Summary of results
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log('\n========= Summary =========');
    console.log(`✅ Successful submissions: ${successful}`);
    console.log(`❌ Failed submissions: ${failed}`);
    console.log(`📊 Total attempts: ${results.length}`);
    
    // Log failed lead details
    if (failed > 0) {
        console.log('\n❌ Failed leads:');
        results.filter(r => r.status === 'rejected').forEach(r => {
            console.log(`   - ${r.leadData.firstName} ${r.leadData.lastName}: ${r.reason instanceof Error ? r.reason.message : r.reason}`);
        });
    }
}

// Run the script
main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
