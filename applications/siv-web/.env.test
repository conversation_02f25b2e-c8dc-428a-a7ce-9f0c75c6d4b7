OAUTH_GOOGLE_CLIENT_ID=402556136231-3mfarbjqo10nm1ios8064vmk8ni73dil.apps.googleusercontent.com
OAUTH_GOOGLE_CLIENT_SECRET=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
SESSION_SECRET=IM5K0B2+N87H52TnqFNOWQ2903RroFZhj61e9TRIFvw=
DATABASE_USER=postgres
DATABASE_PASSWORD=secret-test-postgres-password
DATABASE_HOST=localhost
DATABASE_PORT=5433
DATABASE_NAME=sivtest
LEAD_ASSIGNMENT_INBOUND_WEBHOOK_URL=http://localhost:5173/api/integrations/highlevel/webhook
PINO_LOG_LEVEL=info
#TODO: extract this into .env.local so we can have per-dev environments for tests :)
HIGHLEVEL_TOKEN=pit-4f8d9466-6d71-41fa-a733-a234882236b8
INNGEST_DEV=1
INNGEST_BASE_URL=http://localhost:8289
FORM_EMBED_BASE_URL=http://localhost:5173
FORM_CDN_URL=http://localhost:5173
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_LEAD_FORM_FILE_UPLOAD_BUCKET=test-lead-form-file-uploads
S3_FORCE_PATH_STYLE=true
INTERNAL_API_SECRET=super-secret-internal-api-secret
PACT_BROKER_URL=https://pact-broker.qjxczm22k1618.us-west-2.cs.amazonlightsail.com/
PACT_BROKER_USERNAME=pactbroker
DISABLE_API_DOCUMENTATION=true
SIV_ADMIN_CLERK_PUBLISHABLE_KEY=pk_test_dG9nZXRoZXItaGVuLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
SIV_ADMIN_CLERK_SECRET_KEY=sk_test_FKUSqC2ObecYbRoVave7Iy4mI1Y59FWbdn2uf5ognI
USER_FACING_SIV_CLERK_PUBLISHABLE_KEY=pk_test_dGlnaHQta29kaWFrLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
USER_FACING_SIV_CLERK_SECRET_KEY=sk_test_V8UnXlPOJuGq0gF6w2EylZeyctYUCRZZcOAjlVTlI4
