import type {Plugin, UserConfig, ConfigEnv, PluginOption} from 'vite'
import {Mode, plugin as mdPlugin} from 'vite-plugin-markdown'
// noinspection ES6UnusedImports
import type {UserConfigExport} from 'vitest/config'
/// <reference types="vitest" />
import {defineConfig} from 'vite'
import devServer from '@hono/vite-dev-server'
import build from '@hono/vite-build'
import preserveDirectives from 'rollup-preserve-directives'
import react from '@vitejs/plugin-react'
import path from 'path'
import fs from 'fs'
import {globSync} from 'glob'
import logger from './src/logger'
import tsconfigPaths from 'vite-tsconfig-paths'
import {fileURLToPath} from 'url'

// Plugin to provide virtual modules for asset injection
function assetInjectionPlugin(): Plugin {
    let clientManifest: Record<string, any> = {}

    return {
        name: 'asset-injection',
        enforce: 'post',
        configureServer(server) {
            // In dev mode, return empty virtual modules
            return
        },
        resolveId(id) {
            if (id === 'virtual:vite-dev-server/client-css' || id === 'virtual:vite-dev-server/client-script') {
                return id
            }
        },
        load(id) {
            if (id === 'virtual:vite-dev-server/client-css') {
                if (!import.meta.env.PROD) {
                    return `export default ''`
                }

                // Find all CSS files in the manifest
                const cssFiles = Object.entries(clientManifest)
                    .filter(([_, chunk]) => chunk.fileName?.endsWith('.css'))
                    .map(([_, chunk]) => chunk.fileName)

                const cssLinks = cssFiles
                    .map(fileName => `<link rel="stylesheet" href="/static/${fileName}">`)
                    .join('\n')

                return `export default ${JSON.stringify(cssLinks)}`
            }

            if (id === 'virtual:vite-dev-server/client-script') {
                if (!import.meta.env.PROD) {
                    return `export default ''`
                }

                // Find all entry JS files
                const jsEntries = Object.entries(clientManifest)
                    .filter(([_, chunk]) =>
                        chunk.isEntry && chunk.fileName?.endsWith('.js')
                    )
                    .map(([_, chunk]) => chunk.fileName)

                const jsScripts = jsEntries
                    .map(fileName => `<script type="module" src="/static/${fileName}"></script>`)
                    .join('\n')

                return `export default ${JSON.stringify(jsScripts)}`
            }
        },
        buildStart() {
            // Reset manifest at start of build
            clientManifest = {}
        },
        generateBundle(options, bundle) {
            // Store the bundle for later use
            clientManifest = bundle
        }
    }
}

// Plugin to collect and inject page paths
function pagePathsPlugin(): Plugin {
    let manifest: Record<string, any> = {}

    return {
        name: 'page-paths',
        enforce: 'pre',
        buildEnd() {
            try {
                const manifestPath = path.join(process.cwd(), 'dist', 'client', 'manifest.json')
                if (fs.existsSync(manifestPath)) {
                    manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf-8'))
                    logger.debug({manifest}, 'Loaded manifest')
                }
            } catch (error) {
                logger.warn({error}, 'Could not load manifest')
            }
        },
        generateBundle(options, bundle) {
            const pathMapping: Record<string, any> = {}

            // Find all page components in the manifest
            Object.entries(manifest).forEach(([key, value]) => {
                // Skip non-component entries (like CSS)
                if (!value.file?.endsWith('.js')) return

                // Try to get the component name from the manifest entry
                let componentName = value.name

                // If no name in manifest, try to derive it from the source path
                if (!componentName && value.src) {
                    componentName = value.src.split('/').pop()?.replace('.tsx', '')
                }

                // If still no name, try to derive it from the file path
                if (!componentName) {
                    componentName = value.file.split('/').pop()
                        ?.replace(/\.[^/.]+$/, '') // Remove extension
                        ?.replace(/\.[^/.]+$/, '') // Remove hash
                        ?.replace(/^_/, '')        // Remove leading underscore
                }

                // Only include if it's a Page component
                if (componentName?.endsWith('Page')) {
                    // Get the path without src/ prefix, trying multiple sources
                    let normalizedPath = ''
                    if (value.src) {
                        normalizedPath = value.src.replace(/^src\//, '')
                    } else if (key.startsWith('src/')) {
                        normalizedPath = key.replace(/^src\//, '')
                    } else {
                        normalizedPath = key
                    }

                    pathMapping[componentName] = {
                        name: componentName,
                        paths: [normalizedPath],
                        // Store both the normalized source path and the built file path
                        sourcePath: normalizedPath,
                        builtFile: value.file.replace(/^static\//, '') // Remove static/ prefix from built file
                    }

                    logger.debug({componentName, normalizedPath, file: value.file}, 'Mapped component')
                }
            })

            // Add page paths to the manifest
            this.emitFile({
                type: 'asset',
                fileName: 'page-paths.json',
                source: JSON.stringify(pathMapping, null, 2),
                name: 'page-paths'
            })

            logger.debug({pathMapping}, 'Generated page paths mapping')
        }
    }
}

// Plugin to transform path aliases
function transformAliasPlugin(): Plugin {
    return {
        name: 'transform-alias',
        enforce: 'pre',
        transform(code, id) {
            // Only transform .ts, .tsx, .js, .jsx files
            if (!/\.[tj]sx?$/.test(id)) return null

            // Get the relative path from the current file to the src directory
            const relativePath = path.relative(path.dirname(id), path.resolve(__dirname, 'src'))
            const drizzlePath = path.relative(path.dirname(id), path.resolve(__dirname, 'drizzle'))

            // Replace @/* imports with relative paths
            code = code.replace(
                /from\s+['"]@\/(.*?)['"]/g,
                (match, p1) => `from "${relativePath}/${p1}"`
            ).replace(
                /import\s+['"]@\/(.*?)['"]/g,
                (match, p1) => `import "${relativePath}/${p1}"`
            )

            // Replace #drizzle/* imports with relative paths
            code = code.replace(
                /from\s+['"]#drizzle\/(.*?)['"]/g,
                (match, p1) => `from "${drizzlePath}/${p1}"`
            ).replace(
                /import\s+['"]#drizzle\/(.*?)['"]/g,
                (match, p1) => `import "${drizzlePath}/${p1}"`
            )

            // Replace absolute /modules paths with relative paths
            code = code.replace(
                /from\s+['"]\/(.*?)['"]/g,
                (match, p1) => `from "${relativePath}/${p1}"`
            ).replace(
                /import\s+['"]\/(.*?)['"]/g,
                (match, p1) => `import "${relativePath}/${p1}"`
            )

            return {
                code,
                map: null
            }
        },
        resolveId(source, importer) {
            // Handle absolute paths starting with /
            if (source.startsWith('/')) {
                return path.resolve(__dirname, 'src', source.slice(1))
            }

            // Don't transform node_modules imports
            if (source.startsWith('react/') || source === 'react') {
                return null
            }

            return null
        }
    }
}

// Plugin to copy client manifest to server build
function copyManifestPlugin(): Plugin {
    return {
        name: 'copy-manifest',
        enforce: 'post',
        apply: 'build',
        closeBundle: {
            sequential: true,
            order: 'post',
            handler() {
                // Check multiple possible manifest locations, including .vite directory
                const possiblePaths = [
                    path.join(process.cwd(), 'dist', 'client', '.vite', 'manifest.json'),
                    path.join(process.cwd(), 'dist', 'client', 'manifest.json'),
                    path.join(process.cwd(), 'dist', 'client', 'static', 'manifest.json'),
                ]

                const serverManifestDir = path.join(process.cwd(), 'dist', 'server', 'client')
                let manifestFound = false

                for (const manifestPath of possiblePaths) {
                    if (fs.existsSync(manifestPath)) {
                        try {
                            // Ensure the directory exists
                            fs.mkdirSync(serverManifestDir, {recursive: true})
                            // Copy the manifest
                            fs.copyFileSync(
                                manifestPath,
                                path.join(serverManifestDir, 'manifest.json')
                            )
                            logger.info(`✅ Copied client manifest from ${manifestPath} to server build`)
                            manifestFound = true
                            break
                        } catch (error) {
                            logger.error({error, manifestPath}, 'Failed to copy client manifest')
                            throw error
                        }
                    }
                }

                if (!manifestFound) {
                    logger.warn('Client manifest not found in any of these locations:', possiblePaths)
                }
            }
        }
    }
}

// Plugin to fix relative imports in the server build
function fixRelativeImportsPlugin(): Plugin {
    return {
        name: 'fix-relative-imports',
        enforce: 'post',
        generateBundle(options, bundle) {
            Object.entries(bundle).forEach(([id, chunk]) => {
                if (chunk.type === 'chunk') {
                    // Fix imports to be relative to the current file
                    chunk.code = chunk.code.replace(
                        /from\s+["'].*?\/src\/(.*?)["']/g,
                        (match, importPath) => {
                            const currentDir = path.dirname(id);
                            const targetPath = importPath.replace(/\.tsx?$/, '.js');
                            const relativePath = path.relative(currentDir, targetPath)
                                .replace(/\\/g, '/') // Convert Windows paths to Unix
                                .replace(/^\.\.\/+/, './'); // Convert ../../../something to ./something
                            return `from "${relativePath}"`;
                        }
                    );
                }
            });
        }
    };
}

// Plugin to provide virtual modules for intl-tel-input utils
export function intlTelInputPlugin(): Plugin {
    return {
        name: 'intl-tel-input-utils',
        resolveId(id) {
            if (id === 'intl-tel-input/build/js/utils.js' || id === 'intl-tel-input/utils') {
                // Return a virtual module ID
                return '\0intl-tel-input:utils';
            }
        },
        load(id) {
            if (id === '\0intl-tel-input:utils') {
                // Return the path to the actual utils.js file
                const utilsPath = path.resolve(__dirname, 'node_modules/intl-tel-input/build/js/utils.js');
                // Simply redirect to the actual file
                return `export default ${JSON.stringify(utilsPath)};`;
            }
        }
    };
}

// https://vitejs.dev/config/
export default defineConfig(({mode, command}: ConfigEnv): UserConfig => {
    const isClient = mode === 'client'
    const isProduction = command === 'build'

    const pageEntries = Object.fromEntries(
        globSync('src/**/*Page.tsx', {ignore: ['**/*.test.tsx', '**/*.spec.tsx', '**/*.stories.tsx']})
            .map((file: string) => [
                file.replace(/^src\//, '').replace(/\.tsx$/, ''),
                file
            ])
    )

    const basePlugins: PluginOption[] = [
        react({
            jsxRuntime: 'automatic',
            jsxImportSource: 'react',
            babel: {
                plugins: [
                    ['@babel/plugin-transform-react-display-name']
                ]
            }
        }),
        pagePathsPlugin(),
        assetInjectionPlugin(),
        tsconfigPaths({
            loose: true
        }),
        intlTelInputPlugin(),
        mdPlugin({mode: [Mode.MARKDOWN]})
    ]

    const baseConfig: UserConfig = {
        plugins: basePlugins,
        css: {
            modules: {
                localsConvention: 'camelCaseOnly' as const
            }
        },
        optimizeDeps: {
            include: ['react', 'react-dom', 'intl-tel-input', 'intl-tel-input/react']
        },
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src'),
                '#drizzle': path.resolve(__dirname, './src/drizzle'),
                'intl-tel-input/react': path.resolve(__dirname, 'node_modules/intl-tel-input/react/build/IntlTelInput.js'),
                'intl-tel-input/reactWithUtils': path.resolve(__dirname, 'node_modules/intl-tel-input/react/build/IntlTelInputWithUtils.js')
            }
        },
        // @ts-ignore
        test: {
            workspace: [{
                test: {
                    name: 'node',
                    environment: 'node',
                    include: ['**/*.{spec,test}.ts'],
                    setupFiles: ['src/test/setup.ts']
                },
                resolve: {
                    alias: {
                        '@': path.resolve(__dirname, './src'),
                        '#drizzle': path.resolve(__dirname, './drizzle')
                    }
                }
            }, {
                test: {
                    name: 'components',
                    environment: 'happy-dom',
                    include: ['**/*.{spec,test}.tsx'],
                    setupFiles: ['src/test/setup.ts', 'src/test/ui-component-test-setup.ts'],
                    globals: true,
                },
                resolve: {
                    alias: {
                        '@': path.resolve(__dirname, './src'),
                        '#drizzle': path.resolve(__dirname, './drizzle')
                    }
                }
            }],
            globals: true,
            coverage: {
                provider: 'v8',
                reporter: ['text', 'json', 'html'],
                exclude: [
                    'node_modules/',
                    "src/config",
                    '**/node_modules/',
                    '**/.pnpm/',
                    'deployment/',
                    'src/test/',
                    '**/*.d.ts',
                    '**/*.test.{ts,tsx}',
                    '**/types.ts',
                    'drizzle/',
                    "**/*.js",
                    "**/*.mjs",
                    "*.mjs",
                    ".storybook/",
                    "**/.storybook/",
                    "*.stories.tsx",
                    "**/*.stories.tsx",
                    "**.*.stories.*",
                    "src/components/ui/", //these are from shadcn, no need to test them
                    "drizzle.config.ts",
                    "register.ts",
                    "vite.config.ts",
                    "vitest.workspace.ts",
                    "**/chromatic-test-settings.ts"
                ],
                all: true,
                ignoreEmptyLines: true
            },
            reporters: ['default', ['junit', {
                outputFile: './test-results/junit.xml'
            }], ['html', {
                outputFile: './test-results/html'
            }]],
            alias: {
                '@': path.resolve(__dirname, './src'),
                '#drizzle': path.resolve(__dirname, './drizzle')
            }
        }
    }

    if (command === 'serve') {
        return {
            ...baseConfig,
            ...{
                server: {
                    // Make Vite listen on all network interfaces
                    host: '0.0.0.0',

                    // Configure HMR (Hot Module Replacement) for HTTPS/443 connections
                    hmr: process.env.ENABLE_NGROK_TUNNEL ? {
                        // This tells Vite to use port 443 for client connections
                        clientPort: 443,

                        // This makes HMR work with HTTPS
                        protocol: 'wss',

                        // This allows external connections (from ngrok)
                        host: 'sivdev.ngrok.app',
                        
                        // Disable error overlay
                        overlay: false
                    } : {
                        // Disable error overlay for local development
                        overlay: false
                    }
                }
            },
            plugins: [
                ...basePlugins,
                devServer({
                    entry: 'src/index.tsx',
                    injectClientScript: true
                }),
                build({
                    entry: 'src/index.tsx'
                }),
                preserveDirectives(),
                intlTelInputPlugin()
            ]
        } as UserConfig
    }

    if (isClient) {
        return {
            ...baseConfig,
            build: {
                outDir: 'dist/client',
                manifest: true,
                minify: 'esbuild',
                commonjsOptions: {
                    transformMixedEsModules: true,
                    include: [
                        /node_modules/,
                        /survey-core/,
                        /survey-react-ui/
                    ],
                    // Add these options to better handle UMD modules
                    requireReturnsDefault: 'auto',
                    defaultIsModuleExports: true
                },
                rollupOptions: {
                    input: {
                        'client-entrypoint': 'src/client-entrypoint.tsx',
                        ...pageEntries
                    },
                    output: {
                        format: 'esm',
                        entryFileNames: 'static/[name].[hash].js',
                        chunkFileNames: 'static/[name].[hash].js',
                        assetFileNames: 'static/[name].[hash][extname]',
                        keepNames: true,
                        manifest: "manifest.json",
                        manualChunks: {
                            'survey': ['survey-core', 'survey-react-ui'],
                            'vendor': ['react', 'react-dom']
                        }
                    },
                    preserveEntrySignatures: 'strict',
                    external: [
                        // Exclude server-only dependencies
                        '@hono/node-server',
                        'pg',
                        'dotenv',
                        'dotenv-flow',
                        'dd-trace',
                        // Node built-ins
                        'fs',
                        'path',
                        'crypto',
                        'http',
                        'http2',
                        'stream',
                        'events',
                        'net',
                        'tls',
                        'dns',
                        'os',
                        'util'
                    ]
                },
                writeManifest: true,
                manifestFileName: 'manifest.json',
                terserOptions: {
                    keep_classnames: true,
                    keep_fnames: true,
                    mangle: {
                        keep_classnames: true,
                        keep_fnames: true
                    }
                },
                sourcemap: true
            },
            esbuild: {
                keepNames: true,
                minifyIdentifiers: false,
                tsconfigRaw: {
                    compilerOptions: {
                        "paths": {
                            "@/*": [
                                "./src/*"
                            ]
                        }
                    }
                }
            }
        } as UserConfig
    }

    // Server build configuration
    return {
        ...baseConfig,
        ssr: {
            external: []
        },
        build: {
            sourcemap: true,
            outDir: 'dist/server',
            ssr: true,
            minify: false,
            rollupOptions: {
                input: {
                    'index': 'src/index.tsx',
                    ...Object.fromEntries(
                        globSync('src/**/*.{ts,tsx}', {
                            ignore: [
                                '**/node_modules/**',
                                '**/*.test.{ts,tsx}',
                                '**/*.spec.{ts,tsx}',
                                '**/*.stories.{ts,tsx}',
                                '**/*.d.ts',
                                'src/client-entrypoint.tsx',
                                'src/vite-env.d.ts'
                            ]
                        }).map((file: string) => [
                            file.replace(/^src\//, '').replace(/\.[jt]sx?$/, ''),
                            file
                        ])
                    )
                },
                output: {
                    format: 'esm',
                    inlineSourcemap: true,
                    preserveModules: true,
                    preserveModulesRoot: 'src',
                    entryFileNames: '[name].js',
                    chunkFileNames: '[name].js'
                },
                preserveEntrySignatures: 'strict',
                treeshake: false,
                external: (id) => {
                    // Normalize path to use forward slashes
                    const normalizedId = id.replace(/\\/g, '/');
                    
                    // Don't externalize:
                    // - Relative paths (., ..)
                    // - Absolute paths starting with /
                    // - Paths in src/ or drizzle/ directories
                    // - CSS files
                    // - TypeScript declaration files
                    const projectRoot = path.resolve(__dirname).replace(/\\/g, '/')

                    if (normalizedId.startsWith('.') ||
                        normalizedId.startsWith('/') || 
                        normalizedId.startsWith('src/') ||  normalizedId.startsWith(projectRoot +"/src/") ||
                        normalizedId.startsWith('drizzle/') || normalizedId.startsWith(projectRoot + "/drizzle/") ||
                        normalizedId.endsWith('.css') ||
                        normalizedId.endsWith('.d.ts')) {
                        return false;
                    }
                    
                    // Everything else (node_modules) should be external
                    return true;
                }
            },
            commonjsOptions: {
                include: [
                    /node_modules/
                ],
                transformMixedEsModules: true,
            }
        },
        plugins: [
            ...basePlugins,
            preserveDirectives(),
            copyManifestPlugin(),
            fixRelativeImportsPlugin(),
            intlTelInputPlugin()
        ]
    } as UserConfig
})
