# base node image
FROM node:20-bookworm-slim AS base

# Install specific pnpm version
RUN npm install -g pnpm@9.1.4

# Set pnpm environment
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Set working directory
WORKDIR /app

# Copy workspace files
COPY pnpm-lock.yaml pnpm-workspace.yaml package.json turbo.json ./
COPY applications/ ./applications/
COPY packages/ ./packages/

# Build stage
FROM base AS build

# Install all dependencies with frozen-lockfile
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile

# Build the application and its dependencies using Turborepo
RUN pnpm turbo build --filter=siv-web...

# Create a deployable, non-symlinked node_modules using pnpm deploy
ENV NODE_ENV=production
RUN pnpm --filter siv-web deploy --prod /tmp/siv-web-deploy

# Final stage
FROM node:20-bookworm-slim

# Install specific pnpm version
RUN npm install -g pnpm@9.1.4

# Set pnpm environment
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Set NODE_PATH for module resolution
ENV NODE_PATH="/app/node_modules"

WORKDIR /app

# Copy files from build stages - using the deployed node_modules
COPY --from=build /tmp/siv-web-deploy/node_modules ./node_modules
COPY --from=build /app/applications/siv-web/dist ./dist
COPY --from=build /app/applications/siv-web/package.json ./package.json
#COPY --from=build /app/applications/siv-web/drizzle ./drizzle
COPY --from=build /app/applications/siv-web/loader.js ./loader.js
COPY --from=build /app/applications/siv-web/tracer.js ./tracer.js

#RUN mkdir -p /app/dist/server/drizzle
#COPY --from=build /app/applications/siv-web/drizzle /app/dist/server/drizzle

# Set environment variables
ENV NODE_OPTIONS="--experimental-specifier-resolution=node --enable-source-maps"
ENV NODE_ENV=production

EXPOSE 8080
CMD [ "pnpm", "start" ]

