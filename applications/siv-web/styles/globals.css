:root {
  /* Map our custom variables to shadcn variables */
  --background: 0 0% 96%; /* maps to --ef-background: #f5f5f5 */
  --foreground: 120 100% 50%; /* maps to --ef-text: #00ff00 */

  --primary: 212 67% 59%; /* maps to --ef-primary: #4a90e2 */
  --primary-foreground: 0 0% 100%; /* maps to --ef-button-text: #ffffff */

  --input: 0 0% 82%; /* maps to --ef-border: #d1d1d1 */

  --popover: 0 0% 20%; /* maps to --ef-field-background: #333333 */
  --popover-foreground: 240 100% 50%; /* maps to --ef-input-text: blue */

  --muted: 0 0% 82%; /* maps to --ef-border */
  --muted-foreground: 240 100% 50%; /* maps to --ef-placeholder: #0000ff */

  /* Border radius */
  --radius: 0.25rem; /* maps to --ef-input-radius: 4px */
}

/* You can still keep these as custom properties if needed */
:root {
  --ef-form-radius: 0.5rem;
  --ef-font: "Arial", sans-serif;
}

/* Apply base styles */
body {
  font-family: var(--ef-font);
}

