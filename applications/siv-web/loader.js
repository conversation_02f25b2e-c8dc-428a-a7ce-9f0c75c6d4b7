import "./tracer.js"
import { resolve as resolveTs } from 'ts-node/esm'
import * as tsConfigPaths from 'tsconfig-paths'
import { pathToFileURL } from 'url'
import path from 'path'
import fs from 'fs'

// Determine if we're in production or development
const isProd = process.env.NODE_ENV === 'production';
const serverBasePath = path.resolve('dist/server');
const drizzleProductionPath = path.join(serverBasePath, 'drizzle');
const drizzleFallbackPath = path.resolve('drizzle');

// Check if drizzle directory exists in production location
const hasProdDrizzle = isProd && fs.existsSync(drizzleProductionPath);

const tsConfig = {
    baseUrl: path.resolve('dist/server'),
    paths: {
        '@/*': ['./*'],
        '#drizzle/*': hasProdDrizzle 
            ? ['./drizzle/*'] 
            : ['../drizzle/*', './drizzle/*']
    }
}

console.log(`Loader config: baseUrl=${tsConfig.baseUrl}, drizzlePath=${hasProdDrizzle ? drizzleProductionPath : drizzleFallbackPath}`);

const matchPath = tsConfigPaths.createMatchPath(
    tsConfig.baseUrl,
    tsConfig.paths
)

export function resolve(specifier, ctx, defaultResolve) {
    // Special handling for #drizzle imports
    if (specifier.startsWith('#drizzle/')) {
        const relativePath = specifier.replace('#drizzle/', '');
        
        // Check production path first, then fallback
        const prodPath = path.join(drizzleProductionPath, relativePath);
        const fallbackPath = path.join(drizzleFallbackPath, relativePath);
        
        if (fs.existsSync(`${prodPath}.js`)) {
            return resolveTs(pathToFileURL(`${prodPath}.js`).href, ctx, defaultResolve);
        } else if (fs.existsSync(`${prodPath}.ts`)) {
            return resolveTs(pathToFileURL(`${prodPath}.ts`).href, ctx, defaultResolve);
        } else if (fs.existsSync(fallbackPath)) {
            return resolveTs(pathToFileURL(fallbackPath).href, ctx, defaultResolve);
        }
    }
    
    // Try normal path resolution
    const match = matchPath(specifier)
    if (match) {
        return resolveTs(pathToFileURL(match).href, ctx, defaultResolve)
    }
    return resolveTs(specifier, ctx, defaultResolve)
}

export function load(url, context, defaultLoad) {
    // Handle CSS files
    if (url.endsWith('.css')) {
        return {
            format: 'module',
            shortCircuit: true,
            source: 'export default {}'
        }
    }
    return defaultLoad(url, context, defaultLoad)
}

export { transformSource } from 'ts-node/esm' 
