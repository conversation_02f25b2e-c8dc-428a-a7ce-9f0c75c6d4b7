#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to record a deployment for a participant using the latest available version
 * This fixes the issue where we might not find the specific commit hash in the broker
 */

import { execSync } from 'child_process';
import * as dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
// @ts-ignore
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env.test
dotenv.config({ path: path.resolve(__dirname, './.env.test') });

const recordLatestDeployment = async (participantName: string, environment: string) => {
  // Get broker details from environment variables
  const brokerBaseUrl = process.env.PACT_BROKER_URL;
  const brokerUsername = process.env.PACT_BROKER_USERNAME;
  const brokerPassword = process.env.PACT_BROKER_PASSWORD;

  if (!brokerBaseUrl || !brokerUsername || !brokerPassword) {
    console.error('Error: Pact broker details not found in environment variables!');
    console.error('Make sure PACT_BROKER_URL, PACT_BROKER_USERNAME, and PACT_BROKER_PASSWORD are set.');
    process.exit(1);
  }

  try {
    console.log(`Recording deployment of ${participantName} to ${environment}...`);

    // Get the current git commit - we'll use this if we can't find a version in the broker
    const gitCommit = execSync('git rev-parse HEAD').toString().trim();

    // First check if we need to create a provider version
    // See if there's a matching directory in the pacts structure
    const monoRepoRoot = path.resolve(__dirname, '../../');
    const pactsPath = path.resolve(monoRepoRoot, 'pacts', participantName);

    let version = gitCommit;

    try {
      // Try to get info about this participant from the broker
      const pacticipantOutput = execSync(
        `./node_modules/.bin/pact-broker describe-pacticipant --name=${participantName} --broker-base-url=${brokerBaseUrl} --broker-username=${brokerUsername} --broker-password=${brokerPassword}`
      ).toString();

      // Look for SHA hashes in the output
      const versionMatch = pacticipantOutput.match(/[a-f0-9]{40}/i);

      if (versionMatch && versionMatch[0]) {
        version = versionMatch[0];
        console.log(`Found version in broker: ${version}`);
      } else {
        console.log(`No version found in broker, using current git commit: ${version}`);

        // Create a version in the broker
        console.log(`Creating version for ${participantName} in the broker...`);

        try {
          const createVersionOutput = execSync(
            `./node_modules/.bin/pact-broker create-version-tag --pacticipant=${participantName} --version=${version} --tag=${execSync('git rev-parse --abbrev-ref HEAD').toString().trim()} --broker-base-url=${brokerBaseUrl} --broker-username=${brokerUsername} --broker-password=${brokerPassword}`
          ).toString();

          console.log(`Created version ${version} for ${participantName}`);
        } catch (createVersionError) {
          console.warn(`Warning: Failed to create version for ${participantName}:`, createVersionError);
          // Continue anyway - the version might already exist
        }
      }
    } catch (pacticipantError) {
      console.warn(`Warning: Failed to get information for ${participantName}:`, pacticipantError);
      console.log(`Using current git commit ${version} as version.`);

      // The participant might not exist in the broker yet
      // Try to create it with the current git commit as version
    }

    console.log(`Using version: ${version}`);

    // Record the deployment
    const result = execSync(
      `./node_modules/.bin/pact-broker record-deployment --pacticipant=${participantName} --version=${version} --environment=${environment} --broker-base-url=${brokerBaseUrl} --broker-username=${brokerUsername} --broker-password=${brokerPassword}`
    ).toString();

    console.log(result);
    console.log(`✅ Deployment of ${participantName} to ${environment} recorded successfully!`);
  } catch (error) {
    console.error(`Error recording deployment for ${participantName}:`, error);
    process.exit(1);
  }
};

// Get the participant name and environment from command line arguments
const participantName = process.argv[2];
const environment = process.argv[3] || 'staging';

if (!participantName) {
  console.error('Error: Participant name is required');
  console.error('Usage: record-latest-deployment.ts <participant-name> [environment]');
  process.exit(1);
}

// Execute the main function
recordLatestDeployment(participantName, environment).catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});