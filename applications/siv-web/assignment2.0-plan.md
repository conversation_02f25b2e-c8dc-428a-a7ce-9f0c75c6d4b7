# Lead Assignment 2.0 Implementation Plan

## Context

This document tracks the implementation of the Lead Assignment Configuration 2.0 feature, which evolves the system from a single criterion + exceptions model to a more flexible multiple rules per entity model.

### Problem Being Solved

The current lead assignment system has several limitations:
1. **High Cognitive Load**: The "Exceptions" feature uses negative logic (e.g., "assign all of US *except* California"), making it difficult for users to reason about the final effective rule
2. **Complex Overlap Detection**: The codebase must handle complex intersections of criteria minus exceptions, making maintenance difficult
3. **Inflexible Rule Creation**: Users naturally think in terms of multiple positive responsibilities (e.g., "<PERSON> handles West Coast weddings" AND "<PERSON> handles all Nevada conferences"), but are forced into a single criterion with exceptions
4. **UI Clutter**: The prominent "Add Exception" action adds unnecessary complexity

### Solution

Replace the single-criterion-plus-exceptions model with a multi-rule system where:
- Each individual and team can have multiple rules (a `rules` array)
- Each rule is self-contained with its own criteria
- A lead is assigned if it matches ANY of an entity's rules
- Completely remove the concept of exceptions
- Distinguish between conflicts (inter-entity) and redundancies (intra-entity)

## Implementation Progress

### ✅ Phase 1: Foundational Changes (COMPLETED)

#### Data Model Changes
- **Added**: `AssignmentRule` interface with `id` and `criteria` fields
- **Modified**: `Individual` and `Team` interfaces to include `rules: AssignmentRule[]`
- **Deprecated**: Marked existing `criteria` and `exceptions` fields as `@deprecated`
- **File**: `types/lead-assignment.ts`

#### Core Logic Implementation

1. **Overlap Detection v2** (`overlap-detection-v2.ts`)
   - Simplified `doRulesOverlap()` function without exception logic
   - `findOverlappingAssignments()` that returns:
     - `allOverlaps: OverlapDetail[]` with type "conflict" or "redundancy"
     - `entityIdsWithConflicts: Set<string>`
     - `entityIdsWithRedundanciesOnly: Set<string>`
   - Key insight: Two rules overlap when a single lead could match both rules

2. **Coverage Gap Detection v2** (`coverage-gap-detection-v2.ts`)
   - Simplified to work with rules arrays
   - No exception evaluation needed
   - Generates Cartesian product of all possible value combinations
   - Checks if each combination matches ANY rule

3. **Validation Service v2** (`validation-service-v2.ts`)
   - Updated `ValidationResult` interface:
     ```typescript
     interface ValidationResult {
       isValid: boolean;
       hasConflicts: boolean;
       hasRedundancies: boolean;
       hasCoverageGaps: boolean;
       hasInactiveEntities: boolean;
       allOverlaps: OverlapDetail[];
       entityIdsWithConflicts: Set<string>;
       entityIdsWithRedundanciesOnly: Set<string>;
       coverageGaps: CoverageGap[];
       inactiveEntityIds: string[];
       timestamp: number;
     }
     ```
   - Validation rules:
     - Conflicts = blocking errors
     - Redundancies = non-blocking warnings
     - Coverage gaps = blocking unless acknowledged
     - Inactive entities = non-blocking

#### Testing
- Comprehensive test suites for all v2 implementations
- Tests verify the conflict vs redundancy distinction
- Tests confirm blocking vs non-blocking behavior
- All tests passing ✅

## Remaining Work

### 📋 Phase 2: Migration & Preview (TODO)

1. **Migration Script** (Priority: Medium)
   - Convert existing `criteria` + `exceptions` to `rules` array
   - Handle simple cases automatically
   - Flag complex exceptions for manual review
   - Suggested approach:
     ```typescript
     // Simple case: criteria with no exceptions → single rule
     // Complex case: criteria with exceptions → multiple rules or manual intervention
     ```

2. **Feature Flag Implementation** (Priority: Medium)
   - Add feature flag for gradual rollout
   - Enable read-only preview for existing users
   - Allow opt-in migration flow

### 📋 Phase 3: UI Updates (TODO)

1. **Assignment Table Redesign** (Priority: Medium)
   - Implement collapsible entity rows
   - Show rules as sub-rows under each entity
   - Add "Add Rule" button on each entity row
   - Show "Inactive (No rules defined)" for entities with no rules

2. **Add Rule Functionality** (Priority: Medium)
   - Create modal for adding new rules
   - Allow editing individual rules
   - Implement rule deletion with confirmation

3. **Conflict/Redundancy Visualization** (Priority: Medium)
   - Color coding:
     - Red: Entities with conflicts (highest priority)
     - Yellow: Entities with only redundancies
   - Visual indicators on specific rule sub-rows
   - Overlap details panel

4. **Migration Flow UI** (Priority: Low)
   - Show suggested rule conversion
   - Allow manual adjustments
   - Confirmation before applying migration

### 📋 Future Optimizations (TODO)

1. **Performance Analysis** (Priority: Low)
   - Benchmark O(n²) rule comparison
   - Investigate spatial indexing for geographic data (R-trees)
   - Implement sampling for large rule sets if needed

## Technical Decisions Made

1. **Overlap Definition**: Two rules overlap if a single lead could match both rules (not just if they're identical)

2. **Priority System**: Conflicts take precedence over redundancies in the UI (an entity with both shows as conflicted, not redundant)

3. **Validation Approach**: 
   - Keep validation logic separate from UI
   - Use pure functions for testability
   - Return comprehensive results for flexible UI display

4. **Backwards Compatibility**: Keep deprecated fields to allow gradual migration

## Next Steps

1. Begin Phase 2 with migration script implementation
2. Set up feature flag infrastructure
3. Design UI mockups for the new table structure
4. Plan user communication for the migration

## Success Metrics

- Reduction in support tickets about lead assignment confusion
- Faster configuration time for new users
- Decreased bug rate in lead assignment module
- Positive user feedback on clarity of new system

## Notes

- The PRD specified a phased rollout to minimize risk
- All new code follows existing patterns (neverthrow for errors, domain-driven design)
- Tests are written first or alongside implementation
- The system maintains backward compatibility during migration