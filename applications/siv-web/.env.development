SESSION_SECRET=L0GlxusY8WQ3C/TkJHnvxg3lHtJ+H/sz9YGhnTMFdX0=
OAUTH_GOOGLE_CLIENT_ID=402556136231-3mfarbjqo10nm1ios8064vmk8ni73dil.apps.googleusercontent.com
OAUTH_GOOGLE_CLIENT_SECRET=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
DATABASE_USER=postgres
DATABASE_PASSWORD=secret-postgres-password
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=sivdev
INNGEST_DEV=1
INNGEST_BASE_URL=http://localhost:8288
FORM_EMBED_BASE_URL=http://localhost:5173
FORM_CDN_URL=http://localhost:5173
TEST_AUTH_EMAIL=<EMAIL>
TEST_AUTH_PASSWORD=password123
S3_ENDPOINT=http://localhost:9000
S3_ACCESS_KEY=minioadmin
S3_SECRET_KEY=minioadmin
S3_LEAD_FORM_FILE_UPLOAD_BUCKET=dev-lead-form-file-uploads
S3_FORCE_PATH_STYLE=true
INTERNAL_API_SECRET=siv-web-internal-api-secret-development
SIV_ADMIN_CLERK_SECRET_KEY=sk_test_FKUSqC2ObecYbRoVave7Iy4mI1Y59FWbdn2uf5ognI
SIV_ADMIN_CLERK_PUBLISHABLE_KEY=pk_test_dG9nZXRoZXItaGVuLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
USER_FACING_SIV_CLERK_PUBLISHABLE_KEY=pk_test_dGlnaHQta29kaWFrLTU0LmNsZXJrLmFjY291bnRzLmRldiQ
USER_FACING_SIV_CLERK_SECRET_KEY=sk_test_V8UnXlPOJuGq0gF6w2EylZeyctYUCRZZcOAjlVTlI4
# Enable clearing of conflicting Clerk development cookies (useful for staging with multiple Clerk dev instances on one domain; eg in staging)
# CLEAR_CLERK_DEVELOPMENT_COOKIES_ON_MISMATCH=true
# ADMIN_CLERK_INSTANCE_ID=ins_2y7iApDouuFBiqCjG9KzmSTVGcc
# USER_FACING_CLERK_INSTANCE_ID=ins_2y9yVqC0twDmDnUn49fpCEeQ2V1
