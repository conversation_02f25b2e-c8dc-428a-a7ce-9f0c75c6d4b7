import { Context } from "hono";

// Default pagination values
export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

// Pagination types
export interface PaginationParams {
  page: number;
  pageSize: number;
  offset: number;
}

export interface PaginationMetadata {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

/**
 * Extract pagination parameters from the request context
 * @param c Request context
 * @returns Pagination parameters (page, pageSize, offset)
 */
export function getPaginationParams(c: Context): PaginationParams {
  const page = Math.max(
    parseInt(c.req.query("page") || String(DEFAULT_PAGE), 10),
    1,
  );
  const pageSize = Math.max(
    parseInt(c.req.query("pageSize") || String(DEFAULT_PAGE_SIZE), 10),
    1,
  );
  const offset = (page - 1) * pageSize;
  return { page, pageSize, offset };
}

/**
 * Calculate pagination metadata based on total count and pagination parameters
 * @param totalCount Total number of items
 * @param params Pagination parameters
 * @returns Pagination metadata for UI
 */
export function getPaginationMetadata(
  totalCount: number,
  params: PaginationParams,
): PaginationMetadata {
  const totalPages = Math.max(Math.ceil(totalCount / params.pageSize), 1);
  return {
    currentPage: params.page,
    totalPages,
    totalCount,
    pageSize: params.pageSize,
  };
}
