// Ensure SIV_ADMIN_CLERK_PUBLISHABLE_KEY is set before any imports that might need it
process.env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY =
  process.env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY || "test-clerk-publishable-key";

import { Verifier, VerifierOptions } from "@pact-foundation/pact";
import { db } from "@/db/dbclient";
import { eq, InferInsertModel, sql } from "drizzle-orm";
import {
  integrationLeadSyncLog,
  lead,
  property,
  stsIntegrationSettings,
} from "@/drizzle/schema";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { v4 as uuid } from "uuid";
import { afterAll, beforeAll, describe, it } from "vitest";
import { serve } from "@hono/node-server";
import { randomUUID } from "crypto";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { PARTICIPANT_NAMES } from "@siv/pact-helpers";
import { createApp } from "@/index";

// Use environment variables for Pact broker configuration
const pactBrokerUrl =
  process.env.PACT_BROKER_URL ||
  "https://pact-broker.qjxczm22k1618.us-west-2.cs.amazonlightsail.com/";
// Use commit SHA in CI or default locally
const providerVersion = process.env.SEMAPHORE_GIT_SHA || "1.0.0";

describe("Pact Verification", () => {
  const port = 8989; // Use a different port to avoid conflicts
  const providerBaseUrl = `http://localhost:${port}`;

  let server: any;

  const app = createApp({ disableInternalApiAuth: true });

  withTransactionalTests();

  beforeAll(async () => {
    console.log(`Setting up provider service on port ${port}...`);

    try {
      // Test database connection before starting server
      try {
        await db.execute(sql`SELECT 1`);
        console.log("Database connection test successful");
      } catch (error) {
        console.error("Database connection test failed:", error);
      }

      server = serve({
        fetch: app.fetch,
        port: port,
      });

      console.log(`Provider service starting on port ${port}`);

      const maxAttempts = 30;
      const pollInterval = 200;

      await new Promise<void>((resolve, reject) => {
        let attempts = 0;

        const checkHealth = async () => {
          try {
            console.log(
              `Health check attempt ${attempts + 1}/${maxAttempts}...`,
            );
            const response = await fetch(`http://localhost:${port}/health`);
            console.log(`Health check response status: ${response.status}`);
            console.log(`Health check response headers:`, response.headers);

            if (response.ok) {
              const text = await response.text();
              console.log(
                `Provider service is healthy on port ${port}: ${text}`,
              );
              resolve();
            } else {
              console.warn(`Health check returned status ${response.status}`);
              const body = await response
                .text()
                .catch(() => "Could not read body");
              console.warn(`Health check response body: ${body}`);
              retryOrFail();
            }
          } catch (error) {
            console.warn(
              `Health check failed with error: ${error instanceof Error ? error.message : String(error)}`,
            );
            console.warn(`Full error:`, error);
            retryOrFail();
          }
        };

        const retryOrFail = () => {
          attempts++;
          if (attempts >= maxAttempts) {
            reject(
              new Error(
                `Server failed to become healthy after ${maxAttempts} attempts`,
              ),
            );
          } else {
            setTimeout(checkHealth, pollInterval);
          }
        };

        // Start polling
        checkHealth();
      });
    } catch (error) {
      console.error("Failed to set up provider service:", error);
      throw error;
    }
  });

  afterAll(async () => {
    console.log("Cleaning up provider service...");
    // Close server after tests
    if (server) {
      try {
        await new Promise<void>((resolve) => {
          server.close(() => {
            console.log("Provider service stopped cleanly");
            resolve();
          });
        });
      } catch (error) {
        console.error("Error stopping provider service:", error);
      }
    } else {
      console.log("No server instance to clean up");
    }
  });

  it(
    "validates the expectations of STS Sync Service",
    async () => {
      // Check if we should use local pact files instead of broker
      const useLocalPacts =
        process.env.USE_LOCAL_PACTS === "true" ||
        process.env.USE_PACT_BROKER !== "true";
      // Check if we should disable publishing results (useful for local testing)
      const disablePublish =
        process.env.DISABLE_PUBLISH === "true" || useLocalPacts;

      if (useLocalPacts) {
        console.log("🖥️  Using local pact files for verification (draft mode)");
      } else {
        console.log(
          "🔒 Using pact broker files (stable/committed versions only)",
        );
      }

      if (disablePublish) {
        console.log("📝 Publishing of verification results disabled");
      }

      // Base options for the verifier
      const options: VerifierOptions = {
        provider: PARTICIPANT_NAMES.SIV_WEB,
        providerBaseUrl,

        stateHandlers: {
          // Setup property with STS settings
          "property exists with STS settings": async (parameters: any) => {
            console.log("Setting up property with STS settings", parameters);

            // Use the same fixed ID as in the contract
            const testPropertyId = "123e4567-e89b-12d3-a456-************";
            console.log(
              `Using fixed propertyId from contract: ${testPropertyId}`,
            );

            try {
              const builtProperty = await propertyFactory.build({
                id: testPropertyId,
              });
              await db
                .insert(property)
                .values(builtProperty)
                .onConflictDoUpdate({
                  target: property.id,
                  set: { name: builtProperty.name },
                });
              console.log(`Property upserted with id: ${testPropertyId}`);

              await db
                .delete(stsIntegrationSettings)
                .where(eq(stsIntegrationSettings.propertyId, testPropertyId));

              await db.insert(stsIntegrationSettings).values({
                id: uuid(),
                propertyId: testPropertyId,
                apiKey: "test-api-key",
                stsUsername: "test-username",
                rfpFormId: "test-form-id",
              });
            } catch (error) {
              console.error(
                "Error setting up property with STS settings:",
                error,
              );
              throw error;
            }

            return {
              description: `Property ${testPropertyId} set up with STS settings`,
            };
          },
          // Property exists but has no STS settings
          "property exists but has no STS settings": async (
            parameters: any,
          ) => {
            console.log("Setting up property without STS settings", parameters);

            // Use the same fixed ID as in the contract
            const testPropertyId = "123e4567-e89b-12d3-a456-************";
            console.log(
              `Using fixed propertyId from contract: ${testPropertyId}`,
            );

            const builtProperty = await propertyFactory.build({
              id: testPropertyId,
            });
            await db
              .insert(property)
              .values(builtProperty)
              .onConflictDoUpdate({
                target: property.id,
                set: { name: builtProperty.name },
              });
            console.log(`Property upserted with id: ${testPropertyId}`);

            await db
              .delete(stsIntegrationSettings)
              .where(eq(stsIntegrationSettings.propertyId, testPropertyId));
            console.log(`STS settings removed for property: ${testPropertyId}`);

            return {
              description: `Property ${testPropertyId} has no STS settings`,
            };
          },
          "lead exists with sync logs": async (parameters: any) => {
            console.log("Setting up lead with sync logs", parameters);

            const testLeadId = "123e4567-e89b-12d3-a456-************";
            console.log(`Using fixed leadId from contract: ${testLeadId}`);

            try {
              const property = await propertyFactory.create();
              const testPropertyId = property.id!;

              const leadRec: InferInsertModel<typeof lead> = {
                sourceId: randomUUID(),
                id: testLeadId,
                propertyId: testPropertyId,
                firstName: "Test",
                lastName: "Lead",
                email: "<EMAIL>",
                phone: "1234567890",
                source: "form_submission",
                eventType: "wedding",
              };

              await db.insert(lead).values(leadRec).onConflictDoUpdate({
                target: lead.id,
                set: leadRec,
              });

              await db
                .delete(integrationLeadSyncLog)
                .where(eq(integrationLeadSyncLog.leadId, testLeadId));

              await db.insert(integrationLeadSyncLog).values({
                id: uuid(),
                leadId: testLeadId,
                integrationType: "sts",
                integrationLeadId: "test-integration-lead-id",
                syncedAt: new Date().toISOString(),
              });
            } catch (error) {
              console.error("Error setting up lead with sync logs:", error);
              throw error;
            }

            return { description: `Lead ${testLeadId} set up with sync logs` };
          },
          "lead exists": async (parameters: any) => {
            console.log("Setting up lead without sync logs", parameters);

            const testLeadId = "123e4567-e89b-12d3-a456-************";
            console.log(`Using fixed leadId from contract: ${testLeadId}`);

            const property = await propertyFactory.create();
            const testPropertyId = property.id!;

            const leadRec: InferInsertModel<typeof lead> = {
              sourceId: randomUUID(),
              id: testLeadId,
              propertyId: testPropertyId,
              firstName: "Test",
              lastName: "Lead",
              email: "<EMAIL>",
              phone: "1234567890",
              source: "form_submission",
              eventType: "wedding",
            };

            await db.insert(lead).values(leadRec).onConflictDoUpdate({
              target: lead.id,
              set: leadRec,
            });

            await db
              .delete(integrationLeadSyncLog)
              .where(eq(integrationLeadSyncLog.leadId, testLeadId));

            return {
              description: `Lead ${testLeadId} set up without sync logs`,
            };
          },
          "lead does not exist": async (parameters: any) => {
            console.log("Setting up non-existent lead", parameters);

            const testLeadId = "123e4567-e89b-12d3-a456-************";
            console.log(`Using fixed leadId from contract: ${testLeadId}`);

            await db
              .delete(integrationLeadSyncLog)
              .where(eq(integrationLeadSyncLog.leadId, testLeadId));
            await db.delete(lead).where(eq(lead.id, testLeadId));

            return { description: `Lead ${testLeadId} does not exist` };
          },
          "unauthorized access": async () => {
            console.log("Setting up unauthorized access test");
            return { description: "Unauthorized access test setup" };
          },
        },
      };

      if (useLocalPacts) {
        // Path to the local pact files in the monorepo root
        const monorepoRoot = process.cwd().replace("/applications/siv-web", "");
        const pactFilePath = `${monorepoRoot}/pacts/sts-sync-workflow/sts-sync-workflow-siv-web.json`;

        console.log(`Using local pact file: ${pactFilePath}`);

        options.pactUrls = [pactFilePath];

        options.publishVerificationResult = false;
        options.providerVersion = "local-verification";
      } else {
        options.pactBrokerUrl = pactBrokerUrl;
        options.pactBrokerUsername =
          process.env.PACT_BROKER_USERNAME || "pactbroker";
        options.pactBrokerPassword = process.env.PACT_BROKER_PASSWORD;

        if (disablePublish) {
          options.publishVerificationResult = false;
          options.providerVersion = "local-verification";
        } else {
          options.publishVerificationResult = true;
          options.providerVersion = providerVersion;
        }

        // Configure consumer version selectors for the broker (when not using local files)
        // Only include committed versions (no drafts)
        options.consumerVersionSelectors = [
          {
            deployedOrReleased: true,
          },
        ];
      }

      console.log("Starting Pact verification with timeout of 60 seconds...");
      try {
        // Run the verification
        const result = await new Verifier(options).verifyProvider();
        console.log("Pact verification completed successfully");
        return result;
      } catch (error) {
        console.error("Pact verification failed:", error);
        throw error;
      }
    },
    { timeout: 20000 },
  );
});
