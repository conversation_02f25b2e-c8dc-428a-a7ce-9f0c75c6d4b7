import {
  MessageProviderPact,
  providerWithMetadata,
} from "@pact-foundation/pact";
import { beforeEach, describe, it } from "vitest";
import { InngestEventPublisher } from "@/modules/leads/infrastructure/InngestEventPublisher";
import { createInngestClient } from "@/inngest-client";
import {
  PARTICIPANT_NAMES,
  buildMessageProviderOptions,
} from "@siv/pact-helpers";
import {
  LeadAssignedEvent,
  LeadCreatedEvent,
  LeadSyncedToHighlevelEvent,
} from "@/modules/leads/domain/events";
import { randomUUID } from "crypto";

// PACT setup
const PROVIDER_NAME = PARTICIPANT_NAMES.SIV_WEB_EVENT_PROVIDER;
const CONSUMER_NAME = PARTICIPANT_NAMES.HIGH_LEVEL_LEAD_SYNC_WORKFLOW;

describe("SIV Web Event Publisher - Lead Created Events", () => {
  let messageProvider: MessageProviderPact;

  beforeEach(() => {
    // Define message providers with providerWithMetadata
    const leadCreatedEventName: LeadCreatedEvent["name"] = "lead.created";
    const leadSyncedEventName: LeadSyncedToHighlevelEvent["name"] =
      "lead.synced-to-highlevel";
    const leadAssignedEventName: LeadAssignedEvent["name"] = "lead.assigned";
    const messageProviders = {
      "lead.created": providerWithMetadata(
        () => {
          const leadId = "123e4567-e89b-12d3-a456-************"; // Valid UUID format
          const propertyId = "prop-123e4567-e89b-12d3-a456-************"; // Valid property ID
          const payload: LeadCreatedEvent["data"] = {
            leadId,
            propertyId,
          };
          return payload;
        },
        { name: leadCreatedEventName },
      ),

      "a lead.synced-to-highlevel event": providerWithMetadata(
        () => {
          // Valid UUID format
          const payload: LeadSyncedToHighlevelEvent["data"] = {
            leadId: "123e4567-e89b-12d3-a456-************",
            highLevelContactId: "hl-contact-123",
            highLevelOpportunityId: "hl-opp-123",
            highLevelAuthToken: "auth-token",
            highLevelLocationId: "location-id",
            fileUploads: [
              {
                fileId: "123e4567-e89b-12d3-a456-************",
                s3Key: "submitted/test.pdf",
                keyWithoutStatusPrefix: "test.pdf",
                fileName: "test.pdf",
                contentType: "application/pdf",
              },
            ],
          };

          return payload;
        },
        { name: leadSyncedEventName },
      ),
      "a lead.synced-to-highlevel event with no files": providerWithMetadata(
        () => {
          const payload: LeadSyncedToHighlevelEvent["data"] = {
            leadId: "123e4567-e89b-12d3-a456-************",
            highLevelAuthToken: "highlevel-auth-token-789",
            highLevelContactId: "highlevel-contact-456",
            highLevelLocationId: "highlevel-location-321",
            highLevelOpportunityId: "highlevel-opportunity-123",
            fileUploads: [],
          };

          return payload;
        },
        { name: leadSyncedEventName },
      ),
      "lead.synced-to-highlevel": providerWithMetadata(
        () => {
          const payload: LeadSyncedToHighlevelEvent["data"] = {
            leadId: "123e4567-e89b-12d3-a456-************",
            highLevelAuthToken: "highlevel-auth-token-789",
            highLevelContactId: "highlevel-contact-456",
            highLevelLocationId: "highlevel-location-321",
            highLevelOpportunityId: "highlevel-opportunity-123",
            fileUploads: [
              {
                fileId: "file-123",
                s3Key: "submitted/file1.pdf",
                keyWithoutStatusPrefix: "file1.pdf",
                fileName: "file1.pdf",
                contentType: "application/pdf",
              },
            ],
          };

          return payload;
        },
        { name: leadSyncedEventName },
      ),
      "a lead.assigned event": providerWithMetadata(
        () => {
          const propertyId = "property-456";
          const leadId = "lead-123";

          const payload: LeadAssignedEvent["data"] = {
            leadId,
            leadData: {
              id: leadId,
              firstName: "John",
              lastName: "Doe",
              email: "<EMAIL>",
              phone: "************",
              propertyId,
              eventType: "wedding",
              startDate: null,
              endDate: null,
              assignedSalesRepEmail: null,
              guestCount: null,
              company: null,
              roomCount: null,
              eventNeeds: null,
              eventName: null,
              eventDescription: null,
              flexibleDates: null,
              mealCount: null,
              budget: null,
              createdAt: new Date().toISOString() as unknown as Date,
              updatedAt: null,
              marketingConsent: false,
              assignedAt: null,
              country: null,
              city: null,
              state: null,
              postalCode: null,
              source: "form_submission",
              sourceId: randomUUID(),
              highlevelContactId: null,
              highlevelOpportunityId: null,
              files: [
                {
                  s3Key: "submitted/file1.pdf",
                  fileName: "file1.pdf",
                  contentType: "application/pdf",
                  id: randomUUID(),
                  updatedAt: null,
                  leadId: leadId,
                  s3KeyWithoutStatusPrefix: "file1.pdf",
                  createdAt: new Date().toISOString(),
                },
              ],
            },
          };

          return payload;
        },
        { name: leadAssignedEventName },
      ),
      "a lead.assigned event with populated fields": providerWithMetadata(
        () => {
          const propertyId = "property-456";
          const leadId = "lead-123";

          const payload = {
            leadId,
            leadData: {
              id: leadId,
              firstName: "John",
              lastName: "Doe",
              email: "<EMAIL>",
              phone: "************",
              propertyId,
              eventType: "wedding",
              startDate: "2023-06-15T00:00:00Z",
              endDate: "2023-06-17T00:00:00Z",
              assignedSalesRepEmail: "<EMAIL>",
              guestCount: 150,
              company: "Example Corp",
              roomCount: 75,
              eventNeeds: ["CATERING", "MEETING_SPACE", "GUESTROOMS"],
              eventName: "Smith-Johnson Wedding",
              eventDescription: "Outdoor ceremony with indoor reception",
              flexibleDates: false,
              mealCount: 3,
              budget: 25000,
              createdAt: "2023-04-20T14:30:00Z" as unknown as Date,
              updatedAt: null,
              marketingConsent: false,
              assignedAt: null,
              country: null,
              city: "Austin",
              state: "TX",
              postalCode: "78701",
              source: "form_submission",
              sourceId: randomUUID(),
              highlevelContactId: null,
              highlevelOpportunityId: null,
              files: [
                {
                  s3Key: "submitted/file1.pdf",
                  s3KeyWithoutStatusPrefix: "file1.pdf",
                  fileName: "file1.pdf",
                  contentType: "application/pdf",
                  id: randomUUID(),
                  createdAt: "2023-04-20T14:30:00Z",
                  updatedAt: null,
                  leadId: leadId,
                },
              ],
            },
          };

          return payload;
        },
        { name: leadAssignedEventName },
      ),
    };

    const options = buildMessageProviderOptions({
      provider: PROVIDER_NAME,
      consumer: CONSUMER_NAME,
      messageProviders,
    });

    messageProvider = new MessageProviderPact(options);
  });

  it("verifies the event contracts", async () => {
    await messageProvider.verify();
  });
});
