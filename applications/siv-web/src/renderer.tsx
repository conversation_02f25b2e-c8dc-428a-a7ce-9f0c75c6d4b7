import { reactRenderer } from "@hono/react-renderer";
import type { FC } from "react";
import App from "./view/App";
import "./app.css";
import type { ViewData } from "./global";
import fs from "node:fs";
import path from "node:path";
import logger from "./logger";

// Load manifest in production
let manifest: Record<
  string,
  {
    file: string;
    css?: string[];
    name?: string;
    src?: string;
    isDynamicEntry?: boolean;
    isEntry?: boolean;
    imports?: string[];
    dynamicImports?: string[];
  }
> = {};
if (import.meta.env.PROD) {
  try {
    // Look for manifest in the client directory within server build first
    const manifestPath = path.join(
      process.cwd(),
      "dist",
      "server",
      "client",
      "manifest.json",
    );
    logger.debug({ path: manifestPath }, "Loading manifest from path");
    const manifestContent = fs.readFileSync(manifestPath, "utf-8");
    manifest = JSON.parse(manifestContent);
    logger.debug({ manifest }, "Loaded manifest");
  } catch (error) {
    logger.error({ error }, "Failed to load manifest");
    throw error; // Fail fast in production
  }
}

// Define view type for internal use
export type View = {
  name: FC;
  props: Record<string, unknown>;
  meta: {
    title: string;
    description: string;
  };
};

// Get all available component paths
const componentPaths = new Map<string, string>();
const componentsByFullPath = new Map<string, string>();

// Scan for page components
const scanComponents = async () => {
  try {
    if (import.meta.env.PROD) {
      // In production, use the manifest to find components
      const manifestComponents = Object.entries(manifest)
        .filter(([key]) => {
          // Match both direct Page.tsx files and files in view/pages directories
          return (
            (key.endsWith("Page.tsx") ||
              key.includes("/view/") ||
              key.includes("/pages/")) &&
            !key.includes("node_modules") &&
            !key.includes(".test.") &&
            !key.includes(".spec.") &&
            !key.includes(".stories.")
          );
        })
        .map(([key, value]) => {
          // Extract component name from the path
          const name = key.split("/").pop()?.replace(".tsx", "") || "";
          // Always store without src/ prefix
          const normalizedPath = key.replace(/^src\//, "");
          return { name, path: normalizedPath, normalizedPath };
        });

      // Clear existing paths
      componentPaths.clear();
      componentsByFullPath.clear();

      // Add components from manifest with normalized paths
      manifestComponents.forEach(({ name, path }) => {
        componentPaths.set(name, path);
        // Store only the normalized path without src/
        componentsByFullPath.set(path, name);
      });

      logger.debug(
        {
          components: manifestComponents,
          byName: Object.fromEntries(componentPaths),
          byPath: Object.fromEntries(componentsByFullPath),
        },
        "📦 Production component paths",
      );
    } else {
      // Development mode - use glob pattern
      const modules = import.meta.glob<{ default: FC }>([
        "./view/**/*Page.tsx",
        "./pages/**/*Page.tsx",
        "./**/*Page.tsx",
        "!**/node_modules/**",
        "!**/*.test.tsx",
        "!**/*.spec.tsx",
        "!**/*.stories.tsx",
      ]);

      // Clear existing paths
      componentPaths.clear();
      componentsByFullPath.clear();

      for (const [path, _] of Object.entries(modules)) {
        const componentName = path.split("/").pop()?.replace(".tsx", "");
        if (componentName && componentName.endsWith("Page")) {
          // Store both by name and full path to handle duplicates
          componentPaths.set(componentName, path);
          componentsByFullPath.set(path.replace(/^\.\//, ""), componentName);
        }
      }

      logger.debug(
        {
          byName: Object.fromEntries(componentPaths),
          byPath: Object.fromEntries(componentsByFullPath),
        },
        "📦 Development component paths",
      );
    }
  } catch (error) {
    logger.error({ error }, "Failed to scan for page components");
    throw error; // Fail fast in both dev and prod
  }
};

// Initial scan
scanComponents().catch((error) =>
  logger.error({ error }, "Failed to scan components"),
);

// Set up HMR for page components
if (import.meta.hot) {
  // Watch for any tsx file changes
  import.meta.hot.on("vite:beforeUpdate", ({ updates }) => {
    const hasPageChanges = updates.some((update) =>
      update.path.endsWith("Page.tsx"),
    );

    if (hasPageChanges) {
      logger.debug("🔄 Page components changed, rescanning...");
      scanComponents().catch(console.error);
    }
  });
}

export const renderer = reactRenderer(
  ({ children }) => {
    const view: View = {
      name: children.type as FC,
      props: children.props as Record<string, unknown>,
      meta: {
        title: "Siv",
        description: "Siv Converts",
      },
    };

    // Get the component name and ensure Page suffix
    const rawComponentName = ((view.name as any).displayName ||
      (view.name as any).name ||
      "Unknown") as string;

    // In production, try to resolve minified names back to their original names
    let resolvedComponentName = rawComponentName;
    if (import.meta.env.PROD) {
      logger.debug("manifest====>>", manifest);
      // First try to match against manifest entries
      logger.debug(
        `searching for entry with name === ${rawComponentName} OR extensionless`,
      );
      const manifestEntry = Object.entries(manifest).find(([_, value]) => {
        // Check if the minified name matches the component's name property
        return (
          value.name === rawComponentName ||
          // Also check against the file name without extension
          value.file?.split("/").pop()?.split(".")[0] === rawComponentName
        );
      });

      if (manifestEntry) {
        logger.debug("manifestEntry====>>", manifestEntry);
        // Use the src path if available, otherwise use the manifest key
        const [key, value] = manifestEntry;
        if (value.src) {
          // Extract component name from the source path
          const srcName = value.src.split("/").pop()?.replace(".tsx", "");
          if (srcName) {
            resolvedComponentName = srcName;
            logger.debug("📦 Resolved minified component name from src:", {
              from: rawComponentName,
              to: resolvedComponentName,
              src: value.src,
            });
          }
        } else if (key.startsWith("src/")) {
          // Extract from the manifest key if it's a source path
          const keyName = key.split("/").pop()?.replace(".tsx", "");
          if (keyName) {
            resolvedComponentName = keyName;
            logger.debug("📦 Resolved minified component name from key:", {
              from: rawComponentName,
              to: resolvedComponentName,
              key,
            });
          }
        }
      }
    }

    // Ensure the component name ends with Page but don't append it to minified names
    const componentName = resolvedComponentName.endsWith("Page")
      ? resolvedComponentName
      : resolvedComponentName.includes(".") // If it's a minified name (contains .)
        ? resolvedComponentName // Don't modify it
        : `${resolvedComponentName}Page`; // Add Page suffix to regular names

    // Log component info in development
    logger.debug("🔍 Component debug:", {
      type: view.name,
      displayName: (view.name as any).displayName,
      name: (view.name as any).name,
      rawComponentName,
      resolvedComponentName,
      componentName,
      props: view.props,
      children: children,
      childrenType: children.type,
      childrenName: (children.type as any)?.name,
      childrenDisplayName: (children.type as any)?.displayName,
      manifestComponents: import.meta.env.PROD
        ? Object.keys(manifest)
        : undefined,
    });

    // Try to find the component path - first by exact name, then by normalized name
    let componentPath = componentPaths.get(componentName);

    // If not found, try looking up by the raw name (without Page suffix)
    if (!componentPath) {
      const entries = Array.from(componentPaths.entries());
      const matchingEntry = entries.find(
        ([key]) =>
          key.toLowerCase() === componentName.toLowerCase() ||
          key.toLowerCase().replace("page", "") ===
            rawComponentName.toLowerCase(),
      );
      if (matchingEntry) {
        componentPath = matchingEntry[1];
      }
    }

    // If still not found and it's Unknown, use fallback
    if (
      !componentPath &&
      (rawComponentName === "Unknown" || !rawComponentName)
    ) {
      logger.warn(
        "⚠️ Using fallback page for unknown component:",
        {
          originalComponent: view.name,
          rawComponentName,
          componentName,
          children: children,
          childrenType: children.type,
          childrenName: (children.type as any)?.name,
          childrenDisplayName: (children.type as any)?.displayName,
        },
        "Using fallback page for unknown component",
      );

      // If we can't determine the component name, use HelloPage as a fallback
      const fallbackName = "HelloPage";
      componentPath = componentPaths.get(fallbackName);
      if (!componentPath) {
        logger.error(
          { availableComponents: Object.fromEntries(componentPaths) },
          "HelloPage not found",
        );
        throw new Error("Critical error: HelloPage not found");
      }

      // Create a proper error view
      const errorView: ViewData = {
        name: fallbackName,
        componentPath: fallbackName,
        props: {
          message: "An error occurred",
          tp: "error",
          children: "An error occurred loading the page",
        },
        meta: {
          title: "Error",
          description: "An error occurred",
        },
      };

      // Return a minimal error page
      return (
        <html>
          <head>
            <meta charSet="utf-8" />
            <title>Error</title>
            {/* Include production assets */}
            {import.meta.env.PROD && (
              <>
                <link
                  rel="stylesheet"
                  href={`/${manifest["src/client-entrypoint.tsx"]?.css?.[0]}`}
                />
                <script
                  type="module"
                  src={`/${manifest["src/client-entrypoint.tsx"]?.file}`}
                />
              </>
            )}
            <script
              dangerouslySetInnerHTML={{
                __html: `window._hono_view = ${JSON.stringify(errorView)};`,
              }}
            />
          </head>
          <body
            className="bg-transparent"
            style={{ background: "transparent" }}
          >
            <div id="app-root">
              <App view={errorView}>
                <div>An error occurred loading the page</div>
              </App>
            </div>
          </body>
        </html>
      );
    }

    // Development warning for component name mismatches
    if (import.meta.env.DEV) {
      const actualFileName = Array.from(componentPaths.entries()).find(
        ([_, path]) => path.includes(`/${componentName}.tsx`),
      )?.[0];

      if (actualFileName && actualFileName !== componentName) {
        logger.warn(
          {
            component: componentName,
            fileName: actualFileName,
          },
          "Component name mismatch",
        );
      }
    }

    // Create a serializable version of the view that matches ViewData
    const serializedView: ViewData = {
      name: componentName,
      // In development, use the relative path without the leading ./
      componentPath: componentPath
        ? import.meta.env.DEV
          ? componentPath.replace(/^\.\//, "")
          : componentPath
        : (() => {
            const error = `Component "${componentName}" not found. Available components:\n${Array.from(
              componentPaths.entries(),
            )
              .map(([name, path]) => `  ${name} -> ${path}`)
              .join("\n")}`;
            console.error(error);
            throw new Error(error);
          })(),
      props: view.props,
      meta: view.meta,
    };

    // Development error handling with improved messaging
    const devErrorOverlay =
      !componentPath && import.meta.env.DEV
        ? `
    <div id="page-not-found-overlay" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.85);
      color: #FFF;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      z-index: 99999;
      overflow-y: auto;
      padding: 2rem;
    ">
      <div style="
        max-width: 800px;
        margin: 0 auto;
        background: #1a1a1a;
        border-radius: 6px;
        padding: 2rem;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      ">
        <h2 style="
          color: #e06c75;
          margin: 0 0 1rem 0;
          font-size: 1.5rem;
          font-weight: 600;
        ">Page Component Not Found</h2>
        
        <div style="
          background: #2a2a2a;
          padding: 1rem;
          border-radius: 4px;
          margin-bottom: 1rem;
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
        ">
          <code style="color: #e06c75">${componentName}</code>
        </div>

        <p style="
          color: #abb2bf;
          line-height: 1.5;
          margin-bottom: 1rem;
        ">
          The page component <strong>${componentName}</strong> could not be found.
          ${
            // Show different messages based on whether similar components exist
            Array.from(componentPaths.keys()).filter((key) =>
              key
                .replace("Page", "")
                .includes(componentName.replace("Page", "")),
            ).length > 0
              ? `Did you rename it? Here are components with similar names:`
              : `Make sure the component exists and has the correct Page suffix.`
          }
        </p>

        ${
          // Only show similar components section if there are any
          Array.from(componentPaths.keys()).filter((key) =>
            key.replace("Page", "").includes(componentName.replace("Page", "")),
          ).length > 0
            ? `
              <div style="
                background: #2a2a2a;
                padding: 1rem;
                border-radius: 4px;
                margin-bottom: 1rem;
              ">
                <p style="color: #98c379; margin: 0 0 0.5rem 0;">Similar components found:</p>
                ${Array.from(componentPaths.keys())
                  .filter((key) =>
                    key
                      .replace("Page", "")
                      .includes(componentName.replace("Page", "")),
                  )
                  .map(
                    (key) => `
                    <div style="margin: 0.5rem 0;">
                      <code style="color: #61afef">${key}</code>
                      <span style="color: #5c6370"> → Update your route to use this component</span>
                    </div>
                  `,
                  )
                  .join("")}
              </div>
            `
            : ""
        }

        <p style="color: #abb2bf; margin-bottom: 1rem;">
          Available page components:
        </p>
        <ul style="
          list-style: none;
          padding: 0;
          margin: 0;
          background: #2a2a2a;
          border-radius: 4px;
          overflow: hidden;
        ">
          ${Object.entries(Object.fromEntries(componentPaths))
            .map(
              ([key, value]) => `
              <li style="
                padding: 0.5rem 1rem;
                border-bottom: 1px solid #333;
                font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
              ">
                <span style="color: #61afef">${key}</span>
                <span style="color: #5c6370"> → </span>
                <span style="color: #98c379">${value}</span>
              </li>
            `,
            )
            .join("")}
        </ul>

        <button onclick="document.getElementById('page-not-found-overlay').remove()" style="
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: none;
          border: none;
          color: #abb2bf;
          cursor: pointer;
          font-size: 1.5rem;
          padding: 0.5rem;
        ">×</button>
      </div>
    </div>
  `
        : "";

    // Enhanced console warning in development
    if (!componentPath && import.meta.env.DEV) {
      logger.error({ componentName }, "Page Component Not Found");
      logger.warn(
        `Component "${componentName}" could not be found. Did you forget to add the "Page" suffix?`,
      );
      logger.info("\nTo fix this, rename your component file to:");
      logger.info(componentName + ".tsx");
      logger.info("\nAvailable page components:");
      Object.entries(Object.fromEntries(componentPaths)).forEach(
        ([key, value]) => {
          logger.info(`${key} → ${value}`);
        },
      );
      console.groupEnd();
    }

    // Log component resolution in development
    if (import.meta.env.DEV) {
      logger.debug("🔍 Resolving component:", {
        name: componentName,
        path: componentPath,
        normalizedPath: serializedView.componentPath,
        availableComponents: Object.fromEntries(componentPaths),
        availableByPath: Object.fromEntries(componentsByFullPath),
      });
    }

    // Development scripts need to be before the client entrypoint
    const developmentScripts = !import.meta.env.PROD ? (
      <>
        <script
          type="module"
          dangerouslySetInnerHTML={{
            __html: `
          import RefreshRuntime from '/@react-refresh'
          RefreshRuntime.injectIntoGlobalHook(window)
          window.$RefreshReg$ = () => {}
          window.$RefreshSig$ = () => (type) => type
          window.__vite_plugin_react_preamble_installed__ = true
        `,
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window._hono_view = ${JSON.stringify(serializedView)};
              window.SIV_ADMIN_CLERK_PUBLISHABLE_KEY = ${JSON.stringify(process.env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY)};
              window.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY = ${JSON.stringify(process.env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY)};
            `,
          }}
        />
        <link rel="stylesheet" href="/src/app.css" type="text/css" />
        <script type="module" src="/src/client-entrypoint.tsx" />
      </>
    ) : null;

    // Production assets with explicit order
    const productionAssets = import.meta.env.PROD ? (
      <>
        {manifest["src/client-entrypoint.tsx"]?.css?.[0] && (
          <link
            rel="stylesheet"
            href={`/${manifest["src/client-entrypoint.tsx"].css[0]}`}
          />
        )}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window._hono_view = ${JSON.stringify(serializedView)};
              window.SIV_ADMIN_CLERK_PUBLISHABLE_KEY = ${JSON.stringify(process.env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY)};
              window.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY = ${JSON.stringify(process.env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY)};
            `,
          }}
        />
        <script
          type="module"
          src={`/${manifest["src/client-entrypoint.tsx"].file}`}
        />
        {serializedView.componentPath &&
          manifest[serializedView.componentPath]?.file && (
            <script
              type="module"
              src={`/${manifest[serializedView.componentPath].file}`}
            />
          )}
      </>
    ) : null;

    return (
      <html>
        <head>
          <meta charSet="utf-8" />
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
          />
          <title>{view.meta.title}</title>
          <meta name="description" content={view.meta.description} />

          {/* Development scripts must come first */}
          {developmentScripts}

          {/* Production assets */}
          {productionAssets}
        </head>
        <body className="bg-transparent">
          <div id="app-root">
            <App view={serializedView}>{children}</App>
          </div>
          {/* Insert the error overlay in development */}
          <div dangerouslySetInnerHTML={{ __html: devErrorOverlay }} />
        </body>
      </html>
    );
  },
  {
    docType: true,
  },
);
