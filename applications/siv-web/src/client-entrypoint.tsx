import "./app.css";
import React from "react";
import { hydrateRoot } from "react-dom/client";
import App from "./view/App";
import { ViewData } from "@/global";
import logger from "./logger";

// Use Vite's glob import to get all page components
const modules = import.meta.glob<{ default: React.ComponentType<any> }>(
  [
    "./**/*Page.tsx",
    "!**/node_modules/**",
    "!**/*.test.tsx",
    "!**/*.spec.tsx",
    "!**/*.stories.tsx",
  ],
  {
    eager: false, // Ensure consistent lazy loading for all components
  },
);

logger.debug({ modules }, "Available modules");

// Function to build component map
const buildComponentMap = () => {
  const byName = {} as Record<string, string[]>;
  const byPath = {} as Record<string, string>;

  Object.keys(modules).forEach((path) => {
    const name = path.split("/").pop()?.replace(".tsx", "") || "";
    if (name.endsWith("Page")) {
      // Store by name (allowing multiple paths)
      if (!byName[name]) byName[name] = [];
      byName[name].push(path);

      // Store by normalized path
      byPath[path.replace(/^\.\//, "")] = name;
    }
  });

  return { byName, byPath };
};

// Create maps of component names to their paths for debugging
let componentMaps = buildComponentMap();

// Log available modules in development with better formatting
const logAvailablePages = () => {
  if (import.meta.env.DEV) {
    const moduleInfo = Object.entries(componentMaps.byName).map(
      ([name, paths]) => ({
        name,
        paths: paths.map((p) => p.replace(/^\.\//, "")),
        fullPaths: paths,
      }),
    );
    logger.debug({ moduleInfo }, "Available pages");
  }
};

// Initial logging
logAvailablePages();

// Set up HMR for page components
if (import.meta.env.DEV && import.meta.hot) {
  import.meta.hot.on("vite:beforeUpdate", ({ updates }) => {
    const hasPageChanges = updates.some(
      (update) =>
        update.path.endsWith("Page.tsx") ||
        update.path.includes("pages/") ||
        update.path.includes("view/"),
    );

    if (hasPageChanges) {
      logger.info("🔄 Page components changed, updating component map...");
      componentMaps = buildComponentMap();
      logAvailablePages();
    }
  });
}

declare global {
  interface Window {
    _hono_view: ViewData;
  }
}

// Client-side hydration
if (typeof window !== "undefined") {
  const view = window._hono_view;
  const rootElement = document.getElementById("app-root");

  if (rootElement) {
    // Dynamically import the component based on the view name
    const importComponent = async () => {
      try {
        if (import.meta.env.DEV) {
          logger.debug(
            {
              name: view.name,
              componentPath: view.componentPath,
              props: view.props,
            },
            "View data",
          );
        }

        // Try to find the component by exact path first
        const exactPath = `./${view.componentPath}`;

        if (import.meta.env.DEV) {
          logger.debug(
            {
              name: view.name,
              path: view.componentPath,
              exactPath,
              availableModules: Object.keys(modules),
              componentMaps,
            },
            "Looking for component",
          );
        }

        // Get the module loader
        const moduleLoader = modules[exactPath];

        if (!moduleLoader) {
          // Get a list of available pages for better error messages
          const availablePages = Object.entries(componentMaps.byName)
            .map(([name, paths]) => ({
              name,
              paths: paths.map((p) => p.replace(/^\.\//, "")),
            }))
            .sort((a, b) => a.name.localeCompare(b.name));

          logger.error(
            {
              triedPaths: [
                exactPath,
                view.componentPath,
                `./${view.componentPath}`,
              ],
              availablePages,
            },
            "❌ Page component not found",
          );

          throw new Error(
            `Could not find component "${view.name}" at path "${view.componentPath}".\n\n` +
              "Available pages:\n" +
              availablePages
                .map(
                  (p) =>
                    `- ${p.name} at:\n  ${p.paths.map((path) => `  ${path}`).join("\n  ")}`,
                )
                .join("\n"),
          );
        }

        logger.info("✅ Found component", { path: exactPath });

        const module = await moduleLoader();
        const Component = module.default;

        if (!Component) {
          throw new Error(
            `Component exists but has no default export at ${exactPath}`,
          );
        }

        const app = (
          <App view={view}>
            <Component {...view?.props} />
          </App>
        );

        logger.debug("🔄 Hydrating component...");
        hydrateRoot(rootElement, app);
        logger.info("✅ Hydration complete!");
      } catch (error) {
        logger.error({ error }, "Failed to load component");
      }
    };

    importComponent();
  }
}
