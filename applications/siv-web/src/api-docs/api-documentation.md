## Overview

This documentation describes the Siv API which allows partners to programmatically submit leads to
Siv. This API enables direct integration, streamlining the process of getting potential customer information into our
platform for your properties.

## Authentication

The API uses API key authentication. Include your unique API key in the `X-API-Key` header with every request.

Your point of contact at Siv can provide you with a production API key and an API key for sandbox testing.

**Important**: Keep your API key secure and confidential. If it is ever compromised, contact Siv to rotate the key.

## API Versioning

The Siv API uses date-based versioning via the `X-API-Version` header (e.g., `2025-04-14`). Include the desired version in the `X-API-Version` header.

### Breaking Changes

Any breaking changes will be released in a new API version. Breaking changes are changes that can potentially break an integration. Breaking changes include:

- Removing an entire operation
- Removing or renaming a parameter
- Removing or renaming a response field
- Adding a new required parameter
- Making a previously optional parameter required
- Changing the type of a parameter or response field
- Removing enum values
- Adding a new validation rule to an existing parameter
- Changing authentication or authorization requirements

### Non-breaking Changes

#### Additive Changes
Any additive (non-breaking) changes will be available in all supported API versions. Additive changes are changes that should not break an integration. Additive changes include:

- Adding an operation
- Adding an optional parameter
- Adding an optional request header
- Adding a response field
- Adding a response header
- Adding enum values


#### Additional changes we consider non-breaking:
- Changing error messages
- Changing warning messages
  From time to time we may change our error or warning messages to be more robust. Do not rely on error or warning messages for your application logic. Instead, rely on error codes to make any decisions.

**Note:** Ensure that your API client ignores unrecognized fields, headers, enum values, etc. to avoid breaking your integration due to additive changes. Depending on your programming language and json parsing library, you may need to adjust some settings. For example, in Java when using Jackson, set `FAIL_ON_UNKNOWN_PROPERTIES` to `false` in your `ObjectMapper`.

## Rate Limiting

Some API endpoints are rate limited. Check specific endpoints' documentation for details.

## Error Handling

### Common HTTP Status Codes

While specific endpoints define their exact responses, here are common status codes you might encounter:

*   **201 Created:** Lead successfully submitted.
*   **400 Bad Request:** Invalid input data (e.g., validation errors). Check the `errors` field in the response body.
*   **401 Unauthorized:** Authentication failed (invalid or missing API key).
*   **403 Forbidden:** API key is valid, but not authorized for the requested property.
*   **404 Not Found:** The specified property ID does not exist.
*   **429 Too Many Requests:** You have exceeded the rate limit.
*   **500 Internal Server Error:** An unexpected error occurred on our end.

See the Error Response model section below for more details on error responses.
