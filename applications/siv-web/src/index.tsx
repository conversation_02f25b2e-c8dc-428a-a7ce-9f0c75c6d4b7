import "../tracer.js"; // must come before importing any instrumented module
import { env } from "./config/env"; //we need this import to load and validate the environment vars
import { serve } from "@hono/node-server";
import { serve as serveInngest } from "inngest/hono";
import { serveStatic } from "@hono/node-server/serve-static";
import { renderer } from "./renderer";
import { createAuthMiddleware, Variables } from "./auth";
import { db } from "./db/dbclient";
import React from "react";
import { createAdminRoutes } from "@/modules/admin/admin-routes";
import { createIntegrationRoutes } from "@/modules/integrations/integration-routes";
import { ModuleBaseUrlFactory } from "@/modules/route-helpers";
import logger from "./logger";
import { methodOverride } from "hono/method-override";
import { sql } from "drizzle-orm";
import { datadogMiddleware } from "@/datadog-tracing-hono-lib/dd-trace-hono-middleware";
import { createPublicFormRoutes } from "@/modules/forms/submission/public-form-routes";
import { FormSubmissionApplicationService } from "@/modules/forms/submission/form-submission-application-service";
import { FormRepo } from "@/modules/forms/FormRepo";
// import { HighLevelClient } from "@siv/highlevel-client";
// Mock HighLevelClient for testing
class HighLevelClient {
  constructor(config: any) {}
  async getUser() { return { id: "mock", name: "Mock User" }; }
}
import { createInngestClient } from "@/inngest-client";
import { InngestEventPublisher } from "@/modules/leads/infrastructure/InngestEventPublisher";
import {
  LeadAssignedEvent,
  LeadCreatedEvent,
} from "@/modules/leads/domain/events";
import { createHighLevelLeadSyncWorkflow } from "@/modules/integrations/highlevel/high-level-lead-sync-workflow";
import { HighlevelSyncApplicationService } from "@/modules/integrations/highlevel/HighlevelSyncApplicationService";
import createDatadogInngestFunctionFailedReporter from "@/modules/infrastructure/inngest/inngest-datadog-failed-event-reporter";
import * as process from "node:process";
import { s3Client } from "@/s3Client";
import { Scalar } from "@scalar/hono-api-reference";
import { OpenAPIHono } from "@hono/zod-openapi";
import {
  createPublicLeadSubmissionRoutes,
  LATEST_API_VERSION,
} from "@/modules/leads/public-lead-submission-api.routes";
import { createInternalApiRoutes } from "@/modules/internal-api/internal-routes";
import { createSalesDeploymentOnboardingRoutes } from "@/modules/deployment-rules-onboarding/onboarding-routes";
import LoginPage from "@/modules/auth/LoginPage";
import UserFacingLoginPage from "@/modules/auth/UserFacingLoginPage";
import PlaceholderPage from "@/modules/PlaceholderPage";
import AppHomePage from "@/modules/app/AppHomePage";
import {
  userFacingClerkMiddleware,
  createUserFacingAuthMiddleware,
} from "@/user-facing-auth";
import { jwtDecode } from "jwt-decode";
import { getCookie } from "hono/cookie";
import { Context, MiddlewareHandler } from "hono";

/**
 * Options for creating the app
 */
export interface CreateAppOptions {
  disableInternalApiAuth?: boolean;
  apiDocumentation?: string; // Add this option to allow pre-loading documentation
}

/**
 * Extracts the Clerk instance ID from a secret key or publishable key
 * Secret keys have format: sk_test_<base64-encoded-data>
 * Publishable keys have format: pk_test_<instance-name>.<clerk.accounts.dev>$
 * The instance ID is embedded in the JWT's issuer claim
 */
function extractClerkInstanceFromJwt(jwt: string): string | null {
  try {
    const decoded = jwtDecode<{ iss?: string }>(jwt);
    // Extract instance ID from issuer URL like: https://clerk.<instance-id>
    const match = decoded?.iss?.match(/ins_[a-zA-Z0-9]+/);
    return match ? match[0] : null;
  } catch {
    return null;
  }
}

/**
 * Clears the Clerk session cookie by setting it to expire immediately
 */
function clearSessionCookie(c: Context): void {
  c.header(
    "Set-Cookie",
    "__session=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Lax"
  );
}

/**
 * Creates middleware to clear Clerk cookies that don't match the expected instance
 * @param expectedInstanceId - The Clerk instance ID that SHOULD have cookies on these paths
 * @param pathPredicate - Function to determine if the current path should check cookies
 * @param logMessage - Message to log when clearing cookies
 */
function createClerkCookieValidationMiddleware(
  expectedInstanceId: string | null,
  pathPredicate: (path: string) => boolean,
  logMessage: string
): MiddlewareHandler {
  return async (c, next) => {
    if (!expectedInstanceId) {
      // If we couldn't determine the expected instance, skip validation
      return next();
    }

    const sessionCookie = getCookie(c, "__session");
    
    if (sessionCookie) {
      const currentPath = c.req.path;
      
      if (pathPredicate(currentPath)) {
        const actualInstanceId = extractClerkInstanceFromJwt(sessionCookie);
        
        // Clear cookie if it's from a different instance
        if (actualInstanceId && actualInstanceId !== expectedInstanceId) {
          logger.info(
            { 
              path: currentPath, 
              expectedInstance: expectedInstanceId,
              actualInstance: actualInstanceId 
            },
            logMessage
          );
          clearSessionCookie(c);
        }
      }
    }
    
    return next();
  };
}

/**
 * Creates the application with all routes and middleware without starting the server
 * Can be used for both the main application and for testing
 */
export function createApp(options: CreateAppOptions = {}) {
  // Create the server app with proper type
  const app = new OpenAPIHono<{ Variables: Variables }>();
  app.use("*", datadogMiddleware());

  // Initialize API documentation
  const apiDocumentationMarkdown =
    options.apiDocumentation ||
    (env.DISABLE_API_DOCUMENTATION
      ? "API Documentation disabled in test environment"
      : "API Documentation loading. Refresh to see full content.");

  const inngest = createInngestClient();

  // Serve static files from client build directory
  app.use("/static/*", serveStatic({ root: "./dist/client" }));

  app.use(
    "/*",
    serveStatic({
      root: `./dist/html`,
      rewriteRequestPath: (path) =>
        import.meta.env.DEV ? path.replace(/^\/html/, "/") : path,
    }),
  );

  // Add renderer middleware - exclude API routes
  app.use("*", async (c, next) => {
    const path = c.req.path;
    // Skip renderer for API routes and other non-HTML endpoints
    if (
      path.startsWith("/api/") ||
      path.startsWith("/internal/") ||
      path === "/health" ||
      path === "/openapi" ||
      path === "/api-reference"
    ) {
      return next();
    }
    // Apply renderer for other routes
    return renderer(c, next);
  });

  // Add method override middleware for handling PUT/DELETE in forms
  app.use("*", methodOverride({ app }));

  // Clear conflicting Clerk development cookies if enabled
  if (env.CLEAR_CLERK_DEVELOPMENT_COOKIES_ON_MISMATCH) {
    const adminInstanceId = env.ADMIN_CLERK_INSTANCE_ID;
    const userFacingInstanceId = env.USER_FACING_CLERK_INSTANCE_ID;
    
    // Apply validation middleware for admin paths
    if (adminInstanceId) {
      app.use(
        "*",
        createClerkCookieValidationMiddleware(
          adminInstanceId,
          (path) => path.startsWith("/admin") || path.startsWith("/auth/login"),
          "Clearing non-admin Clerk cookie from admin path"
        )
      );
    }
    
    // Note: We'll add the user-facing middleware later after the /app routes are set up
  }

  // Initialize OAuth and get protect middleware
  const protect = createAuthMiddleware(app);

  app.get("/health", async (c) => {
    try {
      await db.execute(sql`Select 1`);
    } catch (e) {
      logger.error(e, "Healthcheck: DB connection failed");
      return c.newResponse("DOWN", 500);
    }

    logger.debug("Healthcheck: DB connection OK");
    return c.newResponse("OK", 200);
  });

  app.get(
    "/api-reference",
    Scalar({
      title: "SIV API Reference",
      sources: [
        {
          url: "/openapi",
        },
      ],
    }),
  );

  // OpenAPI JSON specification endpoint
  app.doc("/openapi", {
    openapi: "3.0.0",
    info: {
      version: LATEST_API_VERSION,
      title: "Siv Lead Submission API",
      description: apiDocumentationMarkdown,
      contact: {
        name: "Siv API Support",
        url: "https://sivconverts.com/support",
        email: "<EMAIL>",
      },
      license: {
        name: "Proprietary",
        url: "https://sivconverts.com/terms",
      },
    },
    tags: [
      {
        name: "Lead API",
        description: "Endpoints for managing leads",
      },
    ],
  });

  const leadCreatedEventPublisher = new InngestEventPublisher<LeadCreatedEvent>(
    inngest,
  );

  app.route(
    "/",
    createPublicLeadSubmissionRoutes({ leadCreatedEventPublisher }),
  );

  app.get("/auth/login", (c) => {
    // const redirectTo = c.req.query("redirect_to") || "/";
    return c.render(<LoginPage />);
  });

  app.get("/app/sign-in", (c) => {
    return c.render(<UserFacingLoginPage />);
  });

  const highLevelClient = new HighLevelClient();
  const adminRoutesBaseUrl = "/admin";
  const publicFormRoutesBaseUrl = "/forms";
  const adminRoutes = createAdminRoutes({
    authMiddleware: protect,
    adminModuleBaseUrlFactory: new ModuleBaseUrlFactory(adminRoutesBaseUrl),
    publicFormRoutesBaseUrlFactory: new ModuleBaseUrlFactory(
      publicFormRoutesBaseUrl,
    ),
    highLevelClient,
  });
  app.route(adminRoutesBaseUrl, adminRoutes);

  // Set up root placeholder page instead of redirect
  app.get("/", (c) => {
    return c.render(<PlaceholderPage />);
  });

  // Initialize Temporal and set up form routes
  const integrationRoutes = createIntegrationRoutes({
    leadAssignedEventPublisher: new InngestEventPublisher<LeadAssignedEvent>(
      inngest,
    ),
  });
  // Mount integration routes - no auth required for webhooks
  app.route("/api/integrations", integrationRoutes);

  // Initialize form routes with the client
  const publicFormRoutes = createPublicFormRoutes({
    formSumbissionApplicationService: new FormSubmissionApplicationService(
      leadCreatedEventPublisher,
      new FormRepo(),
      s3Client,
    ),
  });
  app.route(publicFormRoutesBaseUrl, publicFormRoutes);

  // Mount internal API routes with auth based on options
  app.route(
    "/internal/api",
    createInternalApiRoutes(options.disableInternalApiAuth),
  );

  // Clear conflicting admin Clerk cookies from user-facing app
  if (env.CLEAR_CLERK_DEVELOPMENT_COOKIES_ON_MISMATCH && env.USER_FACING_CLERK_INSTANCE_ID) {
    app.use(
      "/app/*",
      createClerkCookieValidationMiddleware(
        env.USER_FACING_CLERK_INSTANCE_ID,
        () => true, // All /app/* paths should validate
        "Clearing non-user-facing Clerk cookie from app path"
      )
    );
  }

  // Apply user-facing Clerk middleware for /app routes
  if (!env.DISABLE_AUTH_FOR_TESTING) {
    app.use("/app/*", userFacingClerkMiddleware());
  }

  // Create user-facing auth middleware
  const userFacingProtect = createUserFacingAuthMiddleware();

  // Create a sub-app for user-facing routes
  const appRoutes = new OpenAPIHono<{ Variables: Variables }>();

  // Apply the auth middleware to all routes in this sub-app
  appRoutes.use("*", userFacingProtect);

  // Add app home page
  appRoutes.get("/", (c) => {
    return c.render(<AppHomePage />);
  });

  // Mount sales deployment onboarding routes
  appRoutes.route(
    "/sales-deployment-onboarding",
    createSalesDeploymentOnboardingRoutes(),
  );

  // Mount the sub-app under /app
  app.route("/app", appRoutes);

  if (env.REPORT_INNGEST_FUNCTION_FAILURES_TO_DATADOG) {
    createDatadogInngestFunctionFailedReporter(inngest);
  }

  const highlevelSyncService = new HighlevelSyncApplicationService(
    highLevelClient,
  );
  const inngestFunctions = [];
  inngestFunctions.push(
    createHighLevelLeadSyncWorkflow(inngest, highlevelSyncService),
  );

  app.use(
    "/api/inngest",
    serveInngest({ client: inngest, functions: inngestFunctions }),
  );

  return app;
}

// Function to load API documentation (no top-level await)
async function loadApiDocumentation() {
  try {
    if (!env.DISABLE_API_DOCUMENTATION) {
      // @ts-ignore
      const imported = await import("./api-docs/api-documentation.md");
      return imported.markdown;
    }
  } catch (error) {
    logger.error(error, "Failed to load API documentation");
  }
  return "API Documentation disabled in test environment";
}

// Create the app using the factory function (without preloaded docs)
const app = createApp();

// Only start the HTTP server if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  logger.info("Starting servers...");

  // Main public API server
  serve({
    fetch: app.fetch,
    port: env.PORT,
  });
  logger.info(`Server listening on http://localhost:${env.PORT}`);

  // Load API documentation in the background
  loadApiDocumentation().then((markdown) => {
    logger.debug("API documentation loaded successfully");
  });
}

export default app;
