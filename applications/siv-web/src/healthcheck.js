import pino from 'pino';

const logger = pino();

const healthcheck = async () => {
    try {
        const response = await fetch('http://0.0.0.0:8080/health', {
            timeout: 2000,
            headers: {
                'Accept': 'application/json'
            }
        });

        if (response.ok) {
            logger.debug({ statusCode: response.status }, 'Healthcheck passed!');
            process.exit(0);
        } else {
            const text = await response.text();
            logger.error({
                statusCode: response.status,
                response: text
            }, 'Healthcheck failed!');
            process.exit(1);
        }
    } catch (error) {
        logger.error({
            error: {
                message: error.message,
                name: error.name,
                cause: error.cause
            }
        }, 'Healthcheck failed!');
        process.exit(1);
    }
};

// Run the healthcheck
healthcheck();