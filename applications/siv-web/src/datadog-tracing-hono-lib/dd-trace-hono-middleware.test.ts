import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { Hono } from "hono";
import { datadogMiddleware } from "./dd-trace-hono-middleware";
import type { Span, Scope } from "dd-trace";

// Create a lightweight test double that captures the state we care about
class TestSpan {
  name: string;
  resource: string;
  meta: Record<string, string>;
  error: number;
  finished: boolean;

  constructor(name: string) {
    this.name = name;
    this.resource = "";
    this.meta = {};
    this.error = 0;
    this.finished = false;
  }

  setTag(key: string, value: any) {
    if (key === "resource") {
      this.resource = value;
    } else if (key === "error" && value === true) {
      this.error = 1;
    } else {
      this.meta[key] = String(value);
    }
    return this;
  }

  finish() {
    this.finished = true;
  }
}

// Type guard function
function isTestSpan(span: any): span is TestSpan {
  return span instanceof TestSpan;
}

class TestTracer {
  spans: TestSpan[] = [];
  currentSpan: TestSpan | null = null;

  startSpan(name: string): TestSpan {
    const span = new TestSpan(name);
    this.spans.push(span);
    return span;
  }

  scope() {
    const self = this;
    return {
      active: () => self.currentSpan,
      activate: (span: TestSpan, fn: () => Promise<any>) => {
        const previousSpan = self.currentSpan;
        self.currentSpan = span;
        return fn().finally(() => {
          self.currentSpan = previousSpan;
        });
      },
      bind: <T>(fn: T) => fn,
    };
  }
}

// Create a mock module factory
vi.mock("dd-trace", () => {
  return {
    default: {
      tracer: {
        startSpan: vi.fn(),
        scope: vi.fn(),
      },
    },
  };
});

import ddTrace from "dd-trace";
const tracer = ddTrace.tracer;

describe("datadogMiddleware", () => {
  let app: Hono;
  let testTracer: TestTracer;

  beforeEach(() => {
    app = new Hono();
    testTracer = new TestTracer();

    // Set up mock implementations with type assertions
    vi.mocked(tracer.startSpan).mockImplementation(
      (name: string) => testTracer.startSpan(name) as unknown as Span,
    );
    vi.mocked(tracer.scope).mockImplementation(
      () => testTracer.scope() as unknown as Scope,
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("basic functionality", () => {
    it("should create a span for each request", async () => {
      app.use("*", datadogMiddleware());
      app.get("/", (c) => c.text("ok"));

      const res = await app.request("/");
      expect(res.status).toBe(200);

      expect(testTracer.spans.length).toBe(1);
      const span = testTracer.spans[0];
      expect(span.name).toBe("web.request");
      expect(span.resource).toBe("GET /");
      expect(span.meta["http.method"]).toBe("GET");
      expect(span.meta["http.url"]).toContain("/");
      expect(span.finished).toBe(true);
    });

    it("should set route patterns correctly", async () => {
      app.use("*", datadogMiddleware());
      app.get("/test/:id", (c) => c.text("ok"));

      const res = await app.request("/test/123");
      expect(res.status).toBe(200);

      const span = testTracer.spans[0];
      expect(span.meta["http.route"]).toBe("/test/:id");
      expect(span.resource).toBe("GET /test/:id");
    });

    it("should use custom service name", async () => {
      app.use("*", datadogMiddleware({ serviceName: "custom-service" }));
      app.get("/", (c) => c.text("ok"));

      await app.request("/");

      const span = testTracer.spans[0];
      expect(span.meta["service.name"]).toBe("custom-service");
    });
  });

  describe("route pattern handling", () => {
    it("should handle nested routes", async () => {
      app.use("*", datadogMiddleware());
      const userRouter = new Hono();
      userRouter.get("/:id", (c) => c.text("ok"));
      app.route("/users", userRouter);

      await app.request("/users/123");

      const span = testTracer.spans[0];
      expect(span.meta["http.route"]).toBe("/users/:id");
      expect(span.resource).toBe("GET /users/:id");
    });

    it("should handle path composition with multiple routers", async () => {
      app.use("*", datadogMiddleware());
      const apiRouter = new Hono();
      const userRouter = new Hono();

      userRouter.get("/:id/profile", (c) => c.text("ok"));
      apiRouter.route("/users", userRouter);
      app.route("/api/v1", apiRouter);

      await app.request("/api/v1/users/123/profile");

      const span = testTracer.spans[0];
      expect(span.meta["http.route"]).toBe("/api/v1/users/:id/profile");
      expect(span.resource).toBe("GET /api/v1/users/:id/profile");
    });

    it("should preserve route pattern through middleware chain", async () => {
      app.use("*", datadogMiddleware());
      app.use("/api/users/:id", async (c, next) => {
        await next();
      });
      app.get("/api/users/:id", (c) => c.text("ok"));

      await app.request("/api/users/123");

      const span = testTracer.spans[0];
      expect(span.meta["http.route"]).toBe("/api/users/:id");
      expect(span.resource).toBe("GET /api/users/:id");
    });
  });

  describe("error handling", () => {
    it("should handle client errors", async () => {
      app.use("*", datadogMiddleware());
      app.get("/", (c) => c.json({ error: "Not found" }, 404));

      const res = await app.request("/");
      expect(res.status).toBe(404);

      const span = testTracer.spans[0];
      expect(span.error).toBe(1);
      expect(span.meta["error.type"]).toBe("Client Error");
      expect(span.meta["http.status_code"]).toBe("404");
    });

    it("should handle server errors", async () => {
      app.use("*", datadogMiddleware());
      app.get("/", (c) => c.json({ error: "Server Error" }, 500));

      const res = await app.request("/");
      expect(res.status).toBe(500);

      const span = testTracer.spans[0];
      expect(span.error).toBe(1);
      expect(span.meta["error.type"]).toBe("Internal Server Error");
      expect(span.meta["http.status_code"]).toBe("500");
    });

    it("should handle thrown errors", async () => {
      app.use("*", datadogMiddleware());
      app.get("/", () => {
        throw new Error("test error");
      });

      try {
        await app.request("/");
      } catch (e) {
        // Expected error
      }

      const span = testTracer.spans[0];
      expect(span.error).toBe(1);
      expect(span.meta["error.type"]).toBe("Error");
      expect(span.meta["error.msg"]).toBe("test error");
      expect(span.meta["http.status_code"]).toBe("500");
    });
  });

  describe("scope management", () => {
    it("should create distinct spans for each middleware", async () => {
      app.use("*", datadogMiddleware());
      app.use("/", async (c, next) => {
        testTracer.startSpan("first");
        await next();
      });
      app.use("/", async (c, next) => {
        testTracer.startSpan("second");
        await next();
      });

      await app.request("/");

      expect(testTracer.spans.length).toBe(3); // Including the web.request span
      expect(testTracer.spans[1].name).toBe("first");
      expect(testTracer.spans[2].name).toBe("second");
      expect(testTracer.spans[1]).not.toBe(testTracer.spans[2]);
    });

    it("should clean up scope after request completes", async () => {
      app.use("*", datadogMiddleware());
      app.use("/", async (c, next) => {
        const activeSpan = testTracer.scope().active();
        expect(activeSpan).toBeTruthy();
        expect(activeSpan?.name).toBe("web.request");
        await next();
      });

      await app.request("/");
      expect(testTracer.currentSpan).toBe(null);
    });
  });
});
