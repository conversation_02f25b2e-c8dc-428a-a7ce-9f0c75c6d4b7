import {} from "hono";

export interface ViewData {
  name: string;
  componentPath: string;
  props: Record<string, unknown>;
  meta: {
    title: string;
    description: string;
  };
}

export interface Manifest {
  [key: string]: {
    file: string;
    src: string;
    isEntry?: boolean;
    css?: string[];
  };
}

interface ViewMeta {
  title: string;
  lang?: string;
  description?: string;
  manifest?: Manifest;
}

interface ManifestItem {
  file: string;
  name: string;
  src?: string;
  isEntry?: boolean;
  imports?: string[];
  css?: string[];
}

interface SocialMedia {
  title: string;
  url: string;
  hashtags?: string[];
  images?: string[];
}

declare module "hono" {
  interface Context {
    view(name: string, data: ViewData): Response | Promise<Response>;
  }
}

declare module "@hono/react-renderer" {
  interface Props {
    view: ViewData;
    manifest?: Manifest;
  }
}

declare global {
  interface Window {
    _hono_view: ViewData;
  }
}
