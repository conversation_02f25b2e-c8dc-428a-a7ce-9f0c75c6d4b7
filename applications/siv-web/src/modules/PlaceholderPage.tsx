import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function PlaceholderPage() {
  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle className="text-3xl font-bold text-center">
            Welcome to SIV Converts
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-center text-lg text-muted-foreground">
            Your comprehensive lead management and conversion platform
          </p>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Admin Portal</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Manage properties, forms, and integrations
                </p>
                <Button asChild className="w-full">
                  <a href="/admin">Go to Admin Portal</a>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-xl">User Application</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Access lead assignment and sales tools
                </p>
                <Button asChild className="w-full" variant="outline">
                  <a href="/app">Go to Application</a>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8 pt-8 border-t text-center">
            <p className="text-sm text-muted-foreground">
              For support, please contact{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
