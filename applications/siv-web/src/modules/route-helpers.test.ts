import { HonoRequest } from "hono";
import { mock } from "vitest-mock-extended";
import { describe, it, expect } from "vitest";
import { ModuleBaseUrlFactory, routesBaseUrl } from "@/modules/route-helpers";

function createMockRequest(
  url: string,
  path: string,
  headers: Record<string, string> = {},
) {
  const mockReq = mock<HonoRequest>({
    url,
    path,
  });
  mockReq.header.mockReturnValue(headers);
  return mockReq;
}

describe("route helper", () => {
  describe("routesBaseUrl", () => {
    it("returns base url without x-forwarded-proto", () => {
      const mockReq = createMockRequest(
        "http://localhost:8080/some-router-section/some-page",
        "/some-router-section/some-page",
      );

      const baseUrl = routesBaseUrl(mockReq);
      expect(baseUrl).toEqual("http://localhost:8080");
    });

    it("uses x-forwarded-proto when present", () => {
      const mockReq = createMockRequest(
        "http://localhost:8080/some-router-section/some-page",
        "/some-router-section/some-page",
        { "x-forwarded-proto": "https" },
      );

      const baseUrl = routesBaseUrl(mockReq);
      expect(baseUrl).toEqual("https://localhost:8080");
    });

    it("preserves port when using x-forwarded-proto", () => {
      const mockReq = createMockRequest(
        "http://localhost:3000/some-router-section/some-page",
        "/some-router-section/some-page",
        { "x-forwarded-proto": "https" },
      );

      const baseUrl = routesBaseUrl(mockReq);
      expect(baseUrl).toEqual("https://localhost:3000");
    });

    it("handles query parameters correctly with x-forwarded-proto", () => {
      const mockReq = createMockRequest(
        "http://localhost:8080/some-router-section/some-page?key=value",
        "/some-router-section/some-page",
        { "x-forwarded-proto": "https" },
      );

      const baseUrl = routesBaseUrl(mockReq);
      expect(baseUrl).toEqual("https://localhost:8080");
    });
  });

  describe("base path for module", () => {
    const mockReq = createMockRequest(
      "http://localhost:8080/some-router-section/some-page",
      "/some-router-section/some-page",
    );

    it("returns the base path for the module", () => {
      const basePathFactory = new ModuleBaseUrlFactory("/my-module");
      expect(basePathFactory.baseUrl(mockReq)).toEqual(
        "http://localhost:8080/my-module",
      );
    });

    it("prepends a slash if needed", () => {
      const basePathFactory = new ModuleBaseUrlFactory("my-module");
      expect(basePathFactory.baseUrl(mockReq)).toEqual(
        "http://localhost:8080/my-module",
      );
    });
  });

  describe("creating factories for nested modules", () => {
    const mockReq = createMockRequest(
      "http://localhost:8080/some-router-section/some-page",
      "/some-router-section/some-page",
    );

    it("returns the base path for the NESTED module", () => {
      const basePathFactory = new ModuleBaseUrlFactory("/my-module");
      const nestedModuleFactory =
        basePathFactory.factoryForNestedModule("/nested-module");
      expect(nestedModuleFactory.baseUrl(mockReq)).toEqual(
        "http://localhost:8080/my-module/nested-module",
      );
    });
  });
});
