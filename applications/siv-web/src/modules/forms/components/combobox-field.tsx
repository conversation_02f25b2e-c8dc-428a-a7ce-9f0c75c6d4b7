"use client";

import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from "@headlessui/react";
import { CheckIcon } from "@heroicons/react/20/solid";
import { useState, useEffect, useRef } from "react";

// Define option type
export type ComboboxOption = {
  id: string | number;
  name: string;
};

// Define component props
export interface ComboboxV2Props {
  options: ComboboxOption[];
  value: ComboboxOption | null;
  onChange: (value: ComboboxOption | null) => void;
  placeholder?: string;
  label?: string;
  className?: string;
  id?: string;
  name?: string;
  required?: boolean;
  disabled?: boolean;
  "data-testid"?: string;
  forceOpen?: boolean;
}

export default function ComboboxField({
  options,
  value,
  onChange,
  placeholder = "Select an option",
  label,
  className = "",
  id,
  name,
  required,
  disabled = false,
  "data-testid": testId,
  forceOpen = false,
}: ComboboxV2Props) {
  const [query, setQuery] = useState("");
  const [selectedOption, setSelectedOption] = useState<ComboboxOption | null>(
    value,
  );
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const comboboxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedOption(value);
  }, [value]);

  // Effect to update the input value directly when selectedOption changes
  useEffect(() => {
    if (inputRef.current) {
      // Force the input to display the selected value
      inputRef.current.value = selectedOption?.name || "";
    }
  }, [selectedOption, id]);

  // Handle clicks outside of the combobox
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        comboboxRef.current &&
        !comboboxRef.current.contains(event.target as Node) &&
        isOpen
      ) {
        setIsOpen(false);
      }
    }

    // Only add the event listener if the dropdown is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Filter options based on search query
  const filteredOptions =
    query === ""
      ? options
      : options.filter((option) => {
          return option.name.toLowerCase().includes(query.toLowerCase());
        });

  // Generate component ID prefix for test IDs
  const componentId = id || "combobox";

  return (
    <Combobox
      as="div"
      value={selectedOption}
      onChange={(option) => {
        setQuery("");
        setSelectedOption(option);
        onChange(option);
        // Close dropdown after selection
        setIsOpen(false);
      }}
      disabled={disabled}
      nullable
      className={className}
      onClose={() => setIsOpen(false)}
      ref={comboboxRef}
    >
      {label && (
        <Combobox.Label className="block text-sm/6 font-medium text-gray-900">
          {label}
        </Combobox.Label>
      )}
      <div className="relative mt-2">
        <ComboboxInput
          ref={inputRef}
          className="ef-input"
          style={{
            backgroundColor: "var(--ef-field-background, white) !important",
            color: "var(--ef-input-text, black) !important",
            borderColor: "var(--ef-border, #e5e7eb) !important",
            borderRadius: "var(--ef-input-radius, 0.375rem) !important",
            borderWidth: "1px !important",
            borderStyle: "solid !important",
            position: "relative",
            zIndex: 1,
            height: "auto !important",
            minHeight: "38px !important",
            display: "block !important",
            width: "100% !important",
            visibility: "visible",
            opacity: 1,
            padding: "0.75rem 2.5rem 0.75rem 0.75rem !important",
            margin: "0 !important",
            outline: "none !important",
          }}
          onClick={() => setIsOpen(true)}
          onFocus={() => setIsOpen(true)}
          onChange={(event) => {
            setQuery(event.target.value);
            // Open dropdown whenever typing
            setIsOpen(true);
          }}
          onBlur={(e) => {
            // Close dropdown on blur only if the new focus target is outside the combobox
            if (!comboboxRef.current?.contains(e.relatedTarget as Node)) {
              setIsOpen(false);
            }
            setQuery("");
          }}
          onKeyDown={(e) => {
            // Close on Escape key
            if (e.key === "Escape") {
              setIsOpen(false);
            }
            // Close on Tab if no option is focused
            if (e.key === "Tab") {
              setIsOpen(false);
            }
          }}
          displayValue={(option: ComboboxOption | null) => {
            const displayVal = option?.name || "";
            return displayVal;
          }}
          placeholder={placeholder}
          id={id}
          name={name}
          required={required}
          data-testid={testId}
          autoComplete="off"
          spellCheck="false"
        />
        <ComboboxButton
          className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"
          data-testid={`${componentId}-button`}
          onClick={() => setIsOpen(!isOpen)} // Toggle dropdown state
          style={{
            zIndex: 2,
            right: "5px",
            backgroundColor: "transparent",
          }}
        >
          <div
            className="combobox-triangle"
            style={{
              width: 0,
              height: 0,
              borderLeft: "5px solid transparent",
              borderRight: "5px solid transparent",
              borderTop: "5px solid var(--ef-input-text, #718096)",
              pointerEvents: "none",
            }}
          />
        </ComboboxButton>

        <ComboboxOptions
          className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md py-1 text-base shadow-lg focus:outline-none sm:text-sm"
          data-testid={`${componentId}-option-list`}
          static={isOpen}
          style={{
            backgroundColor: "var(--ef-field-background, white)",
            color: "var(--ef-input-text, black)",
            borderColor: "var(--ef-border, #e5e7eb)",
            borderWidth: "1px",
            borderStyle: "solid",
            borderRadius: "var(--ef-input-radius, 0.375rem)",
          }}
        >
          {filteredOptions.map((option) => (
            <ComboboxOption
              key={option.id}
              value={option}
              className="group relative cursor-default select-none py-2 pl-10 pr-4 data-[focus]:outline-none"
              data-testid={`${componentId}-option-${option.id}`}
              style={
                {
                  color: "var(--ef-input-text, black)",
                  "--highlight-bg": "var(--ef-primary, #3b82f6)",
                  "--highlight-text": "var(--ef-button-text, white)",
                } as React.CSSProperties
              }
            >
              <span className="block truncate group-data-[selected]:font-semibold">
                {option.name}
              </span>

              <span
                className="absolute inset-y-0 left-2 flex items-center hidden group-data-[selected]:flex group-data-[focus]:text-white"
                style={{ color: "var(--ef-input-text, black)" }}
              >
                <CheckIcon className="size-4" aria-hidden="true" />
              </span>
            </ComboboxOption>
          ))}
        </ComboboxOptions>
      </div>
    </Combobox>
  );
}
