// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { withTheme } from "../storybook/ThemeDecorator";
import { themeFixtures } from "../builder/theme-fixtures";
import { EmbeddedForm } from "../submission/EmbeddedForm";
import type { FieldIdentifier, Form, FormField } from "../builder/types";
import { useFormState } from "./useFormState";
import { PREDEFINED_FIELDS } from "@/modules/forms/builder/fieldTypes";
import { FORM_CHROMATIC_PARAMS } from "@/modules/forms/storybook/chromatic-test-settings";
import { fieldType } from "@/drizzle/schema";
import { DEFAULT_STORY_PARAMETERS } from "@/config/chromatic-test-settings";

// Field types we want to showcase
const FIELD_TYPES = fieldType.enumValues;

// Create a test form with a specific field type
const createFieldForm = (
  fieldType: FieldIdentifier,
  theme: any,
  options: { width?: "full" | "half"; required?: boolean } = {},
): Form => {
  const formId = `field-form-${Math.random().toString(36).substring(2, 9)}`;

  const field: FormField = {
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: options.required ?? true,
    width: options.width ?? "full",
    order: 0,
    rowBreakAfter: false,
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return {
    id: formId,
    name: `${getFieldLabel(fieldType)} Field`,
    description: "This form demonstrates a single field type",
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      id: "test-theme-id",
      name: theme.name,
      primaryColor: theme.primaryColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor,
      borderColor: theme.borderColor,
      inputBorderRadius: theme.inputBorderRadius,
      buttonBorderRadius: theme.buttonBorderRadius,
      padding: theme.padding,
      buttonTextColor: theme.buttonTextColor,
      buttonAlignment: theme.buttonAlignment,
      font: theme.font,
      fieldBackgroundColor: theme.fieldBackgroundColor,
      inputTextColor: theme.inputTextColor,
      formBorderRadius: theme.formBorderRadius,
      placeholderColor: theme.placeholderColor,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields: [field],
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// Helper function to get a label for a field type
function getFieldLabel(fieldType: FieldIdentifier): string | null {
  const labels: Record<FieldIdentifier, string | null> = {
    EVENT_DATE_RANGE: "Event Date Range",
    EVENT_NAME: "Event Name",
    EVENT_NEEDS: "Event Needs",
    FLEXIBLE_DATES: "Are your dates flexible?",
    MARKETING_CONSENT: null,
    FIRST_NAME: "First Name",
    LAST_NAME: "Last Name",
    EMAIL: "Email Address",
    PHONE: "Phone Number",
    EVENT_TYPE: "Event Type",
    START_DATE: "Event Start Date",
    END_DATE: "Event End Date",
    GUEST_COUNT: "Number of Guests",
    ROOM_COUNT: "Number of Rooms",
    MEAL_COUNT: "Number of Meals",
    BUDGET: "Event Budget",
    EVENT_DESCRIPTION: "Event Description",
    COMPANY: "Company",
    CITY: "City",
    STATE: "State",
    POSTAL_CODE: "Postal Code",
    COUNTRY: "Country",
    FILE_UPLOAD: "Files",
  };

  return labels[fieldType];
}

// Helper function to get a placeholder for a field type
function getFieldPlaceholder(fieldType: FieldIdentifier): string {
  const placeholders: Record<FieldIdentifier, string> = {
    EVENT_DATE_RANGE: "Select the date range",
    EVENT_NAME: "Event Name",
    EVENT_NEEDS: "Event Needs",
    FLEXIBLE_DATES: "Are your dates flexible?",
    MARKETING_CONSENT: "",
    FIRST_NAME: "Enter your first name",
    LAST_NAME: "Enter your last name",
    EMAIL: "Enter your email address",
    PHONE: "Enter your phone number",
    EVENT_TYPE: "Select an event type",
    START_DATE: "Select a start date",
    END_DATE: "Select an end date",
    GUEST_COUNT: "Enter the number of guests",
    ROOM_COUNT: "Enter the number of rooms",
    MEAL_COUNT: "Enter the number of meals",
    BUDGET: "Enter your budget",
    EVENT_DESCRIPTION: "Describe your event",
    COMPANY: "Enter your company name",
    CITY: "Enter your city",
    STATE: "Select your state",
    POSTAL_CODE: "Enter your postal code",
    COUNTRY: "Select your country",
    FILE_UPLOAD: "",
  };

  return placeholders[fieldType] || "";
}

// Create a form with multiple field variations
const createFieldVariationsForm = (
  fieldType: FieldIdentifier,
  theme: any,
): Form => {
  const formId = `variations-form-${Math.random().toString(36).substring(2, 9)}`;

  // Use type assertion for the field type

  // Create variations of the field
  const fields: FormField[] = [
    // Full width, required
    {
      id: `field-${Math.random().toString(36).substring(2, 9)}`,
      formId,
      type: fieldType,
      label: `${getFieldLabel(fieldType)} (Full Width, Required)`,
      required: true,
      width: "full",
      order: 0,
      rowBreakAfter: false,
      placeholder: getFieldPlaceholder(fieldType),
      hideForEventTypes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    // Half width, required
    {
      id: `field-${Math.random().toString(36).substring(2, 9)}`,
      formId,
      type: fieldType,
      label: `${getFieldLabel(fieldType)} (Half Width, Required)`,
      required: true,
      width: "half",
      order: 1,
      rowBreakAfter: false,
      placeholder: getFieldPlaceholder(fieldType),
      hideForEventTypes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    // Half width, optional
    {
      id: `field-${Math.random().toString(36).substring(2, 9)}`,
      formId,
      type: fieldType,
      label: `${getFieldLabel(fieldType)} (Half Width, Optional)`,
      required: false,
      width: "half",
      order: 2,
      rowBreakAfter: false,
      placeholder: getFieldPlaceholder(fieldType),
      hideForEventTypes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    // Full width, optional
    {
      id: `field-${Math.random().toString(36).substring(2, 9)}`,
      formId,
      type: fieldType,
      label: `${getFieldLabel(fieldType)} (Full Width, Optional)`,
      required: false,
      width: "full",
      order: 3,
      rowBreakAfter: false,
      placeholder: getFieldPlaceholder(fieldType),
      hideForEventTypes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  return {
    id: formId,
    name: `${getFieldLabel(fieldType)} Variations`,
    description:
      "This form demonstrates different variations of the same field type",
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      id: "test-theme-id",
      name: theme.name,
      primaryColor: theme.primaryColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor,
      borderColor: theme.borderColor,
      inputBorderRadius: theme.inputBorderRadius,
      buttonBorderRadius: theme.buttonBorderRadius,
      padding: theme.padding,
      buttonTextColor: theme.buttonTextColor,
      buttonAlignment: theme.buttonAlignment,
      font: theme.font,
      fieldBackgroundColor: theme.fieldBackgroundColor,
      inputTextColor: theme.inputTextColor,
      formBorderRadius: theme.formBorderRadius,
      placeholderColor: theme.placeholderColor,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields,
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// Create a form with all field types
const createAllFieldsForm = (theme: any): Form => {
  const formId = `all-fields-form-${Math.random().toString(36).substring(2, 9)}`;

  // Type assertion for field types
  const fieldTypes = FIELD_TYPES as readonly FieldIdentifier[];

  const fields: FormField[] = fieldTypes.map((fieldType, index) => ({
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: index % 2 === 0, // Alternate required/optional
    width: index % 3 === 0 ? "full" : "half", // Mix of full and half width
    order: index,
    rowBreakAfter: false,
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  }));

  return {
    id: formId,
    name: "All Field Types",
    description: "This form demonstrates all available field types",
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      id: "test-theme-id",
      name: theme.name,
      primaryColor: theme.primaryColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor,
      borderColor: theme.borderColor,
      inputBorderRadius: theme.inputBorderRadius,
      buttonBorderRadius: theme.buttonBorderRadius,
      padding: theme.padding,
      buttonTextColor: theme.buttonTextColor,
      buttonAlignment: theme.buttonAlignment,
      font: theme.font,
      fieldBackgroundColor: theme.fieldBackgroundColor,
      inputTextColor: theme.inputTextColor,
      formBorderRadius: theme.formBorderRadius,
      placeholderColor: theme.placeholderColor,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields,
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// Story configuration
const meta: Meta = {
  title: "Forms/Fields/Collections",
  component: EmbeddedForm,
  parameters: {
    ...DEFAULT_STORY_PARAMETERS,
    layout: "padded",
    // Disable snapshots by default - we'll only enable for specific stories
    chromatic: { disableSnapshot: true },
  },
};

export default meta;

type Story = StoryObj;

// Create a wrapper component that uses the form state hook
const FormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Create a wrapper component specifically for mobile views
const MobileFormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "320px", margin: "0 auto" }}>
        <EmbeddedForm
          form={form}
          values={values}
          onChange={handleChange}
          isMobileGuess={true}
        />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Show all field types with default theme
export const AllFieldTypes: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: {
      viewports: [768, 1440],
    },
  },
  render: () => (
    <FormWithState form={createAllFieldsForm(themeFixtures.default)} />
  ),
};

// Show all field types with dark theme
export const AllFieldTypesWithDarkTheme: Story = {
  decorators: [withTheme("darkCorporate")],
  parameters: {
    chromatic: {
      viewports: [768, 1440],
    },
  },
  render: () => (
    <FormWithState form={createAllFieldsForm(themeFixtures.darkCorporate)} />
  ),
};

// Create a function to render field variations in separate forms
const FieldVariationsInSeparateForms = ({
  fieldType,
  theme,
}: {
  fieldType: FieldIdentifier;
  theme: any;
}) => {
  // Create separate variations of individual fields
  // These are full standalone forms with appropriately sized fields inside
  const fullWidthRequiredForm = createFieldForm(fieldType, theme, {
    width: "full",
    required: true,
  });
  const halfWidthRequiredForm = createFieldForm(fieldType, theme, {
    width: "half",
    required: true,
  });
  const halfWidthOptionalForm = createFieldForm(fieldType, theme, {
    width: "half",
    required: false,
  });
  const fullWidthOptionalForm = createFieldForm(fieldType, theme, {
    width: "full",
    required: false,
  });

  // Create an invalid variant to show validation errors
  const invalidForm = createFieldForm(fieldType, theme, {
    width: "full",
    required: true,
  });

  // Customize labels to indicate the variation
  fullWidthRequiredForm.fields[0].label = `${getFieldLabel(fieldType)} (Full Width, Required)`;
  halfWidthRequiredForm.fields[0].label = `${getFieldLabel(fieldType)} (Half Width, Required)`;
  halfWidthOptionalForm.fields[0].label = `${getFieldLabel(fieldType)} (Half Width, Optional)`;
  fullWidthOptionalForm.fields[0].label = `${getFieldLabel(fieldType)} (Full Width, Optional)`;
  invalidForm.fields[0].label = `${getFieldLabel(fieldType)} (Invalid State)`;

  // Create a special half-width row form that contains both half-width fields
  const halfWidthRowForm: Form = {
    id: `half-width-row-form-${Math.random().toString(36).substring(2, 9)}`,
    name: `${getFieldLabel(fieldType)} Half-Width Fields`,
    description: "This form demonstrates half-width fields in a row",
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: theme,
    fields: [
      // First half-width field - don't break row after this
      {
        ...halfWidthRequiredForm.fields[0],
        id: `half-required-${Math.random().toString(36).substring(2, 9)}`,
        rowBreakAfter: false,
        label: `${getFieldLabel(fieldType)} (Required)`,
      },
      // Second half-width field - break row after this
      {
        ...halfWidthOptionalForm.fields[0],
        id: `half-optional-${Math.random().toString(36).substring(2, 9)}`,
        rowBreakAfter: true,
        label: `${getFieldLabel(fieldType)} (Optional)`,
      },
    ],
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Generate field-specific error message
  let errorMessage = "This field is required";
  if (fieldType === "EMAIL") {
    errorMessage = "Please enter a valid email address";
  } else if (fieldType === "PHONE") {
    errorMessage = "Please enter a valid phone number";
  } else if (fieldType === "POSTAL_CODE") {
    errorMessage = "Please enter a valid postal code";
  } else if (fieldType.includes("DATE")) {
    errorMessage = "Please enter a valid date";
  }

  // Error state to demonstrate validation errors
  const errors = {
    [fieldType]: [errorMessage],
  };

  return (
    <div className="space-y-10">
      <div>
        <h3 className="text-lg font-semibold mb-2">Full-Width Fields</h3>
        <div className="space-y-8">
          <FormWithState form={fullWidthRequiredForm} />
          <FormWithState form={fullWidthOptionalForm} />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-2">Half-Width Fields</h3>
        <div className="space-y-8">
          <FormWithState form={halfWidthRequiredForm} />
          <FormWithState form={halfWidthOptionalForm} />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-2">Half-Width Row</h3>
        <FormWithState form={halfWidthRowForm} />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-2">Invalid State</h3>
        <p className="text-sm text-gray-600 mb-4">
          This example shows how the field appears with validation errors.
        </p>
        <div>
          <EmbeddedForm
            form={invalidForm}
            values={{}}
            errors={errors}
            onChange={() => {}}
          />
        </div>
      </div>
    </div>
  );
};

// Field variations
export const EventTypeVariations: Story = {
  decorators: [withTheme("default")],
  parameters: {
    // Only keep snapshots for this most complex field type
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard variations from FieldVariationsInSeparateForms
    const variationsContent = (
      <FieldVariationsInSeparateForms fieldType="EVENT_TYPE" theme={theme} />
    );

    // Create mobile version
    const mobileEventTypeForm = createFieldForm("EVENT_TYPE", theme);
    mobileEventTypeForm.fields[0].label = "Event Type (Mobile View)";

    // Add event types to both forms
    (mobileEventTypeForm as any).eventTypes = [
      { id: "wedding", name: "Wedding" },
      { id: "corporate_meeting", name: "Corporate Meeting" },
      { id: "celebration", name: "Celebration" },
    ];

    return (
      <div className="space-y-12">
        {/* Standard variations */}
        {variationsContent}

        <div className="border-t pt-8">
          <h2 className="text-xl font-semibold mb-6">
            Mobile Event Type Field
          </h2>

          <div>
            <p className="text-sm text-gray-600 mb-4">
              This example shows how the Event Type field appears on mobile
              devices.
            </p>
            <MobileFormWithState form={mobileEventTypeForm} />
          </div>
        </div>
      </div>
    );
  },
};

export const DateFieldVariations: Story = {
  decorators: [withTheme("default")],
  parameters: {
    // Disable snapshots for this story to reduce test count
    chromatic: { disableSnapshot: true },
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard variations from FieldVariationsInSeparateForms
    const variationsContent = (
      <FieldVariationsInSeparateForms fieldType="START_DATE" theme={theme} />
    );

    // Create mobile versions
    const mobileStartDateForm = createFieldForm("START_DATE", theme);
    mobileStartDateForm.fields[0].label = "Start Date (Mobile View)";

    const mobileEndDateForm = createFieldForm("END_DATE", theme);
    mobileEndDateForm.fields[0].label = "End Date (Mobile View)";

    return (
      <div className="space-y-12">
        {/* Standard variations */}
        {variationsContent}

        <div className="border-t pt-8">
          <h2 className="text-xl font-semibold mb-6">Mobile Date Fields</h2>

          <div className="space-y-8">
            <div>
              <p className="text-sm text-gray-600 mb-4">
                This example shows how the Start Date field appears on mobile
                devices.
              </p>
              <MobileFormWithState form={mobileStartDateForm} />
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-4">
                This example shows how the End Date field appears on mobile
                devices.
              </p>
              <MobileFormWithState form={mobileEndDateForm} />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

export const PhoneFieldVariations: Story = {
  decorators: [withTheme("default")],
  parameters: {
    // Disable snapshots for this story to reduce test count
    chromatic: { disableSnapshot: true },
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard variations from FieldVariationsInSeparateForms
    const variationsContent = (
      <FieldVariationsInSeparateForms fieldType="PHONE" theme={theme} />
    );

    // Create US phone form
    const usPhoneForm = createFieldForm("PHONE", theme);

    // Create international phone form
    const intlPhoneForm = createFieldForm("PHONE", theme);
    const frenchValues = {
      PHONE: "+33123456789",
      PHONE_COUNTRY: "fr",
    };

    const invalidUSForm = createFieldForm("PHONE", theme);
    invalidUSForm.fields[0].label = "Phone (Invalid US Number)";

    const invalidIntlForm = createFieldForm("PHONE", theme);
    invalidIntlForm.fields[0].label = "Phone (Invalid International Number)";

    const invalidValues = {};
    const invalidIntlValues = { PHONE_COUNTRY: "fr" };

    const invalidUSErrors = {
      PHONE: ["Please enter a valid US phone number (10 digits required)"],
    };

    const invalidIntlErrors = {
      PHONE: ["Please enter a valid international phone number"],
    };

    return (
      <div className="space-y-12">
        <p className="text-sm text-gray-600 mb-4 italic">
          Note: the phone field is a special field that uses hidden inputs for
          PHONE and PHONE_COUNTRY values. The component will automatically
          format the phone number based on the country code. The form values in
          this story are not displayed dynamically in the form values section
          below the form. That does not indicate that the phone field is not
          working.
        </p>

        {/* Standard variations */}
        {variationsContent}

        <div className="border-t pt-8">
          <h2 className="text-xl font-semibold mb-6">
            Special Phone Field Examples
          </h2>

          <div className="space-y-8">
            {/* US Phone Field */}
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Default (US) Phone Number
              </h3>
              <FormWithState form={usPhoneForm} />
            </div>

            {/* International Phone Field */}
            <div>
              <h3 className="text-lg font-semibold mb-2">
                International Phone Number
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                This example demonstrates a phone field with international
                number support. Note: The component uses hidden inputs for PHONE
                and PHONE_COUNTRY values.
              </p>
              <FormWithState
                form={intlPhoneForm}
                initialValues={frenchValues}
              />
            </div>

            {/* Invalid US Phone Field */}
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Invalid US Phone Number
              </h3>
              <div style={{ maxWidth: "600px", margin: "0 auto" }}>
                <EmbeddedForm
                  form={invalidUSForm}
                  values={invalidValues}
                  errors={invalidUSErrors}
                  onChange={() => {}}
                />
              </div>
            </div>

            {/* Invalid International Phone Field */}
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Invalid International Phone Number
              </h3>
              <div style={{ maxWidth: "600px", margin: "0 auto" }}>
                <EmbeddedForm
                  form={invalidIntlForm}
                  values={invalidIntlValues}
                  errors={invalidIntlErrors}
                  onChange={() => {}}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  },
};

// Wedding Event Needs with special wedding-specific options
export const WeddingEventNeeds: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    // Create a base form with EVENT_TYPE field
    const form = createTestForm("EVENT_TYPE", themeFixtures.default);

    // Add EVENT_NEEDS and GUEST_COUNT fields
    form.fields.push(
      // Event needs field (shows different options based on event type)
      {
        id: `event-needs-${Math.random().toString(36).substring(2, 9)}`,
        formId: form.id,
        type: "EVENT_NEEDS",
        label: "What do you need for your wedding?",
        required: true,
        width: "full",
        order: 1,
        rowBreakAfter: false,
        hideForEventTypes: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      // Guest count field
      {
        id: `guest-count-${Math.random().toString(36).substring(2, 9)}`,
        formId: form.id,
        type: "GUEST_COUNT",
        label: "Number of Guests",
        required: true,
        width: "full",
        order: 2,
        rowBreakAfter: false,
        hideForEventTypes: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    );

    // Update form name and description
    form.name = "Wedding Event Form";
    form.description =
      "This form demonstrates wedding-specific event needs options";

    // Add event types
    (form as any).eventTypes = [
      { id: "wedding", name: "Wedding" },
      { id: "corporate_meeting", name: "Corporate Meeting" },
      { id: "celebration", name: "Celebration" },
    ];

    // Initial values with wedding pre-selected
    const initialValues = {
      EVENT_TYPE: "wedding",
    };

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Wedding Event Needs Example</h3>
        <p className="text-sm text-gray-600 mb-4">
          This example shows how EVENT_NEEDS options change when "Wedding" is
          selected as the event type. The options shown are specific to
          weddings.
        </p>
        <FormWithState form={form} initialValues={initialValues} />
      </div>
    );
  },
};

// EVENT_TYPE Field with Predefined Options
export const EventTypeWithOptions: Story = {
  decorators: [withTheme("default")],
  render: () => {
    // Create the form using the helper function
    const eventTypeForm = createFieldForm("EVENT_TYPE", themeFixtures.default);

    // Add event types using type assertion
    // This is a workaround since the Form type doesn't include eventTypes
    (eventTypeForm as any).eventTypes = [
      { id: "wedding", name: "Wedding" },
      { id: "corporate", name: "Corporate Event" },
      { id: "birthday", name: "Birthday Party" },
    ];

    return (
      <div className="space-y-8">
        <h3 className="text-lg font-semibold mb-2">Event Type Selection</h3>
        <div>
          {(() => {
            const { values, handleChange } = useFormState({});
            return (
              <>
                <EmbeddedForm
                  form={eventTypeForm}
                  values={values}
                  onChange={handleChange}
                />
                <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
                  <h3 className="text-sm font-medium mb-2">
                    Current Form Values:
                  </h3>
                  <pre className="text-xs overflow-auto max-h-40">
                    {JSON.stringify(values, null, 2)}
                  </pre>
                </div>
              </>
            );
          })()}
        </div>
      </div>
    );
  },
};

// Conditional Field Visibility Based on Event Type
export const ConditionalFieldVisibility: Story = {
  decorators: [withTheme("default")],
  render: () => {
    // Create a form with multiple fields, some conditionally visible
    const form: Form = {
      id: `conditional-form-${Math.random().toString(36).substring(2, 9)}`,
      name: "Conditional Fields Demo",
      description: "This form demonstrates conditional field visibility",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        name: themeFixtures.default.name,
        primaryColor: themeFixtures.default.primaryColor,
        backgroundColor: themeFixtures.default.backgroundColor,
        textColor: themeFixtures.default.textColor,
        borderColor: themeFixtures.default.borderColor,
        inputBorderRadius: themeFixtures.default.inputBorderRadius,
        buttonBorderRadius: themeFixtures.default.buttonBorderRadius,
        padding: themeFixtures.default.padding,
        buttonTextColor: themeFixtures.default.buttonTextColor,
        buttonAlignment: themeFixtures.default.buttonAlignment,
        font: themeFixtures.default.font,
        fieldBackgroundColor: themeFixtures.default.fieldBackgroundColor,
        inputTextColor: themeFixtures.default.inputTextColor,
        formBorderRadius: themeFixtures.default.formBorderRadius,
        placeholderColor: themeFixtures.default.placeholderColor,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        // Event type field - controls visibility of other fields
        {
          id: `event-type-${Math.random().toString(36).substring(2, 9)}`,
          formId: `conditional-form`,
          type: "EVENT_TYPE",
          label: "Event Type",
          required: true,
          width: "full",
          order: 0,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Wedding-specific field
        {
          id: `wedding-field-${Math.random().toString(36).substring(2, 9)}`,
          formId: `conditional-form`,
          type: "GUEST_COUNT",
          label: "Number of Wedding Guests",
          required: true,
          width: "full",
          order: 1,
          rowBreakAfter: false,
          hideForEventTypes: ["corporate_meeting", "corporate_retreat"], // Only show for wedding
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Corporate-specific field
        {
          id: `corporate-field-${Math.random().toString(36).substring(2, 9)}`,
          formId: `conditional-form`,
          type: "COMPANY",
          label: "Company Name",
          required: true,
          width: "full",
          order: 2,
          rowBreakAfter: false,
          hideForEventTypes: ["wedding", "celebration"], // Only show for corporate
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add event types using type assertion
    (form as any).eventTypes = [
      { id: "wedding", name: "Wedding" },
      { id: "corporate_meeting", name: "Corporate Meeting" },
      { id: "celebration", name: "Celebration" },
    ];

    return (
      <div className="space-y-8">
        <h3 className="text-lg font-semibold mb-2">
          Conditional Field Visibility
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Try selecting different event types to see fields appear and
          disappear.
        </p>
        <div>
          {(() => {
            const { values, handleChange } = useFormState({});
            return (
              <>
                <EmbeddedForm
                  form={form}
                  values={values}
                  onChange={handleChange}
                />
                <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
                  <h3 className="text-sm font-medium mb-2">
                    Current Form Values:
                  </h3>
                  <pre className="text-xs overflow-auto max-h-40">
                    {JSON.stringify(values, null, 2)}
                  </pre>
                </div>
              </>
            );
          })()}
        </div>
      </div>
    );
  },
};

// Complex Form Layout with Different Field Widths and Row Arrangements
export const ComplexFormLayout: Story = {
  decorators: [withTheme("default")],
  render: () => {
    // Create a form with complex layout
    const form: Form = {
      id: `layout-form-${Math.random().toString(36).substring(2, 9)}`,
      name: "Complex Layout Demo",
      description: "This form demonstrates complex layout patterns",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        name: themeFixtures.default.name,
        primaryColor: themeFixtures.default.primaryColor,
        backgroundColor: themeFixtures.default.backgroundColor,
        textColor: themeFixtures.default.textColor,
        borderColor: themeFixtures.default.borderColor,
        inputBorderRadius: themeFixtures.default.inputBorderRadius,
        buttonBorderRadius: themeFixtures.default.buttonBorderRadius,
        padding: themeFixtures.default.padding,
        buttonTextColor: themeFixtures.default.buttonTextColor,
        buttonAlignment: themeFixtures.default.buttonAlignment,
        font: themeFixtures.default.font,
        fieldBackgroundColor: themeFixtures.default.fieldBackgroundColor,
        inputTextColor: themeFixtures.default.inputTextColor,
        formBorderRadius: themeFixtures.default.formBorderRadius,
        placeholderColor: themeFixtures.default.placeholderColor,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        // First name and last name on same row
        {
          id: `first-name-${Math.random().toString(36).substring(2, 9)}`,
          formId: `layout-form`,
          type: "FIRST_NAME",
          label: "First Name",
          required: true,
          width: "half",
          order: 0,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: `last-name-${Math.random().toString(36).substring(2, 9)}`,
          formId: `layout-form`,
          type: "LAST_NAME",
          label: "Last Name",
          required: true,
          width: "half",
          order: 1,
          rowBreakAfter: true, // Force next field to new row
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Email on its own row
        {
          id: `email-${Math.random().toString(36).substring(2, 9)}`,
          formId: `layout-form`,
          type: "EMAIL",
          label: "Email Address",
          required: true,
          width: "full",
          order: 2,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Phone next to company
        {
          id: `phone-${Math.random().toString(36).substring(2, 9)}`,
          formId: `layout-form`,
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "half",
          order: 3,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: `company-${Math.random().toString(36).substring(2, 9)}`,
          formId: `layout-form`,
          type: "COMPANY",
          label: "Company",
          required: false,
          width: "half",
          order: 4,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return (
      <div className="space-y-8">
        <h3 className="text-lg font-semibold mb-2">Complex Form Layout</h3>
        <p className="text-sm text-gray-600 mb-4">
          This demonstrates a realistic form with varied field widths and row
          arrangements.
        </p>
        <div>
          {(() => {
            const { values, handleChange } = useFormState({});
            return (
              <>
                <EmbeddedForm
                  form={form}
                  values={values}
                  onChange={handleChange}
                />
                <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
                  <h3 className="text-sm font-medium mb-2">
                    Current Form Values:
                  </h3>
                  <pre className="text-xs overflow-auto max-h-40">
                    {JSON.stringify(values, null, 2)}
                  </pre>
                </div>
              </>
            );
          })()}
        </div>
      </div>
    );
  },
};

// Simple browser-safe version of createTestField that doesn't depend on Node.js modules
const createTestField = (field: Partial<FormField>): FormField => ({
  id: field.id || `field-${Math.random().toString(36).substring(2, 9)}`,
  formId: field.formId || `form-${Math.random().toString(36).substring(2, 9)}`,
  type: field.type || "FIRST_NAME",
  label: field.label || "Test Field",
  required: field.required !== undefined ? field.required : true,
  width: field.width || "full",
  order: field.order || 0,
  rowBreakAfter:
    field.rowBreakAfter !== undefined ? field.rowBreakAfter : false,
  hideForEventTypes: field.hideForEventTypes || [],
  createdAt: field.createdAt || new Date(),
  updatedAt: field.updatedAt || new Date(),
});

// Helper to create multiple test fields with minimal configuration
const createTestFieldList = (
  formId: string,
  fieldDefs: Array<{
    type: FieldIdentifier;
    label?: string;
    required?: boolean;
    width?: "full" | "half";
    rowBreakAfter?: boolean;
  }>,
): FormField[] => {
  return fieldDefs.map((def, index) =>
    createTestField({
      id: `${def.type.toLowerCase()}-${Math.random().toString(36).substring(2, 9)}`,
      formId,
      type: def.type,
      label: def.label || PREDEFINED_FIELDS[def.type]?.defaultLabel || def.type,
      required: def.required !== undefined ? def.required : true,
      width: def.width || "full",
      order: index,
      rowBreakAfter: def.rowBreakAfter !== undefined ? def.rowBreakAfter : true,
      hideForEventTypes: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    }),
  );
};

// Form with extensive validation errors for multiple field types
export const FormWithValidationErrors: Story = {
  parameters: {
    // Disable snapshots for this story to reduce test count
    chromatic: { disableSnapshot: true },
  },
  decorators: [withTheme("default")],
  render: () => {
    const formId = `validation-form-${Math.random().toString(36).substring(2, 9)}`;

    // Create fields with minimal configuration
    const fields = createTestFieldList(formId, [
      { type: "FIRST_NAME", width: "half", rowBreakAfter: false },
      { type: "LAST_NAME", width: "half" },
      { type: "COMPANY", label: "Organization Name" },
      { type: "EMAIL" },
      { type: "PHONE" },
      { type: "COUNTRY", width: "full" },
      { type: "CITY", width: "half", rowBreakAfter: false },
      { type: "STATE", width: "half" },
      { type: "POSTAL_CODE" },
      { type: "EVENT_TYPE" },
      { type: "EVENT_NAME" },
      { type: "START_DATE", width: "half", rowBreakAfter: false },
      { type: "END_DATE", width: "half" },
      { type: "FLEXIBLE_DATES" },
      { type: "GUEST_COUNT", width: "half", rowBreakAfter: false },
      { type: "ROOM_COUNT", width: "half" },
      { type: "MEAL_COUNT", width: "half", rowBreakAfter: false },
      { type: "BUDGET", width: "half" },
      { type: "EVENT_NEEDS", label: "What do you need for your event?" },
      { type: "EVENT_DESCRIPTION", label: "Tell Us More About Your Event" },
      { type: "MARKETING_CONSENT", required: false },
    ]);

    // Create a form with many different field types
    const form: Form = {
      id: formId,
      name: "Validation Error Examples",
      description:
        "This form demonstrates validation errors for various field types",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        ...themeFixtures.default,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields,
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add event types to the form
    (form as any).eventTypes = [
      { id: "wedding", name: "Wedding" },
      { id: "corporate_meeting", name: "Corporate Meeting" },
      { id: "celebration", name: "Celebration" },
    ];

    // Sample invalid values
    const invalidValues = {
      FIRST_NAME: "",
      LAST_NAME: "",
      COMPANY: "",
      EMAIL: "invalid-email",
      PHONE: "123",
      COUNTRY: "", // Nothing selected
      CITY: "",
      STATE: "",
      POSTAL_CODE: "ABC",
      EVENT_TYPE: "", // Nothing selected
      EVENT_NAME: "",
      START_DATE: "2023-02-30", // Invalid date
      END_DATE: "2023-01-01", // Date before start date
      FLEXIBLE_DATES: "invalid",
      GUEST_COUNT: "abc", // Not a number
      ROOM_COUNT: "-5", // Negative number
      MEAL_COUNT: "xyz", // Not a number
      BUDGET: "-100", // Negative number
      EVENT_NEEDS: "",
      EVENT_DESCRIPTION: "", // Empty required field
      MARKETING_CONSENT: "invalid",
    };

    // Comprehensive validation errors
    const validationErrors = {
      _form: [
        "There were multiple errors in your submission. Please correct them and try again.",
      ],
      FIRST_NAME: ["This field is required"],
      LAST_NAME: ["This field is required"],
      COMPANY: ["This field is required"],
      EMAIL: ["Please enter a valid email address"],
      PHONE: ["Please enter a valid phone number"],
      COUNTRY: ["Please select a country"],
      CITY: ["This field is required"],
      STATE: ["This field is required"],
      POSTAL_CODE: ["Please enter a valid postal code"],
      EVENT_TYPE: ["Please select an event type"],
      EVENT_NAME: ["This field is required"],
      START_DATE: ["Please enter a valid date"],
      END_DATE: ["End date must be after start date"],
      FLEXIBLE_DATES: ["Invalid selection"],
      GUEST_COUNT: ["Please enter a valid number"],
      ROOM_COUNT: ["Value must be a positive number"],
      MEAL_COUNT: ["Please enter a valid number"],
      BUDGET: ["Budget must be a positive number"],
      EVENT_NEEDS: ["Please select at least one option"],
      EVENT_DESCRIPTION: ["This field is required"],
      MARKETING_CONSENT: ["Invalid selection"],
    };

    return (
      <div className="space-y-8">
        <h3 className="text-lg font-semibold mb-2">
          Form with Validation Errors
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          This example demonstrates how various field types appear when they
          have validation errors.
        </p>
        <div style={{ maxWidth: "600px", margin: "0 auto" }}>
          <EmbeddedForm
            form={form}
            values={invalidValues}
            errors={validationErrors}
            onChange={() => {}}
          />
        </div>
      </div>
    );
  },
};

const createTestForm = (fieldType: FieldIdentifier, theme: any): Form => {
  const formId = `form-${Math.random().toString(36).substring(2, 9)}`;

  // Create typed field type
  const typedFieldType = fieldType;

  // Create a single field using the type
  const field: FormField = {
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: typedFieldType,
    label: PREDEFINED_FIELDS[typedFieldType]?.defaultLabel || fieldType, // Use actual predefined field label
    required: true,
    width: "full",
    order: 0,
    placeholder: PREDEFINED_FIELDS[typedFieldType]?.placeholder || "",
    hideForEventTypes: [],
    rowBreakAfter: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Return the form with the field and theme
  return {
    id: formId,
    name: `Test ${fieldType} Field`,
    description: `This is a form showing a single ${fieldType} field`,
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      id: "test-theme-id",
      name: theme.name || "Test Theme",
      primaryColor: theme.primaryColor,
      backgroundColor: theme.backgroundColor,
      textColor: theme.textColor,
      borderColor: theme.borderColor,
      inputBorderRadius: theme.inputBorderRadius,
      buttonBorderRadius: theme.buttonBorderRadius,
      padding: theme.padding,
      buttonTextColor: theme.buttonTextColor,
      buttonAlignment: theme.buttonAlignment,
      font: theme.font,
      fieldBackgroundColor: theme.fieldBackgroundColor,
      inputTextColor: theme.inputTextColor,
      formBorderRadius: theme.formBorderRadius,
      placeholderColor: theme.placeholderColor,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields: [field],
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// Country-State Field Relationship
export const CountryStateRelationship: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    // Create a form with country and state fields
    const form: Form = {
      id: `country-state-form-${Math.random().toString(36).substring(2, 9)}`,
      name: "Country-State Relationship Demo",
      description:
        "This form demonstrates the relationship between country and state fields",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        name: themeFixtures.default.name,
        primaryColor: themeFixtures.default.primaryColor,
        backgroundColor: themeFixtures.default.backgroundColor,
        textColor: themeFixtures.default.textColor,
        borderColor: themeFixtures.default.borderColor,
        inputBorderRadius: themeFixtures.default.inputBorderRadius,
        buttonBorderRadius: themeFixtures.default.buttonBorderRadius,
        padding: themeFixtures.default.padding,
        buttonTextColor: themeFixtures.default.buttonTextColor,
        buttonAlignment: themeFixtures.default.buttonAlignment,
        font: themeFixtures.default.font,
        fieldBackgroundColor: themeFixtures.default.fieldBackgroundColor,
        inputTextColor: themeFixtures.default.inputTextColor,
        formBorderRadius: themeFixtures.default.formBorderRadius,
        placeholderColor: themeFixtures.default.placeholderColor,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        // Country field
        {
          id: `country-${Math.random().toString(36).substring(2, 9)}`,
          formId: `country-state-form`,
          type: "COUNTRY",
          label: "Country",
          required: true,
          width: "full",
          order: 0,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // State field
        {
          id: `state-${Math.random().toString(36).substring(2, 9)}`,
          formId: `country-state-form`,
          type: "STATE",
          label: "State/Province",
          required: true,
          width: "full",
          order: 1,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // City field
        {
          id: `city-${Math.random().toString(36).substring(2, 9)}`,
          formId: `country-state-form`,
          type: "CITY",
          label: "City",
          required: true,
          width: "full",
          order: 2,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Postal code field
        {
          id: `postal-code-${Math.random().toString(36).substring(2, 9)}`,
          formId: `country-state-form`,
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          width: "full",
          order: 3,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return (
      <div className="space-y-8">
        <h3 className="text-lg font-semibold mb-2">
          Country-State Relationship
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          This demonstrates how the STATE field options change based on the
          selected COUNTRY. Try selecting different countries to see the
          state/province options update. The state field will be hidden for
          countries that don't have states (e.g., Vatican City).
        </p>
        <div>
          {(() => {
            const { values, handleChange } = useFormState({});
            return (
              <>
                <EmbeddedForm
                  form={form}
                  values={values}
                  onChange={handleChange}
                />
                <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
                  <h3 className="text-sm font-medium mb-2">
                    Current Form Values:
                  </h3>
                  <pre className="text-xs overflow-auto max-h-40">
                    {JSON.stringify(values, null, 2)}
                  </pre>
                </div>
              </>
            );
          })()}
        </div>
      </div>
    );
  },
};
