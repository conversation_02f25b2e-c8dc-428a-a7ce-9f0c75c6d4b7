// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { withTheme } from "../storybook/ThemeDecorator";
import { themeFixtures } from "../builder/theme-fixtures";
import { EmbeddedForm } from "../submission/EmbeddedForm";
import type { Form, FormField, FieldIdentifier } from "../builder/types";
import { useFormState } from "./useFormState";
import { PREDEFINED_FIELDS } from "../builder/fieldTypes";
import { FORM_CHROMATIC_PARAMS } from "@/config/chromatic-test-settings";

// Helper to get field label
function getFieldLabel(fieldType: FieldIdentifier): string | null {
  return PREDEFINED_FIELDS[fieldType]?.defaultLabel || null;
}

// Helper to get field placeholder
function getFieldPlaceholder(fieldType: FieldIdentifier): string {
  return PREDEFINED_FIELDS[fieldType]?.placeholder || "";
}

// Create a test form with a specific field type
const createFieldForm = (
  fieldType: FieldIdentifier,
  theme: any,
  options: { width?: "full" | "half"; required?: boolean } = {},
): Form => {
  const formId = `field-form-${Math.random().toString(36).substring(2, 9)}`;

  const field: FormField = {
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: options.required ?? true,
    width: options.width ?? "full",
    order: 0,
    rowBreakAfter: false,
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return {
    id: formId,
    allowedDomains: [],
    redirectUrl: null,
    name: `${fieldType} Field Form`,
    description: `Form with a single ${fieldType} field`,
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      ...theme,
      id: "test-theme-id",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields: [field],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// FormWithState component for displaying the form with state management
const FormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Mobile form version
const MobileFormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "320px", margin: "0 auto" }}>
        <EmbeddedForm
          form={form}
          values={values}
          onChange={handleChange}
          isMobileGuess={true}
          testForceMobile={true}
        />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Story configuration
const meta: Meta = {
  title: "Forms/Fields/EventDate",
  component: EmbeddedForm,
  parameters: {
    layout: "padded",
  },
};

export default meta;

type Story = StoryObj;

// Event Type field
export const EventTypeField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard version
    const standardForm = createFieldForm("EVENT_TYPE", theme);

    // Mobile version
    const mobileForm = createFieldForm("EVENT_TYPE", theme);
    mobileForm.fields[0].label = "Event Type (Mobile View)";

    return (
      <div className="space-y-12">
        <div>
          <h2 className="text-xl font-semibold mb-6">
            Standard Event Type Field
          </h2>
          <FormWithState form={standardForm} />
        </div>

        <div className="border-t pt-8">
          <h2 className="text-xl font-semibold mb-6">
            Mobile Event Type Field
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            This example shows how the Event Type field appears on mobile
            devices.
          </p>
          <MobileFormWithState form={mobileForm} />
        </div>
      </div>
    );
  },
};

// Date Fields
export const DateFields: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard versions
    const startDateForm = createFieldForm("START_DATE", theme);
    const endDateForm = createFieldForm("END_DATE", theme);

    // Mobile versions
    const mobileStartDateForm = createFieldForm("START_DATE", theme);
    mobileStartDateForm.fields[0].label = "Start Date (Mobile View)";

    const mobileEndDateForm = createFieldForm("END_DATE", theme);
    mobileEndDateForm.fields[0].label = "End Date (Mobile View)";

    return (
      <div className="space-y-12">
        <div>
          <h2 className="text-xl font-semibold mb-6">Standard Date Fields</h2>
          <div className="space-y-8">
            <FormWithState form={startDateForm} />
            <FormWithState form={endDateForm} />
          </div>
        </div>

        <div className="border-t pt-8">
          <h2 className="text-xl font-semibold mb-6">Mobile Date Fields</h2>

          <div className="space-y-8">
            <div>
              <p className="text-sm text-gray-600 mb-4">
                This example shows how the Start Date field appears on mobile
                devices.
              </p>
              <MobileFormWithState form={mobileStartDateForm} />
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-4">
                This example shows how the End Date field appears on mobile
                devices.
              </p>
              <MobileFormWithState form={mobileEndDateForm} />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

// Flexible Dates field
export const FlexibleDatesField: Story = {
  decorators: [withTheme("default")],
  render: () => {
    const form = createFieldForm("FLEXIBLE_DATES", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};
