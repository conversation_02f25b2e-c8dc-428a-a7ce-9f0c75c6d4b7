/* File Upload Field Styles */
.ef-file-upload {
  border: 2px dashed var(--ef-border);
  border-radius: var(--ef-input-radius);
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-color: var(--ef-field-background);
  position: relative;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.ef-file-upload.active {
  border-color: var(--ef-primary);
  background-color: rgba(var(--ef-primary-rgb), 0.05);
}

.ef-file-upload.error {
  border-color: #dc2626;
  background-color: #fef2f2;
}

.ef-file-upload-prompt {
  margin-bottom: 1rem;
}

.ef-file-upload-prompt p {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--ef-text);
}

.ef-file-upload-prompt small {
  color: var(--ef-placeholder);
  font-size: 0.75rem;
}

.ef-file-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.ef-file-upload-loading p {
  margin: 0.5rem 0;
  color: var(--ef-text);
}

.ef-file-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
  width: 100%;
  max-width: 500px;
  text-align: left;
}

.ef-file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: rgba(var(--ef-primary-rgb), 0.1);
  border-radius: var(--ef-input-radius);
  font-size: 0.875rem;
}

.ef-file-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0.5rem;
}

.ef-file-remove {
  background: transparent;
  border: none;
  color: var(--ef-text);
  font-size: 1.25rem;
  line-height: 1;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.ef-file-remove:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.ef-file-errors {
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
  width: 100%;
  max-width: 500px;
  text-align: left;
}

.ef-file-error-item {
  color: #dc2626;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: #fef2f2;
  border-radius: var(--ef-input-radius);
} 