// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { withTheme } from "../storybook/ThemeDecorator";
import { themeFixtures } from "../builder/theme-fixtures";
import { EmbeddedForm } from "../submission/EmbeddedForm";
import type { Form } from "../builder/types";
import { useFormState } from "./useFormState";
import {
  FORM_CHROMATIC_PARAMS,
  DEFAULT_STORY_PARAMETERS,
  MOBILE_CHROMATIC_PARAMS,
} from "@/config/chromatic-test-settings";
import { formFixtures } from "../builder/form-fixtures";
import { expect, userEvent, within } from "@storybook/test";
import { screen } from "@testing-library/react";

// FormWithState component for displaying the form with state management
const FormWithState = ({
  form,
  initialValues = {},
  id = "form-container",
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
  id?: string;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div data-testid={id}>
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre
          data-testid="form-values"
          className="text-xs overflow-auto max-h-40"
        >
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Mobile version
const MobileFormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "320px", margin: "0 auto" }}>
        <EmbeddedForm
          form={form}
          values={values}
          onChange={handleChange}
          isMobileGuess={true}
          testForceMobile={true}
        />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Story configuration
const meta: Meta = {
  title: "Forms/Complete Forms",
  component: EmbeddedForm,
  parameters: {
    ...DEFAULT_STORY_PARAMETERS,
    layout: "padded",
    // Disable snapshots by default - we'll only enable for specific stories
    chromatic: { disableSnapshot: true },
  },
};

export default meta;

type Story = StoryObj;

// Only keeping the Paradise Point theme story to reduce test count
export const ParadisePointEmpty: Story = {
  parameters: {
    // Keep this as our primary form test
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = {
      ...formFixtures.paradisePoint,
      id: `paradise-point-${Math.random().toString(36).substring(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      theme: {
        ...formFixtures.paradisePoint.theme,
        id: "paradise-point-theme-id",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };

    return <FormWithState form={form} id="empty-form" />;
  },
};

// Keep mobile form test
export const ParadisePointMobileForm: Story = {
  parameters: {
    // Only test at mobile viewport sizes
    chromatic: MOBILE_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = {
      ...formFixtures.paradisePoint,
      id: `paradise-point-${Math.random().toString(36).substring(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      theme: {
        ...formFixtures.paradisePoint.theme,
        id: "paradise-point-theme-id",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };

    return <MobileFormWithState form={form} />;
  },
};

// Disable default theme form to reduce test count
export const DefaultThemeForm: Story = {
  parameters: {
    // Disable snapshots for this story to reduce test count
    chromatic: { disableSnapshot: true },
  },
  decorators: [withTheme("default")],
  render: () => {
    const formId = `default-theme-form-${Math.random().toString(36).substring(2, 9)}`;
    // Create a form with all necessary fields and default theme
    const form: Form = {
      id: formId,
      name: "Default Theme Example",
      description: "This form demonstrates our default theme",
      propertyId: "property-id",
      themeId: "default-theme-id",
      theme: {
        id: "default-theme-id",
        ...themeFixtures.default,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        // Event Type
        {
          id: `event-type-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EVENT_TYPE",
          label: "Event Type",
          required: true,
          width: "full",
          order: 0,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Event Name
        {
          id: `event-name-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EVENT_NAME",
          label: "Event Name",
          required: true,
          width: "full",
          order: 1,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // First name
        {
          id: `first-name-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "FIRST_NAME",
          label: "First Name",
          required: true,
          width: "half",
          order: 2,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Last name
        {
          id: `last-name-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "LAST_NAME",
          label: "Last Name",
          required: true,
          width: "half",
          order: 3,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Company
        {
          id: `company-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "COMPANY",
          label: "Organization Name",
          required: false,
          width: "full",
          order: 4,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Email
        {
          id: `email-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EMAIL",
          label: "Email Address",
          required: true,
          width: "half",
          order: 5,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Phone
        {
          id: `phone-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "half",
          order: 6,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Country
        {
          id: `country-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "COUNTRY",
          label: "Country",
          required: true,
          width: "full",
          order: 7,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // City
        {
          id: `city-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "CITY",
          label: "City",
          required: true,
          width: "half",
          order: 8,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // State
        {
          id: `state-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "STATE",
          label: "State/Province",
          required: true,
          width: "half",
          order: 9,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Postal Code
        {
          id: `postal-code-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          width: "half",
          order: 10,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Event Date Range
        {
          id: `event-date-range-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          width: "full",
          order: 11,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Start Date
        {
          id: `start-date-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "START_DATE",
          label: "Start Date",
          required: true,
          width: "half",
          order: 12,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // End Date
        {
          id: `end-date-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "END_DATE",
          label: "End Date",
          required: true,
          width: "half",
          order: 13,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Flexible Dates
        {
          id: `flexible-dates-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "FLEXIBLE_DATES",
          label: "Are your dates flexible?",
          required: false,
          width: "full",
          order: 14,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Guest Count
        {
          id: `guest-count-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "GUEST_COUNT",
          label: "Number of Guests",
          required: true,
          width: "half",
          order: 15,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Room Count
        {
          id: `room-count-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "ROOM_COUNT",
          label: "Number of Rooms",
          required: false,
          width: "half",
          order: 16,
          rowBreakAfter: false,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Meal Count
        {
          id: `meal-count-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "MEAL_COUNT",
          label: "Number of Meals",
          required: false,
          width: "half",
          order: 17,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Budget
        {
          id: `budget-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "BUDGET",
          label: "Budget",
          required: false,
          width: "full",
          order: 18,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Event Needs
        {
          id: `event-needs-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EVENT_NEEDS",
          label: "Event Needs",
          required: false,
          width: "full",
          order: 19,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Event Description
        {
          id: `event-description-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "EVENT_DESCRIPTION",
          label: "Tell Us More",
          required: false,
          width: "full",
          order: 20,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        // Marketing Consent
        {
          id: `marketing-consent-${Math.random().toString(36).substring(2, 9)}`,
          formId,
          type: "MARKETING_CONSENT",
          label: "Marketing Consent",
          required: false,
          width: "full",
          order: 21,
          rowBreakAfter: true,
          hideForEventTypes: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return <FormWithState form={form} />;
  },
};

// Paradise Point Form with Interaction Test
export const ParadisePointFilledOut: Story = {
  parameters: {
    // Explicitly disable snapshots for this story with animations/interactions
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = {
      ...formFixtures.paradisePoint,
      id: `paradise-point-${Math.random().toString(36).substring(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      theme: {
        ...formFixtures.paradisePoint.theme,
        id: "paradise-point-theme-id",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };

    return <FormWithState form={form} id="filled-form" />;
  },
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    // Wait for a key element to be visible, confirming the form is ready
    await canvas.findByTestId("input-EVENT_TYPE");

    // Fill out EVENT_TYPE
    await step("Select Event Type", async () => {
      // Find select element by test ID
      const eventTypeSelect = canvas.getByTestId("input-EVENT_TYPE");
      await userEvent.selectOptions(eventTypeSelect, "wedding");
    });

    // Fill out COMPANY field
    await step("Enter Company Name", async () => {
      const companyField = canvas.getByLabelText("Organization Name", {
        exact: false,
      });
      await userEvent.type(companyField, "Acme Corp", { delay: 10 });
    });

    // Fill out name fields
    await step("Enter First and Last Name", async () => {
      const firstNameField = canvas.getByLabelText("First Name", {
        exact: false,
      });
      await userEvent.type(firstNameField, "Jane", { delay: 10 });

      const lastNameField = canvas.getByLabelText("Last Name", {
        exact: false,
      });
      await userEvent.type(lastNameField, "Smith", { delay: 10 });
    });

    // Fill out contact information
    await step("Enter Contact Info", async () => {
      const emailField = canvas.getByLabelText("Email Address", {
        exact: false,
      });
      await userEvent.type(emailField, "<EMAIL>", { delay: 10 });

      // Use data-testid to find phone input directly (same as in EmbeddedForm.test.tsx)
      const phoneInput = canvas.getByTestId("input-PHONE");
      await userEvent.type(phoneInput, "5551234567", { delay: 10 });
    });

    // Fill out location
    await step("Enter Location", async () => {
      // For the country, we'll use the intl-tel-input country selector
      // which is already set to US by default for the phone field
      // so we can skip explicitly setting the country

      const countryField = canvas.getByTestId("input-country", {
        exact: false,
      });
      await userEvent.click(countryField);
      const countryOption = await screen.findByTestId(`COUNTRY-option-US`);
      await userEvent.click(countryOption);

      const cityField = canvas.getByLabelText("City", { exact: false });
      await userEvent.type(cityField, "San Francisco", { delay: 10 });

      // For the state field, we'll use a direct approach
      // First, find the STATE field by its label
      const stateField = canvas.getByLabelText("State/Province", {
        exact: false,
      });
      await userEvent.click(stateField);

      const stateOption = await screen.findByTestId(`STATE-option-CA`);
      await userEvent.click(stateOption);

      const postalField = canvas.getByLabelText("Postal Code", {
        exact: false,
      });
      await userEvent.type(postalField, "94105", { delay: 10 });
    });

    // Fill out budget
    await step("Enter Budget", async () => {
      const budgetField = canvas.getByLabelText("Budget", { exact: false });
      await userEvent.type(budgetField, "25000", { delay: 10 });
    });

    // Fill out dates
    await step("Select Dates", async () => {
      // Find date fields by their test IDs
      const startDateField = canvas.getByTestId("input-START_DATE");
      await userEvent.clear(startDateField);
      await userEvent.type(startDateField, "06/15/2024", { delay: 10 });

      const endDateField = canvas.getByTestId("input-END_DATE");
      await userEvent.clear(endDateField);
      await userEvent.type(endDateField, "06/18/2024", { delay: 10 });

      // Click away to dismiss any date pickers
      await userEvent.click(document.body);
    });

    // Check flexible dates
    await step("Check Flexible Dates", async () => {
      try {
        const flexibleDatesField = canvas.getByLabelText(
          "Are your dates flexible?",
          { exact: false },
        );
        await userEvent.click(flexibleDatesField);
      } catch (e) {
        // If direct label matching fails, try finding it by checkbox label
        const flexibleDatesField = canvas.getByText("Yes", {
          selector: "label",
        });
        await userEvent.click(flexibleDatesField);
      }
    });

    // Fill out counts
    await step("Enter Counts", async () => {
      const guestCountField = canvas.getByLabelText("Number of Guests", {
        exact: false,
      });
      await userEvent.type(guestCountField, "125", { delay: 10 });

      const roomCountField = canvas.getByLabelText("Number of Rooms Needed", {
        exact: false,
      });
      await userEvent.type(roomCountField, "50", { delay: 10 });
    });

    // Select event needs
    await step("Select Event Needs", async () => {
      // Event needs are rendered as checkboxes, not a dropdown
      // Find checkboxes by their label text
      const rehearsalCheckbox = canvas.getByLabelText("Rehearsal Dinner", {
        exact: false,
      });
      await userEvent.click(rehearsalCheckbox);

      const receptionCheckbox = canvas.getByLabelText("Wedding Reception", {
        exact: false,
      });
      await userEvent.click(receptionCheckbox);
    });

    // Enter event description
    await step("Enter Event Description", async () => {
      try {
        const descriptionField = canvas.getByLabelText("Tell Us More", {
          exact: false,
        });
        await userEvent.type(
          descriptionField,
          "This is a test event description with details about our requirements.",
          { delay: 10 },
        );
      } catch (e) {
        // If direct label matching fails, try finding by element type
        const textareas = canvasElement.querySelectorAll("textarea");
        if (textareas.length > 0) {
          await userEvent.type(
            textareas[0],
            "This is a test event description with details about our requirements.",
            { delay: 10 },
          );
        }
      }
    });

    // Check marketing consent checkbox
    await step("Check Marketing Consent", async () => {
      // Try to find by role and type
      const marketingConsentField = canvas.getByRole("checkbox", {
        name: /marketing/i,
      });
      await userEvent.click(marketingConsentField);
    });

    // Wait to ensure all interactions are complete before snapshot
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Verify values appear in the form values display
    const formValues = canvas.getByTestId("form-values");
    await expect(JSON.parse(formValues.textContent ?? "{}")).toEqual(
      JSON.parse(`{
  "EVENT_TYPE": "wedding",
  "COMPANY": "Acme Corp",
  "FIRST_NAME": "Jane",
  "LAST_NAME": "Smith",
  "EMAIL": "<EMAIL>",
  "COUNTRY": "US",
  "CITY": "San Francisco",
  "STATE": "CA",
  "COUNTRY": "US",
  "POSTAL_CODE": "94105",
  "BUDGET": "25000",
  "FLEXIBLE_DATES": "true",
  "GUEST_COUNT": "125",
  "ROOM_COUNT": "50",
  "EVENT_DESCRIPTION": "This is a test event description with details about our requirements.",
  "MARKETING_CONSENT": "true"
}`),
    );
  },
};
