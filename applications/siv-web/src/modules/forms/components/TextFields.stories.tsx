// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { withTheme } from "../storybook/ThemeDecorator";
import { themeFixtures } from "../builder/theme-fixtures";
import { EmbeddedForm } from "../submission/EmbeddedForm";
import type { Form, FormField, FieldIdentifier } from "../builder/types";
import { useFormState } from "./useFormState";
import { PREDEFINED_FIELDS } from "../builder/fieldTypes";
import {
  DEFAULT_CHROMATIC_PARAMS,
  FORM_CHROMATIC_PARAMS,
} from "@/config/chromatic-test-settings";
import { undefined } from "zod";
import { fieldType } from "@/drizzle/schema";

// Helper to get field label
function getFieldLabel(fieldType: FieldIdentifier): string | null {
  return PREDEFINED_FIELDS[fieldType]?.defaultLabel || null;
}

// Helper to get field placeholder
function getFieldPlaceholder(fieldType: FieldIdentifier): string {
  return PREDEFINED_FIELDS[fieldType]?.placeholder || "";
}

// Create a test form with a specific field type
const createFieldForm = (
  fieldType: FieldIdentifier,
  theme: any,
  options: { width?: "full" | "half"; required?: boolean } = {},
): Form => {
  const formId = `field-form-${Math.random().toString(36).substring(2, 9)}`;

  const field: FormField = {
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: options.required ?? true,
    width: options.width ?? "full",
    order: 0,
    rowBreakAfter: false,
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return {
    allowedDomains: [],
    redirectUrl: null,
    id: formId,
    name: `${fieldType} Field Form`,
    description: `Form with a single ${fieldType} field`,
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      ...theme,
      id: "test-theme-id",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields: [field],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// FormWithState component for displaying the form with state management
const FormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div>
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Story configuration
const meta: Meta = {
  title: "Forms/Fields/Text",
  component: EmbeddedForm,
  parameters: {
    layout: "padded",
  },
};

export default meta;

type Story = StoryObj;

// Field stories for text fields
export const FirstNameField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("FIRST_NAME", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const LastNameField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("LAST_NAME", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const EmailField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("EMAIL", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const CompanyField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("COMPANY", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const CityField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("CITY", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const StateField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("STATE", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

export const PostalCodeField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("POSTAL_CODE", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};
