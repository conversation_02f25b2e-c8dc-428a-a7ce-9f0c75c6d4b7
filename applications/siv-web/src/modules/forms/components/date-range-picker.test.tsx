import { render, screen, within, fireEvent } from "@testing-library/react";
import { DateRangePicker } from "./date-range-picker";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import { format } from "date-fns";

// Mock the client-side detection module
vi.mock("@/lib/client-mobile-detection", () => ({
  clientSideDetectIsMobile: vi.fn(),
}));

import { clientSideDetectIsMobile } from "@/lib/client-mobile-detection";

// Helper function to find a date button in a specific month
async function selectDateFromCalendar(day: string) {
  const calendar = screen.getByRole("dialog");
  const dateCell = within(calendar).getByRole("gridcell", { name: day });
  const dateButton = within(dateCell).getByRole("button");
  await userEvent.click(dateButton);
}

describe("DateRangePicker", () => {
  beforeEach(() => {
    // Mock current date to ensure consistent testing
    vi.setSystemTime(new Date("2025-02-01"));
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe("Mobile Detection", () => {
    it("renders mobile view when server guesses mobile and client confirms", () => {
      (clientSideDetectIsMobile as any).mockReturnValue(true);

      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={true}
        />,
      );

      expect(screen.getByTestId("start-date-input")).toBeInTheDocument();
      expect(screen.getByTestId("end-date-input")).toBeInTheDocument();
      expect(clientSideDetectIsMobile).toHaveBeenCalled();
    });

    it("switches to desktop view when server guesses mobile but client detects desktop", () => {
      (clientSideDetectIsMobile as any).mockReturnValue(false);

      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={true}
        />,
      );

      expect(
        screen.getByTestId("date-range-picker-button"),
      ).toBeInTheDocument();
      expect(clientSideDetectIsMobile).toHaveBeenCalled();
    });

    it("switches to mobile view when server guesses desktop but client detects mobile", () => {
      (clientSideDetectIsMobile as any).mockReturnValue(true);

      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={false}
        />,
      );

      expect(screen.getByTestId("start-date-input")).toBeInTheDocument();
      expect(screen.getByTestId("end-date-input")).toBeInTheDocument();
      expect(clientSideDetectIsMobile).toHaveBeenCalled();
    });
  });

  describe("Desktop View", () => {
    beforeEach(() => {
      (clientSideDetectIsMobile as any).mockReturnValue(false);
    });

    it("renders date range picker button", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={false}
        />,
      );

      expect(
        screen.getByTestId("date-range-picker-button"),
      ).toBeInTheDocument();
      expect(screen.getByText("Pick a date range")).toBeInTheDocument();
    });

    it("displays formatted date range when dates are selected", () => {
      const startDate = new Date("2025-03-20T12:00:00");
      const endDate = new Date("2025-03-21T12:00:00");

      render(
        <DateRangePicker
          initialDateRange={[startDate, endDate]}
          isMobileGuess={false}
        />,
      );

      expect(
        screen.getByText("Mar 20, 2025 - Mar 21, 2025"),
      ).toBeInTheDocument();
    });

    it("opens calendar when button is clicked", async () => {
      const user = userEvent.setup();

      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={false}
        />,
      );

      const button = screen.getByTestId("date-range-picker-button");
      await user.click(button);

      // Calendar should be visible
      expect(screen.getByRole("dialog")).toBeInTheDocument();
    });

    it("displays validation errors below the button", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          errors={{
            START_DATE: ["Start date is required"],
            END_DATE: ["End date must be after start date"],
          }}
          isMobileGuess={false}
        />,
      );

      expect(screen.getByText("Start date is required")).toBeInTheDocument();
      expect(
        screen.getByText("End date must be after start date"),
      ).toBeInTheDocument();
      expect(screen.getByTestId("date-range-picker-button")).toHaveClass(
        "!border-destructive",
      );
    });

    it("displays deduplicated errors when _deduplicated property is provided", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          errors={{
            START_DATE: ["Date is required", "Date must be valid"],
            END_DATE: ["Date is required", "Date must be valid"],
            _deduplicated: ["Date is required", "Date must be valid"],
          }}
          isMobileGuess={false}
        />,
      );

      // Should only show the deduplicated errors once each, not the individual field errors
      const errorMessages = screen.getAllByText(
        /Date is required|Date must be valid/,
      );
      expect(errorMessages).toHaveLength(2);
      expect(screen.getByText("Date is required")).toBeInTheDocument();
      expect(screen.getByText("Date must be valid")).toBeInTheDocument();
      expect(screen.getByTestId("date-range-picker-button")).toHaveClass(
        "!border-destructive",
      );
    });

    it("falls back to displaying individual errors when _deduplicated is not provided", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          errors={{
            START_DATE: ["Start date is required"],
            END_DATE: ["End date must be valid"],
          }}
          isMobileGuess={false}
        />,
      );

      // Should show both individual field errors
      expect(screen.getByText("Start date is required")).toBeInTheDocument();
      expect(screen.getByText("End date must be valid")).toBeInTheDocument();
      expect(screen.getByTestId("date-range-picker-button")).toHaveClass(
        "!border-destructive",
      );
    });
  });

  describe("Mobile View", () => {
    beforeEach(() => {
      (clientSideDetectIsMobile as any).mockReturnValue(true);
    });

    it("renders start and end date inputs", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={true}
        />,
      );

      expect(screen.getByTestId("start-date-input")).toBeInTheDocument();
      expect(screen.getByTestId("end-date-input")).toBeInTheDocument();
    });

    it("shows required indicator when required prop is true", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          required={true}
          isMobileGuess={true}
        />,
      );

      const startDateLabel = screen.getByTestId("start-date-label");
      const endDateLabel = screen.getByTestId("end-date-label");
      expect(startDateLabel).toHaveTextContent("*");
      expect(endDateLabel).toHaveTextContent("*");
    });

    it("submits correct date values with properly named fields", () => {
      render(
        <DateRangePicker
          initialDateRange={[undefined, undefined]}
          isMobileGuess={true}
        />,
      );

      // Get the date inputs
      const startDateInput = screen.getByTestId(
        "start-date-input",
      ) as HTMLInputElement;
      const endDateInput = screen.getByTestId(
        "end-date-input",
      ) as HTMLInputElement;

      // Verify the input names are correctly set
      expect(startDateInput.name).toBe("START_DATE_DISPLAY");
      expect(endDateInput.name).toBe("END_DATE_DISPLAY");

      // Set values using fireEvent for simplicity
      fireEvent.change(startDateInput, { target: { value: "2025-03-15" } });
      fireEvent.change(endDateInput, { target: { value: "2025-03-20" } });

      // Get the hidden inputs
      const startDateHidden = document.querySelector(
        'input[type="hidden"][name="START_DATE"]',
      ) as HTMLInputElement;
      const endDateHidden = document.querySelector(
        'input[type="hidden"][name="END_DATE"]',
      ) as HTMLInputElement;

      // Verify the hidden inputs have the correct ISO string values
      expect(startDateHidden).not.toBeNull();
      expect(endDateHidden).not.toBeNull();
      expect(startDateHidden.value).not.toBe("");
      expect(endDateHidden.value).not.toBe("");

      // Parse the dates to verify they match the input values
      const parsedStartDate = new Date(startDateHidden.value);
      const parsedEndDate = new Date(endDateHidden.value);

      expect(format(parsedStartDate, "yyyy-MM-dd")).toBe("2025-03-15");
      expect(format(parsedEndDate, "yyyy-MM-dd")).toBe("2025-03-20");
    });
  });
});
