// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { EmbeddedForm } from "../submission/EmbeddedForm";
import type { FieldIdentifier, Form, FormField } from "../builder/types";
import { PREDEFINED_FIELDS } from "../builder/fieldTypes";
import { fieldType } from "@/drizzle/schema";
import { useFormState } from "./useFormState";
import { formFixtures } from "../builder/form-fixtures";
import {
  DEFAULT_CHROMATIC_PARAMS,
  FORM_CHROMATIC_PARAMS,
} from "@/config/chromatic-test-settings";
import { themeFixtures } from "../builder/theme-fixtures";

// Array of field types we'll use for forms
const FIELD_TYPES: FieldIdentifier[] = [
  "EVENT_TYPE",
  "FIRST_NAME",
  "LAST_NAME",
  "COMPANY",
  "EMAIL",
  "PHON<PERSON>",
  "CITY",
  "STATE",
  "POS<PERSON>L_CODE",
  "START_DATE",
  "END_DATE",
  "GUEST_COUNT",
  "ROOM_COUNT",
  "MEAL_COUNT",
  "BUDGET",
  "EVENT_DESCRIPTION",
  "FLEXIBLE_DATES",
  "EVENT_NEEDS",
  "MARKETING_CONSENT",
  "EVENT_NAME",
  "EVENT_DATE_RANGE",
  "COUNTRY",
];

// Get a label for a field type
function getFieldLabel(fieldType: FieldIdentifier): string | null {
  return PREDEFINED_FIELDS[fieldType]?.defaultLabel || null;
}

// Get a placeholder for a field type
function getFieldPlaceholder(fieldType: FieldIdentifier): string {
  return PREDEFINED_FIELDS[fieldType]?.placeholder || "";
}

// Create a complete form with all field types
const createCompleteForm = (theme: any): Form => {
  const formId = `theme-form-${Math.random().toString(36).substring(2, 9)}`;

  const fieldTypes = FIELD_TYPES;

  const fields: FormField[] = fieldTypes.map((fieldType, index) => ({
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: index % 2 === 0, // Alternate required/optional
    width: index % 3 === 0 ? "full" : "half", // Mix of full and half width
    order: index,
    rowBreakAfter: index % 4 === 3, // Add row break after every 4th field
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  }));

  return {
    id: formId,
    name: `${theme.name} Form`,
    description: `This form demonstrates the ${theme.name} theme`,
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: theme,
    fields,
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// Create a FormWithState component similar to the one in FormFields.stories.tsx
const FormWithState = ({
  form,
  initialValues = {},
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File>;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  return (
    <div className="w-full">
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
        <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
          <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
          <pre className="text-xs overflow-auto max-h-40">
            {JSON.stringify(values, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

const meta: Meta = {
  title: "Forms/Themes",
  component: EmbeddedForm,
  parameters: {
    layout: "padded",
  },
};

export default meta;

type Story = StoryObj;

export const DefaultTheme: Story = {
  render: () => (
    <FormWithState form={createCompleteForm(themeFixtures.default)} />
  ),
};

export const DarkCorporateTheme: Story = {
  render: () => (
    <FormWithState form={createCompleteForm(themeFixtures.darkCorporate)} />
  ),
};

// Show a form with the Paradise Point theme
export const ParadisePointTheme: Story = {
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    return <FormWithState form={formFixtures.paradisePoint} />;
  },
};

export const LuxuryHospitalityTheme: Story = {
  render: () => (
    <FormWithState form={createCompleteForm(themeFixtures.luxuryHospitality)} />
  ),
};

// Default theme with visual testing
export const DefaultThemeVisualTesting: Story = {
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => (
    <FormWithState form={createCompleteForm(themeFixtures.default)} />
  ),
};

// Responsive testing across different devices
export const ResponsiveFormWithTheme: Story = {
  parameters: {
    chromatic: DEFAULT_CHROMATIC_PARAMS,
  },
  render: () => (
    <FormWithState form={createCompleteForm(themeFixtures.luxuryHospitality)} />
  ),
};
