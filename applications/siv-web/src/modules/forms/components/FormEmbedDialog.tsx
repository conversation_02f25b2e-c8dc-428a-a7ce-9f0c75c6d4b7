import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { CopyIcon } from "lucide-react";
import { toast } from "sonner";
import type { Form } from "../builder/types";
import { EmbeddedForm } from "../submission/EmbeddedForm";

interface FormEmbedDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  form: Form | null;
  publicFormRoutesBaseUrl: string;
}

export const FormEmbedDialog: React.FC<FormEmbedDialogProps> = ({
  open,
  onOpenChange,
  form,
  publicFormRoutesBaseUrl,
}) => {
  if (!form) return null;

  const [formHeight, setFormHeight] = useState(500); // Default height while measuring
  const formRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (formRef.current) {
      // Add a small buffer for padding and to prevent scrolling
      const measuredHeight = formRef.current.offsetHeight + 40;
      setFormHeight(measuredHeight);
    }
  }, [form]); // Re-measure if form changes

  // Generate the embed code with proper indentation
  const formUrl = `${publicFormRoutesBaseUrl}/forms/${form.id}`;
  const embedCode = `<iframe
    data-siv-form-id="${form.id}"
    src="${formUrl}"
    width="100%"
    height="${formHeight}px"
    style="border: none; width: 100%; max-width: 600px;"
    title="${form.name}"
></iframe>
<script src="https://sivform-cdn.sivconverts.com/latest/siv-form-embed.iife.js"></script>`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      toast.success("Copied!", {
        description: "Embed code copied to clipboard",
        duration: 2000,
      });
    } catch (err) {
      console.error("Failed to copy text: ", err);
      toast.error("Failed to copy", {
        description: "Please try again",
        duration: 2000,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Embed Form Code</DialogTitle>
          <DialogDescription>
            Copy and paste this code into your website where you want the form
            to appear.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="relative">
            <Textarea
              value={embedCode}
              readOnly
              className="font-mono text-sm h-[200px] resize-none"
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="absolute right-2 top-4"
              onClick={copyToClipboard}
              aria-label="Copy embed code to clipboard"
            >
              <CopyIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Hidden form for measuring height */}
      <div
        ref={formRef}
        style={{
          position: "fixed",
          left: "-9999px",
          top: "-9999px",
          width: "600px",
        }}
        aria-hidden="true"
      >
        <EmbeddedForm form={form} />
      </div>
    </Dialog>
  );
};
