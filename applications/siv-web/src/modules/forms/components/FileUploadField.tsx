import React, { use<PERSON>allback, useEffect, useState } from "react";
import { FileRejection, useDropzone } from "react-dropzone";
import { v4 as uuidv4 } from "uuid";
import { hc } from "hono/client";
import type { PublicFormRoutes } from "../submission/public-form-routes";

export interface FileData {
  fileId: string;
  fileName: string;
  fileSize: string;
  fileType: string;
  uploadKey: string; // S3 key for the uploaded file
  keyWithoutStatusPrefix: string; // S3 key for the uploaded file
  preview?: string; // For image previews (client-side only)
  uploading?: boolean;
  uploaded?: boolean;
  error?: boolean;
  errorMessage?: string;
}

// Single URL response type as returned by the API
interface SingleUrlResponse {
  fileId: string;
  presignedUrl: string;
  key: string;
  uploadGroupId: string;
}

// Array response type for backward compatibility
interface ArrayResponse {
  uploadId: string;
  urls: Array<{
    fileId: string;
    presignedUrl: string;
    key: string;
    uploadId: string;
  }>;
}

// Type guard for error response
function isErrorResponse(
  response: any,
): response is { error: string; details?: string } {
  return response && typeof response === "object" && "error" in response;
}

// Type guard for checking if response is a single URL
function isSingleUrlResponse(response: any): response is SingleUrlResponse {
  return (
    response &&
    typeof response === "object" &&
    "fileId" in response &&
    "presignedUrl" in response &&
    "key" in response
  );
}

// Type guard for checking if response has urls array
function isArrayResponse(response: any): response is ArrayResponse {
  return (
    response &&
    typeof response === "object" &&
    "urls" in response &&
    Array.isArray(response.urls)
  );
}

// Helper function to format file size
const formatFileSize = (bytes: string | number) => {
  // Convert to number if it's a string
  const size = typeof bytes === "string" ? parseInt(bytes, 10) : bytes;

  if (size < 1024) return size + " bytes";
  else if (size < 1048576) return (size / 1024).toFixed(1) + " KB";
  else return (size / 1048576).toFixed(1) + " MB";
};

interface FileUploadFieldProps {
  name: string;
  label: string;
  required: boolean;
  value: FileData[];
  onChange: (files: FileData[] | any) => void;
  error: string | null;
  accept?: Record<string, string[]>;
  maxFiles?: number;
  maxSize?: number;
  formId: string; // Form ID for presigned URL generation - REQUIRED
  [key: string]: any; // For passing additional props like data-testid
}

export const FileUploadField: React.FC<FileUploadFieldProps> = ({
  name,
  label,
  required,
  value = [],
  onChange,
  error,
  accept = {
    "application/pdf": [".pdf"],
    "application/msword": [".doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
      ".docx",
    ],
    "application/vnd.ms-excel": [".xls"],
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
      ".xlsx",
    ],
    "text/csv": [".csv"],
    "application/vnd.ms-powerpoint": [".ppt"],
    "application/vnd.openxmlformats-officedocument.presentationml.presentation":
      [".pptx"],
    "text/plain": [".txt"],
    "image/jpeg": [".jpg", ".jpeg"],
    "image/png": [".png"],
    "image/gif": [".gif"],
  },
  maxFiles = 1,
  maxSize = 5 * 1024 * 1024, // 5MB
  formId,
  ...rest
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<string[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [isInitializing, setIsInitializing] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const [uploadGroupId, setUploadGroupId] = useState<string | null>(null);
  const client = hc<PublicFormRoutes>("/forms");

  // Initialize component state
  useEffect(() => {
    setIsInitializing(false);
    setUploadGroupId(uuidv4());
  }, []);

  // Check if file limit is reached
  const isFileLimitReached = useCallback(() => {
    return value.length >= maxFiles;
  }, [value.length, maxFiles]);

  // Get simplified extensions list for error messages
  const getSimplifiedExtensions = () => {
    return ".pdf, .doc, .docx, .xls, .xlsx, .csv, .jpg, .jpeg, .png, .gif";
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      // Reset any previous errors
      setUploadError(null);
      setRejectedFiles([]);

      // Handle rejected files
      if (fileRejections.length > 0) {
        setRejectedFiles(fileRejections);
        // Only show the simplified error message
        setUploadError(
          `Invalid file type. Allowed file types are: ${getSimplifiedExtensions()}`,
        );
        return;
      }

      // Check if adding these files would exceed the limit
      if (value.length + acceptedFiles.length > maxFiles) {
        const availableSlots = Math.max(0, maxFiles - value.length);
        acceptedFiles = acceptedFiles.slice(0, availableSlots);

        if (availableSlots === 0) {
          setUploadError(`Maximum of ${maxFiles} files allowed`);
          return;
        }
      }

      // Process each accepted file
      const newFilesPromises = acceptedFiles.map(async (file) => {
        try {
          // Add to uploading files
          setUploadingFiles((prev) => [...prev, file.name]);

          const fileId = uuidv4();
          const preview = file.type.startsWith("image/")
            ? URL.createObjectURL(file)
            : undefined;

          const presignUrlResponse = await client[
            ":formId"
          ].files.presign.$post({
            param: { formId },
            json: {
              uploadGroupId: uploadGroupId!!,
            },
          });

          if (!presignUrlResponse.ok) {
            const errorData = await presignUrlResponse.json();

            if (isErrorResponse(errorData)) {
              throw new Error(
                `File upload failed: ${errorData.error || "Unknown error"}`,
              );
            } else {
              throw new Error("File upload failed: Unknown error");
            }
          }

          const responseData = await presignUrlResponse.json();

          // Upload to S3 using the presigned URL with fetch
          const uploadResponse = await fetch(responseData.presignedUrl, {
            method: "PUT",
            headers: {
              "Content-Type": file.type,
              "Content-Length": String(file.size),
            },
            body: file,
          });

          if (!uploadResponse.ok) {
            throw new Error(`Upload failed: ${uploadResponse.statusText}`);
          }

          // Remove from uploading files
          setUploadingFiles((prev) =>
            prev.filter((name) => name !== file.name),
          );

          // Return file data for form
          return {
            fileId,
            fileName: file.name,
            fileSize: String(file.size),
            fileType: file.type,
            uploadKey: responseData.fullKey,
            keyWithoutStatusPrefix: responseData.keyWithoutStatusPrefix,
            preview,
            uploaded: true,
            uploading: false,
            error: false,
          };
        } catch (error) {
          console.error("File upload error:", error);
          setUploadingFiles((prev) =>
            prev.filter((name) => name !== file.name),
          );

          // Set a user-friendly error message
          return {
            fileId: uuidv4(),
            fileName: file.name,
            fileSize: String(file.size),
            fileType: file.type,
            uploadKey: "",
            uploaded: false,
            uploading: false,
            error: true,
            errorMessage:
              error instanceof Error
                ? error.message
                : "An unknown error occurred during upload",
          };
        }
      });

      // Wait for all uploads to complete
      const newFiles = await Promise.all(newFilesPromises);
      const validNewFiles = newFiles.filter(
        (file) => !file.error,
      ) as FileData[];
      const errorFiles = newFiles.filter((file) => file.error) as FileData[];

      // If there are any error files, show the first error
      if (errorFiles.length > 0) {
        setUploadError(
          errorFiles[0].errorMessage || "Failed to upload some files",
        );
      }

      // Call onChange with updated files
      onChange([...value, ...validNewFiles]);
    },
    [value, onChange, formId, maxFiles, client],
  );

  const handleRemoveFile = useCallback(
    (fileIdToRemove: string) => {
      // Remove any previews to avoid memory leaks
      const fileToRemove = value.find((file) => file.fileId === fileIdToRemove);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }

      onChange(value.filter((file) => file.fileId !== fileIdToRemove));
    },
    [value, onChange],
  );

  // Clean up previews when component unmounts
  useEffect(() => {
    return () => {
      value.forEach((file) => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [value]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept,
      maxSize,
      maxFiles: maxFiles - value.length,
      disabled:
        isInitializing || uploadingFiles.length > 0 || isFileLimitReached(),
    });

  // Extract formId from rest props to prevent passing it to DOM
  const restProps = { ...rest };
  delete restProps.formId;

  // Generate hidden inputs for form submission
  const generateHiddenFields = () => {
    return value.map((file, index) => (
      <React.Fragment key={`hidden-${file.fileId}`}>
        <input
          type="hidden"
          name={`${name}[${index}][fileId]`}
          value={file.fileId}
        />
        <input
          type="hidden"
          name={`${name}[${index}][fileName]`}
          value={file.fileName}
        />
        <input
          type="hidden"
          name={`${name}[${index}][fileSize]`}
          value={file.fileSize}
        />
        <input
          type="hidden"
          name={`${name}[${index}][fileType]`}
          value={file.fileType}
        />
        <input
          type="hidden"
          name={`${name}[${index}][uploadKey]`}
          value={file.uploadKey}
        />
        <input
          type="hidden"
          name={`${name}[${index}][keyWithoutStatusPrefix]`}
          value={file.keyWithoutStatusPrefix}
        />
      </React.Fragment>
    ));
  };

  return (
    <div className="file-upload-container" {...restProps}>
      <div
        className={`dropzone ${isDragActive ? "active" : ""} ${isDragReject || error || uploadError ? "reject" : ""} ${isFileLimitReached() ? "limit-reached" : ""}`}
        {...getRootProps()}
      >
        <input
          {...getInputProps()}
          aria-label="drag & drop files here"
          disabled={isFileLimitReached()}
        />

        {isInitializing ? (
          <div className="dropzone-loading">
            <p>Initializing upload...</p>
          </div>
        ) : uploadingFiles.length > 0 ? (
          <div className="dropzone-loading">
            <p>Uploading {uploadingFiles.length} file(s)...</p>
            <div className="upload-progress"></div>
          </div>
        ) : (
          <div className="dropzone-content">
            {isFileLimitReached() ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="limit-icon"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <p className="dropzone-text">Maximum file limit reached</p>
                <p className="dropzone-hint">
                  Remove existing files to upload more (max {maxFiles} files)
                </p>
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="upload-icon"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="17 8 12 3 7 8"></polyline>
                  <line x1="12" y1="3" x2="12" y2="15"></line>
                </svg>
                <p className="dropzone-text">
                  {isDragActive
                    ? "Drop files here..."
                    : maxFiles === 1
                      ? "Drag & Drop Your File Here"
                      : "Drag & Drop Files Here, or Click to Select Files"}
                </p>
                <p className="dropzone-hint">
                  {maxFiles === 1
                    ? `(Max ${formatFileSize(maxSize)})`
                    : `Upload up to ${maxFiles} Files (Max ${formatFileSize(maxSize)} Each)`}
                </p>
              </>
            )}
          </div>
        )}
      </div>

      {/* Error message */}
      {(error || uploadError) && (
        <div className="field-error" data-testid="upload-error">
          <p>{error || uploadError}</p>
        </div>
      )}

      {/* File list with counter */}
      {value.length > 0 && (
        <div className="file-list">
          <div className="file-counter">
            <span>
              {value.length}/{maxFiles} files
            </span>
          </div>

          {value.map((file) => (
            <div
              key={file.fileId}
              className={`file-item ${file.error ? "has-error" : ""}`}
            >
              <div className="file-preview">
                {file.preview ? (
                  <img
                    src={file.preview}
                    alt={file.fileName}
                    className="preview-image"
                  />
                ) : file.fileType.startsWith("image/") ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="file-icon"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <polyline points="21 15 16 10 5 21"></polyline>
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="file-icon"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                )}
              </div>
              <div className="file-details">
                <div className="file-name">{file.fileName}</div>
                <div className="file-size">{formatFileSize(file.fileSize)}</div>
                {file.error && (
                  <div className="file-error-message">{file.errorMessage}</div>
                )}
              </div>
              <div className="file-status">
                {file.error ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="status-icon error"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                ) : file.uploaded ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="status-icon success"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                ) : file.uploading ? (
                  <div className="upload-progress"></div>
                ) : null}
              </div>
              <button
                type="button"
                className="remove-file"
                aria-label={`Remove ${file.fileName}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveFile(file.fileId);
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="remove-icon"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Hidden form fields for submission */}
      {generateHiddenFields()}

      <style>{`
        .file-upload-container {
          font-family: var(--ef-font, system-ui, sans-serif);
          width: 100%;
          border-radius: var(--ef-form-radius, 8px);
          background-color: var(--ef-background, white);
          color: var(--ef-text, #333);
        }

        .dropzone {
          border: 2px dashed var(--ef-border, #ccc);
          border-radius: var(--ef-input-radius, 4px);
          background-color: var(--ef-field-background, #f9f9f9);
          padding: calc(var(--ef-spacing, 16px) * 2);
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .dropzone.active {
          border-color: var(--ef-primary, #4f46e5);
          background-color: rgba(79, 70, 229, 0.05);
        }

        .dropzone.reject {
          border-color: #ef4444;
          background-color: rgba(239, 68, 68, 0.05);
        }

        .dropzone.limit-reached {
          border-color: #d1d5db;
          background-color: #f3f4f6;
          opacity: 0.75;
          cursor: not-allowed;
        }

        .dropzone-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          text-align: center;
        }

        .upload-icon {
          color: var(--ef-primary, #4f46e5);
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }

        .limit-icon {
          color: #6b7280;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
        }

        .dropzone-text {
          font-size: 1rem;
          font-weight: 500;
          color: var(--ef-input-text, #333);
          margin: 0;
        }

        .dropzone-hint {
          font-size: 0.875rem;
          color: var(--ef-placeholder, #6b7280);
          margin: 0;
        }

        .dropzone-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }

        .upload-progress {
          width: 100%;
          height: 4px;
          background-color: #e5e7eb;
          border-radius: 2px;
          overflow: hidden;
          position: relative;
        }

        .upload-progress::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 50%;
          background-color: var(--ef-primary, #4f46e5);
          animation: progress 1s infinite linear;
        }

        @keyframes progress {
          0% {
            left: -50%;
          }
          100% {
            left: 100%;
          }
        }

        .file-list {
          margin-top: 16px;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .file-counter {
          display: flex;
          justify-content: flex-end;
          font-size: 0.875rem;
          color: var(--ef-placeholder, #6b7280);
          margin-bottom: 4px;
        }

        .file-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border: 1px solid var(--ef-border, #ccc);
          border-radius: var(--ef-input-radius, 4px);
          background-color: var(--ef-field-background, #f9f9f9);
          position: relative;
        }

        .file-item.has-error {
          border-color: #ef4444;
          background-color: rgba(239, 68, 68, 0.05);
        }

        .file-preview {
          width: 40px;
          height: 40px;
          margin-right: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .file-icon {
          color: var(--ef-primary, #4f46e5);
          width: 24px;
          height: 24px;
        }

        .file-details {
          flex: 1;
          min-width: 0;
        }

        .file-name {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--ef-input-text, #333);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .file-size {
          font-size: 0.75rem;
          color: var(--ef-placeholder, #6b7280);
        }

        .file-error-message {
          font-size: 0.75rem;
          color: #ef4444;
          margin-top: 2px;
        }

        .file-status {
          margin-right: 8px;
          display: flex;
          align-items: center;
        }

        .status-icon {
          flex-shrink: 0;
        }

        .status-icon.success {
          color: #10b981;
        }

        .status-icon.error {
          color: #ef4444;
        }

        .remove-file {
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--ef-placeholder, #6b7280);
          transition: color 0.2s ease;
        }

        .remove-file:hover {
          color: #ef4444;
        }

        .remove-icon {
          width: 16px;
          height: 16px;
        }

        .field-error {
          margin-top: 8px;
          color: #b91c1c;
          font-size: 0.875rem;
        }

        .field-error p {
          margin: 0;
        }
      `}</style>
    </div>
  );
};

export default FileUploadField;
