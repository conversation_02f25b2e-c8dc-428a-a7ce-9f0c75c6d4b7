import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { expect, describe, it, vi, beforeEach } from "vitest";
import userEvent from "@testing-library/user-event";
import ComboboxField, { ComboboxOption } from "./combobox-field";

// Sample options for testing
const testOptions: ComboboxOption[] = [
  { id: "apple", name: "Apple" },
  { id: "banana", name: "Banana" },
  { id: "orange", name: "Orange" },
  { id: "grape", name: "Grape" },
];

// Mock console.log to reduce noise in tests
beforeEach(() => {
  vi.spyOn(console, "log").mockImplementation(() => {});
});

describe("Combobox", () => {
  it("renders with placeholder when no value is selected", () => {
    render(
      <ComboboxField
        options={testOptions}
        value={null}
        onChange={vi.fn()}
        placeholder="Select a fruit"
        id="fruit"
        data-testid="fruit-combobox"
      />,
    );

    const input = screen.getByTestId("fruit-combobox");
    expect(input).toHaveAttribute("placeholder", "Select a fruit");
  });

  it("displays the selected value correctly", () => {
    const selectedOption = testOptions[1]; // Banana

    render(
      <ComboboxField
        options={testOptions}
        value={selectedOption}
        onChange={vi.fn()}
        id="fruit"
        data-testid="fruit-combobox"
      />,
    );

    // Check the input displays the selected value
    const input = screen.getByTestId("fruit-combobox");
    expect(input).toHaveValue("Banana");
  });

  it("shows dropdown options and allows selection", async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();

    render(
      <ComboboxField
        options={testOptions}
        value={null}
        onChange={onChange}
        id="fruit"
        data-testid="fruit-combobox"
      />,
    );

    // Get the combobox input and button
    const input = screen.getByTestId("fruit-combobox");
    const button = screen.getByTestId("fruit-button");

    // Click the button to open the dropdown
    await user.click(button);

    // The dropdown should open with options
    await waitFor(() => {
      expect(screen.getByTestId("fruit-option-list")).toBeInTheDocument();
    });

    // Options should be visible
    const optionBanana = screen.getByTestId("fruit-option-banana");
    expect(optionBanana).toBeInTheDocument();

    // Click on the banana option
    await user.click(optionBanana);

    // Verify the onChange callback was called with the banana option
    expect(onChange).toHaveBeenCalledWith(testOptions[1]);
  });

  it("allows filtering options by typing", async () => {
    const user = userEvent.setup();

    render(
      <ComboboxField
        options={testOptions}
        value={null}
        onChange={vi.fn()}
        id="fruit"
        data-testid="fruit-combobox"
      />,
    );

    // Get the combobox input
    const input = screen.getByTestId("fruit-combobox");

    // Click to focus the input
    await user.click(input);

    // Type to filter options
    await user.type(input, "ap");

    // The dropdown should open automatically with filtered options
    await waitFor(() => {
      expect(screen.getByTestId("fruit-option-list")).toBeInTheDocument();
    });

    // Should show matching option (apple) but not others
    expect(screen.getByTestId("fruit-option-apple")).toBeInTheDocument();
    expect(screen.queryByTestId("fruit-option-banana")).not.toBeInTheDocument();
  });

  it("closes dropdown when clicking outside the combobox", async () => {
    const user = userEvent.setup();

    // Create a container with the combobox and an outside element
    const { container } = render(
      <div>
        <div data-testid="outside-element">Click outside</div>
        <ComboboxField
          options={testOptions}
          value={null}
          onChange={vi.fn()}
          id="fruit"
          data-testid="fruit-combobox"
        />
      </div>,
    );

    // Get the combobox button and outside element
    const button = screen.getByTestId("fruit-button");
    const outsideElement = screen.getByTestId("outside-element");

    // Open the dropdown
    await user.click(button);

    // The dropdown should open with options
    await waitFor(() => {
      expect(screen.getByTestId("fruit-option-list")).toBeInTheDocument();
    });

    // Click outside the combobox
    await user.click(outsideElement);

    // The dropdown should close
    await waitFor(() => {
      expect(screen.queryByTestId("fruit-option-list")).not.toBeInTheDocument();
    });
  });

  it("closes dropdown when tabbing away", async () => {
    const user = userEvent.setup();

    // Create a container with the combobox and a focusable element after it
    render(
      <div>
        <ComboboxField
          options={testOptions}
          value={null}
          onChange={vi.fn()}
          id="fruit"
          data-testid="fruit-combobox"
        />
        <input data-testid="next-input" />
      </div>,
    );

    // Get the combobox input and button
    const input = screen.getByTestId("fruit-combobox");
    const button = screen.getByTestId("fruit-button");

    // Open the dropdown
    await user.click(button);

    // The dropdown should open with options
    await waitFor(() => {
      expect(screen.getByTestId("fruit-option-list")).toBeInTheDocument();
    });

    // Press Tab to move focus to the next element
    await user.tab();

    // The dropdown should close
    await waitFor(() => {
      expect(screen.queryByTestId("fruit-option-list")).not.toBeInTheDocument();
    });

    // Focus should be on the next input
    expect(screen.getByTestId("next-input")).toHaveFocus();
  });
});
