// noinspection JSUnusedGlobalSymbols

import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { withTheme } from "../storybook/ThemeDecorator";
import { themeFixtures } from "../builder/theme-fixtures";
import {
  EmbeddedForm,
  generateThemeStyles,
  getThemeCSSVariableMappings,
} from "../submission/EmbeddedForm";
import type { Form, FormField, FieldIdentifier } from "../builder/types";
import { useFormState } from "./useFormState";
import { PREDEFINED_FIELDS } from "../builder/fieldTypes";
import { FORM_CHROMATIC_PARAMS } from "@/config/chromatic-test-settings";
import type { FileData } from "./FileUploadField";
import { undefined } from "zod";
import { fieldType } from "@/drizzle/schema";

// Helper to get field label
function getFieldLabel(fieldType: FieldIdentifier): string | null {
  return PREDEFINED_FIELDS[fieldType]?.defaultLabel || null;
}

// Helper to get field placeholder
function getFieldPlaceholder(fieldType: FieldIdentifier): string {
  return PREDEFINED_FIELDS[fieldType]?.placeholder || "";
}

// Create a test form with a specific field type
const createFieldForm = (
  fieldType: FieldIdentifier,
  theme: any,
  options: { width?: "full" | "half"; required?: boolean } = {},
): Form => {
  const formId = `field-form-${Math.random().toString(36).substring(2, 9)}`;

  const field: FormField = {
    id: `field-${Math.random().toString(36).substring(2, 9)}`,
    formId,
    type: fieldType,
    label: getFieldLabel(fieldType),
    required: options.required ?? true,
    width: options.width ?? "full",
    order: 0,
    rowBreakAfter: false,
    placeholder: getFieldPlaceholder(fieldType),
    hideForEventTypes: [],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return {
    allowedDomains: [],
    redirectUrl: null,
    id: formId,
    name: `${fieldType} Field Form`,
    description: `Form with a single ${fieldType} field`,
    propertyId: "test-property-id",
    themeId: "test-theme-id",
    theme: {
      ...theme,
      id: "test-theme-id",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    fields: [field],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

// FormWithState component for displaying the form with state management
const FormWithState = ({
  form,
  initialValues = {},
  testForceCalendarOpen = false,
}: {
  form: Form;
  initialValues?: Record<string, string | string[] | File | FileData[]>;
  testForceCalendarOpen?: boolean;
}) => {
  const { values, handleChange } = useFormState(initialValues);

  // For dark theme forms, inject global styles to ensure popover inherits theme
  React.useEffect(() => {
    if (form.theme.backgroundColor !== "#ffffffff" && testForceCalendarOpen) {
      const styleId = `form-theme-${form.id}`;
      let styleEl = document.getElementById(styleId) as HTMLStyleElement;

      if (!styleEl) {
        styleEl = document.createElement("style");
        styleEl.id = styleId;
        document.head.appendChild(styleEl);
      }

      // Generate theme styles using the same production function
      const themeStyles = generateThemeStyles(form.theme);
      const cssVars = Object.entries(themeStyles)
        .filter(([key]) => key.startsWith("--"))
        .map(([key, value]) => `${key}: ${value};`)
        .join("\n");

      // Get the CSS variable mappings from production code
      const mappings = getThemeCSSVariableMappings();

      // Target only the specific popover for this form
      const popoverId = `form-${form.id}-date-popover`;

      styleEl.textContent = `
        #${popoverId} {
          ${cssVars}
          ${mappings}
        }
      `;

      return () => {
        styleEl.remove();
      };
    }
  }, [form.theme, form.id, testForceCalendarOpen]);

  return (
    <div>
      <div style={{ maxWidth: "600px", margin: "0 auto" }}>
        <EmbeddedForm
          form={form}
          values={values}
          onChange={handleChange}
          testForceCalendarOpen={testForceCalendarOpen}
        />
      </div>
      <div className="mt-4 p-4 border border-gray-200 rounded bg-gray-50">
        <h3 className="text-sm font-medium mb-2">Current Form Values:</h3>
        <pre className="text-xs overflow-auto max-h-40">
          {JSON.stringify(values, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Story configuration
const meta: Meta = {
  title: "Forms/Fields/Special",
  component: EmbeddedForm,
  parameters: {
    layout: "padded",
  },
};

export default meta;

type Story = StoryObj;

// Phone field with special handling
export const PhoneField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Create US phone form
    const usPhoneForm = createFieldForm("PHONE", theme);

    // Create international phone form
    const intlPhoneForm = createFieldForm("PHONE", theme);
    const frenchValues = {
      PHONE: "+33123456789",
      PHONE_COUNTRY: "fr",
    };

    // Error states
    const invalidUSForm = createFieldForm("PHONE", theme);
    invalidUSForm.fields[0].label = "Phone (Invalid US Number)";

    const invalidIntlForm = createFieldForm("PHONE", theme);
    invalidIntlForm.fields[0].label = "Phone (Invalid International Number)";

    const invalidUSErrors = {
      PHONE: ["Please enter a valid US phone number (10 digits required)"],
    };

    const invalidIntlErrors = {
      PHONE: ["Please enter a valid international phone number"],
    };

    return (
      <div className="space-y-12">
        <p className="text-sm text-gray-600 mb-4 italic">
          Note: the phone field is a special field that uses hidden inputs for
          PHONE and PHONE_COUNTRY values. The component will automatically
          format the phone number based on the country code.
        </p>

        <div className="space-y-8">
          {/* US Phone Field */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Default (US) Phone Number
            </h3>
            <FormWithState form={usPhoneForm} />
          </div>

          {/* International Phone Field */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              International Phone Number
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              This example demonstrates a phone field with international number
              support. Note: The component uses hidden inputs for PHONE and
              PHONE_COUNTRY values.
            </p>
            <FormWithState form={intlPhoneForm} initialValues={frenchValues} />
          </div>

          {/* Invalid US Phone Field */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Invalid US Phone Number
            </h3>
            <div style={{ maxWidth: "600px", margin: "0 auto" }}>
              <EmbeddedForm
                form={invalidUSForm}
                values={{}}
                errors={invalidUSErrors}
                onChange={() => {}}
              />
            </div>
          </div>

          {/* Invalid International Phone Field */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Invalid International Phone Number
            </h3>
            <div style={{ maxWidth: "600px", margin: "0 auto" }}>
              <EmbeddedForm
                form={invalidIntlForm}
                values={{}}
                errors={invalidIntlErrors}
                onChange={() => {}}
              />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

// Event Date Range Field
export const EventDateRangeField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Standard version
    const standardForm = createFieldForm("EVENT_DATE_RANGE", theme);

    // Empty state form
    const emptyForm = createFieldForm("EVENT_DATE_RANGE", theme);

    // Initial values for the date range - using May 7-10, 2025 to match the screenshot
    const initialStartDate = new Date(2025, 4, 7); // May 7, 2025
    const initialEndDate = new Date(2025, 4, 10); // May 10, 2025
    const initialValues = {
      START_DATE: initialStartDate.toISOString(),
      END_DATE: initialEndDate.toISOString(),
    };

    // Error state form
    const errorForm = createFieldForm("EVENT_DATE_RANGE", theme);
    const errorValues = {
      START_DATE: new Date(2025, 4, 10).toISOString(), // End before start
      END_DATE: new Date(2025, 4, 7).toISOString(),
    };
    const errors = {
      _deduplicated: ["End date must be after start date"],
    };

    return (
      <div className="space-y-12">
        <div>
          <h2 className="text-xl font-semibold mb-6">Event Date Range Field</h2>
          <p className="text-sm text-gray-600 mb-4 italic">
            The date range picker highlights the selected date range with a
            background color. Click on the field to open the calendar and select
            a date range.
          </p>
        </div>

        <div className="space-y-8">
          {/* Empty State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Empty State</h3>
            <p className="text-sm text-gray-600 mb-4">
              No dates selected yet. The field shows "Pick a date range"
              placeholder.
            </p>
            <FormWithState form={emptyForm} />
          </div>

          {/* Selected Date Range */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Selected Date Range</h3>
            <p className="text-sm text-gray-600 mb-4">
              Shows May 7-10, 2025 selected. The calendar should highlight this
              range with a background color.
            </p>
            <FormWithState form={standardForm} initialValues={initialValues} />
          </div>

          {/* Open Calendar State - Light Theme */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Open Calendar - Light Background
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Calendar forced open for snapshot testing. Date range (May 7-10)
              should show dark highlighting on light background.
            </p>
            <FormWithState
              form={standardForm}
              initialValues={initialValues}
              testForceCalendarOpen={true}
            />
          </div>

          {/* Open Calendar State - Dark Theme */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Open Calendar - Dark Background
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Calendar forced open on dark background. Date range should show
              light highlighting.
            </p>
            <div
              className="p-6 rounded-lg"
              style={{
                backgroundColor: themeFixtures.darkCorporate.backgroundColor,
              }}
            >
              <FormWithState
                form={createFieldForm(
                  "EVENT_DATE_RANGE",
                  themeFixtures.darkCorporate,
                )}
                initialValues={initialValues}
                testForceCalendarOpen={true}
              />
            </div>
          </div>

          {/* Error State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Error State</h3>
            <p className="text-sm text-gray-600 mb-4">
              Shows validation error when end date is before start date.
            </p>
            <div style={{ maxWidth: "600px", margin: "0 auto" }}>
              <EmbeddedForm
                form={errorForm}
                values={errorValues}
                errors={errors}
                onChange={() => {}}
              />
            </div>
          </div>
        </div>
      </div>
    );
  },
};

// Count fields
export const CountFields: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    const guestCountForm = createFieldForm("GUEST_COUNT", theme);
    const roomCountForm = createFieldForm("ROOM_COUNT", theme);
    const mealCountForm = createFieldForm("MEAL_COUNT", theme);

    return (
      <div className="space-y-8">
        <FormWithState form={guestCountForm} />
        <FormWithState form={roomCountForm} />
        <FormWithState form={mealCountForm} />
      </div>
    );
  },
};

// Budget Field
export const BudgetField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("BUDGET", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

// Event Description Field
export const EventDescriptionField: Story = {
  decorators: [withTheme("default")],
  render: () => {
    const form = createFieldForm("EVENT_DESCRIPTION", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

// Event Needs Field
export const EventNeedsField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const form = createFieldForm("EVENT_NEEDS", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

// Marketing Consent Field
export const MarketingConsentField: Story = {
  decorators: [withTheme("default")],
  render: () => {
    const form = createFieldForm("MARKETING_CONSENT", themeFixtures.default);
    return <FormWithState form={form} />;
  },
};

// File Upload Field
export const FileUploadField: Story = {
  decorators: [withTheme("default")],
  parameters: {
    chromatic: FORM_CHROMATIC_PARAMS,
  },
  render: () => {
    const theme = themeFixtures.default;

    // Create different states of the file upload field
    const emptyForm = createFieldForm("FILE_UPLOAD", theme);
    const uploadingForm = createFieldForm("FILE_UPLOAD", theme);
    const uploadedForm = createFieldForm("FILE_UPLOAD", theme);
    const errorForm = createFieldForm("FILE_UPLOAD", theme);
    const maxFilesForm = createFieldForm("FILE_UPLOAD", theme);

    // Mock file data for different states
    const uploadingFile = {
      fileId: "uploading-123",
      fileName: "document.pdf",
      fileSize: "1024",
      fileType: "application/pdf",
      uploadKey: "pending/123/document.pdf",
      keyWithoutStatusPrefix: "123/document.pdf",
      uploading: true,
      uploaded: false,
      error: false,
    };

    const uploadedFile = {
      fileId: "uploaded-123",
      fileName: "document.pdf",
      fileSize: "1024",
      fileType: "application/pdf",
      uploadKey: "pending/123/document.pdf",
      keyWithoutStatusPrefix: "123/document.pdf",
      uploading: false,
      uploaded: true,
      error: false,
    };

    const errorFile = {
      fileId: "error-123",
      fileName: "invalid.exe",
      fileSize: "1024",
      fileType: "application/x-msdownload",
      uploadKey: "",
      keyWithoutStatusPrefix: "",
      uploading: false,
      uploaded: false,
      error: true,
      errorMessage:
        "Invalid file type. Allowed file types are: .pdf, .doc, .docx, .xls, .xlsx, .csv, .jpg, .jpeg, .png, .gif",
    };

    const maxFiles = [
      uploadedFile,
      {
        ...uploadedFile,
        fileId: "uploaded-124",
        fileName: "document2.pdf",
      },
      {
        ...uploadedFile,
        fileId: "uploaded-125",
        fileName: "document3.pdf",
      },
    ];

    return (
      <div className="space-y-12">
        <p className="text-sm text-gray-600 mb-4 italic">
          Note: The file upload field supports multiple file types and shows
          different states during the upload process. It also handles file size
          limits and type restrictions.
        </p>

        <div className="space-y-8">
          {/* Empty State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Empty State</h3>
            <FormWithState form={emptyForm} />
          </div>

          {/* Uploading State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Uploading State</h3>
            <FormWithState
              form={uploadingForm}
              initialValues={{ FILE_UPLOAD: [uploadingFile] }}
            />
          </div>

          {/* Uploaded State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Uploaded State</h3>
            <FormWithState
              form={uploadedForm}
              initialValues={{ FILE_UPLOAD: [uploadedFile] }}
            />
          </div>

          {/* Error State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Error State</h3>
            <FormWithState
              form={errorForm}
              initialValues={{ FILE_UPLOAD: [errorFile] }}
            />
          </div>

          {/* Maximum Files State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">
              Maximum Files Reached
            </h3>
            <FormWithState
              form={maxFilesForm}
              initialValues={{ FILE_UPLOAD: maxFiles }}
            />
          </div>
        </div>
      </div>
    );
  },
};
