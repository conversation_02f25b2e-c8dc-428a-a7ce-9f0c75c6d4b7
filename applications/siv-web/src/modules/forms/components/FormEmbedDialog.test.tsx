import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import { FormEmbedDialog } from "./FormEmbedDialog";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { toast } from "sonner";
import { formFactory } from "@/modules/admin/forms/form-test-factories";
import { newFormFactory } from "@/modules/admin/forms/new-form-test-factories";

// Mock the clipboard API
const mockClipboard = {
  writeText: vi.fn(),
};
vi.stubGlobal("navigator", {
  clipboard: mockClipboard,
});

// Mock the toast
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Helper function to normalize HTML for comparison
function normalizeHtml(html: string) {
  const div = document.createElement("div");
  div.innerHTML = html.trim();
  return div.innerHTML;
}

describe("FormEmbedDialog", () => {
  const mockForm = formFactory.build({
    name: "Test Form",
    description: "Test Description",
  });

  const mockProps = {
    open: true,
    onOpenChange: vi.fn(),
    form: mockForm,
    publicFormRoutesBaseUrl: "http://test.com",
  };

  const expectedIframeHtml = `<iframe
    data-siv-form-id="${mockForm.id}"
    src="http://test.com/forms/${mockForm.id}"
    width="100%"
    height="800px"
    style="border: none; width: 100%; max-width: 600px;"
    title="${mockForm.name}"
></iframe>
<script src="https://sivform-cdn.sivconverts.com/latest/siv-form-embed.iife.js"></script>`;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders nothing when form is null", () => {
    const { container } = render(
      <FormEmbedDialog {...mockProps} form={null} />,
    );
    expect(container.firstChild).toBeNull();
  });

  it("renders the dialog with correct title when open", () => {
    render(<FormEmbedDialog {...mockProps} />);
    expect(screen.getByText("Embed Form Code")).toBeInTheDocument();
  });

  it("includes data-siv-form-id in the embed code", () => {
    render(<FormEmbedDialog {...mockProps} />);
    const textarea = screen.getByRole("textbox") as HTMLTextAreaElement;
    expect(textarea.value).toContain(`data-siv-form-id="${mockForm.id}"`);
  });

  it("generates the correct embed code structure", () => {
    // Mock the offsetHeight to match our expected height minus the buffer
    Object.defineProperty(HTMLDivElement.prototype, "offsetHeight", {
      configurable: true,
      value: 760, // 800px - 40px buffer
    });

    render(<FormEmbedDialog {...mockProps} />);
    const textarea = screen.getByRole("textbox") as HTMLTextAreaElement;

    // Compare normalized HTML to avoid whitespace issues
    expect(normalizeHtml(textarea.value)).toBe(
      normalizeHtml(expectedIframeHtml),
    );
  });

  describe("form height measurement", () => {
    it("renders a hidden form for measurement", () => {
      const { container } = render(<FormEmbedDialog {...mockProps} />);

      const hiddenForm = container.querySelector('div[aria-hidden="true"]');
      expect(hiddenForm).toBeInTheDocument();

      expect(hiddenForm).toHaveStyle({
        position: "fixed",
        left: "-9999px",
        top: "-9999px",
        width: "600px",
      });
    });

    it("uses measured height in iframe code", () => {
      // Mock the offsetHeight of the form container
      const mockOffsetHeight = 750;
      Object.defineProperty(HTMLDivElement.prototype, "offsetHeight", {
        configurable: true,
        value: mockOffsetHeight,
      });

      render(<FormEmbedDialog {...mockProps} />);

      const textarea = screen.getByRole("textbox") as HTMLTextAreaElement;
      const iframeCode = textarea.value;

      // Should include the measured height plus buffer
      expect(iframeCode).toContain(`height="${mockOffsetHeight + 40}px"`);
    });

    it("updates height when form changes", () => {
      const { rerender } = render(<FormEmbedDialog {...mockProps} />);

      // Initial height
      Object.defineProperty(HTMLDivElement.prototype, "offsetHeight", {
        configurable: true,
        value: 750,
      });

      const initialTextarea = screen.getByRole(
        "textbox",
      ) as HTMLTextAreaElement;
      const initialHeight = 790; // 750 + 40 buffer
      expect(initialTextarea.value).toContain(`height="${initialHeight}px"`);

      // Change form and height
      Object.defineProperty(HTMLDivElement.prototype, "offsetHeight", {
        configurable: true,
        value: 850,
      });

      const updatedForm = formFactory.build({
        name: "Updated Form",
        fields: [...mockForm.fields, ...mockForm.fields], // Double the fields
      });

      rerender(<FormEmbedDialog {...mockProps} form={updatedForm} />);

      const updatedTextarea = screen.getByRole(
        "textbox",
      ) as HTMLTextAreaElement;
      const updatedHeight = 890; // 850 + 40 buffer
      expect(updatedTextarea.value).toContain(`height="${updatedHeight}px"`);
      expect(updatedTextarea.value).toContain(
        `data-siv-form-id="${updatedForm.id}"`,
      );
    });
  });

  describe("copy functionality", () => {
    it("copies embed code to clipboard and shows success toast", async () => {
      mockClipboard.writeText.mockResolvedValueOnce(undefined);

      render(<FormEmbedDialog {...mockProps} />);

      const copyButton = screen.getByRole("button", {
        name: /copy embed code to clipboard/i,
      });
      await fireEvent.click(copyButton);

      const textarea = screen.getByRole("textbox") as HTMLTextAreaElement;
      expect(mockClipboard.writeText).toHaveBeenCalledWith(textarea.value);
      expect(toast.success).toHaveBeenCalledWith("Copied!", expect.any(Object));
    });

    it("shows error toast when copy fails", async () => {
      mockClipboard.writeText.mockRejectedValueOnce(new Error("Copy failed"));

      render(<FormEmbedDialog {...mockProps} />);

      const copyButton = screen.getByRole("button", {
        name: /copy embed code to clipboard/i,
      });
      await fireEvent.click(copyButton);

      expect(toast.error).toHaveBeenCalledWith(
        "Failed to copy",
        expect.any(Object),
      );
    });
  });

  it("calls onOpenChange when dialog is closed", () => {
    render(<FormEmbedDialog {...mockProps} />);

    const closeButton = screen.getByRole("button", { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockProps.onOpenChange).toHaveBeenCalledWith(false);
  });
});
