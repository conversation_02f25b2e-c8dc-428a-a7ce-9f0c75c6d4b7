import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FileUploadField } from "./FileUploadField";
import { FileUploadResponse } from "@/modules/forms/submission/file-upload-service";

// Mock form ID for tests
const MOCK_FORM_ID = "test-form-id";

// Mock the fetch function for presigned URLs
global.fetch = vi.fn().mockImplementation((url) => {
  // Handle presigned URL request
  const response: FileUploadResponse = {
    fileId: "abc-123-def",
    uploadGroupId: "ugid-987-zxy",
    presignedUrl: "https://example.com/upload/file1",
    fullKey: "pending/123e4567-e89b-12d3-a456-************/file1.pdf",
    keyWithoutStatusPrefix: "123e4567-e89b-12d3-a456-************/file1.pdf",
  };

  if (url.includes("/forms/") && url.includes("/files/presign")) {
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve(response),
    });
  }

  // Handle S3 upload request (using PUT)
  return Promise.resolve({
    ok: true,
  });
});

// Mock the UUID generation
vi.mock("uuid", () => ({
  v4: vi.fn(() => "123e4567-e89b-12d3-a456-************"),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => "blob:mock-url");
global.URL.revokeObjectURL = vi.fn();

// Helper function to get the file input element
const getFileInput = () => {
  return screen.getByLabelText(/drag & drop files here/i);
};

describe("FileUploadField", () => {
  // Reset mocks between tests
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockClear();
  });

  it("should render the file upload field with empty state", async () => {
    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={false}
        onChange={vi.fn()}
        value={[]}
        error={null}
        formId={MOCK_FORM_ID}
      />,
    );

    // Should show the dropzone content
    expect(screen.getByText("Drag & Drop Your File Here")).toBeInTheDocument();
    // Default maxSize is 5 * 1024 * 1024, which formats to "5.0 MB"
    expect(screen.getByText("(Max 5.0 MB)")).toBeInTheDocument();
  });

  it("should render correct texts when maxFiles is greater than 1", async () => {
    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={false}
        onChange={vi.fn()}
        value={[]}
        error={null}
        formId={MOCK_FORM_ID}
        maxFiles={5} // Explicitly set maxFiles
      />,
    );

    // Should show the plural dropzone content
    expect(
      screen.getByText("Drag & Drop Files Here, or Click to Select Files"),
    ).toBeInTheDocument();
    // Default maxSize is 5 * 1024 * 1024, which formats to "5.0 MB"
    expect(
      screen.getByText("Upload up to 5 Files (Max 5.0 MB Each)"),
    ).toBeInTheDocument();
  });

  it("should render the file upload field with existing files", async () => {
    const existingFiles = [
      {
        fileId: "123e4567-e89b-12d3-a456-************",
        fileName: "document.pdf",
        fileSize: "1024",
        fileType: "application/pdf",
        uploadKey: "pending/123e4567-e89b-12d3-a456-************/document.pdf",
        keyWithoutStatusPrefix:
          "123e4567-e89b-12d3-a456-************/document.pdf",
        uploaded: true,
        uploading: false,
        error: false,
      },
    ];

    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={vi.fn()}
        value={existingFiles}
        error={null}
        formId={MOCK_FORM_ID}
      />,
    );

    // Check if the file preview is rendered
    expect(screen.getByText("document.pdf")).toBeInTheDocument();
    expect(screen.getByLabelText(/Remove document.pdf/i)).toBeInTheDocument();
  });

  it("should show validation error when provided", async () => {
    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={vi.fn()}
        value={[]}
        error="Please upload a file"
        formId={MOCK_FORM_ID}
      />,
    );

    // Check if the error message is displayed after loading
    expect(screen.getByTestId("upload-error")).toBeInTheDocument();
    expect(screen.getByText("Please upload a file")).toBeInTheDocument();
  });

  it("should handle file uploading correctly", async () => {
    // Mock fetch for the presigned URLs and S3 upload
    const fetchMock = global.fetch as any;

    // Reset the mock for this test
    fetchMock.mockClear();

    const onChangeMock = vi.fn();
    const user = userEvent.setup();

    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={onChangeMock}
        value={[]}
        error={null}
        data-testid="file-upload"
        formId={MOCK_FORM_ID}
      />,
    );

    const file = new File(["file contents"], "test.pdf", {
      type: "application/pdf",
    });
    await user.upload(getFileInput(), file);

    expect(fetchMock).toHaveBeenNthCalledWith(
      1,
      `/forms/${MOCK_FORM_ID}/files/presign`,
      {
        method: "POST",
        headers: expect.any(Headers),
        body: JSON.stringify({
          uploadGroupId: "123e4567-e89b-12d3-a456-************",
        }),
      },
    );

    expect(fetchMock).toHaveBeenNthCalledWith(
      2,
      "https://example.com/upload/file1",
      {
        method: "PUT",
        headers: expect.objectContaining({
          "Content-Type": "application/pdf",
          "Content-Length": expect.any(String),
        }),
        body: file,
      },
    );

    // Verify onChange was called with the expected file data, including new properties
    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith([
        {
          fileId: "123e4567-e89b-12d3-a456-************",
          fileName: "test.pdf",
          fileSize: String(file.size),
          fileType: "application/pdf",
          uploadKey: "pending/123e4567-e89b-12d3-a456-************/file1.pdf",
          keyWithoutStatusPrefix:
            "123e4567-e89b-12d3-a456-************/file1.pdf",
          preview: undefined,
          uploaded: true,
          uploading: false,
          error: false,
        },
      ]);
    });
  });

  it("should handle file removal", async () => {
    const onChangeMock = vi.fn();
    const existingFiles = [
      {
        fileId: "123e4567-e89b-12d3-a456-************",
        fileName: "document.pdf",
        fileSize: "1024",
        fileType: "application/pdf",
        uploadKey: "pending/abc123/document.pdf",
        keyWithoutStatusPrefix: "abc123/document.pdf",
        uploaded: true,
        uploading: false,
        error: false,
      },
      {
        fileId: "123e4567-e89b-12d3-a456-426614174001",
        fileName: "image.jpg",
        fileSize: "2048",
        fileType: "image/jpeg",
        uploadKey: "pending/abc123/image.jpg",
        keyWithoutStatusPrefix: "abc123/image.jpg",
        uploaded: true,
        uploading: false,
        error: false,
      },
    ];

    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        maxFiles={3}
        onChange={onChangeMock}
        value={existingFiles}
        error={null}
        formId={MOCK_FORM_ID}
      />,
    );

    // Verify file counter is displayed
    expect(screen.getByText("2/3 files")).toBeInTheDocument();

    // Click the remove button for the first file
    fireEvent.click(screen.getAllByRole("button", { name: /remove/i })[0]);

    // Check if onChange was called with the updated files array (without the first file)
    expect(onChangeMock).toHaveBeenCalledWith([existingFiles[1]]);
  });

  it("should display visual states during file upload process", async () => {
    // Create manual promise resolvers
    let resolvePresignedUrl: (value: any) => void;
    let resolveUpload: (value: any) => void;

    const presignedUrlPromise = new Promise((resolve) => {
      resolvePresignedUrl = resolve;
    });

    const uploadPromise = new Promise((resolve) => {
      resolveUpload = resolve;
    });

    // Clear any previous mock implementations
    (global.fetch as any).mockClear();

    // Setup fetch mock to use our controllable promises
    (global.fetch as any).mockImplementation((url: string, options: any) => {
      // Handle presigned URL request
      if (url.includes("/forms/") && url.includes("/files/presign")) {
        return presignedUrlPromise;
      }

      // Handle S3 upload request
      return uploadPromise;
    });

    const onChangeMock = vi.fn();
    const user = userEvent.setup();

    // Render component
    const { container } = render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={onChangeMock}
        value={[]}
        error={null}
        data-testid="file-upload"
        formId={MOCK_FORM_ID}
      />,
    );

    // 1. START BY UPLOADING A FILE
    const file = new File(["file contents"], "test.pdf", {
      type: "application/pdf",
    });
    await user.upload(getFileInput(), file);

    // 2. VERIFY FILE IS UPLOADING
    await resolvePresignedUrl!({
      ok: true,
      json: () =>
        Promise.resolve({
          presignedUrl: "https://example.com/upload/file1",
          key: "pending/123e4567-e89b-12d3-a456-************/file1.pdf",
        }),
    });

    // 3. VERIFY FILE IS UPLOADING - check for uploadingFiles state
    await waitFor(() => {
      // Look for any content that suggests uploading
      const text = container.textContent;
      expect(text?.toLowerCase().includes("uploading")).toBe(true);
    });

    // 4. RESOLVE THE UPLOAD
    resolveUpload!({
      ok: true,
      status: 200,
      statusText: "OK",
    });

    // 5. VERIFY CALLBACK WAS CALLED WITH FILE DATA
    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            fileName: "test.pdf",
            fileType: "application/pdf",
            uploaded: true,
            uploading: false,
            error: false,
          }),
        ]),
      );
    });
  });

  it("should display user-friendly error for invalid file types", async () => {
    const onChangeMock = vi.fn();

    // Render the component with an error
    render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={onChangeMock}
        value={[]}
        error="Invalid file type. Allowed file types are: .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .jpeg, .png"
        formId={MOCK_FORM_ID}
      />,
    );

    // Check that error is displayed
    const errorElement = screen.getByTestId("upload-error");
    expect(errorElement).toBeInTheDocument();
    expect(errorElement.textContent).toContain("Invalid file type");
  });

  it("should handle reaching maximum file limit", async () => {
    const onChangeMock = vi.fn();

    // Create files that are already uploaded
    const existingFiles = Array(3)
      .fill(null)
      .map((_, i) => ({
        fileId: `file-${i}`,
        fileName: `file-${i}.pdf`,
        fileSize: "1024",
        fileType: "application/pdf",
        uploadKey: `pending/abc123/file-${i}.pdf`,
        keyWithoutStatusPrefix: `abc123/file-${i}.pdf`,
        uploaded: true,
        uploading: false,
        error: false,
      }));

    const { rerender } = render(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={onChangeMock}
        value={existingFiles}
        error={null}
        maxFiles={3}
        formId={MOCK_FORM_ID}
      />,
    );

    // Check that the max file limit message is shown
    expect(screen.getByText("Maximum file limit reached")).toBeInTheDocument();

    // Simulate removing a file by updating the value prop
    const filesAfterRemoval = existingFiles.slice(1);

    // Rerender with the updated value prop to simulate the state update
    rerender(
      <FileUploadField
        name="FILE_UPLOAD"
        label="Upload Files"
        required={true}
        onChange={onChangeMock}
        value={filesAfterRemoval}
        error={null}
        maxFiles={3}
        formId={MOCK_FORM_ID}
      />,
    );

    // Verify we can upload again
    expect(
      screen.queryByText("Maximum file limit reached"),
    ).not.toBeInTheDocument();
  });
});
