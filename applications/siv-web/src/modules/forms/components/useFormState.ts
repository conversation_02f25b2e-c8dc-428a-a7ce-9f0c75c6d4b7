import { useState, useCallback } from "react";
import type { FileData } from "./FileUploadField";

/**
 * Custom hook for managing form state in forms and stories
 *
 * This hook provides a consistent way to track form field values and handle changes,
 * making it easier to maintain state across different implementations.
 *
 * @param initialValues - Initial values for the form fields
 * @returns An object containing the current values, a change handler, and a function to set all values
 */
export function useFormState(
  initialValues: Record<string, string | string[] | File | FileData[]> = {},
) {
  const [values, setValues] =
    useState<Record<string, string | string[] | File | FileData[]>>(
      initialValues,
    );

  const handleChange = useCallback(
    (name: string, value: string | string[] | File | FileData[]) => {
      setValues((prev) => ({ ...prev, [name]: value }));
    },
    [],
  );

  return {
    values,
    handleChange,
    setValues,
  };
}
