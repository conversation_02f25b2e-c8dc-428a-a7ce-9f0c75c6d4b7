import { db } from "@/db/dbclient";
import { form, theme, formField, formFieldOption } from "@/drizzle/schema";
import { eq, inArray } from "drizzle-orm";
import { Form, NewFormWithTheme, UpsertFormWithTheme } from "./builder/types";
import { mapDbFormToDomainForm } from "./builder/form-mappers";

export class FormRepo {
  private mapToDbFields(input: NewFormWithTheme, formId: string) {
    return input.fields.map((field) => ({
      formId,
      type: field.type,
      label: field.label ?? "", // Convert null to empty string for DB
      required: field.required,
      width: field.width,
      placeholder: field.placeholder, // Keep null values
      order: field.order,
      hideForEventTypes: field.hideForEventTypes || [],
      rowBreakAfter: field.rowBreakAfter ?? false, // Include rowBreakAfter with default to false
    }));
  }

  private async saveFieldOptions(
    tx: any,
    fields: NewFormWithTheme["fields"],
    dbFields: any[],
  ) {
    const options = [];

    // Match each field from input with the created DB field to get IDs
    for (let i = 0; i < fields.length; i++) {
      const inputField = fields[i];
      // Match on type since there can only be one field of each type per form
      const dbField = dbFields.find((f) => f.type === inputField.type);

      if (!dbField) continue;

      // Save checkbox text if present
      if (inputField.checkboxText) {
        options.push({
          formFieldId: dbField.id,
          optionValue: inputField.checkboxText,
        });
      }
    }

    if (options.length > 0) {
      await tx.insert(formFieldOption).values(options);
    }
  }

  async findFormWithRelations(formId: string): Promise<Form | null> {
    // Query the form with theme and fields
    const dbForm = await db.query.form.findFirst({
      where: eq(form.id, formId),
      with: {
        theme: true,
        formFields: {
          orderBy: (fields) => [fields.order],
        },
      },
    });

    if (!dbForm) {
      return null;
    }

    // Fetch form field options if there are fields
    if (dbForm.formFields?.length) {
      const fieldIds = dbForm.formFields.map((f) => f.id);
      const options = await db.query.formFieldOption.findMany({
        where: inArray(formFieldOption.formFieldId, fieldIds),
      });

      // Add checkbox text to fields
      for (const field of dbForm.formFields) {
        const option = options.find((o) => o.formFieldId === field.id);
        if (option) {
          (field as any).checkboxText = option.optionValue;
        } else if (field.type === "MARKETING_CONSENT") {
          // Add default checkbox text for marketing consent fields that don't have options
          // This default is not persisted to the database
          (field as any).checkboxText =
            "I consent to receiving marketing communications.";
        }
      }
    }

    // Create a properly typed object to pass to the mapper
    const typedDbForm = {
      ...dbForm,
      formFields: dbForm.formFields,
      theme: dbForm.theme,
    };

    return mapDbFormToDomainForm(typedDbForm);
  }

  async createForm(input: NewFormWithTheme): Promise<Form> {
    const [newForm] = await db.transaction(async (tx) => {
      // First create the theme
      const [newTheme] = await tx
        .insert(theme)
        .values({
          name: input.theme.name,
          primaryColor: input.theme.primaryColor,
          backgroundColor: input.theme.backgroundColor,
          textColor: input.theme.textColor,
          borderColor: input.theme.borderColor,
          inputBorderRadius: input.theme.inputBorderRadius,
          buttonBorderRadius: input.theme.buttonBorderRadius,
          padding: input.theme.padding,
          buttonTextColor: input.theme.buttonTextColor,
          buttonAlignment: input.theme.buttonAlignment,
          font: input.theme.font,
          fieldBackgroundColor: input.theme.fieldBackgroundColor,
          inputTextColor: input.theme.inputTextColor,
          formBorderRadius: input.theme.formBorderRadius,
          placeholderColor: input.theme.placeholderColor,
        })
        .returning();

      // Then create the form
      const [newFormData] = await tx
        .insert(form)
        .values({
          name: input.name,
          description: input.description,
          propertyId: input.propertyId,
          themeId: newTheme.id,
          redirectUrl: input.redirectUrl,
          allowedDomains: input.allowedDomains,
        })
        .returning();

      // Create all fields
      const fields = await tx
        .insert(formField)
        .values(this.mapToDbFields(input, newFormData.id))
        .returning();

      // Save field options (checkbox text)
      await this.saveFieldOptions(tx, input.fields, fields);

      // Add checkbox text to fields to include in the returned form
      const fieldsWithCheckboxText = fields.map((field) => {
        const inputField = input.fields.find((f) => f.type === field.type);
        return {
          ...field,
          checkboxText: inputField?.checkboxText,
        };
      });

      // Return the complete form with relations
      return [
        {
          ...newFormData,
          theme: newTheme,
          formFields: fieldsWithCheckboxText.sort((a, b) => a.order - b.order),
        },
      ];
    });

    return mapDbFormToDomainForm(newForm);
  }

  async updateForm(formId: string, input: NewFormWithTheme): Promise<Form> {
    const [updatedForm] = await db.transaction(async (tx) => {
      // First delete all fields (and cascade delete options)
      await tx.delete(formField).where(eq(formField.formId, formId));

      // Get and update the form to get its theme ID
      const [updatedFormData] = await tx
        .update(form)
        .set({
          name: input.name,
          description: input.description,
          redirectUrl: input.redirectUrl,
          allowedDomains: input.allowedDomains,
        })
        .where(eq(form.id, formId))
        .returning({
          id: form.id,
          name: form.name,
          description: form.description,
          propertyId: form.propertyId,
          themeId: form.themeId,
          redirectUrl: form.redirectUrl,
          allowedDomains: form.allowedDomains,
          createdAt: form.createdAt,
          updatedAt: form.updatedAt,
        });

      if (!updatedFormData) {
        throw new Error("Form not found");
      }

      // Update the existing theme
      const [updatedTheme] = await tx
        .update(theme)
        .set({
          name: input.theme.name,
          primaryColor: input.theme.primaryColor,
          backgroundColor: input.theme.backgroundColor,
          textColor: input.theme.textColor,
          borderColor: input.theme.borderColor,
          inputBorderRadius: input.theme.inputBorderRadius,
          buttonBorderRadius: input.theme.buttonBorderRadius,
          padding: input.theme.padding,
          buttonTextColor: input.theme.buttonTextColor,
          buttonAlignment: input.theme.buttonAlignment,
          font: input.theme.font,
          fieldBackgroundColor: input.theme.fieldBackgroundColor,
          inputTextColor: input.theme.inputTextColor,
          formBorderRadius: input.theme.formBorderRadius,
          placeholderColor: input.theme.placeholderColor,
        })
        .where(eq(theme.id, updatedFormData.themeId))
        .returning();

      // Create new fields
      const newFields = await tx
        .insert(formField)
        .values(this.mapToDbFields(input, formId))
        .returning();

      // Save field options (checkbox text)
      await this.saveFieldOptions(tx, input.fields, newFields);

      // Add checkbox text to fields to include in the returned form
      const fieldsWithCheckboxText = newFields.map((field) => {
        const inputField = input.fields.find((f) => f.type === field.type);
        return {
          ...field,
          checkboxText: inputField?.checkboxText,
        };
      });

      // Return assembled form data
      return [
        {
          ...updatedFormData,
          theme: updatedTheme,
          formFields: fieldsWithCheckboxText.sort((a, b) => a.order - b.order),
        },
      ];
    });

    return mapDbFormToDomainForm(updatedForm);
  }

  async deleteForm(formId: string): Promise<void> {
    await db.transaction(async (tx) => {
      // Get the form and theme ID first
      const [formToDelete] = await tx
        .select()
        .from(form)
        .where(eq(form.id, formId))
        .limit(1);

      if (!formToDelete) {
        return;
      }

      // Delete form fields first (will cascade delete options)
      await tx.delete(formField).where(eq(formField.formId, formId));

      // Delete the form
      await tx.delete(form).where(eq(form.id, formId));

      // Finally delete the theme
      await tx.delete(theme).where(eq(theme.id, formToDelete.themeId));
    });
  }

  /**
   * Upsert a form - creates a new form with the specified ID if it doesn't exist,
   * or updates an existing form if it does exist
   * @param formId - The ID to use for the form
   * @param input - The form data with optional ID
   */
  async upsertForm(formId: string, input: UpsertFormWithTheme): Promise<Form> {
    // Check if form exists
    const existingForm = await db.query.form.findFirst({
      where: eq(form.id, formId),
      columns: {
        id: true,
      },
    });

    if (existingForm) {
      // Form exists, update it
      return this.updateForm(formId, input);
    } else {
      // Form doesn't exist, create it with the specified ID
      const [newForm] = await db.transaction(async (tx) => {
        // First create the theme
        const [newTheme] = await tx
          .insert(theme)
          .values({
            name: input.theme.name,
            primaryColor: input.theme.primaryColor,
            backgroundColor: input.theme.backgroundColor,
            textColor: input.theme.textColor,
            borderColor: input.theme.borderColor,
            inputBorderRadius: input.theme.inputBorderRadius,
            buttonBorderRadius: input.theme.buttonBorderRadius,
            padding: input.theme.padding,
            buttonTextColor: input.theme.buttonTextColor,
            buttonAlignment: input.theme.buttonAlignment,
            font: input.theme.font,
            fieldBackgroundColor: input.theme.fieldBackgroundColor,
            inputTextColor: input.theme.inputTextColor,
            formBorderRadius: input.theme.formBorderRadius,
            placeholderColor: input.theme.placeholderColor,
          })
          .returning();

        // Then create the form with the specified ID
        const [newFormData] = await tx
          .insert(form)
          .values({
            id: formId, // Use the provided ID
            name: input.name,
            description: input.description,
            propertyId: input.propertyId,
            themeId: newTheme.id,
            redirectUrl: input.redirectUrl,
            allowedDomains: input.allowedDomains,
          })
          .returning();

        // Create all fields
        const fields = await tx
          .insert(formField)
          .values(this.mapToDbFields(input, newFormData.id))
          .returning();

        // Save field options (checkbox text)
        await this.saveFieldOptions(tx, input.fields, fields);

        // Add checkbox text to fields to include in the returned form
        const fieldsWithCheckboxText = fields.map((field) => {
          const inputField = input.fields.find((f) => f.type === field.type);
          return {
            ...field,
            checkboxText: inputField?.checkboxText,
          };
        });

        // Return the complete form with relations
        return [
          {
            ...newFormData,
            theme: newTheme,
            formFields: fieldsWithCheckboxText.sort(
              (a, b) => a.order - b.order,
            ),
          },
        ];
      });

      return mapDbFormToDomainForm(newForm);
    }
  }

  /**
   * Find a form by name and propertyId and upsert it
   * If the form exists, it will be updated; if not, a new form will be created
   * @param input - The form data with optional ID
   * @param allowUpsert - Whether to create a new form if one doesn't exist (default: false)
   */
  async findAndUpsertForm(
    input: UpsertFormWithTheme,
    allowUpsert: boolean = false,
  ): Promise<Form> {
    // First try to find by ID if provided
    if (input.id) {
      try {
        const existingForm = await this.findFormWithRelations(input.id);
        if (existingForm) {
          return this.updateForm(existingForm.id, input);
        }
      } catch (error) {
        // If form not found by ID, continue to search by name and propertyId
      }
    }

    // Try to find by name and propertyId
    const existingForms = await db.query.form.findMany({
      where: (fields, { eq, and }) =>
        and(
          eq(fields.name, input.name),
          eq(fields.propertyId, input.propertyId),
        ),
      columns: {
        id: true,
      },
    });

    if (existingForms.length > 0) {
      // Form exists, update it
      return this.updateForm(existingForms[0].id, input);
    } else if (allowUpsert) {
      // Form doesn't exist, create a new one
      return this.createForm(input);
    } else {
      throw new Error("Form not found and upsert not allowed");
    }
  }
}
