/**
 * Centralized configuration for Chromatic visual testing
 * Used across Storybook stories to ensure consistent testing parameters
 */

// Viewport sizes for responsive testing
export const VIEWPORT_SIZES = [320, 600, 1024] as const;

// Default Chromatic parameters
export const DEFAULT_CHROMATIC_PARAMS = {
  viewports: VIEWPORT_SIZES,
  // A small delay to ensure components are fully rendered
  delay: 200,
} as const;

// Form-specific settings that may need special handling
export const FORM_CHROMATIC_PARAMS = {
  ...DEFAULT_CHROMATIC_PARAMS,
  // Increase delay for forms with more complex rendering
  delay: 200,
} as const;
