import React from "react";
import { Decorator } from "@storybook/react";
import { themeFixtures } from "../builder/theme-fixtures";
import type { Theme } from "../builder/types";

// CSS variables for theming that will be applied to the container
const generateThemeCssVars = (
  theme: Omit<Theme, "id" | "createdAt" | "updatedAt">,
) => ({
  "--ef-primary": theme.primaryColor,
  "--ef-background": theme.backgroundColor,
  "--ef-text": theme.textColor,
  "--ef-border": theme.borderColor,
  "--ef-input-border-radius": theme.inputBorderRadius,
  "--ef-button-border-radius": theme.buttonBorderRadius,
  "--ef-padding": theme.padding,
  "--ef-button-text": theme.buttonTextColor,
  "--ef-font": theme.font,
  "--ef-field-background": theme.fieldBackgroundColor,
  "--ef-input-text": theme.inputTextColor,
  "--ef-form-border-radius": theme.formBorderRadius,
  "--ef-placeholder": theme.placeholderColor,
});

/**
 * Storybook decorator that applies a theme to the story
 * Use this decorator to wrap components with different themes for visual testing
 *
 * @example
 * export default {
 *   title: 'Forms/Input',
 *   component: Input,
 *   decorators: [withTheme('luxuryHospitality')]
 * }
 */
export const withTheme = (themeName: keyof typeof themeFixtures): Decorator => {
  return (Story, context) => {
    // Get the theme from fixtures
    const theme = themeFixtures[themeName];

    // Generate CSS variables from theme
    const themeVars = generateThemeCssVars(theme);

    return (
      <div
        className="form-theme-container p-4 rounded-lg"
        style={{
          ...themeVars,
          fontFamily: "var(--ef-font)",
          color: "var(--ef-text)",
          backgroundColor: "var(--ef-background)",
          maxWidth: "800px",
          margin: "0 auto",
        }}
        data-testid="themed-container"
        data-theme-name={themeName}
      >
        <Story {...context} />
      </div>
    );
  };
};

/**
 * Storybook decorator that applies all themes to the story
 * Use this to show a component rendered with all available themes at once
 */
export const withAllThemes: Decorator = (Story, context) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-4">
      {Object.entries(themeFixtures).map(([themeName, theme]) => {
        const themeVars = generateThemeCssVars(theme);

        return (
          <div key={themeName} className="theme-preview">
            <h3 className="text-lg font-semibold mb-2">{theme.name}</h3>
            <div
              className="form-theme-container p-4 rounded-lg"
              style={{
                ...themeVars,
                fontFamily: "var(--ef-font)",
                color: "var(--ef-text)",
                backgroundColor: "var(--ef-background)",
              }}
              data-testid={`themed-container-${themeName}`}
              data-theme-name={themeName}
            >
              <Story {...context} />
            </div>
          </div>
        );
      })}
    </div>
  );
};
