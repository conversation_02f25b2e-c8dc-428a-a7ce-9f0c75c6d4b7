import type { Form, FormField, Theme } from "./types";
import type { form, theme, formField } from "@/drizzle/schema";

export function mapDbFormToDomainForm(
  dbForm: typeof form.$inferSelect & {
    theme: typeof theme.$inferSelect;
    formFields: Array<
      typeof formField.$inferSelect & {
        checkboxText?: string;
      }
    >;
  },
): Form {
  return {
    id: dbForm.id,
    name: dbForm.name,
    description: dbForm.description,
    propertyId: dbForm.propertyId,
    themeId: dbForm.theme.id,
    theme: mapDbThemeToDomainTheme(dbForm.theme),
    fields: dbForm.formFields.map(mapDbFieldToDomainField),
    redirectUrl: dbForm.redirectUrl,
    allowedDomains: dbForm.allowedDomains || [],
    createdAt: new Date(dbForm.createdAt),
    updatedAt: new Date(dbForm.updatedAt),
  };
}

export function mapDbThemeToDomainTheme(
  dbTheme: typeof theme.$inferSelect,
): Theme {
  return {
    id: dbTheme.id,
    name: dbTheme.name,
    primaryColor: dbTheme.primaryColor,
    backgroundColor: dbTheme.backgroundColor,
    textColor: dbTheme.textColor,
    borderColor: dbTheme.borderColor,
    inputBorderRadius: dbTheme.inputBorderRadius,
    buttonBorderRadius: dbTheme.buttonBorderRadius,
    padding: dbTheme.padding,
    buttonTextColor: dbTheme.buttonTextColor,
    buttonAlignment: dbTheme.buttonAlignment as "left" | "right",
    font: dbTheme.font,
    fieldBackgroundColor: dbTheme.fieldBackgroundColor,
    inputTextColor: dbTheme.inputTextColor,
    formBorderRadius: dbTheme.formBorderRadius,
    placeholderColor: dbTheme.placeholderColor,
    createdAt: new Date(dbTheme.createdAt),
    updatedAt: new Date(dbTheme.updatedAt),
  };
}

export function mapDbFieldToDomainField(
  dbField: typeof formField.$inferSelect & {
    checkboxText?: string;
  },
): FormField {
  return {
    id: dbField.id,
    formId: dbField.formId,
    type: dbField.type,
    label: dbField.label,
    required: dbField.required,
    width: dbField.width,
    placeholder: dbField.placeholder,
    order: Number(dbField.order),
    hideForEventTypes: dbField.hideForEventTypes,
    rowBreakAfter: dbField.rowBreakAfter,
    createdAt: new Date(dbField.createdAt),
    updatedAt: new Date(dbField.updatedAt),
    checkboxText: dbField.checkboxText,
  };
}
