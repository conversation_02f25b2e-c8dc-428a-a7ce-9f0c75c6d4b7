import {
  buildBaseNumberSchema,
  buildRequiredNumberSchema,
  validateFormSubmission,
} from "./fieldTypes";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { subDays } from "date-fns";
import { z } from "zod";
import {
  createTestField,
  createTestFieldList,
} from "@/modules/admin/forms/new-form-test-factories";

describe("Form Field Validation", () => {
  describe("String Fields", () => {
    const requiredFields = [
      createTestField({
        type: "FIRST_NAME",
        label: "First Name",
        required: true,
        order: 0,
        width: "full",
      }),
      createTestField({
        type: "LAST_NAME",
        label: "Last Name",
        required: true,
        order: 1,
        width: "full",
      }),
      createTestField({
        type: "COMPANY",
        label: "Company",
        required: true,
        order: 2,
        width: "full",
      }),
      createTestField({
        type: "CITY",
        label: "City",
        required: true,
        order: 3,
        width: "full",
      }),
    ];

    it("should reject empty strings", () => {
      const result = validateFormSubmission(requiredFields, {
        FIRST_NAME: "",
        LAST_NAME: "",
        COMPANY: "",
        CITY: "",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(4);
      }
    });

    it("should reject whitespace-only strings", () => {
      const result = validateFormSubmission(requiredFields, {
        FIRST_NAME: "   ",
        LAST_NAME: "\t\n",
        COMPANY: "  \t  ",
        CITY: " ",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(4);
      }
    });

    it("should accept valid values", () => {
      const result = validateFormSubmission(requiredFields, {
        FIRST_NAME: "John",
        LAST_NAME: "O'Doe-Smith",
        COMPANY: "Acme Corp",
        CITY: "New York",
      });
      expect(result.success).toBe(true);
    });
  });

  describe("Email Field", () => {
    describe("when required", () => {
      const fields = [
        createTestField({
          type: "EMAIL",
          label: "Email",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      it("should reject empty strings", () => {
        const result = validateFormSubmission(fields, { EMAIL: "" });
        expect(result.success).toBe(false);
      });

      it("should reject invalid email formats", () => {
        const result = validateFormSubmission(fields, { EMAIL: "notanemail" });
        expect(result.success).toBe(false);
      });

      it("should accept valid email", () => {
        const result = validateFormSubmission(fields, {
          EMAIL: "<EMAIL>",
        });
        expect(result.success).toBe(true);
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "EMAIL",
          label: "Email",
          required: false,
          order: 0,
          width: "full",
        }),
      ];

      it("should accept empty strings", () => {
        const result = validateFormSubmission(fields, { EMAIL: "" });
        expect(result.success).toBe(true);
      });

      it("should accept missing field", () => {
        const result = validateFormSubmission(fields, {});
        expect(result.success).toBe(true);
      });

      it("should still validate format when value is provided", () => {
        const result = validateFormSubmission(fields, { EMAIL: "notanemail" });
        expect(result.success).toBe(false);
      });
    });
  });

  describe("Postal Code Field", () => {
    describe("when required", () => {
      const fields = [
        createTestField({
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      it("should reject empty strings", () => {
        const result = validateFormSubmission(fields, { POSTAL_CODE: "" });
        expect(result.success).toBe(false);
      });

      it("should reject invalid formats", () => {
        const invalidFormats = [
          "123",
          "1234",
          "123456",
          "12345-",
          "12345-123",
          "abcde",
        ];
        invalidFormats.forEach((code) => {
          const result = validateFormSubmission(fields, { POSTAL_CODE: code });
          expect(result.success).toBe(false);
        });
      });

      it("should accept valid formats", () => {
        const validFormats = ["12345", "12345-6789"];
        validFormats.forEach((code) => {
          const result = validateFormSubmission(fields, { POSTAL_CODE: code });
          expect(result.success).toBe(true);
        });
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: false,
          order: 0,
          width: "full",
        }),
      ];

      it("should accept empty strings", () => {
        const result = validateFormSubmission(fields, { POSTAL_CODE: "" });
        expect(result.success).toBe(true);
      });

      it("should accept missing field", () => {
        const result = validateFormSubmission(fields, {});
        expect(result.success).toBe(true);
      });

      it("should still validate format when value is provided", () => {
        const result = validateFormSubmission(fields, { POSTAL_CODE: "123" });
        expect(result.success).toBe(false);
      });
    });
  });

  describe("Phone Field", () => {
    describe("when required", () => {
      const fields = [
        createTestField({
          type: "PHONE",
          label: "Phone",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      it("should reject empty strings", () => {
        const result = validateFormSubmission(fields, { PHONE: "" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe("Required");
        }
      });

      it("should reject invalid phone formats", () => {
        const invalidFormats = [
          "123", // too short
          "123456789", // missing digit
          "abc1234567890", // contains letters
          "12345678901234", // too long
          "5551234567", // missing country code
        ];

        invalidFormats.forEach((phone) => {
          const result = validateFormSubmission(fields, { PHONE: phone });
          expect(result.success).toBe(false);
          if (!result.success) {
            expect(result.error.issues[0].message).toBe(
              "Please enter a valid phone number",
            );
          }
        });
      });

      it("should accept valid phone formats", () => {
        const validFormats = [
          "+13124567890",
          "+17203734253",
          "+13036543210",
          "+61448756523",
          "+59323991398",
        ];

        validFormats.forEach((phone) => {
          const result = validateFormSubmission(fields, { PHONE: phone });
          expect(result.success, `phone number: ${phone}`).toBe(true);
          if (result.success) {
            expect(result.data.PHONE).toBe(phone);
          }
        });
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "PHONE",
          label: "Phone",
          required: false,
          order: 0,
          width: "full",
        }),
      ];

      it("should accept empty strings", () => {
        const result = validateFormSubmission(fields, { PHONE: "" });
        expect(result.success).toBe(true);
      });

      it("should accept missing field", () => {
        const result = validateFormSubmission(fields, {});
        expect(result.success).toBe(true);
      });

      it("should still validate format when value is provided", () => {
        const result = validateFormSubmission(fields, { PHONE: "123" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "Please enter a valid phone number",
          );
        }
      });

      it("should accept valid phone numbers when provided", () => {
        const result = validateFormSubmission(fields, {
          PHONE: "+17208389882",
        });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.PHONE).toBe("+17208389882");
        }
      });
    });
  });

  describe("State Field", () => {
    describe("state behavior with country field", () => {
      it("should not accept any state when COUNTRY field exists but is not set", () => {
        // Create a form with both COUNTRY and STATE fields
        const fields = [
          createTestField({
            type: "COUNTRY",
            label: "Country",
            required: false,
            order: 0,
            width: "full",
          }),
          createTestField({
            type: "STATE",
            label: "State",
            required: false,
            order: 1,
            width: "full",
          }),
        ];

        // Try to submit with a US state but no country
        const result = validateFormSubmission(fields, { STATE: "CA" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "State/Region is required for the selected country",
          );
        }

        // Try to submit with a Canadian province but no country
        const resultCA = validateFormSubmission(fields, { STATE: "ON" });
        expect(resultCA.success).toBe(false);

        // Verify that we accept US states when there's no COUNTRY field
        const fieldsWithoutCountry = [
          createTestField({
            type: "STATE",
            label: "State",
            required: true,
            order: 0,
            width: "full",
          }),
        ];

        // This should pass because without a COUNTRY field, we default to US states
        const resultWithoutCountry = validateFormSubmission(
          fieldsWithoutCountry,
          { STATE: "CA" },
        );
        expect(resultWithoutCountry.success).toBe(true);
      });
    });

    describe("when required (no country selected)", () => {
      const fields = [
        createTestField({
          type: "STATE",
          label: "State",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      it("should reject empty strings", () => {
        const result = validateFormSubmission(fields, { STATE: "" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe("Required");
        }
      });

      it("should reject invalid state codes", () => {
        const invalidStates = ["XX", "ZZ", "ABC", "12", "california", "CX "];
        invalidStates.forEach((state) => {
          const result = validateFormSubmission(fields, {
            STATE: state,
            COUNTRY: "US",
          });
          expect(
            result.success,
            `State ${state} should be invalid for US`,
          ).toBe(false);
          if (!result.success) {
            expect(result.error.issues[0].message).toBe(
              "Please select a valid state/region",
            );
          }
        });
      });

      it("should accept valid US state codes when no country is selected", () => {
        const validStates = ["CA", "NY", "TX", "CO", "FL"];
        validStates.forEach((state) => {
          const result = validateFormSubmission(fields, { STATE: state });
          expect(result.success).toBe(true);
          if (result.success) {
            expect(result.data.STATE).toBe(state);
          }
        });
      });
    });

    describe("when required with country selected", () => {
      const fields = [
        createTestField({
          type: "COUNTRY",
          label: "Country",
          required: true,
          order: 0,
          width: "full",
        }),
        createTestField({
          type: "STATE",
          label: "State",
          required: true,
          order: 1,
          width: "full",
        }),
      ];

      it("should accept valid state codes for selected country", () => {
        // Canada provinces
        const validProvinces = [
          { country: "CA", state: "ON" }, // Ontario
          { country: "CA", state: "QC" }, // Quebec
          { country: "CA", state: "BC" }, // British Columbia
        ];

        validProvinces.forEach(({ country, state }) => {
          const result = validateFormSubmission(fields, {
            COUNTRY: country,
            STATE: state,
          });
          expect(result.success).toBe(true);
          if (result.success) {
            expect(result.data.STATE).toBe(state);
          }
        });

        // Australia states
        const validAustralianStates = [
          { country: "AU", state: "NSW" }, // New South Wales
          { country: "AU", state: "VIC" }, // Victoria
          { country: "AU", state: "QLD" }, // Queensland
        ];

        validAustralianStates.forEach(({ country, state }) => {
          const result = validateFormSubmission(fields, {
            COUNTRY: country,
            STATE: state,
          });
          expect(result.success).toBe(true);
          if (result.success) {
            expect(result.data.STATE).toBe(state);
          }
        });
      });

      it("should reject invalid state codes for selected country", () => {
        // Invalid combinations
        const invalidStateCombinations = [
          { country: "CA", state: "CA" }, // California is a US state, not Canadian
          { country: "AU", state: "ON" }, // Ontario is Canadian, not Australian
          { country: "GB", state: "TX" }, // Texas is US, not UK
        ];

        invalidStateCombinations.forEach(({ country, state }) => {
          const result = validateFormSubmission(fields, {
            COUNTRY: country,
            STATE: state,
          });
          expect(result.success).toBe(false);
          if (!result.success) {
            expect(result.error.issues[0].message).toBe(
              "Please select a valid state/region for the selected country",
            );
          }
        });
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "STATE",
          label: "State",
          required: false,
          order: 0,
          width: "full",
        }),
      ];

      it("should accept missing field", () => {
        const result = validateFormSubmission(fields, {});
        expect(result.success).toBe(true);
      });

      it("should still validate format when value is provided", () => {
        const result = validateFormSubmission(fields, { STATE: "XX" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "Please select a valid state/region",
          );
        }
      });
    });
  });

  describe("Country Field", () => {
    describe("when required", () => {
      const fields = [
        createTestField({
          type: "COUNTRY",
          label: "Country",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      it("should reject empty strings", () => {
        const result = validateFormSubmission(fields, { COUNTRY: "" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "Please select a valid country",
          );
        }
      });

      it("should reject invalid country codes", () => {
        const invalidCountries = ["XX", "ZZZ", "123", "INVALID"];
        invalidCountries.forEach((country) => {
          const result = validateFormSubmission(fields, { COUNTRY: country });
          expect(result.success).toBe(false);
          if (!result.success) {
            expect(result.error.issues[0].message).toBe(
              "Please select a valid country",
            );
          }
        });
      });

      it("should accept valid country codes", () => {
        const validCountries = ["US", "CA", "GB", "AU", "FR"];
        validCountries.forEach((country) => {
          const result = validateFormSubmission(fields, { COUNTRY: country });
          expect(result.success).toBe(true);
          if (result.success) {
            expect(result.data.COUNTRY).toBe(country);
          }
        });
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "COUNTRY",
          label: "Country",
          required: false,
          order: 0,
          width: "full",
        }),
      ];

      it("should accept missing field", () => {
        const result = validateFormSubmission(fields, {});
        expect(result.success).toBe(true);
      });

      it("should still validate format when value is provided", () => {
        const result = validateFormSubmission(fields, { COUNTRY: "XX" });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "Please select a valid country",
          );
        }
      });
    });
  });

  describe("Date Range Field", () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it("should handle dates with timezone information", () => {
      const NOW = new Date("2924-02-20T12:00:00.000Z");
      vi.setSystemTime(NOW);

      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const testCases = [
        {
          date: "2924-02-19T12:00:00.000Z", // Yesterday
          expected: true, // Yesterday is valid
          description: "Yesterday",
        },
        {
          date: NOW.toISOString(), // Now
          expected: true,
          description: "Today",
        },
        {
          date: "2924-02-21T12:00:00.000Z", // Tomorrow at same time
          expected: true,
          description: "Tomorrow",
        },
      ];

      testCases.forEach(({ date, expected, description }) => {
        console.log(`Testing ${description}: ${date}`);
        const result = validateFormSubmission(fields, {
          START_DATE: date,
          END_DATE: "2924-02-21T23:59:59.999Z", // End of the last test day
        });
        expect(result.success).toBe(expected);
        if (!result.success && !expected) {
          expect(result.error.issues[0].message).toBe(
            "Start date must be in the future",
          );
        }
      });
    });

    it("should reject empty values", () => {
      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const result = validateFormSubmission(fields, {
        START_DATE: "",
        END_DATE: "",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toHaveLength(2);
      }
    });

    it("should reject when end date is before start date", () => {
      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const result = validateFormSubmission(fields, {
        START_DATE: "2924-02-21",
        END_DATE: "2924-02-20",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "End date must be on or after start date",
        );
      }
    });

    it("should accept valid date range with different dates", () => {
      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const result = validateFormSubmission(fields, {
        START_DATE: "2194-03-20",
        END_DATE: "2194-03-21",
      });
      expect(result.success).toBe(true);
    });

    it("should accept valid date range with same date", () => {
      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const result = validateFormSubmission(fields, {
        START_DATE: "2194-03-20",
        END_DATE: "2194-03-20",
      });
      expect(result.success).toBe(true);
    });

    it("should reject dates in the past", () => {
      const fields = [
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          order: 0,
          width: "full",
        }),
      ];

      const pastDate = subDays(new Date(), 10);
      const pastDateStr = pastDate.toISOString().split("T")[0];

      const result = validateFormSubmission(fields, {
        START_DATE: pastDateStr,
        END_DATE: "2293-03-20",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "Start date must be in the future",
        );
      }
    });
  });

  describe("Numeric Fields", () => {
    describe("number schema tests (just to guide implementation; could delete later if we wish)", () => {
      function buildTestSchema(fooSchema: any) {
        return z.object({
          foo: fooSchema,
        });
      }

      describe("optional values", () => {
        it("parses null --> undefined", () => {
          const val = { foo: null };
          const result = buildTestSchema(buildBaseNumberSchema()).safeParse(
            val,
          );
          expect(result.data).toStrictEqual({ foo: undefined });
        });
        it("handles number validations too", () => {
          const greaterThanNinentySchema = buildTestSchema(
            buildBaseNumberSchema((baseSchema) => baseSchema.min(90)),
          );

          const val = { foo: "89" };
          const result = greaterThanNinentySchema.safeParse(val);

          expect(result.success).toEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual(
            "Number must be greater than or equal to 90",
          );

          const goodVal = { foo: "90" };
          const successResult = greaterThanNinentySchema.safeParse(goodVal);
          expect(successResult.success).toEqual(true);
        });

        it("leaves undefined alone", () => {
          //needs the value schema to be optional for it not to error
          const undefinedVal = { foo: undefined };
          const undefinedResult = buildTestSchema(
            buildBaseNumberSchema(),
          ).safeParse(undefinedVal);
          expect(undefinedResult.data).toStrictEqual({ foo: undefined });
        });

        it("parses numbers", () => {
          //needs the value schema to be optional for it not to error
          const val = { foo: "22" };
          const result = buildTestSchema(buildBaseNumberSchema()).safeParse(
            val,
          );
          expect(result.data).toStrictEqual({ foo: 22 });
        });
        it("trims whitespace", () => {
          const val = { foo: "  22 " };
          const result = buildTestSchema(buildBaseNumberSchema()).safeParse(
            val,
          );
          expect(result.data).toStrictEqual({ foo: 22 });
        });

        it("rejects invalid numbers", () => {
          const val = { foo: "  zie " };
          const result = buildTestSchema(buildBaseNumberSchema()).safeParse(
            val,
          );
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors[0].message).toEqual("must be a number");
        });
      });

      describe("required values", () => {
        it("null --> error", () => {
          const val = { foo: null };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual("Required");
        });

        it("empty str --> error", () => {
          const val = { foo: "" };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual("Required");
        });

        it("white space only str --> error", () => {
          const val = { foo: "   " };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual("Required");
        });

        it("undefined --> error", () => {
          //needs the value schema to be optional for it not to error
          const val = { foo: undefined };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual("Required");
        });

        it("works with number validations", () => {
          const val = { foo: "99" };
          const result = buildTestSchema(
            buildRequiredNumberSchema(
              buildBaseNumberSchema((numSchema) => numSchema.min(100)),
            ),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors.length).toEqual(1);
          expect(result.error?.errors[0].message).toEqual(
            "Number must be greater than or equal to 100",
          );
        });

        it("parses numbers", () => {
          //needs the value schema to be optional for it not to error
          const val = { foo: "22" };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.data).toStrictEqual({ foo: 22 });
        });
        it("trims whitespace", () => {
          const val = { foo: "  22 " };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.data).toStrictEqual({ foo: 22 });
        });

        it("rejects invalid numbers", () => {
          const val = { foo: "  zie " };
          const result = buildTestSchema(
            buildRequiredNumberSchema(buildBaseNumberSchema()),
          ).safeParse(val);
          expect(result.success).toStrictEqual(false);
          expect(result.error?.errors[0].message).toEqual("must be a number");
        });
      });
    });

    describe("when optional", () => {
      const fields = [
        createTestField({
          type: "GUEST_COUNT",
          label: "Guest Count",
          required: false,
          order: 0,
          width: "full",
        }),
        createTestField({
          type: "ROOM_COUNT",
          label: "Room Count",
          required: false,
          order: 1,
          width: "full",
        }),
        createTestField({
          type: "MEAL_COUNT",
          label: "Meal Count",
          required: false,
          order: 2,
          width: "full",
        }),
      ];

      it("should accept various empty values", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "",
          ROOM_COUNT: undefined,
          MEAL_COUNT: null,
          BUDGET: "",
        });
        expect(result.success, `Result: ${JSON.stringify(result)}`).toBe(true);
        if (result.success) {
          expect(result.data).toEqual({
            GUEST_COUNT: null,
            ROOM_COUNT: null,
            MEAL_COUNT: null,
          });
        }
      });

      it("should validate non-empty values against constraints", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "-1",
          ROOM_COUNT: "20001",
          MEAL_COUNT: "-5",
        });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                path: ["GUEST_COUNT"],
                message: "Cannot be negative",
              }),
              expect.objectContaining({
                path: ["ROOM_COUNT"],
                message: "Cannot exceed 20,000 rooms",
              }),
              expect.objectContaining({
                path: ["MEAL_COUNT"],
                message: "Cannot be negative",
              }),
            ]),
          );
        }
      });

      it("should accept valid numeric values", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "100",
          ROOM_COUNT: "50",
          MEAL_COUNT: "200",
        });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data).toEqual({
            GUEST_COUNT: 100,
            ROOM_COUNT: 50,
            MEAL_COUNT: 200,
          });
        }
      });

      it("should handle mixed valid and empty values", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "100",
          ROOM_COUNT: "",
          MEAL_COUNT: undefined,
          BUDGET: "1000",
        });

        expect(result.success).toBe(true);

        if (result.success) {
          expect(result.data).toEqual({
            GUEST_COUNT: 100,
            ROOM_COUNT: null,
            MEAL_COUNT: null,
          });
        }
      });
    });

    describe("when required", () => {
      const fields = [
        createTestField({
          type: "GUEST_COUNT",
          label: "Guest Count",
          required: true,
          order: 0,
          width: "full",
        }),
        createTestField({
          type: "ROOM_COUNT",
          label: "Room Count",
          required: true,
          order: 1,
          width: "full",
        }),
      ];

      it("should reject empty values", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "",
          ROOM_COUNT: undefined,
        });
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                path: ["GUEST_COUNT"],
                message: "Required",
              }),
              expect.objectContaining({
                path: ["ROOM_COUNT"],
                message: "Required",
              }),
            ]),
          );
        }
      });

      it("should accept valid numeric values", () => {
        const result = validateFormSubmission(fields, {
          GUEST_COUNT: "100",
          ROOM_COUNT: "50",
        });
        expect(result.success, `result: ${JSON.stringify(result)}`).toBe(true);
        if (result.success) {
          expect(result.data).toEqual({
            GUEST_COUNT: 100,
            ROOM_COUNT: 50,
          });
        }
      });
    });
  });
});

describe("Conditional Field Validation", () => {
  it("should validate required fields based on event type visibility", () => {
    const fields = [
      createTestField({
        type: "EVENT_TYPE",
        label: "Event Type",
        required: true,
        width: "full",
        order: 0,
      }),
      createTestField({
        type: "GUEST_COUNT",
        label: "Guest Count",
        required: true,
        width: "full",
        order: 1,
        hideForEventTypes: ["corporate_meeting"],
      }),
      createTestField({
        type: "ROOM_COUNT",
        label: "Room Count",
        required: true,
        width: "full",
        order: 2,
        hideForEventTypes: ["wedding"],
      }),
    ];

    // For wedding event type:
    // - GUEST_COUNT should be required (visible)
    // - ROOM_COUNT should not be required (hidden)
    const weddingResult = validateFormSubmission(fields, {
      EVENT_TYPE: "wedding",
      GUEST_COUNT: "", // Empty required field that is visible
      ROOM_COUNT: "", // Empty required field that is hidden
    });
    expect(weddingResult.success).toBe(false);
    if (!weddingResult.success) {
      expect(weddingResult.error.issues).toHaveLength(1);
      expect(weddingResult.error.issues[0].path).toEqual(["GUEST_COUNT"]);
    }

    // For corporate meeting:
    // - GUEST_COUNT should not be required (hidden)
    // - ROOM_COUNT should be required (visible)
    const corporateResult = validateFormSubmission(fields, {
      EVENT_TYPE: "corporate_meeting",
      GUEST_COUNT: "", // Empty required field that is hidden
      ROOM_COUNT: "", // Empty required field that is visible
    });
    expect(corporateResult.success).toBe(false);
    if (!corporateResult.success) {
      expect(corporateResult.error.issues).toHaveLength(1);
      expect(corporateResult.error.issues[0].path).toEqual(["ROOM_COUNT"]);
    }
  });

  it("should validate EVENT_TYPE field regardless of hide rules", () => {
    const fields = [
      createTestField({
        type: "EVENT_TYPE",
        label: "Event Type",
        required: true,
        width: "full",
        order: 0,
        hideForEventTypes: ["wedding", "corporate_meeting"], // These should be ignored
      }),
    ];

    // EVENT_TYPE should always be validated
    const result = validateFormSubmission(fields, {
      EVENT_TYPE: "",
    });
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues).toHaveLength(1);
      expect(result.error.issues[0].path).toEqual(["EVENT_TYPE"]);
    }
  });
});

describe("Date Field Parsing", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    // Set a fixed date for testing
    vi.setSystemTime(new Date("2025-03-22T00:00:00.000Z"));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should preserve the exact date string for START_DATE and END_DATE", () => {
    const fields = [
      createTestField({
        type: "START_DATE",
        label: "Start Date",
        required: true,
        order: 0,
        width: "full",
      }),
      createTestField({
        type: "END_DATE",
        label: "End Date",
        required: true,
        order: 1,
        width: "full",
      }),
    ];

    // Test with a specific date string that would be affected by timezone conversion
    const result = validateFormSubmission(fields, {
      START_DATE: "2026-10-25",
      END_DATE: "2026-10-30",
    });

    expect(result.success).toBe(true);
    if (result.success) {
      // Verify the dates are preserved exactly as strings
      expect(result.data.START_DATE).toBe("2026-10-25");
      expect(result.data.END_DATE).toBe("2026-10-30");
    }
  });

  it("should handle date objects correctly for START_DATE and END_DATE", () => {
    const fields = [
      createTestField({
        type: "START_DATE",
        label: "Start Date",
        required: true,
        order: 0,
        width: "full",
      }),
      createTestField({
        type: "END_DATE",
        label: "End Date",
        required: true,
        order: 1,
        width: "full",
      }),
    ];

    // Test with Date objects
    const startDate = "2026-10-25";
    const endDate = "2026-10-30";

    const result = validateFormSubmission(fields, {
      START_DATE: startDate,
      END_DATE: endDate,
    });

    expect(result.success, `result: ${JSON.stringify(result)}`).toBe(true);
    if (result.success) {
      expect(result.data.START_DATE).toBe("2026-10-25");
      expect(result.data.END_DATE).toBe("2026-10-30");
    }
  });
});

describe("Field Preprocessing", () => {
  it("should handle text preprocessing correctly", () => {
    const fields = [
      createTestField({
        type: "FIRST_NAME",
        label: "First Name",
        required: true,
        order: 0,
      }),
    ];

    const testCases = [
      {
        input: "  ",
        expected: false,
        message: "should reject whitespace only",
      },
      {
        input: " John ",
        expected: true,
        message: "should trim and accept valid text",
      },
      {
        input: undefined,
        expected: false,
        message: "should reject undefined for required field",
      },
      { input: "", expected: false, message: "should reject empty string" },
    ];

    testCases.forEach(({ input, expected, message }) => {
      const result = validateFormSubmission(fields, { FIRST_NAME: input });
      expect(result.success, message).toBe(expected);
    });
  });

  it("should handle number preprocessing correctly", () => {
    const fields = [
      createTestField({
        type: "GUEST_COUNT",
        label: "Guest Count",
        required: true,
        order: 0,
      }),
    ];

    const testCases = [
      {
        input: "  ",
        expected: false,
        message: "should reject whitespace only",
      },
      {
        input: " 123 ",
        expected: true,
        message: "should trim and parse valid number",
      },
      {
        input: "abc",
        expected: false,
        message: "should reject non-numeric string",
      },
      { input: "0", expected: true, message: "should accept zero" },
      {
        input: undefined,
        expected: false,
        message: "should reject undefined for required field",
      },
    ];

    testCases.forEach(({ input, expected, message }) => {
      const result = validateFormSubmission(fields, { GUEST_COUNT: input });
      expect(result.success, message).toBe(expected);
    });
  });
});

describe("Field Visibility Rules", () => {
  it("should handle nested visibility rules correctly", () => {
    const fields = [
      createTestField({
        type: "EVENT_TYPE",
        label: "Event Type",
        required: true,
        order: 0,
      }),
      createTestField({
        type: "EVENT_DATE_RANGE",
        label: "Event Dates",
        required: true,
        order: 1,
        hideForEventTypes: ["corporate_meeting"],
      }),
      createTestField({
        type: "GUEST_COUNT",
        label: "Guest Count",
        required: true,
        order: 2,
        hideForEventTypes: ["corporate_meeting"],
      }),
    ];

    // Test that hidden compound fields don't affect validation
    const corporateResult = validateFormSubmission(fields, {
      EVENT_TYPE: "corporate_meeting",
      START_DATE: "", // Should be ignored
      END_DATE: "", // Should be ignored
      GUEST_COUNT: "", // Should be ignored
    });
    expect(corporateResult.success).toBe(true);

    // Test that visible compound fields are properly validated
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2023-12-31T12:00:00.000Z"));

    const weddingResult = validateFormSubmission(fields, {
      EVENT_TYPE: "wedding",
      START_DATE: "2024-01-01",
      END_DATE: "2024-01-02",
      GUEST_COUNT: "100",
    });
    expect(weddingResult.success).toBe(true);

    vi.useRealTimers();
  });
});

describe("Currency Input Type", () => {
  describe("server-side validation", () => {
    const fields = [
      createTestField({
        type: "BUDGET",
        label: "Budget",
        required: true,
        order: 0,
        width: "full",
      }),
    ];

    it("should accept positive numeric values", () => {
      const result = validateFormSubmission(fields, { BUDGET: "1000.75" });
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.BUDGET).toBe(1000.75);
      }
    });

    it("should accept empty values as null", () => {
      const result = validateFormSubmission(
        createTestFieldList([
          {
            type: "BUDGET",
            required: false,
          },
          { type: "ROOM_COUNT" },
        ]),
        { BUDGET: "", ROOM_COUNT: "" },
      );

      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.ROOM_COUNT).toBe(null);
        expect(result.data.BUDGET).toBe(null);
      }
    });

    it("should reject negative values", () => {
      const result = validateFormSubmission(fields, { BUDGET: "-100" });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues).toContainEqual(
          expect.objectContaining({
            path: ["BUDGET"],
            message: "Cannot be negative",
          }),
        );
      }
    });

    it("should reject non-numeric values", () => {
      const result = validateFormSubmission(fields, { BUDGET: "abc" });
      expect(result.success).toBe(false);
    });
  });
});

describe("Edge Cases", () => {
  it("should handle malformed input data gracefully", () => {
    const fields = [
      createTestField({
        type: "EMAIL",
        label: "Email",
        required: true,
        order: 0,
      }),
    ];

    const testCases = [
      { input: null, expected: false },
      { input: {}, expected: false },
      { input: [], expected: false },
      { input: new Date(), expected: false },
      { input: Symbol("test"), expected: false },
    ];

    testCases.forEach(({ input, expected }) => {
      const result = validateFormSubmission(fields, { EMAIL: input });
      expect(result.success).toBe(expected);
    });
  });

  it("should handle maximum field values correctly", () => {
    const fields = [
      createTestField({
        type: "GUEST_COUNT",
        label: "Guest Count",
        required: true,
        order: 0,
      }),
      createTestField({
        type: "ROOM_COUNT",
        label: "Room Count",
        required: true,
        order: 1,
      }),
    ];

    const testCases = [
      {
        data: { GUEST_COUNT: "500001", ROOM_COUNT: "20000" },
        expected: false,
        message: "should reject guest count above maximum",
      },
      {
        data: { GUEST_COUNT: "500000", ROOM_COUNT: "20000" },
        expected: true,
        message: "should accept maximum values",
      },
      {
        data: { GUEST_COUNT: "500000", ROOM_COUNT: "20001" },
        expected: false,
        message: "should reject room count above maximum",
      },
    ];

    testCases.forEach(({ data, expected, message }) => {
      const result = validateFormSubmission(fields, data);
      expect(result.success, `${message} : ${JSON.stringify(result)}`).toBe(
        expected,
      );
    });
  });
});

describe("CITY Field", () => {
  describe("when required", () => {
    const fields = [
      createTestField({
        type: "CITY",
        label: "City",
        required: true,
        order: 0,
        width: "full",
      }),
    ];

    it("should reject empty strings", () => {
      const result = validateFormSubmission(fields, { CITY: "" });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Required");
      }
    });

    it("should accept valid city names", () => {
      const validCities = [
        "New York",
        "Los Angeles",
        "Chicago",
        "San Francisco",
        "Denver",
      ];
      validCities.forEach((city) => {
        const result = validateFormSubmission(fields, { CITY: city });
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.CITY).toBe(city);
        }
      });
    });

    it("should trim whitespace from city names", () => {
      const result = validateFormSubmission(fields, { CITY: "  Denver  " });
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.CITY).toBe("Denver");
      }
    });
  });

  describe("when optional", () => {
    const fields = [
      createTestField({
        type: "CITY",
        label: "City",
        required: false,
        order: 0,
        width: "full",
      }),
    ];

    it("should accept missing field", () => {
      const result = validateFormSubmission(fields, {});
      expect(result.success).toBe(true);
    });

    it("should accept empty string", () => {
      const result = validateFormSubmission(fields, { CITY: "" });
      expect(result.success).toBe(true);
    });
  });
});

describe("Country-State-City Dependencies", () => {
  describe("when all fields are required", () => {
    const fields = [
      createTestField({
        type: "COUNTRY",
        label: "Country",
        required: true,
        order: 0,
        width: "full",
      }),
      createTestField({
        type: "STATE",
        label: "State/Province",
        required: true,
        order: 1,
        width: "full",
      }),
      createTestField({
        type: "CITY",
        label: "City",
        required: true,
        order: 2,
        width: "full",
      }),
    ];

    it("should accept valid combinations", () => {
      const validCombinations = [
        { country: "US", state: "CA", city: "Los Angeles" },
        { country: "US", state: "NY", city: "New York" },
        { country: "CA", state: "ON", city: "Toronto" },
        { country: "CA", state: "BC", city: "Vancouver" },
        { country: "AU", state: "NSW", city: "Sydney" },
      ];

      validCombinations.forEach(({ country, state, city }) => {
        const result = validateFormSubmission(fields, {
          COUNTRY: country,
          STATE: state,
          CITY: city,
        });
        expect(
          result.success,
          `${country}, ${state}, ${city} should be valid`,
        ).toBe(true);
      });
    });

    it("should reject when country is empty but state and city are provided", () => {
      const result = validateFormSubmission(fields, {
        COUNTRY: "",
        STATE: "CA",
        CITY: "Los Angeles",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some((issue) => issue.path.includes("COUNTRY")),
        ).toBe(true);
      }
    });

    it("should reject when state is empty but country and city are provided", () => {
      const result = validateFormSubmission(fields, {
        COUNTRY: "US",
        STATE: "",
        CITY: "Los Angeles",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some((issue) => issue.path.includes("STATE")),
        ).toBe(true);
      }
    });

    it("should reject when city is empty but country and state are provided", () => {
      const result = validateFormSubmission(fields, {
        COUNTRY: "US",
        STATE: "CA",
        CITY: "",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some((issue) => issue.path.includes("CITY")),
        ).toBe(true);
      }
    });

    it("should reject invalid state for selected country", () => {
      const result = validateFormSubmission(fields, {
        COUNTRY: "US",
        STATE: "ON",
        CITY: "Chicago",
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some(
            (issue) =>
              issue.message ===
              "Please select a valid state/region for the selected country",
          ),
        ).toBe(true);
      }
    });

    it("should not require STATE field for countries without regions", () => {
      // Use American Samoa (AS) which doesn't have regions in country-state-city library
      const countriesWithoutRegions = ["AS"]; // American Samoa

      countriesWithoutRegions.forEach((country) => {
        const result = validateFormSubmission(fields, {
          COUNTRY: country,
          STATE: "", // Empty state should be valid for countries without regions
          CITY: "City Name",
        });

        expect(
          result.success,
          `${country} should not require STATE field`,
        ).toBe(true);

        if (!result.success) {
          console.log(`Failed for country ${country}:`, result.error);
        }
      });
    });
  });

  describe("with conditional visibility", () => {
    const fields = [
      createTestField({
        type: "EVENT_TYPE",
        label: "Event Type",
        required: true,
        order: 0,
        width: "full",
      }),
      createTestField({
        type: "COUNTRY",
        label: "Country",
        required: true,
        order: 1,
        width: "full",
        hideForEventTypes: ["wedding"],
      }),
      createTestField({
        type: "STATE",
        label: "State/Province",
        required: true,
        order: 2,
        width: "full",
        hideForEventTypes: ["wedding"],
      }),
      createTestField({
        type: "CITY",
        label: "City",
        required: true,
        order: 3,
        width: "full",
        hideForEventTypes: ["wedding"],
      }),
    ];

    it("should ignore hidden country/state/city fields for wedding events", () => {
      const result = validateFormSubmission(fields, {
        EVENT_TYPE: "wedding",
        COUNTRY: "", // Should be ignored
        STATE: "", // Should be ignored
        CITY: "", // Should be ignored
      });

      expect(result.success).toBe(true);
    });

    it("should validate country/state/city fields for non-wedding events", () => {
      const result = validateFormSubmission(fields, {
        EVENT_TYPE: "corporate_meeting",
        COUNTRY: "", // Should be required
        STATE: "CA", // Should be validated, but country is missing
        CITY: "San Francisco",
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(
          result.error.issues.some((issue) => issue.path.includes("COUNTRY")),
        ).toBe(true);
      }
    });
  });
});

describe("File Upload Field", () => {
  describe("when required", () => {
    const fields = [
      createTestField({
        type: "FILE_UPLOAD",
        label: "File Upload",
        required: true,
        order: 0,
        width: "full",
      }),
    ];

    it("should reject empty arrays", () => {
      const result = validateFormSubmission(fields, { FILE_UPLOAD: [] });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Required");
      }
    });

    it("should accept valid file references", () => {
      const fileData = [
        {
          fileId: "123e4567-e89b-12d3-a456-************",
          fileName: "document.pdf",
          fileSize: "1024",
          fileType: "application/pdf",
          uploadKey:
            "pending/04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe7f943",
          keyWithoutStatusPrefix:
            "04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe",
        },
      ];

      const result = validateFormSubmission(fields, { FILE_UPLOAD: fileData });
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.FILE_UPLOAD).toEqual([
          {
            fileId: "123e4567-e89b-12d3-a456-************",
            fileName: "document.pdf",
            fileSize: 1024,
            fileType: "application/pdf",
            uploadKey:
              "pending/04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe7f943",
            keyWithoutStatusPrefix:
              "04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe",
          },
        ]);
      }
    });

    it("should accept array of file IDs", () => {
      const fileIds = [
        "123e4567-e89b-12d3-a456-************",
        "123e4567-e89b-12d3-a456-************",
      ];

      const result = validateFormSubmission(fields, { FILE_UPLOAD: fileIds });
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.FILE_UPLOAD).toEqual(fileIds);
      }
    });

    it("should reject invalid file references", () => {
      const invalidFileData = [
        {
          fileId: "not-a-uuid", // Invalid UUID
          fileName: "document.pdf",
          fileSize: "1024",
          fileType: "application/pdf",
          uploadKey:
            "pending/04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe7f943",
          keyWithoutStatusPrefix:
            "04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe",
        },
      ];

      const result = validateFormSubmission(fields, {
        FILE_UPLOAD: invalidFileData,
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "File ID must be a valid UUID",
        );
      }
    });

    it("should reject negative file sizes", () => {
      const invalidFileData = [
        {
          fileId: "123e4567-e89b-12d3-a456-************",
          fileName: "document.pdf",
          fileSize: "-1024", // Negative size
          fileType: "application/pdf",
          uploadKey:
            "pending/04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe7f943",
        },
      ];

      const result = validateFormSubmission(fields, {
        FILE_UPLOAD: invalidFileData,
      });
      expect(result.success).toBe(false);
    });
  });

  describe("when optional", () => {
    const fields = [
      createTestField({
        type: "FILE_UPLOAD",
        label: "File Upload",
        required: false,
        order: 0,
        width: "full",
      }),
    ];

    it("should accept empty arrays", () => {
      const result = validateFormSubmission(fields, { FILE_UPLOAD: [] });
      expect(result.success).toBe(true);
    });

    it("should accept missing field", () => {
      const result = validateFormSubmission(fields, {});
      expect(result.success).toBe(true);
    });

    it("should still validate file data when provided", () => {
      const invalidFileData = [
        {
          fileId: "not-a-uuid",
          fileName: "document.pdf",
          fileSize: "1024",
          fileType: "application/pdf",
          uploadKey:
            "pending/04f82673-01a7-42e9-8fc2-20caa9b65b37/adbed2eb-28b6-4465-a025-61e553b8c185/fdea8ea5-0611-4738-9c6b-f246cfe7f943",
        },
      ];

      const result = validateFormSubmission(fields, {
        FILE_UPLOAD: invalidFileData,
      });
      expect(result.success).toBe(false);
    });
  });
});

// Types for form rendering mode
export type FormRenderMode = "iframe" | "html";

export interface FormRenderConfig {
  mode: FormRenderMode;
  theme?: {
    primaryColor: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    padding: string;
  };
  customCss?: string;
  customJs?: string;
  initScript?: string;
}
