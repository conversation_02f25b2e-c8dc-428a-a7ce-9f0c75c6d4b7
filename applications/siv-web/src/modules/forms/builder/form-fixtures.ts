import { Form, NewFormWithTheme } from "./types";
import { themeFixtures } from "./theme-fixtures";
import { WithOptionalFields } from "@/modules/admin/forms/type-helpers";

/**
 * Collection of production form fixtures for testing and demonstration purposes
 * These represent real form configurations being used in production
 */
export const formFixtures: Record<string, Form> = {
  // Paradise Point form configuration - copied from a real production form
  paradisePoint: {
    id: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
    name: "[TEST] Paradise Point LOCAL Clone",
    description: "",
    propertyId: "7d6a5f2b-8824-4964-8c8d-6efe41ea6e8e",
    themeId: "ff177ebd-c625-4802-bbc3-d5023fae0768",
    theme: {
      id: "ff177ebd-c625-4802-bbc3-d5023fae0768",
      name: "Default Theme",
      primaryColor: "#101213ff",
      backgroundColor: "#ffffffff",
      textColor: "#58585aff",
      borderColor: "#ffffffff",
      inputBorderRadius: "0rem",
      buttonBorderRadius: "0rem",
      padding: "1rem",
      buttonTextColor: "#ffffffff",
      buttonAlignment: "left",
      font: '"Open Sans", sans-serif',
      fieldBackgroundColor: "#223a4eff",
      inputTextColor: "#ffffffff",
      formBorderRadius: "0",
      placeholderColor: "#6b7280",
      createdAt: new Date("2025-02-21T00:15:25.824Z"),
      updatedAt: new Date("2025-03-09T03:11:48.606Z"),
    },
    fields: [
      {
        id: "0a2cb3f4-1308-4b35-a0e3-81b826afcda0",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "EVENT_TYPE",
        label: "Event Type",
        required: true,
        width: "half",
        placeholder: "",
        order: 0,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "d42cccf1-ef19-42c7-848e-a8e1e395f27a",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "EVENT_NAME",
        label: "Event Name",
        required: false,
        width: "half",
        placeholder: "",
        order: 1,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "1ac6d13f-015c-4eb4-95ae-14581debea3f",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "COMPANY",
        label: "Organization Name",
        required: false,
        width: "full",
        placeholder: "",
        order: 2,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "1901ca56-187a-4ce2-b4f1-108322a7fcef",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "FIRST_NAME",
        label: "First Name",
        required: true,
        width: "half",
        placeholder: "",
        order: 3,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "1796d463-3c67-4fc3-b64a-f39eb9a0b851",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "LAST_NAME",
        label: "Last Name",
        required: true,
        width: "half",
        placeholder: "",
        order: 4,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "98e036eb-27e7-4ec5-8f26-3bf514f803c2",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "PHONE",
        label: "Cell Phone",
        required: true,
        width: "half",
        placeholder: "e.g. +****************",
        order: 5,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "b1204b9b-3e9d-457a-9e9f-a835aa63ddd4",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "EMAIL",
        label: "Email Address",
        required: true,
        width: "half",
        placeholder: "",
        order: 6,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "a279acab-f840-4edf-a024-838cba597666",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "COUNTRY",
        label: "Country",
        required: true,
        width: "full",
        placeholder: "",
        order: 7,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "6ddee254-4198-47fd-835c-27ec0353382e",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "CITY",
        label: "City",
        required: true,
        width: "half",
        placeholder: "",
        order: 8,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "684b805a-1000-43e4-b239-80f7aaa8bc25",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "STATE",
        label: "State/Province",
        required: true,
        width: "half",
        placeholder: "Enter your state or province",
        order: 9,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },

      {
        id: "3cc0122d-435a-4eac-815f-5f17321fed3c",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "POSTAL_CODE",
        label: "Postal Code",
        required: true,
        width: "half",
        placeholder: "",
        order: 10,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "6aaf3979-498d-458f-80ad-4f97ad8e5085",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "BUDGET",
        label: "Estimated Budget",
        required: false,
        width: "half",
        placeholder: "",
        order: 11,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "4271578e-49f5-4d49-a2aa-2b9e95ca0114",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "START_DATE",
        label: "Preferred Start Date",
        required: true,
        width: "half",
        placeholder: null,
        order: 12,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "a2bbb13c-81e1-440f-83a2-0fefd4c1e9f6",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "END_DATE",
        label: "Preferred End Date",
        required: true,
        width: "half",
        placeholder: null,
        order: 13,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "8fabd13a-82a0-42ef-a368-77ca71b9a245",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "EVENT_NEEDS",
        label: "Event Needs",
        required: false,
        width: "half",
        placeholder: null,
        order: 14,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "ef8e2657-5c2a-4716-940d-bc7a23ff69ca",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "FLEXIBLE_DATES",
        label: "Are your dates flexible?",
        required: false,
        width: "half",
        placeholder: null,
        order: 15,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
        checkboxText: "Yes",
      },
      {
        id: "0bd36ba4-dd5b-4a7a-964b-c7047ec61ffd",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "GUEST_COUNT",
        label: "Number of Guests",
        required: true,
        width: "half",
        placeholder: "",
        order: 16,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "bac206bf-043d-4f88-9a47-0e7c40e0dbc5",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "ROOM_COUNT",
        label: "Number of Rooms Needed",
        required: true,
        width: "half",
        placeholder: "",
        order: 17,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "93bc3efc-29e9-48cc-a997-e38265457400",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "EVENT_DESCRIPTION",
        label: "Tell Us More",
        required: false,
        width: "full",
        placeholder: "",
        order: 18,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
      },
      {
        id: "1a0eb5e9-2566-4146-b4c1-f8f0998056a1",
        formId: "14afa14c-e4b0-4b15-a102-439f83c1cfc6",
        type: "MARKETING_CONSENT",
        label: "",
        required: true,
        width: "full",
        placeholder: null,
        order: 19,
        hideForEventTypes: [],
        rowBreakAfter: false,
        createdAt: new Date("2025-03-09T03:11:48.606Z"),
        updatedAt: new Date("2025-03-09T03:11:48.606Z"),
        checkboxText:
          "I agree to receive transactional & marketing communications from Paradise Point via email, text message, and telephone. Our Privacy Policy & Terms can be found at https://paradisepoint.com/privacy-policy/ and https://paradisepoint.com/terms-of-use/. Txt and data rates may apply.  Reply STOP at any time to unsubscribe.",
      },
    ],
    redirectUrl: null,
    allowedDomains: [],
    createdAt: new Date("2025-02-20T03:55:48.532Z"),
    updatedAt: new Date("2025-03-09T03:11:55.135Z"),
  },

  // Add other form fixtures as needed
};
