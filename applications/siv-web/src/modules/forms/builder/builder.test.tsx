import { beforeEach, describe, expect, it, vi } from "vitest";
import "@testing-library/jest-dom";
import React from "react";
import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import FormBuilder, { FormBuilderProps } from "./builder";
import { Form, FormField } from "./types";
import { DEFAULT_THEME } from "./ThemeEditor";
import { formFactory } from "@/modules/admin/forms/form-test-factories";
import { createTestField } from "@/modules/admin/forms/new-form-test-factories";

window.HTMLElement.prototype.hasPointerCapture = vi.fn(); //needed for radix select components since happy-dom doesn't provide this

describe("FormBuilder", () => {
  const mockOnSave = vi.fn();
  const defaultProps: FormBuilderProps = {
    backLink: { href: "/last-page-link", previousPageTitle: "Last Page" },
    propertyId: "test-property-id",
    onSave: mockOnSave,
    isSaving: false,
    initialForm: null,
  };

  beforeEach(() => {
    mockOnSave.mockClear();
  });

  const setup = () => {
    const user = userEvent.setup();
    const renderResult = render(<FormBuilder {...defaultProps} />);
    return {
      user,
      ...renderResult,
    };
  };

  it("renders empty form builder when no initial form is provided", () => {
    render(<FormBuilder {...defaultProps} />);

    expect(screen.getByLabelText(/form name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByTestId("header-save-button")).toBeInTheDocument();
  });

  it("allows adding a new field", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Click the First Name field in the sidebar
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    // Verify field was added
    expect(screen.getByTestId("sortable-FIRST_NAME")).toBeInTheDocument();
  });

  it("allows updating field properties including placeholder", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add a field first
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    // Click on the field to edit it
    const field = screen.getByTestId("sortable-FIRST_NAME");
    await user.click(field);

    // Update field label
    const labelInput = screen.getByTestId("field-settings-label-input");
    await user.clear(labelInput);
    await user.type(labelInput, "Custom Label");

    // Update placeholder
    const placeholderInput = screen.getByLabelText("Placeholder");
    await user.clear(placeholderInput);
    await user.type(placeholderInput, "Custom placeholder");

    // Get preview elements for verification
    const previewContainer = screen.getByTestId("preview-container");
    const preview = within(previewContainer).getByTestId("form-container");
    const firstNameLabel = within(preview).getByTestId("label-FIRST_NAME");

    // Toggle required - verify it's initially required
    const requiredCheckbox = screen.getByTestId(
      "field-settings-required-checkbox",
    );
    expect(firstNameLabel.querySelector(".ef-required")).toBeInTheDocument();
    await user.click(requiredCheckbox);

    // Change width
    const widthSelect = screen.getByTestId("field-settings-width-select");
    await user.click(widthSelect);
    await user.click(screen.getByTestId("field-width-half"));

    // Verify updates
    // Check label text in preview
    expect(within(preview).getByTestId("label-FIRST_NAME")).toHaveTextContent(
      "Custom Label",
    );

    // Check for required indicator - should now be removed
    expect(
      firstNameLabel.querySelector(".ef-required"),
    ).not.toBeInTheDocument();

    // Check placeholder in preview
    expect(within(preview).getByTestId("input-FIRST_NAME")).toHaveAttribute(
      "placeholder",
      "Custom placeholder",
    );

    await user.keyboard("{Escape}"); //ensure the sheet is closed
    // Save the form and verify placeholder is included
    await user.click(screen.getByTestId("header-save-button"));
    expect(mockOnSave).toHaveBeenCalledTimes(1);
    const savedForm = mockOnSave.mock.calls[0][0];
    expect(savedForm.fields[0]).toMatchObject({
      type: "FIRST_NAME",
      label: "Custom Label",
      required: false,
      width: "half",
      placeholder: "Custom placeholder",
      order: 0,
    });
  });

  it("restricts EVENT_DATE_RANGE field to full width only", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add an EVENT_DATE_RANGE field
    const dateRangeButton = screen.getByRole("button", {
      name: /event dates/i,
    });
    await user.click(dateRangeButton);

    // Click on the field to edit it
    const field = screen.getByTestId("sortable-EVENT_DATE_RANGE");
    await user.click(field);

    // Verify width options
    const widthSelect = screen.getByTestId("field-settings-width-select");
    await user.click(widthSelect);

    // Half width should be disabled
    const halfWidthOption = screen.getByTestId("field-width-half");
    expect(halfWidthOption).toHaveAttribute("data-disabled");
    expect(halfWidthOption).toHaveClass("opacity-50");

    // Full width should be enabled and selected
    const fullWidthOption = screen.getByTestId("field-width-full");
    expect(fullWidthOption).not.toHaveAttribute("data-disabled");
    expect(fullWidthOption).not.toHaveClass("opacity-50");

    await user.keyboard("{Escape}"); // Close the width selector
    await user.keyboard("{Escape}"); // Close the field editor

    // Save the form and verify the width
    await user.click(screen.getByTestId("header-save-button"));
    expect(mockOnSave).toHaveBeenCalledTimes(1);
    const savedForm = mockOnSave.mock.calls[0][0];
    expect(savedForm.fields[0]).toMatchObject({
      type: "EVENT_DATE_RANGE",
      width: "full",
    });
  });

  it("allows removing a field", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add a field first
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    const firstNameSortableField = screen.getByTestId("sortable-FIRST_NAME");
    expect(firstNameSortableField).toBeInTheDocument();
    await user.click(firstNameSortableField); //open the panel

    const removeButton = screen.getByTestId("remove-field-button-FIRST_NAME");
    await user.click(removeButton);

    // Verify field was removed
    expect(screen.queryByTestId("sortable-FIRST_NAME")).not.toBeInTheDocument();
  });

  it("loads and displays initial form data correctly", () => {
    const initialForm: Form = {
      id: "test-id",
      name: "Test Form",
      description: "Test Description",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        name: "Test Theme",
        primaryColor: "#FF0000",
        backgroundColor: "#FFFFFF",
        textColor: "#000000",
        borderColor: "#CCCCCC",
        placeholderColor: "#CFCFCF",
        inputBorderRadius: "4px",
        buttonBorderRadius: "4px",
        padding: "16px",
        buttonTextColor: "#FFFFFF",
        buttonAlignment: "left",
        font: "Arial, sans-serif",
        fieldBackgroundColor: "#FFFFFF",
        inputTextColor: "#000000",
        formBorderRadius: "4px",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        {
          id: "field-1",
          formId: "test-id",
          type: "FIRST_NAME",
          label: "Custom First Name",
          required: true,
          width: "half",
          hideForEventTypes: [],
          order: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

    // Verify form data is loaded
    expect(screen.getByLabelText(/form name/i)).toHaveValue("Test Form");
    expect(screen.getByLabelText(/description/i)).toHaveValue(
      "Test Description",
    );

    // Verify field is loaded
    expect(screen.getAllByTestId("label-FIRST_NAME")[0]).toHaveTextContent(
      "Custom First Name",
    );
  });

  it("calls onSave with updated form data when save button is clicked", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Fill in form details
    await user.type(screen.getByLabelText(/form name/i), "New Form");
    await user.type(screen.getByLabelText(/description/i), "New Description");

    // Add a field
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    // Save the form
    await user.click(screen.getByTestId("header-save-button"));

    // Verify onSave was called with correct data
    expect(mockOnSave).toHaveBeenCalledTimes(1);
    const savedForm = mockOnSave.mock.calls[0][0];
    expect(savedForm).toMatchObject({
      name: "New Form",
      description: "New Description",
      propertyId: "test-property-id",
      fields: expect.arrayContaining([
        expect.objectContaining({
          type: "FIRST_NAME",
          label: "First Name",
          required: true,
          width: "full",
          order: 0,
        }),
      ]),
    });
  });

  it("disables save button while saving", () => {
    render(<FormBuilder {...defaultProps} isSaving={true} />);

    const saveButton = screen.getByTestId("header-save-button");
    expect(saveButton).toBeDisabled();
    expect(saveButton).toHaveTextContent(/saving/i);
  });

  it("loads and displays custom placeholders from initial form data", () => {
    const initialForm: Form = {
      id: "test-id",
      name: "Test Form",
      description: "Test Description",
      propertyId: "test-property-id",
      themeId: "test-theme-id",
      theme: {
        id: "test-theme-id",
        name: "Test Theme",
        primaryColor: "#FF0000",
        backgroundColor: "#FFFFFF",
        placeholderColor: "#CFCFCF",
        textColor: "#000000",
        borderColor: "#CCCCCC",
        inputBorderRadius: "4px",
        buttonBorderRadius: "4px",
        padding: "16px",
        buttonTextColor: "#FFFFFF",
        buttonAlignment: "left",
        font: "Arial, sans-serif",
        fieldBackgroundColor: "#FFFFFF",
        inputTextColor: "#000000",
        formBorderRadius: "4px",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      fields: [
        {
          id: "field-1",
          formId: "test-id",
          type: "FIRST_NAME",
          label: "Custom First Name",
          required: true,
          width: "half",
          placeholder: "Custom placeholder",
          hideForEventTypes: [],
          order: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      redirectUrl: null,
      allowedDomains: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

    // Click on the field to edit it
    const field = screen.getByTestId("sortable-FIRST_NAME");
    // userEvent.click(field);

    // Verify placeholder is loaded
    expect(screen.getAllByTestId("input-FIRST_NAME")[0]).toHaveAttribute(
      "placeholder",
      "Custom placeholder",
    );
  });

  describe("Hide Rules", () => {
    it("should show logic icon when field has hide rules", async () => {
      const user = userEvent.setup();
      const initialForm = formFactory.build({
        fields: [
          createTestField({
            id: "field-1",
            type: "GUEST_COUNT",
            hideForEventTypes: ["wedding", "celebration"],
          }),
        ],
      });

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Verify the logic icon is present
      expect(
        screen.getByTestId("conditional-logic-indicator-GUEST_COUNT"),
      ).toBeInTheDocument();

      // Add a field without hide rules and verify it doesn't have the icon
      const roomCountButton = screen.getByRole("button", {
        name: /number of rooms/i,
      });
      await user.click(roomCountButton);
      expect(
        screen.queryByTestId("conditional-logic-indicator-ROOM_COUNT"),
      ).not.toBeInTheDocument();
    });

    it("should not show hide rules section for EVENT_TYPE field", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add EVENT_TYPE field
      const eventTypeButton = screen.getByRole("button", {
        name: /event type/i,
      });
      await user.click(eventTypeButton);

      // Click to edit the field
      const field = screen.getByTestId("sortable-EVENT_TYPE");
      await user.click(field);

      // Hide rules section should not be present
      expect(
        screen.queryByText("Hide For Event Types"),
      ).not.toBeInTheDocument();
    });

    it("should show hide rules section for non-EVENT_TYPE fields", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add GUEST_COUNT field
      const guestCountButton = screen.getByRole("button", {
        name: /number of guests/i,
      });
      await user.click(guestCountButton);

      // Click to edit the field
      const field = screen.getByTestId("sortable-GUEST_COUNT");
      await user.click(field);

      // Hide rules section should be present
      expect(screen.getByText("Hide For Event Types")).toBeInTheDocument();
    });

    it("should allow selecting multiple event types to hide field for", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add GUEST_COUNT field
      const guestCountButton = await screen.findByRole("button", {
        name: /number of guests/i,
      });
      await user.click(guestCountButton);

      // Click to edit the field
      const field = await screen.findByTestId("sortable-GUEST_COUNT");
      await user.click(field);

      // Click the multi-select to open it
      const multiSelect = await screen.findByTestId(
        "hide-for-event-types-select",
      );
      await user.click(multiSelect);

      // Get the input and type into it directly
      const input = await screen.findByTestId(
        "hide-for-event-types-select-input",
      );
      await user.type(input, "Wedding");
      await user.keyboard("{Enter}");

      // Type the second option
      await user.type(input, "Corporate Meeting");
      await user.keyboard("{Enter}");

      // Verify badges are present - use findByTestId to wait for them
      expect(
        await screen.findByTestId("hide-for-event-types-select-badge-wedding"),
      ).toBeInTheDocument();
      expect(
        await screen.findByTestId(
          "hide-for-event-types-select-badge-corporate_meeting",
        ),
      ).toBeInTheDocument();

      // Save form and verify
      await user.keyboard("{Escape}");
      await user.click(screen.getByTestId("header-save-button"));

      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          fields: expect.arrayContaining([
            expect.objectContaining({
              type: "GUEST_COUNT",
              hideForEventTypes: ["wedding", "corporate_meeting"],
            }),
          ]),
        }),
      );
    });

    it("should load and display initial hide rules correctly", async () => {
      const user = userEvent.setup();
      const initialForm: Form = formFactory.build({
        ...defaultProps.initialForm,
        fields: [
          createTestField({
            id: "field-1",
            type: "GUEST_COUNT",
            hideForEventTypes: ["wedding", "celebration"],
          }),
        ],
      });

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Click to edit the field
      const field = screen.getByTestId("sortable-GUEST_COUNT");
      await user.click(field);

      // Verify selected event types are shown as badges
      expect(
        screen.getByTestId("hide-for-event-types-select-badge-wedding"),
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("hide-for-event-types-select-badge-celebration"),
      ).toBeInTheDocument();
    });
  });

  describe("Theme Editor", () => {
    it("switches to theme editor when theme tab is clicked", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      const themeTab = screen.getByRole("tab", { name: /theme/i });
      await user.click(themeTab);

      expect(screen.getByText("Theme Editor")).toBeInTheDocument();
      expect(screen.getByTestId("theme-name-input")).toBeInTheDocument();
    });

    it("preserves theme changes when switching between tabs", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Switch to theme tab
      await user.click(screen.getByRole("tab", { name: /theme/i }));

      // Change theme name
      const themeNameInput = screen.getByTestId("theme-name-input");
      await user.clear(themeNameInput);
      await user.type(themeNameInput, "Custom Theme");

      // Switch back to fields tab
      await user.click(screen.getByRole("tab", { name: /fields/i }));

      // Switch back to theme tab
      await user.click(screen.getByRole("tab", { name: /theme/i }));

      // Verify theme name is preserved
      expect(screen.getByTestId("theme-name-input")).toHaveValue(
        "Custom Theme",
      );
    });

    it("includes theme changes when saving the form", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Switch to theme tab
      await user.click(screen.getByRole("tab", { name: /theme/i }));

      // Make theme changes
      await user.clear(screen.getByTestId("theme-name-input"));
      await user.type(screen.getByTestId("theme-name-input"), "Custom Theme");

      // Change primary color
      fireEvent.change(screen.getByTestId("primary-color-input"), {
        target: { value: "#FF0000" },
      });

      // Change font
      await user.click(screen.getByTestId("font-select"));
      await user.click(screen.getByTestId("font-option-arial"));

      // Save form
      await user.click(screen.getByTestId("header-save-button"));

      // Verify saved data includes theme changes
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          theme: expect.objectContaining({
            name: "Custom Theme",
            primaryColor: "#FF0000",
            font: "Arial, sans-serif",
          }),
        }),
      );
    });

    it("shows theme changes in form preview", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Switch to theme tab and make changes
      await user.click(screen.getByRole("tab", { name: /theme/i }));
      fireEvent.change(screen.getByTestId("primary-color-input"), {
        target: { value: "#FF0000" },
      });

      // Open preview
      await user.click(screen.getByRole("button", { name: /preview form/i }));

      // Verify preview shows updated styles
      const previewDialog = screen.getByRole("dialog");
      const formContainer = within(previewDialog).getByTestId("form-container");
      expect(formContainer).toHaveStyle({
        "--ef-primary": "#FF0000",
      });
    });

    it("loads and displays initial theme settings correctly", async () => {
      const user = userEvent.setup();
      const initialForm: Form = {
        id: "test-id",
        name: "Test Form",
        description: "Test Description",
        propertyId: "test-property-id",
        themeId: "test-theme-id",
        theme: {
          id: "test-theme-id",
          name: "Custom Theme",
          primaryColor: "#FF0000",
          backgroundColor: "#FFFFFF",
          textColor: "#000000",
          borderColor: "#CCCCCC",
          inputBorderRadius: "4px",
          buttonBorderRadius: "4px",
          placeholderColor: "#CCCCCC",
          padding: "16px",
          buttonTextColor: "#FFFFFF",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#FFFFFF",
          inputTextColor: "#000000",
          formBorderRadius: "8px",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        fields: [],
        redirectUrl: null,
        allowedDomains: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Switch to theme tab
      const themeTab = screen.getByRole("tab", { name: /theme/i });
      await user.click(themeTab);

      // Verify initial theme values are loaded
      expect(screen.getByTestId("theme-name-input")).toHaveValue(
        "Custom Theme",
      );
      expect(screen.getByTestId("primary-color-input")).toHaveValue("#FF0000");
      expect(screen.getByTestId("button-alignment-select")).toHaveTextContent(
        "Left",
      );
      expect(screen.getByTestId("form-border-radius-input")).toHaveValue("8px");
    });

    it("resets theme to default values when reset button is clicked", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Switch to theme tab
      await user.click(screen.getByRole("tab", { name: /theme/i }));

      // Make some changes
      await user.clear(screen.getByTestId("theme-name-input"));
      await user.type(screen.getByTestId("theme-name-input"), "Custom Theme");
      fireEvent.change(screen.getByTestId("primary-color-input"), {
        target: { value: "#FF0000" },
      });

      // Click reset button
      await user.click(screen.getByTestId("reset-theme-button"));

      // Verify values are reset to default
      expect(screen.getByTestId("theme-name-input")).toHaveValue(
        "Custom Theme",
      ); // Name should be preserved
      expect(screen.getByTestId("primary-color-input")).toHaveValue(
        DEFAULT_THEME.primaryColor,
      );
    });
  });

  describe("Live Preview", () => {
    it("shows live preview side by side on large screens", () => {
      render(<FormBuilder {...defaultProps} />);

      // Preview container should be hidden by default (mobile)
      const previewContainer = screen.getByTestId("preview-container");
      expect(previewContainer).toHaveClass("hidden lg:block");

      // Should have a "Live Preview" title
      expect(screen.getByText("Preview")).toBeInTheDocument();
    });

    it("updates preview in real-time when adding fields", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a field
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Preview should show the added field - get preview card specifically
      const previewContainer = screen.getByTestId("preview-container");
      const preview = within(previewContainer).getByTestId("form-container");
      expect(
        within(preview).getByTestId("label-FIRST_NAME"),
      ).toBeInTheDocument();
    });

    it("updates preview when editing field properties", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a field first
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Click on the field to edit it
      const field = screen.getByTestId("sortable-FIRST_NAME");
      await user.click(field);

      // Update field label
      const labelInput = screen.getByTestId("field-settings-label-input");
      await user.clear(labelInput);
      await user.type(labelInput, "Custom Label");

      // Preview should show updated label
      const previewContainer = screen.getByTestId("preview-container");
      const preview = within(previewContainer).getByTestId("form-container");
      expect(within(preview).getByTestId("label-FIRST_NAME")).toHaveTextContent(
        "Custom Label",
      );
    });

    it("updates preview when changing theme", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Switch to theme tab
      await user.click(screen.getByRole("tab", { name: /theme/i }));

      // Change primary color
      const primaryColorInput = screen.getByTestId("primary-color-input");
      fireEvent.change(primaryColorInput, { target: { value: "#FF0000" } });

      // Preview should have updated color
      const preview = screen.getByTestId("form-container");
      expect(preview).toHaveStyle({
        "--ef-primary": "#FF0000",
      });
    });

    it("opens full screen preview dialog", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a field
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Click full screen button
      const fullScreenButton = screen.getByTestId("full-screen-preview-button");
      await user.click(fullScreenButton);

      // Dialog should be visible with preview
      const dialog = screen.getByRole("dialog");
      expect(dialog).toBeInTheDocument();
      expect(
        within(dialog).getByTestId("label-FIRST_NAME"),
      ).toBeInTheDocument();
    });

    it("shows preview button in header on mobile", () => {
      render(<FormBuilder {...defaultProps} />);

      // Header preview button should be visible
      const headerPreviewButton = screen.getByRole("button", {
        name: /preview form/i,
      });
      expect(headerPreviewButton).toBeInTheDocument();
    });

    it("preserves form state when toggling full screen preview", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a field and customize it
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);
      const field = screen.getByTestId("sortable-FIRST_NAME");
      await user.click(field);
      const labelInput = screen.getByTestId("field-settings-label-input");
      await user.clear(labelInput);
      await user.type(labelInput, "Custom First Name");

      // Close the field settings dialog
      await user.keyboard("{Escape}");

      // Open preview dialog
      const fullScreenButton = screen.getByTestId("full-screen-preview-button");
      await user.click(fullScreenButton);

      // Check preview has customized field
      const dialog = screen.getByRole("dialog");
      expect(dialog).toBeInTheDocument();
      expect(within(dialog).getByTestId("label-FIRST_NAME")).toHaveTextContent(
        "Custom First Name",
      );

      // Close dialog
      await user.keyboard("{Escape}");

      // Side preview should still show customized field
      const previewContainer = screen.getByTestId("preview-container");
      const preview = within(previewContainer).getByTestId("form-container");
      expect(within(preview).getByTestId("label-FIRST_NAME")).toHaveTextContent(
        "Custom First Name",
      );
    });
  });

  describe("Form Preview State", () => {
    it("should maintain form state between preview card and dialog", async () => {
      const user = userEvent.setup();
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
          createTestField({
            type: "ROOM_COUNT",
            label: "Number of Rooms",
            required: true,
            width: "full",
            order: 2,
            hideForEventTypes: ["wedding"],
          }),
        ],
      });

      render(<FormBuilder {...defaultProps} initialForm={form} />);

      // Select wedding in preview card
      const previewContainer = screen.getByTestId("preview-container");

      const eventTypeSelect =
        within(previewContainer).getByTestId("input-EVENT_TYPE");
      await user.selectOptions(eventTypeSelect, "wedding");

      // Verify guest count is visible and room count is hidden in preview card
      await waitFor(() => {
        expect(
          within(previewContainer).getByTestId("input-GUEST_COUNT"),
        ).toBeInTheDocument();
        expect(
          within(previewContainer).queryByTestId("input-ROOM_COUNT"),
        ).not.toBeInTheDocument();
      });

      // Open preview dialog
      await user.click(
        within(previewContainer).getByTestId("full-screen-preview-button"),
      );

      // Verify same state in dialog
      await waitFor(() => {
        const dialog = screen.getByRole("dialog");
        const dialogEventType = screen.getAllByTestId(
          "input-EVENT_TYPE",
        )[1] as HTMLSelectElement;
        expect(dialogEventType.value).toBe("wedding");

        // Verify GUEST_COUNT is visible in both places
        expect(
          within(previewContainer).getByTestId("input-GUEST_COUNT"),
        ).toBeInTheDocument();
        expect(
          within(dialog).getByTestId("input-GUEST_COUNT"),
        ).toBeInTheDocument();

        // Verify ROOM_COUNT is hidden in both places
        expect(
          within(previewContainer).queryByTestId("input-ROOM_COUNT"),
        ).not.toBeInTheDocument();
        expect(
          within(dialog).queryByTestId("input-ROOM_COUNT"),
        ).not.toBeInTheDocument();
      });

      // Change event type in dialog
      const dialogEventType = screen.getAllByTestId("input-EVENT_TYPE")[1];
      await user.selectOptions(dialogEventType, "corporate_meeting");

      // Verify state is updated in both card and dialog
      await waitFor(() => {
        const dialog = screen.getByRole("dialog");

        // Verify GUEST_COUNT is hidden in both places
        expect(
          within(previewContainer).queryByTestId("input-GUEST_COUNT"),
        ).not.toBeInTheDocument();
        expect(
          within(dialog).queryByTestId("input-GUEST_COUNT"),
        ).not.toBeInTheDocument();

        // Verify ROOM_COUNT is visible in both places
        expect(
          within(previewContainer).getByTestId("input-ROOM_COUNT"),
        ).toBeInTheDocument();
        expect(
          within(dialog).getByTestId("input-ROOM_COUNT"),
        ).toBeInTheDocument();
      });
    });
  });

  describe("Checkbox Fields", () => {
    it("initializes checkbox fields with default checkbox text", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a checkbox field
      const marketingConsentButton = screen.getByRole("button", {
        name: /marketing consent/i,
      });
      await user.click(marketingConsentButton);

      // Click on the field to edit it
      const field = screen.getByTestId("sortable-MARKETING_CONSENT");
      await user.click(field);

      // Verify checkbox text field exists and is initialized with default text
      const checkboxTextField = screen.getByLabelText("Checkbox Text");
      expect(checkboxTextField).toHaveValue(
        "I agree to receive marketing communications via email, text messages, and/or phone calls",
      );

      // Close the field editor
      await user.keyboard("{Escape}");

      // Save the form
      await user.click(screen.getByTestId("header-save-button"));

      // Verify checkbox text is included in saved form
      expect(mockOnSave).toHaveBeenCalledTimes(1);
      const savedForm = mockOnSave.mock.calls[0][0];
      expect(savedForm.fields[0]).toMatchObject({
        type: "MARKETING_CONSENT",
        checkboxText:
          "I agree to receive marketing communications via email, text messages, and/or phone calls",
      });
    });

    it("allows editing checkbox text", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a checkbox field
      const marketingConsentButton = screen.getByRole("button", {
        name: /marketing consent/i,
      });
      await user.click(marketingConsentButton);

      // Click on the field to edit it
      const field = screen.getByTestId("sortable-MARKETING_CONSENT");
      await user.click(field);

      // Edit the checkbox text
      const checkboxTextField = screen.getByLabelText("Checkbox Text");
      await user.clear(checkboxTextField);
      await user.type(checkboxTextField, "Custom checkbox text");

      // Close the field editor
      await user.keyboard("{Escape}");

      // Save the form
      await user.click(screen.getByTestId("header-save-button"));

      // Verify custom checkbox text is included in saved form
      expect(mockOnSave).toHaveBeenCalledTimes(1);
      const savedForm = mockOnSave.mock.calls[0][0];
      expect(savedForm.fields[0]).toMatchObject({
        type: "MARKETING_CONSENT",
        checkboxText: "Custom checkbox text",
      });
    });

    it("loads and displays initial checkbox text correctly", () => {
      const initialForm: Form = {
        id: "test-id",
        name: "Test Form",
        description: "Test Description",
        propertyId: "test-property-id",
        themeId: "test-theme-id",
        theme: {
          id: "test-theme-id",
          name: "Test Theme",
          primaryColor: "#FF0000",
          backgroundColor: "#FFFFFF",
          textColor: "#000000",
          borderColor: "#CCCCCC",
          placeholderColor: "#CFCFCF",
          inputBorderRadius: "4px",
          buttonBorderRadius: "4px",
          padding: "16px",
          buttonTextColor: "#FFFFFF",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#FFFFFF",
          inputTextColor: "#000000",
          formBorderRadius: "4px",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        fields: [
          {
            id: "field-1",
            formId: "test-id",
            type: "MARKETING_CONSENT",
            label: null,
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Verify checkbox field is loaded
      expect(
        screen.getByTestId("sortable-MARKETING_CONSENT"),
      ).toBeInTheDocument();

      // Click on the field to edit it
      const field = screen.getByTestId("sortable-MARKETING_CONSENT");
      fireEvent.click(field);

      // Verify checkbox text is loaded
      expect(screen.getByLabelText("Checkbox Text")).toHaveValue(
        "I agree to receive marketing communications via email, text messages, and/or phone calls",
      );
    });
  });

  describe("Field Row Number Assignment", () => {
    it("assigns rowBreakAfter when needed for layout", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a first field (full width)
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Add a second field (half width)
      const lastNameButton = screen.getByRole("button", { name: /last name/i });
      await user.click(lastNameButton);

      // Change the first field to half width
      await user.click(screen.getByTestId("sortable-FIRST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Add a third field (half width)
      const emailButton = screen.getByRole("button", { name: /email/i });
      await user.click(emailButton);

      // Save the form and verify rowBreakAfter is set correctly
      await user.click(screen.getByTestId("header-save-button"));

      // The first field should have rowBreakAfter=false (pairs with second field)
      // The second field should have rowBreakAfter=true (to start a new row)
      // The third field should not have rowBreakAfter set (last field)
    });

    it("handles full-width fields correctly", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add a first field (full width)
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Add a second field (full width)
      const lastNameButton = screen.getByRole("button", { name: /last name/i });
      await user.click(lastNameButton);

      // Save the form and verify
      await user.click(screen.getByTestId("header-save-button"));

      // Full width fields should automatically break to new rows
      // No need to set rowBreakAfter explicitly
    });

    it("pairs half-width fields correctly", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add first field (half width)
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Change first field to half-width
      await user.click(screen.getByTestId("sortable-FIRST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Add second field (half width)
      const lastNameButton = screen.getByRole("button", { name: /last name/i });
      await user.click(lastNameButton);

      // Change second field to half-width
      await user.click(screen.getByTestId("sortable-LAST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Save the form and verify
      await user.click(screen.getByTestId("header-save-button"));

      // Two half-width fields should be paired in the same row
      // First field should not have rowBreakAfter
    });

    it("handles mixed width fields correctly", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add first field (half width)
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Change first field to half-width
      await user.click(screen.getByTestId("sortable-FIRST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Add second field (half width)
      const lastNameButton = screen.getByRole("button", { name: /last name/i });
      await user.click(lastNameButton);

      // Change second field to half-width
      await user.click(screen.getByTestId("sortable-LAST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Add third field (full width)
      const emailButton = screen.getByRole("button", { name: /email/i });
      await user.click(emailButton);

      // Save the form and verify
      await user.click(screen.getByTestId("header-save-button"));

      // Two half-width fields followed by a full-width field
      // Full width field should automatically start a new row
    });

    it("recalculates row layout when a field is moved via drag and drop", async () => {
      // This test can remain as is since it's testing the drag and drop functionality
      // which should still work with the new rowBreakAfter approach
      const user = userEvent.setup();

      // Create initial form with fields
      const initialForm: Form = {
        id: "test-id",
        name: "Test Form",
        description: "Test Description",
        propertyId: "test-property-id",
        themeId: "test-theme-id",
        theme: {
          id: "test-theme-id",
          name: "Test Theme",
          primaryColor: "#FF0000",
          backgroundColor: "#FFFFFF",
          textColor: "#000000",
          borderColor: "#CCCCCC",
          placeholderColor: "#CFCFCF",
          inputBorderRadius: "4px",
          buttonBorderRadius: "4px",
          padding: "16px",
          buttonTextColor: "#FFFFFF",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#FFFFFF",
          inputTextColor: "#000000",
          formBorderRadius: "4px",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        fields: [
          {
            id: "field-1",
            formId: "test-id",
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 0,
            rowBreakAfter: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "field-2",
            formId: "test-id",
            type: "LAST_NAME",
            label: "Last Name",
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 1,
            rowBreakAfter: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "field-3",
            formId: "test-id",
            type: "EMAIL",
            label: "Email",
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 2,
            rowBreakAfter: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Save the form to trigger row layout recalculation
      await user.click(screen.getByTestId("header-save-button"));

      // We expect fields to maintain their layout as order changes
      const savedForm = mockOnSave.mock.calls[0][0];
      expect(savedForm.fields.length).toBe(3);
    });

    it("updates row layout when a field width is changed", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Add first field (half width)
      const firstNameButton = screen.getByRole("button", {
        name: /first name/i,
      });
      await user.click(firstNameButton);

      // Change first field to half-width
      await user.click(screen.getByTestId("sortable-FIRST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Add second field (full width)
      const lastNameButton = screen.getByRole("button", { name: /last name/i });
      await user.click(lastNameButton);

      // Change second field to half width
      await user.click(screen.getByTestId("sortable-LAST_NAME"));
      await user.click(screen.getByTestId("field-settings-width-select"));
      await user.click(screen.getByTestId("field-width-half"));
      await user.keyboard("{Escape}"); // Close the field settings

      // Save the form and verify
      await user.click(screen.getByTestId("header-save-button"));

      // Two half-width fields should be paired in the same row
      // First field should not have rowBreakAfter
      expect(mockOnSave).toHaveBeenCalledTimes(1);
      const savedForm = mockOnSave.mock.calls[0][0];
      expect(savedForm.fields.length).toBe(2);
      expect(savedForm.fields[0].width).toBe("half");
      expect(savedForm.fields[1].width).toBe("half");
    });
  });

  it("disables row break checkbox for full width fields and enables it for half width fields", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add a field first
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    // Click on the field to edit it
    const field = screen.getByTestId("sortable-FIRST_NAME");
    await user.click(field);

    // Get the row break checkbox
    const rowBreakCheckbox = screen.getByTestId(
      "field-settings-row-break-checkbox",
    );

    // Initially the field is full width, so checkbox should be disabled
    expect(rowBreakCheckbox).toBeDisabled();

    // Change width to half
    const widthSelect = screen.getByTestId("field-settings-width-select");
    await user.click(widthSelect);
    await user.click(screen.getByTestId("field-width-half"));

    // Now checkbox should be enabled
    expect(rowBreakCheckbox).toBeEnabled();

    // Change width back to full
    await user.click(widthSelect);
    await user.click(screen.getByTestId("field-width-full"));

    // Checkbox should be disabled again
    expect(rowBreakCheckbox).toBeDisabled();
  });

  it("shows wrap text icon for fields with row break after", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add a first field (half width)
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);

    // Click on the field to edit it
    const field = screen.getByTestId("sortable-FIRST_NAME");
    await user.click(field);

    // Change width to half
    const widthSelect = screen.getByTestId("field-settings-width-select");
    await user.click(widthSelect);
    await user.click(screen.getByTestId("field-width-half"));

    // Enable row break
    const rowBreakCheckbox = screen.getByTestId(
      "field-settings-row-break-checkbox",
    );
    await user.click(rowBreakCheckbox);

    // Close the editor
    await user.keyboard("{Escape}");

    // Verify the row break icon is displayed in the UI
    expect(screen.getByTestId("row-break-icon")).toBeInTheDocument();

    // Save the form
    await user.click(screen.getByTestId("header-save-button"));

    // Verify the form was saved with rowBreakAfter=true
    expect(mockOnSave).toHaveBeenCalled();
    const savedForm = mockOnSave.mock.calls[0][0];
    expect(savedForm.fields[0].rowBreakAfter).toBe(true);
  });

  it("maintains consistent label formatting in field editor and form", async () => {
    const user = userEvent.setup();
    render(<FormBuilder {...defaultProps} />);

    // Add a field and customize it with a specific format
    const firstNameButton = screen.getByRole("button", { name: /first name/i });
    await user.click(firstNameButton);
    const field = screen.getByTestId("sortable-FIRST_NAME");
    await user.click(field);

    // Set a custom label with specific formatting
    const labelInput = screen.getByTestId("field-settings-label-input");
    await user.clear(labelInput);
    await user.type(labelInput, "Custom First Name");

    // Verify the label is updated with formatting in the field editor
    expect(screen.getAllByTestId("label-FIRST_NAME")[0]).toHaveTextContent(
      "Custom First Name",
    );

    await user.keyboard("{Escape}");

    // Check if save button is available and enabled
    const saveButton = screen.getByTestId("header-save-button");
    console.log("Save button found:", saveButton.outerHTML);

    // Make sure any panels/dialogs are closed
    await user.keyboard("{Escape}");

    // Try to directly use fireEvent instead of userEvent
    fireEvent.click(saveButton);

    // Verify onSave was called
    expect(mockOnSave).toHaveBeenCalledTimes(1);
    const savedForm = mockOnSave.mock.calls[0][0];
    expect(savedForm.fields[0]).toMatchObject({
      type: "FIRST_NAME",
      label: "Custom First Name",
      required: true,
      width: "full",
      order: 0,
    });
  });

  describe("Redirect Functionality", () => {
    it("should render redirect settings disabled by default", async () => {
      render(<FormBuilder {...defaultProps} />);

      // Check that the enable redirect checkbox exists and is unchecked
      const enableRedirectCheckbox = screen.getByLabelText(/enable redirect/i);
      expect(enableRedirectCheckbox).toBeInTheDocument();
      expect(enableRedirectCheckbox).not.toBeChecked();

      // Redirect URL field should not be visible
      expect(screen.queryByLabelText(/redirect url/i)).not.toBeInTheDocument();
    });

    it("should show redirect URL input when redirect is enabled", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Enable redirect
      const enableRedirectCheckbox = screen.getByLabelText(/enable redirect/i);
      await user.click(enableRedirectCheckbox);

      // Redirect URL field should be visible
      expect(screen.getByLabelText(/redirect url/i)).toBeInTheDocument();
    });

    it("should save form with redirect URL when redirect is enabled", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Fill in form details
      await user.type(screen.getByLabelText(/form name/i), "Test Form");

      // Enable redirect
      const enableRedirectCheckbox = screen.getByLabelText(/enable redirect/i);
      await user.click(enableRedirectCheckbox);

      // Enter redirect URL
      const redirectUrlInput = screen.getByLabelText(/redirect url/i);
      await user.type(redirectUrlInput, "https://example.com/thank-you");

      // Save the form
      await user.click(screen.getByTestId("header-save-button"));

      // Verify the form was saved with redirect URL
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Test Form",
          redirectUrl: "https://example.com/thank-you",
        }),
      );
    });

    it("should save form with null redirectUrl when redirect is disabled", async () => {
      const user = userEvent.setup();
      render(<FormBuilder {...defaultProps} />);

      // Fill in form details
      await user.type(screen.getByLabelText(/form name/i), "Test Form");

      // Save the form (without enabling redirect)
      await user.click(screen.getByTestId("header-save-button"));

      // Verify the form was saved with null redirectUrl
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Test Form",
          redirectUrl: null,
        }),
      );
    });

    it("should load and display existing redirect URL", () => {
      // Create a form with redirect URL
      const initialForm: Form = {
        id: "test-id",
        name: "Test Form",
        description: "Test Description",
        propertyId: "test-property-id",
        themeId: "test-theme-id",
        theme: {
          id: "test-theme-id",
          name: "Test Theme",
          primaryColor: "#FF0000",
          backgroundColor: "#FFFFFF",
          textColor: "#000000",
          borderColor: "#CCCCCC",
          placeholderColor: "#CFCFCF",
          inputBorderRadius: "4px",
          buttonBorderRadius: "4px",
          padding: "16px",
          buttonTextColor: "#FFFFFF",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#FFFFFF",
          inputTextColor: "#000000",
          formBorderRadius: "4px",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        fields: [],
        redirectUrl: "https://example.com/thank-you",
        allowedDomains: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      render(<FormBuilder {...defaultProps} initialForm={initialForm} />);

      // Check that redirect is enabled
      const enableRedirectCheckbox = screen.getByLabelText(/enable redirect/i);
      expect(enableRedirectCheckbox).toBeChecked();

      // Check that redirect URL is loaded
      const redirectUrlInput = screen.getByLabelText(/redirect url/i);
      expect(redirectUrlInput).toHaveValue("https://example.com/thank-you");
    });
  });
});
