/* public/form-base.css */
.ef-root {
    all: initial;
    box-sizing: border-box;
    display: block;
    font-family: system-ui, sans-serif;

    /* Theme variables - will be overridden inline */
    --ef-primary: #2563eb;
    --ef-background: #ffffff;
    --ef-text: #1f2937;
    --ef-border: #e5e7eb;
    --ef-error: #ef4444;
    --ef-radius: 0.375rem;
    --ef-spacing: 1rem;
}

.ef-form {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ef-spacing);
    margin: 0 calc(var(--ef-spacing) * -0.5);
}

.ef-field {
    padding: 0 calc(var(--ef-spacing) * 0.5);
    width: 100%;
}

.ef-field[data-width="half"] {
    width: 50%;
}

.ef-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--ef-border);
    border-radius: var(--ef-radius);
    background: var(--ef-background);
    color: var(--ef-text);
    margin: 0;
    font: inherit;
}

.ef-input:focus {
    outline: 2px solid var(--ef-primary);
    outline-offset: 2px;
}

.ef-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--ef-text);
    font-size: 0.875rem;
    font-weight: 500;
}

.ef-button {
    display: inline-block;
    background: var(--ef-primary);
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--ef-radius);
    cursor: pointer;
    font: inherit;
}

.ef-button:hover {
    opacity: 0.9;
}

.ef-error {
    color: var(--ef-error);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.ef-success {
    color: #22c55e;
    text-align: center;
    padding: var(--ef-spacing);
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    .ef-field {
        width: 100% !important;
    }

    .ef-root {
        --ef-spacing: 0.75rem;
    }
}
