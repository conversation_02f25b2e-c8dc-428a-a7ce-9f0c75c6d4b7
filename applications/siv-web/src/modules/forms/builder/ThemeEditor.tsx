import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Palette, Clipboard, Check } from "lucide-react";
import type { Theme } from "./types";
import { SidebarHeader, SidebarContent } from "@/components/ui/sidebar";
import { toast } from "sonner";

export interface ThemeEditorProps {
  theme: Omit<Theme, "id" | "createdAt" | "updatedAt">;
  onThemeChange: (theme: Omit<Theme, "id" | "createdAt" | "updatedAt">) => void;
}

// Add supported fonts
export const SUPPORTED_FONTS = [
  { label: "System Default", value: "system-ui, -apple-system, sans-serif" },
  { label: "Open Sans", value: '"Open Sans", sans-serif' },
  { label: "Arial", value: "Arial, sans-serif" },
  { label: "Helvetica", value: "Helvetica, sans-serif" },
  { label: "Times New Roman", value: '"Times New Roman", serif' },
  { label: "Georgia", value: "Georgia, serif" },
];

export const BUTTON_ALIGNMENTS = [
  { label: "Left", value: "left" },
  { label: "Right", value: "right" },
];

export const DEFAULT_THEME = {
  name: "Default Theme",
  primaryColor: "#3b82f6ff",
  backgroundColor: "#ffffffff",
  textColor: "#000000ff",
  borderColor: "#e2e8f0ff",
  inputBorderRadius: "0.375rem",
  buttonBorderRadius: "0.375rem",
  padding: "1rem",
  buttonTextColor: "#ffffffff",
  buttonAlignment: "right" as const,
  font: "system-ui, -apple-system, sans-serif",
  fieldBackgroundColor: "#ffffffff",
  inputTextColor: "#000000ff",
  formBorderRadius: "0.75rem",
  placeholderColor: "#6b7280ff",
};

export function ThemeEditor({ theme, onThemeChange }: ThemeEditorProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleReset = () => {
    onThemeChange({
      ...DEFAULT_THEME,
      name: theme.name, // Preserve the current theme name
    });
  };

  const handleCopyJSON = () => {
    // Create a clean version of the theme without functions
    const themeJson = JSON.stringify(theme, null, 2);

    // Copy to clipboard
    navigator.clipboard
      .writeText(themeJson)
      .then(() => {
        setIsCopied(true);
        toast.success("Copied!", {
          description: "Theme JSON copied to clipboard",
          duration: 2000,
        });

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((err) => {
        console.error("Failed to copy theme JSON:", err);
        toast.error("Failed to copy", {
          description: "Please try again",
          duration: 2000,
        });
      });
  };

  return (
    <>
      <SidebarHeader className="border-b p-4">
        <div className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          <h3 className="font-semibold">Theme Editor</h3>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <div className="space-y-4 p-4">
          {/* Theme Name */}
          <div className="grid gap-2">
            <Label htmlFor="themeName">Theme Name</Label>
            <Input
              id="themeName"
              value={theme.name}
              onChange={(e) =>
                onThemeChange({ ...theme, name: e.target.value })
              }
              data-testid="theme-name-input"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="font">Font Family</Label>
            <Select
              value={theme.font}
              onValueChange={(value) =>
                onThemeChange({ ...theme, font: value })
              }
            >
              <SelectTrigger id="font" data-testid="font-select">
                <SelectValue placeholder="Select font" />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_FONTS.map((font) => (
                  <SelectItem
                    key={font.value}
                    value={font.value}
                    data-testid={`font-option-${font.label.toLowerCase()}`}
                  >
                    {font.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="buttonAlignment">Button Alignment</Label>
            <Select
              value={theme.buttonAlignment}
              onValueChange={(value) =>
                onThemeChange({
                  ...theme,
                  buttonAlignment: value as "left" | "right",
                })
              }
            >
              <SelectTrigger
                id="buttonAlignment"
                data-testid="button-alignment-select"
              >
                <SelectValue placeholder="Select alignment" />
              </SelectTrigger>
              <SelectContent>
                {BUTTON_ALIGNMENTS.map((alignment) => (
                  <SelectItem
                    key={alignment.value}
                    value={alignment.value}
                    data-testid={`button-alignment-${alignment.value}`}
                  >
                    {alignment.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="primaryColor">Primary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={theme.primaryColor.slice(0, 7)}
                  onChange={(e) =>
                    onThemeChange({
                      ...theme,
                      primaryColor: `${e.target.value}ff`,
                    })
                  }
                  className="w-16 h-10 p-1"
                  data-testid="primary-color-picker"
                />
                <Input
                  value={theme.primaryColor}
                  onChange={(e) =>
                    onThemeChange({ ...theme, primaryColor: e.target.value })
                  }
                  className="font-mono"
                  data-testid="primary-color-input"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="backgroundColor">Background Color</Label>
              <div className="flex gap-2">
                <Input
                  id="backgroundColor"
                  type="color"
                  value={theme.backgroundColor.slice(0, 7)}
                  onChange={(e) =>
                    onThemeChange({
                      ...theme,
                      backgroundColor: `${e.target.value}ff`,
                    })
                  }
                  className="w-16 h-10 p-1"
                  data-testid="background-color-picker"
                />
                <Input
                  value={theme.backgroundColor}
                  onChange={(e) =>
                    onThemeChange({ ...theme, backgroundColor: e.target.value })
                  }
                  className="font-mono"
                  data-testid="background-color-input"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="textColor">Text Color</Label>
              <div className="flex gap-2">
                <Input
                  id="textColor"
                  type="color"
                  value={theme.textColor.slice(0, 7)}
                  onChange={(e) =>
                    onThemeChange({
                      ...theme,
                      textColor: `${e.target.value}ff`,
                    })
                  }
                  className="w-16 h-10 p-1"
                  data-testid="text-color-picker"
                />
                <Input
                  value={theme.textColor}
                  onChange={(e) =>
                    onThemeChange({ ...theme, textColor: e.target.value })
                  }
                  className="font-mono"
                  data-testid="text-color-input"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="borderColor">Border Color</Label>
              <div className="flex gap-2">
                <Input
                  id="borderColor"
                  type="color"
                  value={theme.borderColor.slice(0, 7)}
                  onChange={(e) =>
                    onThemeChange({
                      ...theme,
                      borderColor: `${e.target.value}ff`,
                    })
                  }
                  className="w-16 h-10 p-1"
                  data-testid="border-color-picker"
                />
                <Input
                  value={theme.borderColor}
                  onChange={(e) =>
                    onThemeChange({ ...theme, borderColor: e.target.value })
                  }
                  className="font-mono"
                  data-testid="border-color-input"
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label>Form</Label>
              <div className="space-y-4 pl-4">
                <div className="grid gap-2">
                  <Label htmlFor="formBorderRadius">Form Border Radius</Label>
                  <Input
                    id="formBorderRadius"
                    value={theme.formBorderRadius}
                    onChange={(e) =>
                      onThemeChange({
                        ...theme,
                        formBorderRadius: e.target.value,
                      })
                    }
                    placeholder="0.75rem"
                    data-testid="form-border-radius-input"
                  />
                </div>
              </div>
            </div>

            <div className="grid gap-2">
              <Label>Input Fields</Label>
              <div className="space-y-4 pl-4">
                <div className="grid gap-2">
                  <Label htmlFor="fieldBackgroundColor">
                    Field Background Color
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="fieldBackgroundColor"
                      type="color"
                      value={theme.fieldBackgroundColor.slice(0, 7)}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          fieldBackgroundColor: `${e.target.value}ff`,
                        })
                      }
                      className="w-16 h-10 p-1"
                      data-testid="field-background-color-picker"
                    />
                    <Input
                      value={theme.fieldBackgroundColor}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          fieldBackgroundColor: e.target.value,
                        })
                      }
                      className="font-mono"
                      data-testid="field-background-color-input"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="inputTextColor">Input Text Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="inputTextColor"
                      type="color"
                      value={theme.inputTextColor.slice(0, 7)}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          inputTextColor: `${e.target.value}ff`,
                        })
                      }
                      className="w-16 h-10 p-1"
                      data-testid="input-text-color-picker"
                    />
                    <Input
                      value={theme.inputTextColor}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          inputTextColor: e.target.value,
                        })
                      }
                      className="font-mono"
                      data-testid="input-text-color-input"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="placeholderColor">
                    Placeholder Text Color
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="placeholderColor"
                      type="color"
                      value={theme.placeholderColor.slice(0, 7)}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          placeholderColor: `${e.target.value}ff`,
                        })
                      }
                      className="w-16 h-10 p-1"
                      data-testid="placeholder-color-picker"
                    />
                    <Input
                      value={theme.placeholderColor}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          placeholderColor: e.target.value,
                        })
                      }
                      className="font-mono"
                      data-testid="placeholder-color-input"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="inputBorderRadius">Input Border Radius</Label>
                  <Input
                    id="inputBorderRadius"
                    value={theme.inputBorderRadius}
                    onChange={(e) =>
                      onThemeChange({
                        ...theme,
                        inputBorderRadius: e.target.value,
                      })
                    }
                    placeholder="0.375rem"
                    data-testid="input-border-radius-input"
                  />
                </div>
              </div>
            </div>

            <div className="grid gap-2">
              <Label>Submit Button</Label>
              <div className="space-y-4 pl-4">
                <div className="grid gap-2">
                  <Label htmlFor="buttonTextColor">Button Text Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="buttonTextColor"
                      type="color"
                      value={theme.buttonTextColor.slice(0, 7)}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          buttonTextColor: `${e.target.value}ff`,
                        })
                      }
                      className="w-16 h-10 p-1"
                      data-testid="button-text-color-picker"
                    />
                    <Input
                      value={theme.buttonTextColor}
                      onChange={(e) =>
                        onThemeChange({
                          ...theme,
                          buttonTextColor: e.target.value,
                        })
                      }
                      className="font-mono"
                      data-testid="button-text-color-input"
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="buttonBorderRadius">
                    Button Border Radius
                  </Label>
                  <Input
                    id="buttonBorderRadius"
                    value={theme.buttonBorderRadius}
                    onChange={(e) =>
                      onThemeChange({
                        ...theme,
                        buttonBorderRadius: e.target.value,
                      })
                    }
                    placeholder="0.375rem"
                    data-testid="button-border-radius-input"
                  />
                </div>
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="padding">Padding</Label>
              <Input
                id="padding"
                value={theme.padding}
                onChange={(e) =>
                  onThemeChange({ ...theme, padding: e.target.value })
                }
                placeholder="1rem"
                data-testid="padding-input"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Button
              variant="outline"
              onClick={handleCopyJSON}
              className="w-full"
              data-testid="copy-theme-json-button"
            >
              {isCopied ? (
                <Check className="mr-2 h-4 w-4" />
              ) : (
                <Clipboard className="mr-2 h-4 w-4" />
              )}
              {isCopied ? "Copied!" : "Copy Theme JSON"}
            </Button>

            <Button
              variant="outline"
              onClick={handleReset}
              className="w-full"
              data-testid="reset-theme-button"
            >
              <Palette className="mr-2 h-4 w-4" />
              Reset to Default Theme
            </Button>
          </div>
        </div>
      </SidebarContent>
    </>
  );
}
