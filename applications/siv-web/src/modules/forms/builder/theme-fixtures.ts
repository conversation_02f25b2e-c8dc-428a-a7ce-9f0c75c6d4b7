import type { Theme } from "./types";

/**
 * Collection of production theme fixtures for testing purposes
 * These represent real themes being used in production
 */
export const themeFixtures: Record<
  string,
  Omit<Theme, "id" | "createdAt" | "updatedAt">
> = {
  // Default theme from the application
  default: {
    name: "Default Theme",
    primaryColor: "#3b82f6ff",
    backgroundColor: "#ffffffff",
    textColor: "#000000ff",
    borderColor: "#e2e8f0ff",
    inputBorderRadius: "0.375rem",
    buttonBorderRadius: "0.375rem",
    padding: "1rem",
    buttonTextColor: "#ffffffff",
    buttonAlignment: "right" as const,
    font: "system-ui, -apple-system, sans-serif",
    fieldBackgroundColor: "#ffffffff",
    inputTextColor: "#000000ff",
    formBorderRadius: "0.75rem",
    placeholderColor: "#6b7280ff",
  },

  // Example of a dark theme from production
  darkCorporate: {
    name: "Dark Corporate",
    primaryColor: "#1e40afff",
    backgroundColor: "#1f2937ff",
    textColor: "#f3f4f6ff",
    borderColor: "#374151ff",
    inputBorderRadius: "0.25rem",
    buttonBorderRadius: "0.25rem",
    padding: "1.25rem",
    buttonTextColor: "#f3f4f6ff",
    buttonAlignment: "right" as const,
    font: "Inter, system-ui, sans-serif",
    fieldBackgroundColor: "#111827ff",
    inputTextColor: "#f9fafbff",
    formBorderRadius: "0.5rem",
    placeholderColor: "#9ca3afff",
  },

  // Example of a luxury hospitality theme from production
  luxuryHospitality: {
    name: "Luxury Hospitality",
    primaryColor: "#b45309ff",
    backgroundColor: "#fffbebff",
    textColor: "#422006ff",
    borderColor: "#fef3c7ff",
    inputBorderRadius: "0rem",
    buttonBorderRadius: "0rem",
    padding: "1.5rem",
    buttonTextColor: "#fffbebff",
    buttonAlignment: "left" as const,
    font: "Playfair Display, serif",
    fieldBackgroundColor: "#fffbebff",
    inputTextColor: "#422006ff",
    formBorderRadius: "0rem",
    placeholderColor: "#92400eff",
  },

  // Example of a modern colorful theme from production
  modernColorful: {
    name: "Modern Colorful",
    primaryColor: "#8b5cf6ff",
    backgroundColor: "#f5f3ffff",
    textColor: "#1e1b4bff",
    borderColor: "#ddd6feff",
    inputBorderRadius: "1rem",
    buttonBorderRadius: "2rem",
    padding: "1.5rem",
    buttonTextColor: "#f5f3ffff",
    buttonAlignment: "right" as const,
    font: "Poppins, sans-serif",
    fieldBackgroundColor: "#ffffff",
    inputTextColor: "#4c1d95ff",
    formBorderRadius: "1.5rem",
    placeholderColor: "#8b5cf6ff",
  },

  // Add more production themes as needed...
};
