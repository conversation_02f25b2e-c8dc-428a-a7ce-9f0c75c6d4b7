import "@testing-library/jest-dom";
import React from "react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ThemeEditor, DEFAULT_THEME } from "./ThemeEditor";

window.HTMLElement.prototype.hasPointerCapture = vi.fn(); //needed for radix select components since happy-dom doesn't provide this

describe("ThemeEditor", () => {
  const mockOnThemeChange = vi.fn();
  const defaultProps = {
    theme: DEFAULT_THEME,
    onThemeChange: mockOnThemeChange,
  };

  beforeEach(() => {
    mockOnThemeChange.mockClear();
  });

  it("renders with all theme controls", () => {
    render(<ThemeEditor {...defaultProps} />);

    // Check for presence of all major inputs by their test IDs
    expect(screen.getByTestId("theme-name-input")).toBeInTheDocument();
    expect(screen.getByTestId("font-select")).toBeInTheDocument();
    expect(screen.getByTestId("primary-color-input")).toBeInTheDocument();
    expect(screen.getByTestId("background-color-input")).toBeInTheDocument();
    expect(screen.getByTestId("text-color-input")).toBeInTheDocument();
    expect(screen.getByTestId("border-color-input")).toBeInTheDocument();
    expect(screen.getByTestId("form-border-radius-input")).toBeInTheDocument();
    expect(
      screen.getByTestId("field-background-color-input"),
    ).toBeInTheDocument();
    expect(screen.getByTestId("input-text-color-input")).toBeInTheDocument();
    expect(screen.getByTestId("input-border-radius-input")).toBeInTheDocument();
    expect(screen.getByTestId("button-text-color-input")).toBeInTheDocument();
    expect(
      screen.getByTestId("button-border-radius-input"),
    ).toBeInTheDocument();
    expect(screen.getByTestId("button-alignment-select")).toBeInTheDocument();
    expect(screen.getByTestId("padding-input")).toBeInTheDocument();
  });

  it("allows updating theme name", async () => {
    const user = userEvent.setup();
    render(<ThemeEditor {...defaultProps} />);

    const input = screen.getByTestId("theme-name-input");
    fireEvent.change(input, { target: { value: "New Theme Name" } });

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      name: "New Theme Name",
    });
  });

  it("allows updating colors", async () => {
    const user = userEvent.setup();
    render(<ThemeEditor {...defaultProps} />);

    const primaryColorInput = screen.getByTestId("primary-color-input");
    fireEvent.change(primaryColorInput, { target: { value: "#FF0000" } });

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      primaryColor: "#FF0000",
    });
  });

  it("allows updating font", async () => {
    const user = userEvent.setup();
    render(<ThemeEditor {...defaultProps} />);

    await user.click(screen.getByTestId("font-select"));
    await user.click(screen.getByTestId("font-option-arial"));

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      font: "Arial, sans-serif",
    });
  });

  it("allows updating button alignment", async () => {
    const user = userEvent.setup();
    render(<ThemeEditor {...defaultProps} />);

    await user.click(screen.getByTestId("button-alignment-select"));
    await user.click(screen.getByTestId("button-alignment-left"));

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      buttonAlignment: "left",
    });
  });

  it("allows updating border radius values", async () => {
    const user = userEvent.setup();
    render(<ThemeEditor {...defaultProps} />);

    const formBorderRadiusInput = screen.getByTestId(
      "form-border-radius-input",
    );
    fireEvent.change(formBorderRadiusInput, { target: { value: "1rem" } });

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      formBorderRadius: "1rem",
    });
  });

  it("resets theme to default values when reset button is clicked", async () => {
    const user = userEvent.setup();
    render(
      <ThemeEditor
        {...defaultProps}
        theme={{
          ...DEFAULT_THEME,
          primaryColor: "#FF0000",
          name: "Custom Theme",
        }}
      />,
    );

    await user.click(screen.getByTestId("reset-theme-button"));

    expect(mockOnThemeChange).toHaveBeenCalledWith({
      ...DEFAULT_THEME,
      name: "Custom Theme",
    });
  });
});
