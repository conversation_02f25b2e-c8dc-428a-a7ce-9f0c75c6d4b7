import {
  z,
  ZodEffe<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>al,
  Zod<PERSON><PERSON>eline,
  ZodString,
  ZodType,
} from "zod";
import type { EventType, FormField as DomainFormField } from "./types";
import { getEventTypeOptions } from "@/modules/leads/domain/event-type-mapping";
import { eventNeed, eventType } from "@/drizzle/schema";
import { subHours } from "date-fns";
import parsePhoneNumber from "libphonenumber-js/min";
import { Country, State } from "country-state-city";

// Type definitions
type SimpleSchema = {
  type: "simple";
  schema: z.ZodTypeAny;
};

type CompoundSchema = {
  type: "compound";
  fields: Record<string, z.ZodTypeAny>;
  validate?: (values: any) => boolean;
  errorMessage: string;
};

type FieldSchema = SimpleSchema | CompoundSchema;

export type InputType =
  | "TEXT"
  | "EMAIL"
  | "TEL"
  | "DATE"
  | "DATE_RANGE"
  | "NUMBER"
  | "CURRENCY"
  | "SELECT"
  | "TEXTAREA"
  | "CHECKBOX"
  | "CHECKBOX_GROUP"
  | "COMBO_BOX"
  | "EVENT_DATE_RANGE"
  | "FILE_UPLOAD";

export type FieldIdentifier = keyof typeof baseSchemas;
export type EventNeedValue = (typeof eventNeed.enumValues)[number];

// Validation context for each field
interface FieldValidationContext {
  isRequired: boolean;
  isVisible: boolean;
}

type BaseFieldDefinition = {
  defaultLabel: string | null;
  defaultRequired: boolean;
  baseSchema: FieldSchema;
  width?: "full" | "half";
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string }>;
  htmlAttrs?: Record<string, string>;
  group?: "contact" | "event" | "venue" | "details";
  format?: (value: any) => string;
  parse?: (value: string) => any;
  displayName?: string;
  allowedWidths?: ("full" | "half")[];
};

export type FieldDefinition =
  | (BaseFieldDefinition & {
      inputType: Omit<InputType, "CHECKBOX">;
    })
  | (BaseFieldDefinition & {
      inputType: "CHECKBOX";
      defaultCheckboxText: string;
    });

// Define the FieldOption type based on the options in BaseFieldDefinition
export type FieldOption = {
  label: string;
  value: string;
};

// Constants
const postalCodeRegex = /^[0-9]{5}(?:-[0-9]{4})?$/;
const phoneErrorMessage = "Please enter a valid phone number";

// Map of event need values to their display names
export const EVENT_NEEDS_DISPLAY_MAPPING: Record<EventNeedValue, string> = {
  MEETING_SPACE: "Meeting Space",
  CATERING: "Catering",
  GUESTROOMS: "Guestrooms",
  ACTIVITIES: "Activities",
  WEDDING_CEREMONY: "Wedding Ceremony",
  WEDDING_RECEPTION: "Wedding Reception",
  REHEARSAL: "Rehearsal",
  REHEARSAL_DINNER: "Rehearsal Dinner",
  SENDOFF_BRUNCH: "Send-Off Brunch",
  HONEYMOON: "Honeymoon",
  BACHELOR_PARTY: "Bachelor/Bachelorette Party",
};

// Define common sets of event needs
const STANDARD_EVENT_NEEDS: EventNeedValue[] = [
  "MEETING_SPACE",
  "CATERING",
  "GUESTROOMS",
  "ACTIVITIES",
];

const WEDDING_EVENT_NEEDS: EventNeedValue[] = [
  "WEDDING_CEREMONY",
  "WEDDING_RECEPTION",
  "REHEARSAL",
  "REHEARSAL_DINNER",
  "SENDOFF_BRUNCH",
  "HONEYMOON",
  "BACHELOR_PARTY",
];

// Define which event needs are applicable to which event types
export const EVENT_TYPE_TO_NEEDS_MAPPING: Record<string, EventNeedValue[]> = {
  corporate_meeting: STANDARD_EVENT_NEEDS,
  corporate_retreat: STANDARD_EVENT_NEEDS,
  association_event: STANDARD_EVENT_NEEDS,
  wedding: WEDDING_EVENT_NEEDS,
  family_reunion: STANDARD_EVENT_NEEDS,
  celebration: STANDARD_EVENT_NEEDS,
  social_or_sport_club: STANDARD_EVENT_NEEDS,
};

// Default needs to display when no event type is selected
const DEFAULT_EVENT_NEEDS: EventNeedValue[] = STANDARD_EVENT_NEEDS;

// Retrieves event needs options formatted for form fields
export const getEventNeedsOptions = (eventType?: string): FieldOption[] => {
  // Determine which event needs to display based on selected event type
  let applicableNeeds: EventNeedValue[];

  if (eventType && EVENT_TYPE_TO_NEEDS_MAPPING[eventType]) {
    applicableNeeds = EVENT_TYPE_TO_NEEDS_MAPPING[eventType];
  } else {
    applicableNeeds = DEFAULT_EVENT_NEEDS;
  }

  // Filter the EVENT_NEEDS_DISPLAY_MAPPING to only include applicable needs
  return applicableNeeds.map((need) => ({
    value: need,
    label: EVENT_NEEDS_DISPLAY_MAPPING[need],
  }));
};

// Helper functions for schema preprocessing
function preprocessText(schema: ZodType<any, any, any>) {
  return z.preprocess((val: unknown) => {
    if (val === undefined || val === null) return val;
    if (typeof val !== "string") return val;
    const trimmed = val.trim();
    return trimmed === "" ? undefined : trimmed;
  }, schema);
}

export function buildBaseNumberSchema(
  addValidationsFn?: (base: z.ZodNumber) => z.ZodNumber,
) {
  const target = z.coerce.number({
    invalid_type_error: "must be a number",
  });
  const baseSchemaWithAddedValidations = addValidationsFn
    ? addValidationsFn(target)
    : target;
  return z
    .string()
    .trim()
    .optional()
    .nullable()
    .transform((it) =>
      it === null || it === undefined || it === "" ? undefined : it,
    )
    .pipe(baseSchemaWithAddedValidations.optional());
}

export function buildRequiredNumberSchema(
  optionalNumSchema: ZodPipeline<
    ZodEffects<ZodNullable<ZodOptional<ZodString>>, undefined | string>,
    ZodOptional<ZodNumber>
  >,
) {
  return optionalNumSchema.pipe(z.number());
}

export function buildOptionalNumberSchema(
  optionalNumSchema: ZodPipeline<
    ZodEffects<ZodNullable<ZodOptional<ZodString>>, undefined | string>,
    ZodOptional<ZodNumber>
  >,
) {
  return optionalNumSchema.transform((it) =>
    it === null || it == undefined ? null : it,
  );
}

// Date validation
function isDateInFuture(dateStr: string): boolean {
  const minDate = subHours(new Date(), 26);
  return new Date(dateStr) >= minDate;
}

// Base date validation
const baseDateSchema = z
  .string()
  .date("must be a valid date")
  .refine(isDateInFuture, "Date must be in the future");

function dateSchema(errorMessage?: string) {
  return z
    .string()
    .refine(isDateInFuture, errorMessage ?? "Date must be in the future");
}

// Update existing preprocessors to use new helpers
const BooleanStringZod = z.preprocess((val: any) => {
  if (typeof val === "string") {
    if (["1", "true", "checked"].includes(val.toLowerCase())) return true;
    if (["0", "false"].includes(val.toLowerCase())) return false;
  }
  if (typeof val === "boolean") {
    return val;
  }
  return val;
}, z.coerce.boolean());

// Helper function for state validation
function isValidStateForCountry(
  stateCode: string,
  countryCode?: string,
): boolean {
  if (!stateCode) return false;

  // If no country provided, default to US states
  if (!countryCode) {
    // Use the country-state-city library to get US states
    const usStatesFromLib = State.getStatesOfCountry("US");
    return usStatesFromLib.some((state) => state.isoCode === stateCode.trim());
  }

  // For any other country, validate against the country-state-city library
  const statesForCountry = State.getStatesOfCountry(countryCode);
  if (!statesForCountry || statesForCountry.length === 0) {
    // If country has no states, any non-empty state code is invalid
    return false;
  }
  return statesForCountry.some((state) => state.isoCode === stateCode.trim());
}

// Helper function to check if a country has any states/regions
function countryHasStates(countryCode: string): boolean {
  const states = State.getStatesOfCountry(countryCode);
  return states && states.length > 0;
}

// Debug function to find countries without states
function findCountriesWithoutStates(): string[] {
  const countries = Country.getAllCountries();
  const countriesWithoutStates: string[] = [];

  countries.forEach((country) => {
    const states = State.getStatesOfCountry(country.isoCode);
    if (states.length === 0) {
      countriesWithoutStates.push(country.isoCode);
    }
  });

  return countriesWithoutStates;
}

// Define base schemas first
export const baseSchemas = {
  EVENT_TYPE: {
    type: "simple" as const,
    schema: z.enum(eventType.enumValues),
  },
  EVENT_NAME: {
    type: "simple" as const,
    schema: z.string().trim().max(255),
  },
  FIRST_NAME: {
    type: "simple" as const,
    schema: z.string().trim().max(50),
  },
  LAST_NAME: {
    type: "simple" as const,
    schema: z.string().trim().max(50),
  },
  COMPANY: {
    type: "simple" as const,
    schema: z.string().trim().max(100),
  },
  EMAIL: {
    type: "simple" as const,
    schema: z.string().trim().email("Please enter a valid email address"),
  },
  PHONE: {
    type: "simple" as const,
    schema: z
      .string()
      .trim()
      .refine(
        (value) => {
          if (!value) return false;
          const phoneNumber = parsePhoneNumber(value);
          return phoneNumber ? phoneNumber.isValid() : false;
        },
        { message: phoneErrorMessage },
      ),
  },
  CITY: {
    type: "simple" as const,
    schema: z.string().trim().max(100),
  },
  STATE: {
    type: "simple" as const,
    schema: z.string().trim().min(1, "Please select a valid state/region"),
  },
  POSTAL_CODE: {
    type: "simple" as const,
    schema: z
      .string()
      .trim()
      .regex(postalCodeRegex, "Please enter a valid postal code")
      .max(10),
  },
  START_DATE: {
    type: "simple" as const,
    schema: baseDateSchema,
  },
  END_DATE: {
    type: "simple" as const,
    schema: baseDateSchema,
  },
  GUEST_COUNT: {
    type: "simple" as const,
    schema: buildBaseNumberSchema((numSchema) =>
      numSchema
        .min(0, "Cannot be negative")
        .max(500000, "Cannot exceed 500,000 guests"),
    ),
  },
  ROOM_COUNT: {
    type: "simple" as const,
    schema: buildBaseNumberSchema((numSchema) =>
      numSchema
        .min(0, "Cannot be negative")
        .max(20000, "Cannot exceed 20,000 rooms"),
    ),
  },
  MEAL_COUNT: {
    type: "simple" as const,
    schema: buildBaseNumberSchema((numSchema) =>
      numSchema.min(0, "Cannot be negative"),
    ),
  },
  BUDGET: {
    type: "simple" as const,
    schema: buildBaseNumberSchema((numSchema) =>
      numSchema.min(0, "Cannot be negative"),
    ),
  },
  EVENT_DESCRIPTION: {
    type: "simple" as const,
    schema: z.string().max(2000),
  },
  FLEXIBLE_DATES: {
    type: "simple" as const,
    schema: BooleanStringZod,
  },
  EVENT_NEEDS: {
    type: "simple" as const,
    schema: z.preprocess(
      (val: unknown) => {
        if (Array.isArray(val)) return val;
        if (val === undefined || val === null) return [];
        return [val];
      },
      z
        .enum(eventNeed.enumValues)
        .array()
        .min(0)
        .refine(
          (arr: string[]) => new Set(arr).size === arr.length,
          "Event needs must be unique",
        ),
    ),
  },
  MARKETING_CONSENT: {
    type: "simple" as const,
    schema: z.coerce.boolean().refine((val) => val === true, {
      message: "You must agree to receive communications",
    }),
  },
  EVENT_DATE_RANGE: {
    type: "compound" as const,
    fields: {
      START_DATE: dateSchema("Start date must be in the future"),
      END_DATE: dateSchema("End date must be in the future"),
    },
    validate: (values: Record<string, string | undefined>) => {
      // If either date is provided, both must be valid dates with end >= start
      if (values.START_DATE || values.END_DATE) {
        if (!values.START_DATE || !values.END_DATE) return false;
        const start = new Date(values.START_DATE);
        const end = new Date(values.END_DATE);

        // End date must be after or equal to start date
        if (end < start) return false;

        return true;
      }
      // If neither date is provided, that's fine (will be caught by required/optional check)
      return true;
    },
    errorMessage: "End date must be on or after start date",
  },
  COUNTRY: {
    type: "simple" as const,
    schema: z
      .string()
      .trim()
      .min(2, "Please select a valid country")
      .refine(
        (value) => {
          // Validate against actual country codes using the country-state-city library
          return Country.getAllCountries().some(
            (country) => country.isoCode === value,
          );
        },
        {
          message: "Please select a valid country",
        },
      ),
  },
  FILE_UPLOAD: {
    type: "simple" as const,
    schema: z.preprocess(
      (val: unknown) => {
        if (Array.isArray(val)) return val;
        if (val === undefined || val === null) return [];
        return [];
      },
      z.union([
        z.array(
          z.object({
            fileId: z.string().uuid("File ID must be a valid UUID"),
            fileName: z.string(),
            fileSize: z.preprocess(
              (val) => (typeof val === "string" ? parseInt(val, 10) : val),
              z.number().int().positive(),
            ),
            fileType: z.string(),
            uploadKey: z.string(),
            keyWithoutStatusPrefix: z.string(),
          }),
        ),
        z.array(z.string().uuid()),
      ]),
    ),
  },
} as const;

export const PREDEFINED_FIELDS: Record<FieldIdentifier, FieldDefinition> = {
  EVENT_TYPE: {
    inputType: "SELECT",
    defaultLabel: "Event Type",
    defaultRequired: true,
    placeholder: "Select event type",
    baseSchema: baseSchemas.EVENT_TYPE,
    options: getEventTypeOptions(),
    group: "event",
  },
  EVENT_NAME: {
    inputType: "TEXT",
    defaultLabel: "Event Name",
    defaultRequired: false,
    baseSchema: baseSchemas.EVENT_NAME,
    placeholder: "Enter event name",
    group: "event",
  },
  FIRST_NAME: {
    inputType: "TEXT",
    defaultLabel: "First Name",
    defaultRequired: true,
    baseSchema: baseSchemas.FIRST_NAME,
    placeholder: "Enter your first name",
    htmlAttrs: { autocomplete: "given-name" },
    group: "contact",
  },
  LAST_NAME: {
    inputType: "TEXT",
    defaultLabel: "Last Name",
    defaultRequired: true,
    baseSchema: baseSchemas.LAST_NAME,
    placeholder: "Enter your last name",
    htmlAttrs: { autocomplete: "family-name" },
    group: "contact",
  },
  COMPANY: {
    inputType: "TEXT",
    defaultLabel: "Company Name",
    defaultRequired: false,
    baseSchema: baseSchemas.COMPANY,
    placeholder: "Enter your company name",
    htmlAttrs: { autocomplete: "organization" },
    group: "contact",
  },
  EMAIL: {
    inputType: "EMAIL",
    defaultLabel: "Email Address",
    defaultRequired: true,
    baseSchema: baseSchemas.EMAIL,
    placeholder: "Enter your email address",
    htmlAttrs: { autocomplete: "email" },
    group: "contact",
  },
  PHONE: {
    inputType: "TEL",
    defaultLabel: "Phone Number",
    defaultRequired: true,
    baseSchema: baseSchemas.PHONE,
    htmlAttrs: {
      autocomplete: "tel",
      inputmode: "numeric",
      title: "Enter your US phone number",
      "data-mask": "phone",
      maxlength: "14",
    },
    group: "contact",
  },
  COUNTRY: {
    inputType: "COMBO_BOX",
    defaultLabel: "Country",
    defaultRequired: true,
    baseSchema: baseSchemas.COUNTRY,
    placeholder: "Select a country",
    options: Country.getAllCountries().map((country) => ({
      value: country.isoCode,
      label: country.name,
    })),
    htmlAttrs: { autocomplete: "country" },
    group: "contact",
  },
  CITY: {
    inputType: "TEXT",
    defaultLabel: "City",
    defaultRequired: true,
    baseSchema: baseSchemas.CITY,
    placeholder: "Enter your city",
    htmlAttrs: { autocomplete: "address-level2" },
    group: "contact",
  },
  STATE: {
    inputType: "COMBO_BOX",
    defaultLabel: "State/Region",
    defaultRequired: true,
    baseSchema: {
      type: "simple",
      schema: z.string().trim().min(1, "Please select a valid state/region"),
    },
    placeholder: "Select a state/region",
    options: State.getStatesOfCountry("US").map((state) => ({
      value: state.isoCode,
      label: state.name,
    })), // Default to US states, will be updated dynamically
    htmlAttrs: { autocomplete: "address-level1" },
    group: "contact",
  },
  POSTAL_CODE: {
    inputType: "TEXT",
    defaultLabel: "Postal Code",
    defaultRequired: true,
    baseSchema: baseSchemas.POSTAL_CODE,
    placeholder: "Enter your postal code",
    htmlAttrs: { autocomplete: "postal-code" },
    group: "contact",
  },
  START_DATE: {
    inputType: "DATE",
    defaultLabel: "Event Start Date",
    defaultRequired: true,
    baseSchema: baseSchemas.START_DATE,
    group: "event",
  },
  END_DATE: {
    inputType: "DATE",
    defaultLabel: "Event End Date",
    defaultRequired: false,
    baseSchema: baseSchemas.END_DATE,
    group: "event",
  },
  GUEST_COUNT: {
    inputType: "NUMBER",
    defaultLabel: "Number of Guests",
    defaultRequired: true,
    baseSchema: baseSchemas.GUEST_COUNT,
    placeholder: "Enter expected number of guests",
    group: "event",
  },
  ROOM_COUNT: {
    inputType: "NUMBER",
    defaultLabel: "Number of Rooms Needed",
    defaultRequired: false,
    baseSchema: baseSchemas.ROOM_COUNT,
    placeholder: "Enter number of rooms needed",
    group: "venue",
  },
  MEAL_COUNT: {
    inputType: "NUMBER",
    defaultLabel: "Number of Meals",
    defaultRequired: false,
    baseSchema: baseSchemas.MEAL_COUNT,
    placeholder: "Enter number of meals needed",
    group: "venue",
  },
  BUDGET: {
    inputType: "CURRENCY",
    defaultLabel: "Estimated Budget",
    defaultRequired: false,
    baseSchema: baseSchemas.BUDGET,
    placeholder: "Enter your estimated budget",
    group: "details",
  },
  EVENT_DESCRIPTION: {
    inputType: "TEXTAREA",
    defaultLabel: "Event Description",
    defaultRequired: false,
    baseSchema: baseSchemas.EVENT_DESCRIPTION,
    placeholder: "Please describe your event",
    group: "details",
  },
  FLEXIBLE_DATES: {
    inputType: "CHECKBOX",
    defaultLabel: "Are your dates flexible?",
    defaultRequired: false,
    baseSchema: baseSchemas.FLEXIBLE_DATES,
    group: "event",
    defaultCheckboxText: "Yes",
  },
  EVENT_NEEDS: {
    inputType: "CHECKBOX_GROUP",
    defaultLabel: "Event Needs",
    defaultRequired: false,
    baseSchema: baseSchemas.EVENT_NEEDS,
    options: getEventNeedsOptions(),
    group: "venue",
  },
  MARKETING_CONSENT: {
    inputType: "CHECKBOX",
    defaultLabel: null,
    displayName: "Marketing Consent",
    defaultRequired: true,
    baseSchema: baseSchemas.MARKETING_CONSENT,
    group: "contact",
    defaultCheckboxText:
      "I agree to receive marketing communications via email, text messages, and/or phone calls",
  },
  EVENT_DATE_RANGE: {
    inputType: "EVENT_DATE_RANGE",
    defaultLabel: "Event Dates",
    defaultRequired: true,
    baseSchema: baseSchemas.EVENT_DATE_RANGE,
    group: "event",
    width: "full",
    allowedWidths: ["full"],
  },
  FILE_UPLOAD: {
    inputType: "FILE_UPLOAD",
    defaultLabel: "File Upload",
    defaultRequired: false,
    baseSchema: baseSchemas.FILE_UPLOAD,
    placeholder: "Drop files here or click to browse",
    group: "details",
    allowedWidths: ["full"],
    width: "full",
    htmlAttrs: {
      accept: ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png",
      multiple: "true",
      "data-max-size": "10485760", // 10MB in bytes
      "data-max-files": "5",
    },
  },
} as const;

// Stage 1: Determine validation rules based on event type
function computeFieldVisibilityAndRequirement(
  fields: DomainFormField[],
  submittedData: Record<string, unknown>,
): Record<string, FieldValidationContext> {
  const selectedEventType = submittedData.EVENT_TYPE as EventType | undefined;
  const fieldContexts: Record<string, FieldValidationContext> = {};

  fields.forEach((field) => {
    const isVisible =
      field.type === "EVENT_TYPE" ||
      !field.hideForEventTypes?.includes(selectedEventType as any);

    const required = isVisible && field.required;
    fieldContexts[field.type] = {
      isRequired: required,
      isVisible,
    };

    // Handle compound fields by copying visibility context to all subfields
    const fieldDefinition = PREDEFINED_FIELDS[field.type];
    const baseSchema = fieldDefinition.baseSchema;
    if (baseSchema.type === "compound") {
      Object.keys(baseSchema.fields).forEach((subfield) => {
        fieldContexts[subfield] = {
          isRequired: required,
          isVisible,
        };
      });
    }
  });

  return fieldContexts;
}

// Stage 2: Build field-level validation schemas
function buildFieldValidationSchemas(
  fields: DomainFormField[],
  fieldContexts: Record<string, FieldValidationContext>,
  submittedData: Record<string, unknown>,
): z.ZodTypeAny {
  const validationSchemas: Record<string, z.ZodTypeAny> = {};

  // First pass: Handle all fields except STATE field
  fields.forEach((field) => {
    if (field.type === "STATE") return; // Skip STATE field for now

    const fieldDefinition = PREDEFINED_FIELDS[field.type];
    const baseSchema = fieldDefinition.baseSchema;
    const context = fieldContexts[field.type];

    if (!context.isVisible) {
      if (baseSchema.type === "compound") {
        Object.keys(baseSchema.fields).forEach((key) => {
          validationSchemas[key] = z.any().optional().nullable();
        });
      } else {
        validationSchemas[field.type] = z.any().optional().nullable();
      }
      return;
    }

    if (baseSchema.type === "compound") {
      Object.entries(baseSchema.fields).forEach(([key, schema]) => {
        validationSchemas[key] = context.isRequired
          ? schema
          : schema.optional();
      });
    } else {
      let schema = baseSchema.schema;

      // Special handling for FILE_UPLOAD fields
      if (field.type === "FILE_UPLOAD") {
        if (context.isRequired) {
          // For required FILE_UPLOAD fields, enforce at least one file
          schema = schema.refine((arr) => arr.length > 0, {
            message: "Required",
          });
        }
      }
      // For numeric fields, handle nullable/optional state after preprocessing
      else if (
        fieldDefinition.inputType === "NUMBER" ||
        fieldDefinition.inputType === "CURRENCY"
      ) {
        if (context.isRequired) {
          schema = buildRequiredNumberSchema(schema);
        } else {
          schema = buildOptionalNumberSchema(schema);
        }
      } else {
        // For non-numeric fields, handle optional state first
        if (!context.isRequired) {
          schema = schema.optional();
        }
        // Then apply text preprocessing if needed
        if (
          fieldDefinition.inputType === "TEXT" ||
          fieldDefinition.inputType === "EMAIL" ||
          fieldDefinition.inputType === "TEXTAREA" ||
          fieldDefinition.inputType === "TEL"
        ) {
          schema = preprocessText(schema);
        }
      }

      validationSchemas[field.type] = schema;
    }
  });

  // Second pass: Handle STATE field specially
  fields.forEach((field) => {
    if (field.type !== "STATE") return; // Only process STATE field

    const fieldDefinition = PREDEFINED_FIELDS[field.type];
    const baseSchema = fieldDefinition.baseSchema;
    const context = fieldContexts[field.type];

    if (!context.isVisible) {
      validationSchemas[field.type] = z.any().optional().nullable();
      return;
    }

    // Safely access schema - STATE always has a simple schema type
    if (baseSchema.type !== "simple") {
      throw new Error("STATE field should have a simple schema type");
    }

    let schema = baseSchema.schema;

    // Check if the country has states - if not, make the STATE field optional regardless of required setting
    const country = submittedData.COUNTRY as string | undefined;
    if (country && !countryHasStates(country)) {
      // Country has no states, so STATE field should be optional
      schema = schema.optional();
    } else if (!context.isRequired) {
      // Country has states but field is not required
      schema = schema.optional();
    }

    // Apply text preprocessing
    schema = preprocessText(schema);

    validationSchemas[field.type] = schema;
  });

  return z.object(validationSchemas);
}

// Stage 3: Add cross-field validations
function addCrossFieldValidations(
  baseSchema: z.ZodTypeAny,
  fields: DomainFormField[],
  fieldContexts: Record<string, FieldValidationContext>,
): z.ZodTypeAny {
  let enhancedSchema = baseSchema;

  fields.forEach((field) => {
    const fieldDefinition = PREDEFINED_FIELDS[field.type];
    const baseSchema = fieldDefinition.baseSchema;
    const context = fieldContexts[field.type];

    if (
      baseSchema.type === "compound" &&
      baseSchema.validate &&
      context.isVisible
    ) {
      enhancedSchema = enhancedSchema.refine(
        (data) => baseSchema.validate!(data),
        { message: baseSchema.errorMessage },
      );
    }
  });

  // Add state validation (both independent and with country)
  const hasStateField = fields.some(
    (field) => field.type === "STATE" && fieldContexts[field.type].isVisible,
  );
  const hasCountryField = fields.some(
    (field) => field.type === "COUNTRY" && fieldContexts[field.type].isVisible,
  );

  if (hasStateField) {
    // 1. First, for state without country, validate against US states
    enhancedSchema = enhancedSchema.refine(
      (data) => {
        const state = data.STATE as string | undefined;
        const country = data.COUNTRY as string | undefined;

        if (
          !state ||
          state === "" ||
          (hasCountryField && country && country !== "")
        ) {
          return true;
        }

        return isValidStateForCountry(state);
      },
      {
        message: "Please select a valid state/region",
        path: ["STATE"],
      },
    );

    if (hasCountryField) {
      enhancedSchema = enhancedSchema.refine(
        (data) => {
          const state = data.STATE as string | undefined;
          const country = data.COUNTRY as string | undefined;

          if (!hasCountryField) {
            return true;
          }

          // If country is not selected, state should be invalid
          if (!country || country === "") {
            return false;
          }

          // Check if country has states - if not, empty state is valid
          if (!countryHasStates(country)) {
            return true;
          }

          // If country has states, validate that state is provided
          if (!state || state === "") {
            return false;
          }

          return true;
        },
        {
          message: "State/Region is required for the selected country",
          path: ["STATE"],
        },
      );

      // 2b. Validate that the state is valid for the selected country
      enhancedSchema = enhancedSchema.refine(
        (data) => {
          const state = data.STATE as string | undefined;
          const country = data.COUNTRY as string | undefined;

          // Skip validation for empty state
          if (!state || state === "") {
            return true;
          }

          // If country field exists but is not set, state should be invalid
          if (hasCountryField && (!country || country === "")) {
            return false;
          }

          // If country doesn't have states, any state value is invalid
          if (country && !countryHasStates(country) && state !== "") {
            return false;
          }

          // Make sure country is defined before passing to isValidStateForCountry
          return isValidStateForCountry(state, country || undefined);
        },
        {
          message:
            "Please select a valid state/region for the selected country",
          path: ["STATE"],
        },
      );
    }
  }

  return enhancedSchema;
}

// Main schema creator function
function buildSchemaCreatorForForm(fields: DomainFormField[]) {
  return (submittedData: Record<string, unknown>) => {
    // Stage 1: Determine which fields are visible and required
    const fieldContexts = computeFieldVisibilityAndRequirement(
      fields,
      submittedData,
    );

    // Stage 2: Create field-level validation schemas
    let formSchema = buildFieldValidationSchemas(
      fields,
      fieldContexts,
      submittedData,
    );

    // Stage 3: Add cross-field validation rules
    formSchema = addCrossFieldValidations(formSchema, fields, fieldContexts);

    return formSchema;
  };
}

export function validateFormSubmission(
  fields: DomainFormField[],
  data: Record<string, unknown>,
) {
  const schema = buildSchemaCreatorForForm(fields)(data);
  return schema.safeParse(data);
}

// Types for form rendering mode
export type FormRenderMode = "iframe" | "html";

export interface FormRenderConfig {
  mode: FormRenderMode;
  theme?: {
    primaryColor: string;
    backgroundColor: string;
    textColor: string;
    borderColor: string;
    borderRadius: string;
    padding: string;
  };
  customCss?: string;
  customJs?: string;
  initScript?: string;
}
