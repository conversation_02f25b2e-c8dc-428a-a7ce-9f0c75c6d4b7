declare global {
  interface Window {
    SivForms: typeof SivForms;
  }
}

interface FormData {
  [key: string]: string | File;
}

interface SivFormOptions {
  formId: string;
  target: string | HTMLElement;
  mode?: "iframe" | "inline";
  theme?: {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
    borderRadius?: string;
    padding?: string;
    inputBorderRadius?: string;
    buttonBorderRadius?: string;
    buttonTextColor?: string;
    font?: string;
    fieldBackgroundColor?: string;
    inputTextColor?: string;
    formBorderRadius?: string;
  };
  onSubmit?: (data: FormData) => void;
  onError?: (error: Error) => void;
}

interface FormSubmitMessage {
  type: "SIV_FORM_SUBMIT";
  data: FormData;
}

interface FormHeightMessage {
  type: "SIV_FORM_HEIGHT";
  data: { height: number };
}

interface FormErrorMessage {
  type: "SIV_FORM_ERROR";
  data: { message: string };
}

type FormMessage = FormSubmitMessage | FormHeightMessage | FormErrorMessage;

class SivForms {
  private static instance: SivForms;
  private baseUrl: string;

  private constructor() {
    // In production, this should be your CDN/API domain
    this.baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://forms.sivhospitality.com"
        : "http://localhost:3000";
  }

  public static getInstance(): SivForms {
    if (!SivForms.instance) {
      SivForms.instance = new SivForms();
    }
    return SivForms.instance;
  }

  public async render(options: SivFormOptions): Promise<void> {
    try {
      const target =
        typeof options.target === "string"
          ? document.querySelector(options.target)
          : options.target;

      if (!target) {
        throw new Error(`Target element not found: ${options.target}`);
      }

      if (options.mode === "iframe") {
        this.renderIframe(target as HTMLElement, options);
      } else {
        await this.renderInline(target as HTMLElement, options);
      }
    } catch (error) {
      if (options.onError) {
        options.onError(
          error instanceof Error ? error : new Error("Failed to render form"),
        );
      }
    }
  }

  private renderIframe(target: HTMLElement, options: SivFormOptions): void {
    const iframe = document.createElement("iframe");
    iframe.src = `${this.baseUrl}/forms/embed/${options.formId}`;
    iframe.style.width = "100%";
    iframe.style.height = "500px";
    iframe.style.border = "none";
    iframe.style.overflow = "hidden";

    // Handle iframe messaging
    window.addEventListener("message", (event) => {
      if (event.origin !== this.baseUrl) return;

      const message = event.data as FormMessage;
      switch (message.type) {
        case "SIV_FORM_SUBMIT":
          if (options.onSubmit) options.onSubmit(message.data);
          break;
        case "SIV_FORM_HEIGHT":
          iframe.style.height = `${message.data.height}px`;
          break;
        case "SIV_FORM_ERROR":
          if (options.onError) options.onError(new Error(message.data.message));
          break;
      }
    });

    target.appendChild(iframe);
  }

  private async renderInline(
    target: HTMLElement,
    options: SivFormOptions,
  ): Promise<void> {
    try {
      // Fetch form HTML
      const response = await fetch(
        `${this.baseUrl}/api/forms/${options.formId}/html`,
      );
      if (!response.ok) throw new Error("Failed to fetch form HTML");

      const html = await response.text();
      target.innerHTML = html;

      // Apply theme if provided
      if (options.theme) {
        const style = document.createElement("style");
        style.textContent = this.generateThemeCSS(options.theme);
        target.appendChild(style);
      }

      // Initialize form behavior
      const form = target.querySelector("form");
      if (!form) throw new Error("Form element not found in response");

      form.addEventListener("submit", async (e) => {
        e.preventDefault();
        const submitButton = form.querySelector(
          'button[type="submit"]',
        ) as HTMLButtonElement | null;
        if (submitButton) submitButton.disabled = true;

        try {
          const formData = new FormData(form as HTMLFormElement);
          const data = Object.fromEntries(formData.entries()) as FormData;

          const submitResponse = await fetch(
            `${this.baseUrl}/api/forms/submit`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                formId: options.formId,
                data,
              }),
            },
          );

          if (!submitResponse.ok) {
            throw new Error("Submission failed");
          }

          const result = (await submitResponse.json()) as FormData;
          if (options.onSubmit) options.onSubmit(result);
          form.reset();
        } catch (error) {
          if (options.onError) {
            options.onError(
              error instanceof Error
                ? error
                : new Error("Failed to submit form"),
            );
          }
        } finally {
          if (submitButton) submitButton.disabled = false;
        }
      });
    } catch (error) {
      if (options.onError) {
        options.onError(
          error instanceof Error
            ? error
            : new Error("Failed to render inline form"),
        );
      }
    }
  }

  private generateThemeCSS(theme: SivFormOptions["theme"]): string {
    if (!theme) {
      theme = {};
    }

    return `
            :root {
                --primary-color: ${theme.primaryColor || "#3b82f6"};
                --background-color: ${theme.backgroundColor || "#ffffff"};
                --text-color: ${theme.textColor || "#000000"};
                --border-color: ${theme.borderColor || "#e2e8f0"};
                --input-border-radius: ${theme.inputBorderRadius || "0.375rem"};
                --button-border-radius: ${theme.buttonBorderRadius || "0.375rem"};
                --padding: ${theme.padding || "1rem"};
                --button-text-color: ${theme.buttonTextColor || "#ffffff"};
                --font: ${theme.font || "system-ui, -apple-system, sans-serif"};
                --field-background-color: ${theme.fieldBackgroundColor || "#ffffff"};
                --input-text-color: ${theme.inputTextColor || "#000000"};
                --form-border-radius: ${theme.formBorderRadius || "0.75rem"};
            }
        `;
  }
}

// Export for use in browser
export { SivForms };

window.SivForms = SivForms;

// Usage example:
/*
    <div id="siv-form-container"></div>
    <script src="https://cdn.sivhospitality.com/forms/v1/siv-forms.js"></script>
    <script>
        const sivForms = SivForms.getInstance();
        sivForms.render({
            formId: 'your-form-id',
            target: '#siv-form-container',
            mode: 'iframe', // or 'inline'
            theme: {
                primaryColor: '#4f46e5',
                borderRadius: '0.5rem'
            },
            onSubmit: (data) => {
                console.log('Form submitted:', data);
            },
            onError: (error) => {
                console.error('Form error:', error);
            }
        });
    </script>
*/
