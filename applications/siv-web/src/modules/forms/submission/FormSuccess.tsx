import React, { useEffect, useState } from "react";
import { Form } from "@/modules/forms/builder/types";

// Check if we're in a browser environment
const isBrowser =
  typeof window !== "undefined" && typeof document !== "undefined";

/**
 * Helper function to check if the current page is in an iframe
 */
function isInIframe(): boolean {
  if (!isBrowser) return false;
  try {
    // Simple check to determine if we're in an iframe
    return window.self !== window.top;
  } catch (e) {
    // If we can't access window.top due to security restrictions,
    // we're definitely in an iframe
    return true;
  }
}

// Create a reusable debug logger
const debug = {
  log: (message: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.log(`[SIV:FormSuccess] ${message}`, ...processedArgs);
    }
  },
  warn: (message: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.warn(`[SIV:FormSuccess] ${message}`, ...processedArgs);
    }
  },
  error: (message: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.error(`[SIV:FormSuccess] ${message}`, ...processedArgs);
    }
  },
};

interface FormSuccessProps {
  form: Form;
  successMessage?: string;
  className?: string;
}

/**
 * FormSuccess handles what happens after a successful form submission.
 * It can either display a success message or redirect the user, handling both
 * standalone and iframe scenarios appropriately.
 */
const FormSuccess: React.FC<FormSuccessProps> = ({
  form,
  successMessage = "Thank you for your submission! A member of our team will reach out to you soon.",
  className = "",
}) => {
  const [isRedirecting, setIsRedirecting] = useState(!!form.redirectUrl);

  // Handle redirect logic on mount
  useEffect(() => {
    if (!isBrowser) return;

    // If we have a redirect URL configured
    if (form.redirectUrl) {
      debug.log("Redirect URL detected", { url: form.redirectUrl });

      const inIframe = isInIframe();
      debug.log("Environment check", { inIframe });

      setIsRedirecting(true);

      if (inIframe) {
        // For iframe: notify parent to redirect
        debug.log("In iframe, will attempt to notify parent");

        // First try using iframe-resizer's API if available
        const win = window as any;
        if (win.parentIFrame) {
          debug.log("parentIFrame API available, sending message");
          try {
            win.parentIFrame.sendMessage(
              {
                type: "redirect",
                url: form.redirectUrl,
              },
              "*",
            );
            debug.log("Message sent via parentIFrame.sendMessage");
          } catch (error) {
            debug.error("Error sending message via parentIFrame", error);
          }
        } else {
          // Fallback to standard postMessage
          debug.log("parentIFrame not available, using postMessage fallback");
          try {
            // Use postMessage with any cast to avoid TypeScript issues
            win.parent.postMessage(
              {
                type: "redirect",
                url: form.redirectUrl,
              },
              "*",
            );
            debug.log("Message sent via window.parent.postMessage");
          } catch (error) {
            debug.error("Error sending message via postMessage", error);
          }
        }
      } else {
        // For standalone: direct redirect
        debug.log("Not in iframe, redirecting directly");
        try {
          window.location.href = form.redirectUrl;
        } catch (error) {
          debug.error("Error redirecting directly", error);
        }
      }
    } else {
      debug.log("No redirect URL configured, displaying success message");
    }
  }, [form.redirectUrl]);

  // If redirecting, show a brief message
  if (form.redirectUrl) {
    return (
      <div
        className={`flex items-center justify-center p-4 text-center ${className}`}
        data-testid="form-redirecting"
      >
        <div>
          <h2
            className="text-2xl font-bold mb-2"
            style={{ color: form.theme.textColor }}
          >
            Thank you!
          </h2>
          <p
            className="mb-4"
            style={{ color: form.theme.textColor }}
            data-testid="redirecting-message"
          >
            Redirecting you now...
          </p>
          <div
            className="w-8 h-8 border-t-2 border-b-2 rounded-full animate-spin mx-auto"
            style={{ borderColor: form.theme.primaryColor }}
          ></div>
        </div>
      </div>
    );
  }

  // Otherwise show success message
  return (
    <div className={`form-success p-4 text-center ${className}`}>
      <p
        data-testid="submit-success-message"
        style={{ color: form.theme.textColor }}
      >
        {successMessage}
      </p>
    </div>
  );
};

export default FormSuccess;
