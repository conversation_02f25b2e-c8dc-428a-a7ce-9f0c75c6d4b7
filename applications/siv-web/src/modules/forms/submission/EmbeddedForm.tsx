import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import type { Form, FormField } from "../builder/types";
import { getEventNeedsOptions, PREDEFINED_FIELDS } from "../builder/fieldTypes";
import * as z from "zod";
import intlTelInput from "intl-tel-input";
import "intl-tel-input/build/css/intlTelInput.css";
import { DateRangePicker } from "@/modules/forms/components/date-range-picker";
import { eventType } from "@/drizzle/schema";
import { type FieldDefinition } from "@/modules/forms/builder/fieldTypes";
import FormSuccess from "./FormSuccess";
import { State } from "country-state-city";
import { type FileData, FileUploadField } from "../components/FileUploadField";
import ComboboxField, {
  type ComboboxOption,
} from "../components/combobox-field";

export type FormValue = string | string[] | File | FileData[] | any; // Using 'any' to resolve type conflicts
export type FormValues = Record<string, FormValue>;

interface Props {
  form: Form;
  errors?: Record<string, string[]>;
  values?: FormValues;
  onChange?: (name: string, value: FormValue) => void;
  success?: boolean;
  isMobileGuess?: boolean;
  testForceMobile?: boolean;
  testForceCalendarOpen?: boolean;
}

export function generateThemeStyles(theme: Form["theme"]): React.CSSProperties {
  // Calculate if we need to invert the calendar icon based on the background color
  // Convert the hex color to RGB and calculate luminance
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  const calculateLuminance = (color: string) => {
    const rgb = hexToRgb(color);
    if (!rgb) return 0;
    // Relative luminance formula
    return (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  };

  const bgLuminance = calculateLuminance(theme.fieldBackgroundColor);
  const calendarIconFilter = bgLuminance > 0.5 ? "none" : "invert(1)";

  return {
    "--ef-primary": theme.primaryColor,
    "--ef-background": theme.backgroundColor,
    "--ef-text": theme.textColor,
    "--ef-border": theme.borderColor,
    "--ef-input-radius": theme.inputBorderRadius,
    "--ef-button-radius": theme.buttonBorderRadius,
    "--ef-spacing": theme.padding,
    "--ef-button-text": theme.buttonTextColor,
    "--ef-font": theme.font,
    "--ef-field-background": theme.fieldBackgroundColor,
    "--ef-input-text": theme.inputTextColor,
    "--ef-form-radius": theme.formBorderRadius,
    "--ef-placeholder": theme.placeholderColor,
    "--ef-calendar-icon-filter": calendarIconFilter,
    display: "flex",
    flexDirection: "column",
    alignItems: "stretch",
  } as React.CSSProperties;
}

// Export the CSS variable mappings for reuse in stories
export function getThemeCSSVariableMappings() {
  return `
        --background: var(--ef-field-background);
        --foreground: var(--ef-input-text);
        --muted: var(--ef-border);
        --muted-foreground: var(--ef-placeholder);
        --popover: var(--ef-field-background);
        --popover-foreground: var(--ef-input-text);
        --border: var(--ef-border);
        --input: var(--ef-border);
        --primary: var(--ef-primary);
        --primary-foreground: var(--ef-button-text);
        --ring: var(--ef-primary);
        --radius: var(--ef-input-radius);
        --accent: var(--ef-field-background);
        --accent-foreground: var(--ef-input-text);
    `;
}

// Base CSS styles as a separate const for cleaner component rendering
const baseStyles = `
    .ef-root {
        font-family: var(--ef-font);
        background-color: var(--ef-background);
        color: var(--ef-text);
        padding: var(--ef-spacing);
        border-radius: var(--ef-form-radius);
        max-width: 100%;
        box-sizing: border-box;

        /* shadcn theme variable mappings */
        --background: var(--ef-field-background);
        --foreground: var(--ef-input-text);
        --muted: var(--ef-border);
        --muted-foreground: var(--ef-placeholder);
        --popover: var(--ef-field-background);
        --popover-foreground: var(--ef-input-text);
        --border: var(--ef-border);
        --input: var(--ef-border);
        --primary: var(--ef-primary);
        --primary-foreground: var(--ef-button-text);
        --ring: var(--ef-primary);
        --radius: var(--ef-input-radius);

        /* Override shadcn command colors */
        --command-foreground: var(--ef-input-text);
        --command-background: var(--ef-field-background);
        --command-border: var(--ef-border);
        --command-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .ef-form {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem; /* Reduce the gap slightly */
        width: 100%;
        align-items: flex-start;
    }

    .ef-field {
        width: 100%; /* Default to full width on mobile */
        box-sizing: border-box;
        margin-bottom: 1rem; /* Add consistent bottom margin */
    }

    /* Media query for screens larger than 37rem */
    @media (min-width: 37rem) {
        .ef-form {
            row-gap: 1.5rem; /* Explicit row gap */
            column-gap: 1.5rem; /* Explicit column gap */
        }
        
        .ef-field[data-width="half"] {
            width: calc(50% - 0.75rem); /* 50% minus half the gap */
            flex: 0 0 calc(50% - 0.75rem); /* Don't grow, don't shrink, fixed width */
            margin-bottom: 0; /* Remove bottom margin in desktop view */
        }
        
        .ef-field[data-width="full"] {
            width: 100%;
            flex: 0 0 100%; /* Don't grow, don't shrink, full width */
            margin-bottom: 0; /* Remove bottom margin in desktop view */
        }
        
        /* Force a row break after this field */
        .ef-field[data-row-break-after="true"] {
            margin-right: 100%;
        }
    }

    .ef-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .ef-input,
    .ef-select,
    .ef-textarea {
        width: 100%;
        padding: 0.75rem; /* Slightly more padding */
        border: 1px solid var(--ef-border);
        border-radius: var(--ef-input-radius);
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        font-size: 1rem; /* Explicit font size */
        line-height: 1.5; /* Better line height */
        min-height: 2.5rem; /* Minimum height for all inputs */
        box-sizing: border-box; /* Ensure padding doesn't affect width */
    }

    .ef-textarea {
        min-height: 6rem; /* Taller for textareas */
        resize: vertical; /* Allow vertical resizing only */
    }

    /* Placeholder styling */
    .ef-input::placeholder,
    .ef-textarea::placeholder {
        color: var(--ef-placeholder);
        opacity: 1;
    }

    /* Select placeholder */
    .ef-select option[value=""][disabled] {
        color: var(--ef-placeholder);
    }

    /* Select placeholder when selected */
    .ef-select:not(:focus):invalid {
        color: var(--ef-placeholder);
    }

    .ef-input:focus,
    .ef-textarea:focus {
        outline: none;
        border-color: var(--ef-primary);
        box-shadow: 0 0 0 1px var(--ef-primary);
    }
    
    /* Combobox specific styles */
    .ef-combobox {
        width: 100%;
        font-size: 1rem;
        line-height: 1.5;
        box-sizing: border-box;
    }
    
    /* Combobox wrapper to control dropdown styling */
    .ef-combobox-wrapper {
        width: 100%;
        position: relative;
    }
    
    /* Additional styles for Headless UI Combobox */
    .ef-combobox-wrapper [role="combobox"] input {
        width: 100% !important;
        visibility: visible !important;
        opacity: 1 !important;
        display: block !important;
        position: relative !important;
        z-index: 1 !important;
        border: 1px solid var(--ef-border) !important;
    }
    
    /* Override any Headless UI styles that might be hiding the input */
    .ef-combobox-wrapper [role="combobox"] {
        position: relative !important;
        display: block !important;
    }
    
    /* Make sure button is above input for clicks */
    .ef-combobox-wrapper button[data-headlessui-state] {
        position: absolute !important;
        right: 0 !important;
        top: 0 !important;
        bottom: 0 !important;
        z-index: 2 !important;
        border: none !important;
        background: transparent !important;
    }
    
    /* Style the dropdown options list */
    .ef-combobox-wrapper [role="listbox"] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        border: 1px solid var(--ef-border) !important;
        border-radius: var(--ef-input-radius) !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        margin-top: 4px !important;
        padding: 4px 0 !important;
    }
    
    /* Style each option */
    .ef-combobox-wrapper [role="option"] {
        padding: 8px 16px 8px 40px !important;
        cursor: pointer !important;
        position: relative !important;
        color: var(--ef-input-text) !important;
    }
    
    /* Checkmark and icon styling */
    .ef-combobox-wrapper [role="option"] svg {
        position: absolute !important;
        left: 10px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        width: 16px !important; 
        height: 16px !important;
        color: var(--ef-input-text) !important;
    }
    
    .ef-combobox-wrapper [role="option"]:hover,
    .ef-combobox-wrapper [role="option"][data-headlessui-state~="active"] {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }
    
    /* When option is both selected and active (hovered), use button text color for the checkmark */
    .ef-combobox-wrapper [role="option"][data-headlessui-state~="active"] svg,
    .ef-combobox-wrapper [role="option"]:hover svg {
        color: var(--ef-button-text) !important;
    }
    
    /* Make selected options bold */
    .ef-combobox-wrapper [role="option"][data-headlessui-state~="selected"] {
        font-weight: 600 !important;
    }
    
    /* Style the combobox button triangle consistently with other select boxes */
    .ef-combobox-wrapper .combobox-triangle {
        position: absolute !important;
        right: 1rem !important;
        top: 50% !important;
        transform: translateY(-25%) !important;
        width: 0 !important;
        height: 0 !important;
        border-left: 5px solid transparent !important;
        border-right: 5px solid transparent !important;
        border-top: 5px solid var(--ef-input-text) !important;
        pointer-events: none !important;
    }
    
    .ef-combobox-wrapper [role="option"][data-headlessui-state~="selected"] {
        font-weight: 600 !important;
    }
    
    /* Combobox popover styling */
    .ef-root div[data-radix-popper-content-wrapper] {
        z-index: 9999 !important;
    }
    
    /* Target the popover content directly */
    .ef-root div[data-radix-popper-content-wrapper] > div {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
        border-radius: var(--ef-input-radius) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Command component styling */
    .ef-root div[cmdk-root] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }
    
    .ef-root div[cmdk-input-wrapper] {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
    }
    
    .ef-root input[cmdk-input] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
    }
    
    .ef-root div[cmdk-list] {
        background-color: var(--ef-field-background) !important;
    }
    
    .ef-root div[cmdk-empty] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-placeholder) !important;
    }
    
    .ef-root div[cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
        padding: 0.75rem !important;
    }
    
    .ef-root div[cmdk-item][aria-selected="true"],
    .ef-root div[cmdk-item][data-selected="true"],
    .ef-root div[cmdk-item]:hover {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }

    .ef-submit-wrapper {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        margin-top: 1.5rem;
    }

    .ef-submit-wrapper[data-alignment="left"] {
        justify-content: flex-start;
    }

    .ef-submit {
        background-color: var(--ef-primary);
        color: var(--ef-button-text);
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--ef-button-radius);
        font-weight: 500;
        cursor: pointer;
        transition: opacity 0.2s;
    }

    .ef-submit:hover {
        opacity: 0.9;
    }

    .ef-error {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Add styles for field error container to match ef-error */
    .ef-field-error {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Add styles for fields with errors */
    .ef-field.has-error .ef-input,
    .ef-field.has-error .ef-select,
    .ef-field.has-error .ef-textarea,
    .ef-field.has-error .ef-combobox button {
        border-color: #dc2626;
        background-color: #fef2f2;
    }

    .ef-field.has-error .ef-input:focus,
    .ef-field.has-error .ef-select:focus,
    .ef-field.has-error .ef-textarea:focus,
    .ef-field.has-error .ef-combobox button:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 1px #dc2626;
    }

    /* Handle special case for date range picker in error state */
    .ef-field.has-error .ef-date-range-picker input[type="date"] {
        border-color: #dc2626;
    }

    .ef-field.has-error .ef-date-range-picker input[type="date"]:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 1px #dc2626;
    }

    .ef-success {
        text-align: center;
        padding: 2rem;
        background-color: #f0fdf4;
        border-radius: var(--ef-input-radius);
        color: #166534;
    }

    .ef-checkbox-wrapper {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .ef-checkbox {
        width: 1rem;
        height: 1rem;
        border: 1px solid var(--ef-border);
        border-radius: 0.25rem;
        accent-color: var(--ef-primary);
        margin-top: 0.25rem;
    }

    .ef-checkbox-label {
        font-size: 0.875rem;
        color: var(--ef-text);
        user-select: none;
    }

    .ef-checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    .ef-required-checkbox-inline {
        margin-left: -.25rem;
    }

    .ef-select {
        width: 100%;
        border: 1px solid var(--ef-border);
        border-radius: var(--ef-input-radius);
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        font: inherit;
        cursor: pointer;
        appearance: none;
        padding-right: 2.5rem;
        position: relative;
    }

    .ef-select:focus {
        outline: none;
        border-color: var(--ef-primary);
        box-shadow: 0 0 0 1px var(--ef-primary);
    }

    .ef-select:hover {
        border-color: var(--ef-primary);
    }

    .ef-select-wrapper {
        width: 100%;
        position: relative;
    }

    .ef-select-wrapper::after {
        content: '';
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-25%);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid var(--ef-input-text);
        pointer-events: none;
    }

    /* Custom styles for intl-tel-input */
    .iti {
        width: 100%;
    }

    .iti--tel-input {
        width: 100%;
    }

    .iti__flag-container {
        right: auto;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .iti--allow-dropdown input {
        padding-left: 52px;
    }

    .iti__selected-flag {
        background-color: var(--ef-field-background);
        border-radius: var(--ef-input-radius) 0 0 var(--ef-input-radius);
    }

    .iti__country-list {
        border-radius: var(--ef-input-radius);
        border-color: var(--ef-border);
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        
        /* Custom scrollbar styles for Firefox */
        scrollbar-width: thin;
        scrollbar-color: var(--ef-border, #4a5568) var(--ef-field-background);
    }
    
    /* WebKit scrollbar styles (Chrome, Safari, Edge) */
    .iti__country-list::-webkit-scrollbar {
        width: 8px;
    }
    
    .iti__country-list::-webkit-scrollbar-track {
        background: var(--ef-field-background);
        border-radius: 4px;
    }
    
    .iti__country-list::-webkit-scrollbar-thumb {
        background: var(--ef-border, #4a5568);
        border-radius: 4px;
    }
    
    .iti__country-list::-webkit-scrollbar-thumb:hover {
        background: var(--ef-input-text, #718096);
    }
       .ef-date-range-picker .flex {
        display: flex;
    }

    .iti__country:hover {
        background-color: var(--ef-primary);
        color: var(--ef-button-text);
    }
     .ef-date-range-picker .flex-col {
        flex-direction: column;
    }

    .ef-input[type="date"]:not(:focus):not([value=""]) {
        color: var(--ef-input-text);
    }
        .ef-date-range-picker .flex-wrap {
        flex-wrap: wrap;
    }

    .ef-input[type="date"]:not(:focus):not([value=""])::-webkit-datetime-edit {
        color: var(--ef-input-text);
    }
      .ef-date-range-picker .items-center {
        align-items: center;
    }

    .ef-input[type="date"]:not(:focus)[value=""]::-webkit-datetime-edit {
        color: var(--ef-placeholder);
    }
    .ef-date-range-picker .gap-2 {
        gap: 0.5rem;
    }

    /* Calendar icon color adjustments for all browsers */
    .ef-input[type="date"]::-webkit-calendar-picker-indicator,
    .ef-input[type="date"]::-webkit-inner-spin-button {
        color-scheme: auto;
        opacity: 0.8;
        cursor: pointer;
        filter: var(--ef-calendar-icon-filter);
    }
      .ef-date-range-picker .mt-1 {
        margin-top: 0.25rem;
    }

    .ef-input[type="date"]::-webkit-calendar-picker-indicator:hover,
    .ef-input[type="date"]::-webkit-inner-spin-button:hover {
        opacity: 1;
    }
     .ef-date-range-picker .grid {
        display: grid;
    }

    /* Firefox */
    .ef-input[type="date"] {
        color-scheme: auto;
    }

    /* Date range picker mobile styles */
    .ef-date-range-picker input[type="date"] {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--ef-border);
        border-radius: var(--ef-input-radius);
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        font: inherit;
    }
       .ef-date-range-picker .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
        
    }

    .ef-date-range-picker input[type="date"]:focus {
        outline: none;
        border-color: var(--ef-primary);
        box-shadow: 0 0 0 1px var(--ef-primary);
    }
    .ef-date-range-picker .input-sm {
        padding: 0.4rem 0.5rem;
        height: 2rem;
    }

    .ef-date-range-picker label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--ef-text);
    }

    .ef-date-range-picker .text-muted-foreground {
        color: var(--ef-placeholder);
    }

    /* Mobile responsive layout for date pickers */
    .ef-date-range-picker .grid {
        display: grid;
        grid-gap: 1rem;
        width: 100%;
    }

    /* By default, stack date inputs on small screens */
    .ef-date-range-picker .grid > div {
        width: 100%;
    }

    /* For medium-sized screens and up, show date inputs side by side */
    @media (min-width: 37rem) {
        .ef-date-range-picker .grid {
            grid-template-columns: 1fr 1fr;
        }
    }

    @media (max-width: 37rem) {
        .ef-date-range-picker .flex-wrap {
            width: 100%;
        }
        
        .ef-date-range-picker .flex-wrap > div {
            width: 100%;
        }
    }

    .ef-date-range-picker input,
    .ef-date-range-picker .input,
    .ef-date-range-picker button,
    .ef-date-range-picker [data-element="input"] {
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        border-color: var(--ef-border);
        min-height: 2.5rem;
        line-height: 1.5;
        height: 2.5rem; /* Use 2.5rem instead of 40px to match other inputs */
        box-sizing: border-box;
        padding: 0.75rem; /* Consistent padding with other inputs */
    }
    
    /* Specifically style the date picker button to ensure it matches other inputs */
    .ef-date-range-picker button {
        height: 3rem;
        padding: .75rem;
    }

    /* ComboBox styling */
    .ef-combobox-wrapper {
        width: 100%;
        font-size: 1rem;
        line-height: 1.5;
        position: relative;
    }
    
    /* Ensure ComboBoxInput has sufficient right padding */
    .ef-combobox-wrapper [role="combobox"] input {
        padding-right: 2.5rem !important;
    }
    
    /* Position the button correctly */
    .ef-combobox-wrapper button[data-headlessui-state] {
        position: absolute !important;
        right: 2px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        width: 2.5rem !important;
        height: 80% !important;
        z-index: 2 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .ef-combobox button:focus {
        outline: none;
        border-color: var(--ef-primary);
        box-shadow: 0 0 0 1px var(--ef-primary);
    }
    
    .ef-field.has-error .ef-combobox button {
        border-color: #dc2626;
    }
    
    .ef-field.has-error .ef-combobox button:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 1px #dc2626;
    }
    
    /* Make ComboBox popover use our theme colors */
    [cmdk-root] {
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
    }
    
    [cmdk-input] {
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        font-family: var(--ef-font);
    }
    
    [cmdk-item] {
        color: var(--ef-input-text);
        font-family: var(--ef-font);
    }
    
    [cmdk-item][data-selected="true"] {
        background-color: var(--ef-primary);
        color: var(--ef-button-text);
    }
    
    [cmdk-empty] {
        color: var(--ef-placeholder);
        font-family: var(--ef-font);
    }

    /* Style dropdown menu */
    body [data-radix-popper-content-wrapper] > div {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
        border-radius: var(--ef-input-radius) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
    }

    /* Style command dialog and its components */
    body [cmdk-root],
    body [cmdk-list],
    body [cmdk-empty],
    body [cmdk-input-wrapper],
    body [cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
    }

    /* Style command input */
    body [cmdk-input] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
        border-color: var(--ef-border) !important;
    }

    /* Style command input placeholder */
    body [cmdk-input]::placeholder {
        color: var(--ef-placeholder) !important;
    }

    /* Style command items */
    body [cmdk-item] {
        padding: 0.75rem !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
        font-size: 1rem !important;
    }

    /* Style selected and hovered command items */
    body [cmdk-item][data-selected="true"],
    body [cmdk-item]:hover {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }

    /* Style message for no results */
    body [cmdk-empty] {
        color: var(--ef-placeholder) !important;
        padding: 0.75rem !important;
    }

    /* Make sure popper content inherits our CSS variables */
    [data-radix-popper-content-wrapper] {
        --background: var(--ef-field-background) !important;
        --foreground: var(--ef-input-text) !important;
        --border: var(--ef-border) !important;
        --radius: var(--ef-input-radius) !important;
        --primary: var(--ef-primary) !important;
        --primary-foreground: var(--ef-button-text) !important;
        --accent: var(--ef-primary) !important;
        --accent-foreground: var(--ef-button-text) !important;
        --muted: var(--ef-border) !important;
        --muted-foreground: var(--ef-placeholder) !important;
        --card: var(--ef-field-background) !important;
        --card-foreground: var(--ef-input-text) !important;
    }
    
    /* Ensure all dropdown items have correct styling */
    [data-radix-popper-content-wrapper] [cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }
    
    [data-radix-popper-content-wrapper] [cmdk-item][data-selected="true"],
    [data-radix-popper-content-wrapper] [cmdk-item]:hover {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }
    
    /* Ensure the root CommandDialog has correct background */
    [data-radix-popper-content-wrapper] [cmdk-root] {
        background-color: var(--ef-field-background) !important;
    }

    /* Target command dialog specifically */
    .ef-root div[cmdk-root] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    .ef-root div[cmdk-input-wrapper] {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
    }

    .ef-root input[cmdk-input] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
    }

    .ef-root div[cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        font-family: var(--ef-font) !important;
        padding: 0.75rem !important;
    }

    .ef-root div[cmdk-item][aria-selected="true"],
    .ef-root div[cmdk-item][data-selected="true"],
    .ef-root div[cmdk-item]:hover {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }

    /* Target the popover content */
    .ef-root [data-radix-popper-content-wrapper] {
        background-color: var(--ef-field-background) !important;
    }

    .ef-root [data-radix-popper-content-wrapper] > div {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
        color: var(--ef-input-text) !important;
    }

    /* Override any remaining command styles */
    .ef-root [role="combobox"],
    .ef-root [role="option"] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    /* Style the search input */
    .ef-root [cmdk-input]::placeholder {
        color: var(--ef-placeholder) !important;
    }

    /* Empty state styling */
    .ef-root [cmdk-empty] {
        color: var(--ef-placeholder) !important;
        font-family: var(--ef-font) !important;
    }

    /* Ensure the list container is also themed */
    .ef-root [cmdk-list] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    /* Override any Radix UI specific styles */
    .ef-root [data-radix-collection-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    /* Style Popover and its contents */
    .ef-root [data-radix-popper-content-wrapper] {
        --background: var(--ef-field-background);
        --foreground: var(--ef-input-text);
    }

    .ef-root [data-radix-popper-content-wrapper] > [role="presentation"] {
        background-color: var(--ef-field-background) !important;
        border-color: var(--ef-border) !important;
    }

    .ef-root [data-radix-popper-content-wrapper] [role="dialog"] {
        background-color: var(--ef-field-background) !important;
    }

    /* Style the Command component inside Popover */
    .ef-root [data-radix-popper-content-wrapper] [cmdk-root] {
        background-color: var(--ef-field-background) !important;
    }

    .ef-root [data-radix-popper-content-wrapper] [cmdk-list] {
        background-color: var(--ef-field-background) !important;
    }

    .ef-root [data-radix-popper-content-wrapper] [cmdk-input] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    .ef-root [data-radix-popper-content-wrapper] [cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
    }

    /* Style the search box */
    .ef-root [data-radix-popper-content-wrapper] [cmdk-input-wrapper] {
        background-color: var(--ef-field-background) !important;
        border-bottom: 1px solid var(--ef-border) !important;
    }

    /* Style selected and hover states */
    .ef-root [data-radix-popper-content-wrapper] [cmdk-item][data-selected="true"],
    .ef-root [data-radix-popper-content-wrapper] [cmdk-item]:hover {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }

    /* Style empty state */
    .ef-root [data-radix-popper-content-wrapper] [cmdk-empty] {
        color: var(--ef-placeholder) !important;
    }

    /* Global styles for Popover content - these need to work outside .ef-root */
    :root {
        --ef-field-background: #1f2937;
        --ef-input-text: #ffffff;
        --ef-border: #374151;
        --ef-primary: #2563eb;
        --ef-button-text: #ffffff;
        --ef-placeholder: #9ca3af;
        --ef-input-radius: 0.375rem;
    }

    /* Target Popover content directly */
    [data-radix-popper-content-wrapper] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        border-color: var(--ef-border) !important;
    }

    [data-radix-popper-content-wrapper] > div {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        border-color: var(--ef-border) !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-root] {
        background-color: var(--ef-field-background) !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-input] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        border-color: var(--ef-border) !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-list] {
        background-color: var(--ef-field-background) !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-item] {
        background-color: var(--ef-field-background) !important;
        color: var(--ef-input-text) !important;
        padding: 0.75rem !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-item][data-selected="true"] {
        background-color: var(--ef-primary) !important;
        color: var(--ef-button-text) !important;
    }

    [data-radix-popper-content-wrapper] [cmdk-empty] {
        color: var(--ef-placeholder) !important;
        padding: 0.75rem !important;
    }

    /* More specific selectors to override shadcn Button */
    .ef-root .ef-combobox button[role="combobox"],
    .ef-root .ef-combobox [data-state] button[role="combobox"] {
        width: 100%;
        border: 1px solid var(--ef-border);
        border-radius: var(--ef-input-radius);
        background-color: var(--ef-field-background);
        color: var(--ef-input-text);
        font-size: 1rem;
        min-height: 2.3125rem;
        height: 2.3125rem;
        padding: 0.5rem 0.75rem;
        text-align: left;
        font-family: var(--ef-font);
        display: flex;
        align-items: center;
        box-sizing: border-box;
        line-height: 1.5;
    }

    /* Ensure the arrow in the phone input uses the theme text color */
    .iti__arrow {
        border-top-color: var(--ef-input-text) !important;
    }
    
    /* When dropdown is open, the arrow should point up with the same color */
    .iti__arrow--up {
        border-bottom-color: var(--ef-input-text) !important;
        border-top-color: transparent !important;
    }
    
    .iti__country:hover {
        background-color: var(--ef-primary);
        color: var(--ef-button-text);
    }
`;

/**
 * Gets the test ID for a form field
 */
function getTestId(field: FormField) {
  return `input-${field.type}`;
}

/**
 * Helper function to check if a country has any states/regions
 */
function countryHasStates(countryCode?: string): boolean {
  if (!countryCode) {
    return true; // Default to showing state field
  }

  const states = State.getStatesOfCountry(countryCode);
  return states.length > 0;
}

/**
 * Determines if a field should be shown based on current event type
 */
function shouldShowField(
  field: FormField,
  currentEventType: (typeof eventType.enumValues)[number] | undefined,
  selectedCountry?: string,
): boolean {
  // EVENT_TYPE field should always be visible
  if (field.type === "EVENT_TYPE") return true;

  // Hide STATE field if country has no states
  if (
    field.type === "STATE" &&
    selectedCountry &&
    !countryHasStates(selectedCountry)
  ) {
    return false;
  }

  // If no event type is selected, show all fields
  if (!currentEventType) return true;

  // Hide field if it's configured to be hidden for the current event type
  return !field.hideForEventTypes?.includes(currentEventType);
}

/**
 * Type guard to check if a field is a checkbox field
 */
function isCheckboxField(field: FieldDefinition): field is FieldDefinition & {
  inputType: "CHECKBOX";
  defaultCheckboxText: string;
} {
  return field.inputType === "CHECKBOX";
}

/**
 * Creates a mapping of compound field subfields to their parent fields
 */
function createCompoundFieldMapping(
  fields: FormField[],
): Record<string, string> {
  const mapping: Record<string, string> = {};

  fields.forEach((field) => {
    const fieldDef = PREDEFINED_FIELDS[field.type];
    // Add safety check for Storybook environment
    if (!fieldDef || !fieldDef.baseSchema) {
      return; // Skip this field if no fieldDef or baseSchema
    }

    if (fieldDef.baseSchema.type === "compound") {
      // For each sub-field in the compound field, map it to the parent
      Object.keys(fieldDef.baseSchema.fields).forEach((subFieldKey) => {
        mapping[subFieldKey] = field.type;
      });
    }
  });

  return mapping;
}

/**
 * Calculates which values should be visible based on field visibility rules
 */
function calculateVisibleValues(
  values: FormValues,
  fields: FormField[],
  currentEventType: (typeof eventType.enumValues)[number] | undefined,
  compoundFieldMapping: Record<string, string>,
): FormValues {
  return Object.fromEntries(
    Object.entries(values).filter(([fieldType]) => {
      // First check if this is a direct field
      const field = fields.find((f) => f.type === fieldType);
      if (field) {
        return shouldShowField(field, currentEventType);
      }

      // If not a direct field, check if it's a sub-field of a compound field
      const parentFieldType = compoundFieldMapping[fieldType];
      if (parentFieldType) {
        const parentField = fields.find((f) => f.type === parentFieldType);
        return parentField && shouldShowField(parentField, currentEventType);
      }

      return false;
    }),
  );
}

/**
 * Initializes phone input fields with international phone input library
 */
function initializePhoneInputs(
  fields: FormField[],
  phoneInputRefs: React.MutableRefObject<Record<string, any>>,
  values?: FormValues,
  onChange?: (name: string, value: FormValue) => void,
): () => void {
  // More robust filter for Storybook environment
  const phoneFields = fields.filter((field) => {
    // Skip if field type doesn't exist in PREDEFINED_FIELDS
    if (!PREDEFINED_FIELDS[field.type]) {
      console.warn(
        `Field type ${field.type} not found in PREDEFINED_FIELDS. This may happen in Storybook.`,
      );
      return false;
    }
    return PREDEFINED_FIELDS[field.type].inputType === "TEL";
  });

  const cleanupFns: Array<() => void> = [];

  phoneFields.forEach((field) => {
    const inputEl = phoneInputRefs.current[field.type];
    if (!inputEl) return;

    console.log(`Phone field ${field.type} current DOM value:`, inputEl.value);
    console.log(
      `Phone field ${field.type} current state value:`,
      values?.[field.type],
    );

    // Check if there's already an instance initialized
    if (inputEl.iti) {
      console.log(
        `Phone field ${field.type} already has iti instance, skipping initialization`,
      );
      // If we already have an instance and the input has a value, preserve it
      const currentStateValue = values?.[field.type];
      // Only apply string values (ignore FileData[] and other complex types)
      if (
        typeof currentStateValue === "string" &&
        inputEl.value !== currentStateValue
      ) {
        console.log(
          `Updating phone field ${field.type} DOM value from state:`,
          currentStateValue,
        );
        inputEl.value = currentStateValue;
      }
      return;
    }

    // Check if server passed us an initial phone country (from validation error)
    const phoneCountryValue = values?.["PHONE_COUNTRY"];
    const phoneCountry =
      typeof phoneCountryValue === "string" ? phoneCountryValue : "us";

    // Get current value if any
    const currentValue = values?.[field.type];
    const stringValue = typeof currentValue === "string" ? currentValue : "";

    console.log(
      `Initializing phone field ${field.type} with country:`,
      phoneCountry,
      "and value:",
      stringValue,
    );

    try {
      // Initialize the phone input
      const iti = intlTelInput(inputEl, {
        nationalMode: true,
        initialCountry: phoneCountry,
        formatAsYouType: true,
        autoPlaceholder: "aggressive",
        hiddenInput: (telInputName) => ({
          phone: "PHONE",
          country: "PHONE_COUNTRY",
        }),
        loadUtils: () =>
          import("intl-tel-input/utils").catch((e) => {
            console.error("Failed to load intl-tel-input utils:", e);
            return {} as any; // Return empty object to avoid breaking in Storybook
          }),
      });

      // Store iti instance on the input element for future reference
      inputEl.iti = iti;

      // Create a hidden input for the country code
      const countryInput = document.createElement("input");
      countryInput.type = "hidden";
      countryInput.name = `${field.type}_COUNTRY`;
      countryInput.value = iti.getSelectedCountryData().iso2 || "us";

      const form = inputEl.closest("form");

      // Keep track of the last value to avoid unnecessary updates
      let lastValue = stringValue;
    } catch (e) {
      console.error("Error initializing phone input:", e);
    }
  });

  return () => {
    cleanupFns.forEach((cleanup) => {
      try {
        cleanup();
      } catch (e) {
        console.warn("Error during phone input cleanup:", e);
      }
    });
  };
}

/**
 * Renders form level errors
 */
function renderFormErrors(
  errors: Record<string, string[]>,
): React.JSX.Element | null {
  return errors._form ? (
    <div className="ef-form-error">
      {errors._form.map((error, i) => (
        <div key={i}>{error}</div>
      ))}
    </div>
  ) : null;
}

/**
 * Renders field-level error messages
 */
function renderFieldErrors(errors: string[]): React.JSX.Element | null {
  if (errors.length === 0) {
    return null;
  }

  // Deduplicate error messages
  const uniqueErrors = [...new Set(errors)];

  return (
    <div className="ef-field-error">
      {uniqueErrors.map((error, index) => (
        <div key={index}>{error}</div>
      ))}
    </div>
  );
}

/**
 * Renders the submit button with proper alignment
 */
function renderSubmitButton(buttonAlignment: string): React.JSX.Element {
  return (
    <div className="ef-submit-wrapper" data-alignment={buttonAlignment}>
      <button type="submit" className="ef-submit">
        Submit
      </button>
    </div>
  );
}

function RenderEmbedFormField(
  form: Form,
  handleFieldChange: (name: string, value: FormValue) => void,
  setPhoneInputRef: (
    fieldType: string,
  ) => (el: HTMLInputElement | null) => void,
  visibleValues: FormValues,
  values: FormValues,
  errors: Record<string, string[]> | undefined,
  isMobileGuess: undefined | boolean,
  testForceMobile: boolean = false,
  testForceCalendarOpen: boolean = false,
) {
  return useCallback(
    (field: Form["fields"][number], value?: FormValue) => {
      // Add safety check for Storybook environment
      const fieldDef = PREDEFINED_FIELDS[field.type];
      if (!fieldDef) {
        console.warn(
          `Field type ${field.type} not found in PREDEFINED_FIELDS. This may happen in Storybook.`,
        );
        return null; // Return null for fields that don't have definitions
      }
      const baseSchema = fieldDef.baseSchema;
      const commonProps = {
        className: "ef-input",
        "data-testid": getTestId(field),
        name: field.type,
        id: field.type,
        required: field.required,
        placeholder: field.placeholder === null ? undefined : field.placeholder,
        value:
          value instanceof File ? "" : Array.isArray(value) ? "" : value || "",
        onChange: (
          e: React.ChangeEvent<
            HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
          >,
        ) => {
          handleFieldChange(field.type, e.target.value as FormValue);
        },
        ...(fieldDef.htmlAttrs &&
          Object.fromEntries(
            Object.entries(fieldDef.htmlAttrs)
              .map(([key, value]) => {
                // Convert to proper React casing
                if (key === "autocomplete") return ["autoComplete", value];
                if (key === "pattern") return ["pattern", value];
                if (key === "inputmode") return ["inputMode", value];
                if (key === "maxlength") return ["maxLength", value];
                if (key === "data-mask") return undefined; // Skip data-mask since we're using intl-tel-input
                return [key, value];
              })
              .filter((entry): entry is [string, any] => entry !== undefined),
          )),
      };

      switch (fieldDef.inputType) {
        case "CHECKBOX":
          const isChecked =
            value === "true" ||
            value === "on" ||
            (typeof value === "boolean" && value);
          return (
            <div className="ef-checkbox-wrapper">
              <input
                type="checkbox"
                className="ef-checkbox"
                name={field.type}
                data-testid={getTestId(field)}
                id={field.type}
                required={field.required}
                value="true"
                checked={isChecked}
                onChange={(e) =>
                  handleFieldChange(
                    field.type,
                    e.target.checked ? "true" : "false",
                  )
                }
              />
              <label
                htmlFor={field.type}
                className="ef-checkbox-label"
                data-testid={`checkbox-label-${field.type}`}
              >
                {field.required && (
                  <span
                    className="ef-required ef-required-checkbox-inline"
                    data-testid={`required-indicator-${field.type}`}
                  >
                    *
                  </span>
                )}
                {field.checkboxText ||
                  (isCheckboxField(fieldDef)
                    ? fieldDef.defaultCheckboxText
                    : "")}
              </label>
            </div>
          );

        case "CHECKBOX_GROUP":
          // Ensure we have an array of values
          const selectedValues = Array.isArray(value)
            ? value
            : value
              ? [value]
              : [];

          // Get options based on event type for EVENT_NEEDS field
          let options = fieldDef.options || [];
          if (field.type === "EVENT_NEEDS") {
            const eventType = visibleValues["EVENT_TYPE"] as string | undefined;
            options = getEventNeedsOptions(eventType);
          }

          return (
            <div className="ef-checkbox-group">
              {options.map((option) => (
                <div key={option.value} className="ef-checkbox-wrapper">
                  <input
                    type="checkbox"
                    className="ef-checkbox"
                    data-testid={getTestId(field)}
                    name={field.type}
                    id={`${field.type}-${option.value}`}
                    value={option.value}
                    defaultChecked={selectedValues.includes(option.value)}
                  />
                  <label
                    htmlFor={`${field.type}-${option.value}`}
                    className="ef-checkbox-label"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          );

        case "SELECT":
          // For STATE field, dynamically load options based on selected country
          let selectOptions = fieldDef.options || [];

          if (field.type === "STATE") {
            const selectedCountry = visibleValues["COUNTRY"] as string;
            const hasCountryField = form.fields.some(
              (f) => f.type === "COUNTRY",
            );

            // If there's no COUNTRY field, default to US states
            if (!hasCountryField) {
              selectOptions = PREDEFINED_FIELDS["STATE"].options || [];
            }
            // If there's a COUNTRY field and it's set, show states for that country
            else if (selectedCountry) {
              try {
                const regions = State.getStatesOfCountry(selectedCountry);
                selectOptions = regions.map(
                  (region: { isoCode: string; name: string }) => ({
                    value: region.isoCode,
                    label: region.name,
                  }),
                );
              } catch (error) {
                console.error("Error loading regions:", error);
                // Empty options on error instead of defaulting to US states
                selectOptions = [];
              }
            }
            // If there's a COUNTRY field but it's not set, show empty options
            else {
              selectOptions = [];
            }
          }

          return (
            <div className="ef-select-wrapper">
              <select
                {...commonProps}
                className="ef-select"
                value={
                  value instanceof File
                    ? undefined
                    : Array.isArray(value)
                      ? undefined
                      : value || ""
                }
                onChange={(e) => {
                  // When country changes, clear the state field
                  if (field.type === "COUNTRY") {
                    const previousCountry = values["COUNTRY"];
                    const newCountry = e.target.value;

                    // Only reset if the country has actually changed
                    if (previousCountry !== newCountry && values["STATE"]) {
                      // Clear state value
                      handleFieldChange("STATE", "");
                    }
                  }

                  // Update the changed field
                  handleFieldChange(field.type, e.target.value as FormValue);
                }}
              >
                <option value="" disabled hidden>
                  {commonProps.placeholder}
                </option>
                {selectOptions.map((opt) => (
                  <option key={opt.value} value={opt.value}>
                    {opt.label}
                  </option>
                ))}
              </select>
            </div>
          );

        case "TEXTAREA":
          return <textarea {...commonProps} />;

        case "DATE":
          return (
            <input
              {...commonProps}
              type="date"
              min={new Date().toISOString().split("T")[0]}
            />
          );

        case "NUMBER":
          return (
            <input
              {...commonProps}
              type="number"
              min={
                baseSchema && baseSchema instanceof z.ZodNumber
                  ? (baseSchema.minValue ?? undefined)
                  : undefined
              }
              max={
                baseSchema && baseSchema instanceof z.ZodNumber
                  ? (baseSchema.maxValue ?? undefined)
                  : undefined
              }
              step="1"
            />
          );

        case "CURRENCY":
          return (
            <div className="currency-input-wrapper">
              {/* Hidden input for form submission with numeric value */}
              <input
                type="hidden"
                name={field.type}
                value={commonProps.value || ""}
                data-testid={`hidden-field-${field.type}`}
              />
              {/* Visible input with formatted value */}
              <input
                {...commonProps}
                type="text"
                name={`visible-${field.type}`}
                value={(() => {
                  if (
                    commonProps.value === undefined ||
                    commonProps.value === null ||
                    commonProps.value === ""
                  )
                    return "";
                  const num = parseFloat(commonProps.value);
                  if (isNaN(num)) return "";
                  return `$${new Intl.NumberFormat("en-US").format(num)}`;
                })()}
                onChange={(e) => {
                  // When user types in a currency value, we need to:
                  // 1. Remove $ and commas
                  // 2. Preserve the decimal point
                  // 3. Pass the cleaned value to the handler

                  // Get the raw value from the input
                  const rawValue = e.target.value;

                  // Remove $ and commas, but keep the decimal point
                  // We need to ensure the decimal point is preserved
                  const value = rawValue.replace(/[$,]/g, "");

                  handleFieldChange(field.type, value as FormValue);
                }}
              />
            </div>
          );

        case "TEL":
          // For phone fields, we need to handle the value differently to prevent it from being cleared
          return (
            <input
              className="ef-input"
              data-testid={getTestId(field)}
              name={field.type}
              id={field.type}
              required={field.required}
              placeholder={
                field.placeholder === null ? undefined : field.placeholder
              }
              ref={setPhoneInputRef(field.type)}
              defaultValue={
                value instanceof File
                  ? ""
                  : Array.isArray(value)
                    ? ""
                    : value || ""
              }
              type="tel"
            />
          );

        case "EMAIL":
          return <input {...commonProps} type="email" />;

        case "EVENT_DATE_RANGE": {
          const startDate = visibleValues["START_DATE"]
            ? new Date(visibleValues["START_DATE"] as string)
            : undefined;
          const endDate = visibleValues["END_DATE"]
            ? new Date(visibleValues["END_DATE"] as string)
            : undefined;

          const dateErrors: {
            START_DATE: string[];
            END_DATE: string[];
            _deduplicated?: string[];
          } = {
            START_DATE: errors?.["START_DATE"] || [],
            END_DATE: errors?.["END_DATE"] || [],
          };

          const combinedErrors = [
            ...(errors?.["START_DATE"] || []),
            ...(errors?.["END_DATE"] || []),
          ];
          dateErrors._deduplicated = [...new Set(combinedErrors)];

          // Create a handler for date range changes that updates both START_DATE and END_DATE
          const handleDateRangeChange = (
            newStartDate: Date | undefined,
            newEndDate: Date | undefined,
          ) => {
            // Format dates as ISO strings or empty strings if undefined
            const startDateValue = newStartDate
              ? newStartDate.toISOString().split("T")[0]
              : "";
            const endDateValue = newEndDate
              ? newEndDate.toISOString().split("T")[0]
              : "";

            // Update both the START_DATE and END_DATE fields
            if (startDateValue) {
              handleFieldChange("START_DATE", startDateValue as FormValue);
            }
            if (endDateValue) {
              handleFieldChange("END_DATE", endDateValue as FormValue);
            }
          };

          return (
            <div className="ef-field" data-testid={getTestId(field)}>
              <DateRangePicker
                initialDateRange={[startDate, endDate]}
                data-testid={`date-range-picker-${field.type}`}
                required={field.required}
                startLabel="Start Date"
                endLabel="End Date"
                className="ef-date-range-picker"
                errors={dateErrors}
                isMobileGuess={isMobileGuess}
                testModeForceMobile={testForceMobile}
                testModeForceOpen={testForceCalendarOpen}
                onChange={handleDateRangeChange}
                popoverId={`form-${form.id}-date-popover`}
              />
            </div>
          );
        }

        case "COMBO_BOX":
          // Get options - for ComboBox, we use the same options as for SELECT
          let comboBoxOptions = fieldDef.options || [];

          // Special case for STATE field (same as in the SELECT case)
          if (field.type === "STATE") {
            const selectedCountry = visibleValues["COUNTRY"] as string;
            const hasCountryField = form.fields.some(
              (f) => f.type === "COUNTRY",
            );

            // If there's no COUNTRY field, default to US states
            if (!hasCountryField) {
              comboBoxOptions = PREDEFINED_FIELDS["STATE"].options || [];
            }
            // If there's a COUNTRY field and it's set, show states for that country
            else if (selectedCountry) {
              try {
                const regions = State.getStatesOfCountry(selectedCountry);
                comboBoxOptions = regions.map(
                  (region: { isoCode: string; name: string }) => ({
                    value: region.isoCode,
                    label: region.name,
                  }),
                );
              } catch (error) {
                console.error("Error loading regions:", error);
                // Empty options on error instead of defaulting to US states
                comboBoxOptions = [];
              }
            }
            // If there's a COUNTRY field but it's not set, show empty options
            else {
              comboBoxOptions = [];
            }
          }

          // Format the options for Combobox which expects { id, name } structure
          const formattedOptions = comboBoxOptions.map((option) => ({
            id: option.value,
            name: option.label,
          }));

          // Get the current selected option formatted for Combobox
          const selectedValue =
            value instanceof File
              ? ""
              : Array.isArray(value)
                ? ""
                : value || "";
          const selectedOption = selectedValue
            ? formattedOptions.find(
                (option) => String(option.id) === String(selectedValue),
              ) || null
            : null;

          console.log(`ComboBox field ${field.type}:`, {
            selectedValue,
            selectedOption,
            formattedOptions,
            "value prop": value,
          });

          // Create a hidden input for form submission
          return (
            <div>
              <input
                type="hidden"
                name={field.type}
                value={selectedValue}
                data-testid={`hidden-field-${field.type}`}
              />
              <div className="ef-combobox-wrapper">
                <ComboboxField
                  options={formattedOptions}
                  value={selectedOption}
                  onChange={(option: ComboboxOption | null) => {
                    const newValue = option?.id?.toString() || "";
                    console.log(`ComboBox onChange (${field.type}):`, {
                      option,
                      newValue,
                    });

                    // Same logic as in SELECT case for country-state relationship
                    if (field.type === "COUNTRY") {
                      const previousCountry = values["COUNTRY"];
                      const newCountry = newValue;

                      // Only reset if the country has actually changed
                      if (previousCountry !== newCountry && values["STATE"]) {
                        // Clear state value
                        handleFieldChange("STATE", "");
                      }
                    }

                    // Update the field value
                    handleFieldChange(field.type, newValue as FormValue);
                  }}
                  placeholder={commonProps.placeholder}
                  id={field.type}
                  name={`visible-${field.type}`}
                  required={field.required}
                  data-testid={getTestId(field)}
                  className="ef-combobox"
                />
              </div>
            </div>
          );

        case "FILE_UPLOAD":
          // Safely check if value is a FileData array
          const isFileDataArray = (val: any): val is FileData[] =>
            Array.isArray(val) &&
            val.length > 0 &&
            typeof val[0] === "object" &&
            "fileId" in val[0];

          // Convert value to FileData array
          const fileData: FileData[] = isFileDataArray(value) ? value : [];

          const acceptTypes = fieldDef.htmlAttrs?.accept?.split(",").reduce(
            (acc, type) => {
              // Convert .ext to MIME type
              const mimeType = type.startsWith(".")
                ? {
                    ".pdf": "application/pdf",
                    ".doc": "application/msword",
                    ".docx":
                      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    ".xls": "application/vnd.ms-excel",
                    ".xlsx":
                      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    ".ppt": "application/vnd.ms-powerpoint",
                    ".pptx":
                      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    ".txt": "text/plain",
                    ".jpg": "image/jpeg",
                    ".jpeg": "image/jpeg",
                    ".png": "image/png",
                  }[type]
                : type;

              if (mimeType) {
                acc[mimeType] = [type];
              }
              return acc;
            },
            {} as Record<string, string[]>,
          );

          // Create a callback function for handling file changes
          const handleFileChange = (files: FileData[]) => {
            handleFieldChange(field.type, files);
          };

          return (
            <FileUploadField
              name={field.type}
              label={field.label ?? fieldDef.defaultLabel ?? ""}
              required={field.required}
              value={fileData}
              onChange={handleFileChange}
              error={errors?.[field.type]?.[0] ?? null}
              accept={acceptTypes}
              maxFiles={1} // setting maxFiles to 1 is important for properties with STS sync configured; STS only supports uploading one file
              maxSize={1024 * 1024 * 5}
              data-testid={getTestId(field)}
              formId={form.id}
            />
          );

        default:
          return <input {...commonProps} type="text" />;
      }
    },
    [
      handleFieldChange,
      visibleValues,
      errors,
      isMobileGuess,
      testForceMobile,
      testForceCalendarOpen,
    ],
  );
}

export function EmbeddedForm({
  form,
  errors = {},
  values = {},
  onChange,
  success = false,
  isMobileGuess = false,
  testForceMobile = false,
  testForceCalendarOpen = false,
}: Props) {
  // References
  const formRef = useRef<HTMLFormElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const phoneInputRefs = useRef<Record<string, any>>({});

  // Get the current event type from values
  const currentEventType = values["EVENT_TYPE"] as
    | (typeof eventType.enumValues)[number]
    | undefined;

  // Get selected country
  const selectedCountry = values["COUNTRY"] as string | undefined;

  // Create compound field mapping
  const compoundFieldMapping = useMemo(
    () => createCompoundFieldMapping(form.fields),
    [form.fields],
  );

  // Calculate visible values
  const visibleValues = useMemo(() => {
    return calculateVisibleValues(
      values,
      form.fields,
      currentEventType,
      compoundFieldMapping,
    );
  }, [values, form.fields, currentEventType, compoundFieldMapping]);

  // Get visible fields
  const visibleFields = useMemo(() => {
    // Filter fields by visibility
    const fields = form.fields.filter((field) =>
      shouldShowField(field, currentEventType, selectedCountry),
    );

    // Sort fields by order
    return [...fields].sort((a, b) => a.order - b.order);
  }, [form.fields, currentEventType, selectedCountry]);

  // Handle field changes
  const handleFieldChange = (name: string, value: FormValue) => {
    console.log(`handleFieldChange(${name}, ${value})`);
    onChange?.(name, value);
  };

  // Create a ref callback for phone inputs
  const setPhoneInputRef = useCallback(
    (fieldType: string) => (el: HTMLInputElement | null) => {
      phoneInputRefs.current[fieldType] = el;
    },
    [],
  );

  // Initialize phone inputs when component mounts or when values change
  useEffect(() => {
    const cleanup = initializePhoneInputs(
      form.fields,
      phoneInputRefs,
      values,
      onChange,
    );
    return cleanup;
  }, [form.fields, values, onChange]);

  // Create field renderer
  const renderFieldContent = RenderEmbedFormField(
    form,
    handleFieldChange,
    setPhoneInputRef,
    visibleValues,
    values,
    errors,
    isMobileGuess,
    testForceMobile,
    testForceCalendarOpen,
  );

  // Force re-render of STATE field when COUNTRY changes
  const [countryChangeFlag, setCountryChangeFlag] = useState(0);
  const previousCountry = useRef(values["COUNTRY"] as string | undefined);

  useEffect(() => {
    // When country changes, trigger re-render of STATE options
    const currentCountry = values["COUNTRY"] as string | undefined;
    if (previousCountry.current !== currentCountry) {
      previousCountry.current = currentCountry;
      setCountryChangeFlag((prev) => prev + 1);
    }
  }, [values["COUNTRY"]]);

  // Inside the EmbeddedForm component, add this useEffect
  // This will set CSS variables at the :root level to match the current form theme
  useEffect(() => {
    // Apply the form theme variables to the document root
    document.documentElement.style.setProperty(
      "--ef-field-background",
      form.theme.fieldBackgroundColor,
    );
    document.documentElement.style.setProperty(
      "--ef-input-text",
      form.theme.inputTextColor,
    );
    document.documentElement.style.setProperty(
      "--ef-border",
      form.theme.borderColor,
    );
    document.documentElement.style.setProperty(
      "--ef-primary",
      form.theme.primaryColor,
    );
    document.documentElement.style.setProperty(
      "--ef-button-text",
      form.theme.buttonTextColor,
    );
    document.documentElement.style.setProperty(
      "--ef-placeholder",
      form.theme.placeholderColor,
    );
    document.documentElement.style.setProperty(
      "--ef-input-radius",
      `${form.theme.formBorderRadius}px`,
    );

    // Clean up when component unmounts
    return () => {
      // Reset the variables to default values when form is unmounted
      document.documentElement.style.removeProperty("--ef-field-background");
      document.documentElement.style.removeProperty("--ef-input-text");
      document.documentElement.style.removeProperty("--ef-border");
      document.documentElement.style.removeProperty("--ef-primary");
      document.documentElement.style.removeProperty("--ef-button-text");
      document.documentElement.style.removeProperty("--ef-placeholder");
      document.documentElement.style.removeProperty("--ef-input-radius");
    };
  }, [form.theme]); // Re-run when theme changes

  // Return success message if form was submitted
  if (success) {
    return (
      <>
        <style>{baseStyles}</style>
        <div className="ef-root" style={generateThemeStyles(form.theme)}>
          <FormSuccess
            form={form}
            successMessage="Thank you for your submission! A member of our team will reach out to you soon."
            className="ef-success-container"
          />
        </div>
      </>
    );
  }

  return (
    <div
      ref={containerRef}
      data-testid="form-container"
      className="ef-root"
      style={generateThemeStyles(form.theme)}
    >
      <style>{baseStyles}</style>
      <form
        ref={formRef}
        className="ef-form"
        method="post"
        action={`/forms/${form.id}/submission`}
      >
        {renderFormErrors(errors)}

        {visibleFields.map((field, index) => {
          const fieldErrors = errors[field.type] || [];
          const hasError = fieldErrors.length > 0;
          const value = visibleValues[field.type];

          // Determine if this field should force a row break
          const shouldBreakRow =
            field.rowBreakAfter === true || field.width === "full";

          // Add key with country change flag for STATE field to force re-render
          const fieldKey =
            field.type === "STATE"
              ? `${field.id}-${countryChangeFlag}`
              : field.id;

          return (
            <div
              key={fieldKey}
              className={`ef-field ${hasError ? "has-error" : ""}`}
              data-width={field.width}
              data-row-break-after={shouldBreakRow}
              data-testid={`field-${field.type}`}
            >
              <label
                htmlFor={field.type}
                data-testid={`label-${field.type}`}
                className="ef-label"
              >
                {field.label && field.required && (
                  <span className="ef-required">*</span>
                )}
                {field.label ?? PREDEFINED_FIELDS[field.type].defaultLabel}
              </label>
              {renderFieldContent(field, value)}
              {renderFieldErrors(fieldErrors)}
            </div>
          );
        })}

        {renderSubmitButton(form.theme.buttonAlignment)}
      </form>
    </div>
  );
}
