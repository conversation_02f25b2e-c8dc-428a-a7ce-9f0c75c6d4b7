import React, { useEffect, useState, useCallback } from "react";
import { Form } from "@/modules/forms/builder/types";
import { EmbeddedForm } from "./EmbeddedForm";
import FormSuccess from "./FormSuccess";

// Check if we're in a browser environment
const isBrowser =
  typeof window !== "undefined" && typeof document !== "undefined";

// Add a type declaration for iframe-resizer's parentIFrame global (using more generic approach to avoid conflicts)
interface IFrameResizerChildAPI {
  sendMessage: (message: any, targetOrigin?: string) => void;
  close: () => void;
  // Other methods that might be needed
}

interface FormPageProps {
  form: Form;
  success: boolean;
  errors?: Record<string, string[]>;
  values?: Record<string, string | string[] | File>;
  isMobileGuess: boolean;
}

// Create a reusable debug logger that prepends a tag
const debug = {
  log: (tag: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.log(`[SIV:${tag}]`, ...processedArgs);
    }
  },
  error: (tag: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.error(`[SIV:${tag}]`, ...processedArgs);
    }
  },
  warn: (tag: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.warn(`[SIV:${tag}]`, ...processedArgs);
    }
  },
  important: (tag: string, ...args: any[]) => {
    if (isBrowser) {
      // Stringify any objects for better visibility
      const processedArgs = args.map((arg) =>
        typeof arg === "object" && arg !== null
          ? JSON.stringify(arg, null, 2)
          : arg,
      );
      console.log(
        `%c[SIV:${tag}]`,
        "background: #ff0; color: #000; font-weight: bold;",
        ...processedArgs,
      );
    }
  },
};

// Load the iframe-resizer script directly
const loadIframeResizerScriptDirectly = (): Promise<void> => {
  if (!isBrowser) return Promise.resolve();

  return new Promise((resolve, reject) => {
    // Skip if already loaded
    if (document.querySelector("script[data-siv-iframe-resizer]")) {
      debug.log("IframeResizer", "Script already in DOM, skipping load");
      resolve();
      return;
    }

    debug.log("IframeResizer", "Adding script tag to document");
    const script = document.createElement("script");
    script.setAttribute("data-siv-iframe-resizer", "true");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.2/iframeResizer.contentWindow.min.js";
    script.onload = () => {
      debug.log("IframeResizer", "Script loaded successfully");
      resolve();
    };
    script.onerror = (err) => {
      debug.error("IframeResizer", "Failed to load script", err);
      reject(err);
    };
    document.head.appendChild(script);
  });
};

// Simple function to notify parent about redirect
function notifyParentToRedirect(url: string): void {
  if (!isBrowser || !url) return;

  debug.important(
    "Redirect",
    `🚨 Attempting to notify parent to redirect to: ${url}`,
  );

  // Don't redirect the iframe itself! Only notify the parent
  try {
    // Method 1: Use iframe-resizer's message channel if available
    if (
      "parentIFrame" in window &&
      typeof (window as any).parentIFrame?.sendMessage === "function"
    ) {
      const message = { type: "redirect", url: url };
      debug.log("Redirect", `Using parentIFrame.sendMessage() with:`, message);
      (window as any).parentIFrame.sendMessage(message);
      debug.log("Redirect", "parentIFrame.sendMessage() call completed");
    } else {
      debug.warn(
        "Redirect",
        "parentIFrame not available or sendMessage not a function",
      );
    }

    // Method 2: Always use postMessage as most reliable cross-origin method
    const postMessage = { type: "redirect", url: url };
    debug.log(
      "Redirect",
      `Using window.parent.postMessage() with:`,
      postMessage,
    );
    window.parent.postMessage(postMessage, "*");
    debug.log("Redirect", "window.parent.postMessage() call completed");

    debug.log("Redirect", "✅ Redirect messages sent to parent");
  } catch (error) {
    debug.error("Redirect", "Error notifying parent:", error);
  }
}

/**
 * Try different methods to safely redirect
 */
function performSafeRedirect(redirectUrl: string, isIframe: boolean): void {
  if (!isBrowser || !redirectUrl) return;

  console.log("Redirect", `⚡ Attempting to redirect to: ${redirectUrl}`);
  console.log("Redirect", `Is in iframe: ${isIframe}`);

  // First notify the parent page about the redirect
  notifyParentToRedirect(redirectUrl);

  // Try parentIFrame method if available
  if (
    isIframe &&
    "parentIFrame" in window &&
    typeof (window as any).parentIFrame?.sendMessage === "function"
  ) {
    console.log("Redirect", "Using parentIFrame.sendMessage()");
    try {
      (window as any).parentIFrame.sendMessage({
        type: "redirect",
        url: redirectUrl,
      });
    } catch (error) {
      debug.error("Redirect", "Failed to use parentIFrame for redirect", error);
    }
  }

  // Try standard postMessage as fallback
  if (isIframe && window !== window.parent) {
    debug.log("Redirect", "Using window.parent.postMessage()");
    try {
      window.parent.postMessage(
        {
          type: "siv:form:redirect",
          redirectUrl,
        },
        "*",
      );

      // Also send a more standardized format
      window.parent.postMessage(
        {
          type: "redirect",
          url: redirectUrl,
        },
        "*",
      );
    } catch (error) {
      debug.error("Redirect", "Failed to send postMessage for redirect", error);
    }
  }

  // Direct navigation as final fallback (will work if same origin)
  setTimeout(() => {
    debug.log("Redirect", "Using direct window.location.href navigation");
    try {
      window.location.href = redirectUrl;
    } catch (error) {
      debug.error(
        "Redirect",
        "Failed to redirect via direct navigation",
        error,
      );
    }
  }, 100);
}

const FormPage: React.FC<FormPageProps> = ({
  form,
  errors,
  success,
  values: initialValues = {},
  isMobileGuess,
}) => {
  const [values, setValues] =
    useState<Record<string, string | string[] | File>>(initialValues);
  const [iframeResizerInitAttempted, setIframeResizerInitAttempted] =
    useState(false);
  const [iframeResizerLoaded, setIframeResizerLoaded] = useState(false);

  // Initialize iframe-resizer if we're in an iframe
  useEffect(() => {
    if (!isBrowser || iframeResizerInitAttempted) return;

    // Check if the page is loaded in an iframe
    const isInIframe = window !== window.parent;

    if (isInIframe) {
      debug.log(
        "IframeResizer",
        "We are in an iframe, initializing iframe-resizer",
      );
      setIframeResizerInitAttempted(true);

      // Load iframe-resizer script
      loadIframeResizerScriptDirectly().then(() => {
        debug.log("IframeResizer", "iframe-resizer script loaded");

        // Give it a moment to initialize
        setTimeout(() => {
          if ("parentIFrame" in window) {
            debug.log("IframeResizer", "parentIFrame is now available", {
              methods: Object.keys((window as any).parentIFrame || {}),
            });
            setIframeResizerLoaded(true);
          } else {
            debug.warn(
              "IframeResizer",
              "WARNING: parentIFrame NOT available after loading library",
            );

            // Last attempt - sometimes it takes a moment to initialize
            setTimeout(() => {
              if ("parentIFrame" in window) {
                debug.log(
                  "IframeResizer",
                  "SUCCESS on final attempt! parentIFrame is now available",
                );
                setIframeResizerLoaded(true);
              } else {
                debug.error(
                  "IframeResizer",
                  "FAILED: parentIFrame still not available after multiple attempts",
                );
              }
            }, 200);
          }
        }, 100);
      });
    } else if (!isInIframe) {
      debug.log(
        "IframeResizer",
        "Not in an iframe, skipping iframe-resizer initialization",
      );
    }
  }, [iframeResizerInitAttempted]);

  // Log initial mount
  useEffect(() => {
    if (!isBrowser) return;

    const isInIframe = window !== window.parent;

    debug.log("FormPage", "Component mounted", {
      isInIframe,
      success,
      hasRedirectUrl: !!form.redirectUrl,
      redirectUrl: form.redirectUrl,
    });

    // Log when component unmounts
    return () => {
      debug.log("FormPage", "Component unmounting");
    };
  }, [success, form.redirectUrl]);

  // Log when success or redirectUrl changes
  useEffect(() => {
    if (!isBrowser) return;

    debug.log("FormPage", "Props updated", {
      success,
      redirectUrl: form.redirectUrl,
    });
  }, [success, form.redirectUrl]);

  const handleChange = useCallback(
    (name: string, value: string | string[] | File) => {
      setValues((prev) => ({ ...prev, [name]: value }));
    },
    [],
  );

  // Handle incoming messages from parent window
  useEffect(() => {
    if (!isBrowser) return;

    const handleMessage = (event: MessageEvent) => {};

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  return (
    <div className={`p-4 ${isMobileGuess ? "max-w-lg mx-auto" : ""}`}>
      {success && form.redirectUrl ? (
        <FormSuccess form={form} className="mt-4" />
      ) : (
        <EmbeddedForm
          form={form}
          errors={errors}
          values={values}
          success={success && !form.redirectUrl}
          onChange={handleChange}
          isMobileGuess={isMobileGuess}
        />
      )}
    </div>
  );
};

export default FormPage;
