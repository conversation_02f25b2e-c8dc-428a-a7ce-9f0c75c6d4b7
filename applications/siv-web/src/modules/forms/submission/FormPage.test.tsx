import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import { vi, beforeEach, afterEach, describe, it, expect } from "vitest";
import FormPage from "./FormPage";
import { formFactory } from "@/modules/admin/forms/form-test-factories";
import userEvent from "@testing-library/user-event";
import { Form } from "@/modules/forms/builder/types";
import { DEFAULT_THEME } from "@/modules/forms/builder/ThemeEditor";
import { createTestField } from "@/modules/admin/forms/new-form-test-factories";

// Create a mock module with a spy we can track
const mockModule = {
  iframeResizer: vi.fn(),
};

// Mock window.parentIFrame
const mockParentIFrame = {
  sendMessage: vi.fn(),
  close: vi.fn(),
};

// Mock the dynamic import
vi.mock("@iframe-resizer/child", () => mockModule);

// Mock document.createElement to intercept script creation
const originalCreateElement = document.createElement;

// Create a basic form for testing
const createTestForm = (redirectUrl: string | null = null): Form => ({
  id: "test-id",
  name: "Test Form",
  description: "Test Description",
  propertyId: "test-property-id",
  themeId: "test-theme-id",
  theme: {
    id: "test-theme-id",
    name: "Test Theme",
    primaryColor: DEFAULT_THEME.primaryColor,
    backgroundColor: DEFAULT_THEME.backgroundColor,
    textColor: DEFAULT_THEME.textColor,
    borderColor: DEFAULT_THEME.borderColor,
    inputBorderRadius: DEFAULT_THEME.inputBorderRadius,
    buttonBorderRadius: DEFAULT_THEME.buttonBorderRadius,
    padding: DEFAULT_THEME.padding,
    buttonTextColor: DEFAULT_THEME.buttonTextColor,
    buttonAlignment: DEFAULT_THEME.buttonAlignment,
    font: DEFAULT_THEME.font,
    fieldBackgroundColor: DEFAULT_THEME.fieldBackgroundColor,
    inputTextColor: DEFAULT_THEME.inputTextColor,
    formBorderRadius: DEFAULT_THEME.formBorderRadius,
    placeholderColor: DEFAULT_THEME.placeholderColor,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  fields: [],
  redirectUrl,
  allowedDomains: [],
  createdAt: new Date(),
  updatedAt: new Date(),
});

describe("FormPage", () => {
  const originalParent = window.parent;
  const originalTop = window.top;
  const originalLocation = window.location;
  const originalCreateElement = document.createElement;
  const originalDispatchEvent = document.dispatchEvent;
  const mockPostMessage = vi.fn();
  let locationHref = "";

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock createElement to handle script creation
    document.createElement = vi.fn().mockImplementation((tagName: string) => {
      const element = originalCreateElement.call(document, tagName);
      if (tagName === "script") {
        // Mock the script onload behavior
        setTimeout(() => {
          if (element.onload) {
            // When the script loads, add the parentIFrame object to window
            (window as any).parentIFrame = mockParentIFrame;
            element.onload(new Event("load"));
          }
        }, 0);
      }
      return element;
    });

    // Create a completely new mock location object
    locationHref = "";
    const mockLocation = {
      ...originalLocation,
      get href() {
        return locationHref;
      },
      set href(value) {
        locationHref = value;
      },
    };

    // Replace the entire window.location object
    Object.defineProperty(window, "location", {
      value: mockLocation,
      configurable: true,
      writable: true,
    });

    // Mock document.dispatchEvent
    document.dispatchEvent = vi.fn().mockImplementation(originalDispatchEvent);
  });

  afterEach(() => {
    // Restore original window.parent
    Object.defineProperty(window, "parent", {
      value: originalParent,
      configurable: true,
      writable: true,
    });

    // Restore original window.top
    Object.defineProperty(window, "top", {
      value: originalTop,
      configurable: true,
      writable: true,
    });

    // Restore original location
    Object.defineProperty(window, "location", {
      value: originalLocation,
      configurable: true,
    });

    // Restore original createElement
    document.createElement = originalCreateElement;

    // Restore original dispatchEvent
    document.dispatchEvent = originalDispatchEvent;

    // Cleanup parentIFrame - safely handle non-configurable properties
    if ("parentIFrame" in window) {
      try {
        delete (window as any).parentIFrame;
      } catch (e) {
        // If we can't delete the property, set it to undefined
        (window as any).parentIFrame = undefined;
      }
    }

    vi.restoreAllMocks();
  });

  it("should dynamically import iframe-resizer when in an iframe", async () => {
    // Mock being in an iframe by making window.parent a different object
    const mockParent = { postMessage: mockPostMessage };
    Object.defineProperty(window, "parent", {
      value: mockParent,
      configurable: true,
      writable: true,
    });

    // Set parentIFrame before test
    (window as any).parentIFrame = mockParentIFrame;

    const testForm = formFactory.build();
    render(<FormPage isMobileGuess={false} form={testForm} success={false} />);

    // Wait for any pending promises to resolve
    await vi.dynamicImportSettled();

    // The module should have been imported
    expect(vi.isMockFunction(mockModule.iframeResizer)).toBe(true);

    // parentIFrame should be available as we mocked it
    expect("parentIFrame" in window).toBe(true);
  });

  it("should not import iframe-resizer when not in an iframe", async () => {
    // For not being in an iframe, window.parent should reference window itself
    Object.defineProperty(window, "parent", {
      value: window,
      configurable: true,
      writable: true,
    });

    const testForm = formFactory.build();
    render(<FormPage isMobileGuess={false} form={testForm} success={false} />);

    // Wait for any pending promises to resolve
    await vi.dynamicImportSettled();

    // The module should not have been imported
    expect(mockModule.iframeResizer).not.toHaveBeenCalled();
  });

  it("should display success message when success is true and no redirect URL is present", async () => {
    const testForm = createTestForm(null);
    render(<FormPage isMobileGuess={false} form={testForm} success={true} />);

    // Success message should be displayed
    expect(
      await screen.findByText(/Thank you for your submission/i),
    ).toBeInTheDocument();

    // There should be no redirect happening
    expect(locationHref).toBe("");
  });

  it("should redirect when success is true and a redirect URL is present", async () => {
    // Configure as not in an iframe first
    Object.defineProperty(window, "parent", {
      value: window,
      configurable: true,
      writable: true,
    });

    const redirectUrl = "https://example.com/thank-you";
    const testForm = createTestForm(redirectUrl);

    render(<FormPage isMobileGuess={false} form={testForm} success={true} />);

    // Wait for any pending promises to resolve
    await vi.dynamicImportSettled();

    // Location should be updated to the redirect URL
    await waitFor(() => {
      expect(locationHref).toBe(redirectUrl);
    });

    // Success message should not be displayed
    expect(
      screen.queryByText(/Thank you for your submission/i),
    ).not.toBeInTheDocument();
  });

  it("should redirect in an iframe by sending a message to the parent", async () => {
    // Save original window references
    const originalWindowSelf = window.self;
    const originalWindowTop = window.top;
    const originalWindowParent = window.parent;

    // Mock being in an iframe
    Object.defineProperty(window, "self", {
      value: {},
      writable: true,
      configurable: true,
    });

    Object.defineProperty(window, "top", {
      value: { someProperty: "top window" },
      writable: true,
      configurable: true,
    });

    Object.defineProperty(window, "parent", {
      value: { postMessage: vi.fn() },
      writable: true,
      configurable: true,
    });

    // Set up parentIFrame mock
    const mockSendMessage = vi.fn();
    (window as any).parentIFrame = {
      sendMessage: mockSendMessage,
    };

    const redirectUrl = "https://example.com/thank-you";
    const form = createTestForm(redirectUrl);

    // Render with success=true to simulate successful submission and redirect
    const { getByTestId } = render(
      <FormPage form={form} isMobileGuess={false} success={true} />,
    );

    // Verify FormSuccess component is rendered with redirecting message
    await waitFor(() => {
      expect(getByTestId("form-redirecting")).toBeInTheDocument();
      expect(getByTestId("redirecting-message")).toHaveTextContent(
        "Redirecting you now...",
      );
    });

    // Verify the redirect message was sent
    await waitFor(
      () => {
        expect(mockSendMessage).toHaveBeenCalledWith(
          { type: "redirect", url: redirectUrl },
          "*",
        );
      },
      { timeout: 3000 },
    );

    // Clean up - restore original values
    Object.defineProperty(window, "self", {
      value: originalWindowSelf,
      writable: true,
      configurable: true,
    });

    Object.defineProperty(window, "top", {
      value: originalWindowTop,
      writable: true,
      configurable: true,
    });

    Object.defineProperty(window, "parent", {
      value: originalWindowParent,
      writable: true,
      configurable: true,
    });

    try {
      delete (window as any).parentIFrame;
    } catch (e) {
      (window as any).parentIFrame = undefined;
    }
  });

  describe("Field visibility and state", () => {
    it("should maintain form state and field visibility based on event type", async () => {
      const user = userEvent.setup();
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
          createTestField({
            type: "ROOM_COUNT",
            label: "Number of Rooms",
            required: true,
            width: "full",
            order: 2,
            hideForEventTypes: ["wedding"],
          }),
        ],
      });

      // Render with initial event type
      render(
        <FormPage
          form={form}
          values={{ EVENT_TYPE: "wedding" }}
          success={false}
          isMobileGuess={false}
        />,
      );

      // Initially wedding fields should be visible
      await waitFor(
        () => {
          expect(screen.getByTestId("input-EVENT_TYPE")).toBeInTheDocument();
          expect(screen.getByTestId("input-GUEST_COUNT")).toBeInTheDocument();
          expect(
            screen.queryByTestId("input-ROOM_COUNT"),
          ).not.toBeInTheDocument();
        },
        { timeout: 100 },
      );

      // Change to corporate meeting
      const eventTypeSelect = screen.getByTestId("input-EVENT_TYPE");
      await user.selectOptions(eventTypeSelect, "corporate_meeting");

      // Corporate meeting fields should be visible
      await waitFor(
        () => {
          expect(
            screen.queryByTestId("input-GUEST_COUNT"),
          ).not.toBeInTheDocument();
          expect(screen.getByTestId("input-ROOM_COUNT")).toBeInTheDocument();
        },
        { timeout: 100 },
      );
    });

    it("should maintain values for visible fields while clearing hidden field values", async () => {
      const user = userEvent.setup();
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
          createTestField({
            type: "ROOM_COUNT",
            label: "Number of Rooms",
            required: true,
            width: "full",
            order: 2,
            hideForEventTypes: ["wedding"],
          }),
        ],
      });

      // Render with initial values including event type
      render(
        <FormPage
          form={form}
          values={{
            EVENT_TYPE: "wedding",
            GUEST_COUNT: "100",
            ROOM_COUNT: "50",
          }}
          success={false}
          isMobileGuess={false}
        />,
      );

      // Initially wedding fields should be visible with values
      await waitFor(
        () => {
          const guestInput = screen.getByTestId(
            "input-GUEST_COUNT",
          ) as HTMLInputElement;
          expect(guestInput.value).toBe("100");
          expect(
            screen.queryByTestId("input-ROOM_COUNT"),
          ).not.toBeInTheDocument();
        },
        { timeout: 100 },
      );

      // Change to corporate meeting
      const eventTypeSelect = screen.getByTestId("input-EVENT_TYPE");
      await user.selectOptions(eventTypeSelect, "corporate_meeting");

      // Corporate meeting fields should be visible with values
      await waitFor(
        () => {
          const roomCount = screen.getByTestId(
            "input-ROOM_COUNT",
          ) as HTMLInputElement;
          expect(
            screen.queryByTestId("input-GUEST_COUNT"),
          ).not.toBeInTheDocument();
          expect(roomCount.value).toBe("50");
        },
        { timeout: 100 },
      );
    });
  });

  describe("Redirect functionality", () => {
    let locationHref = "";
    let originalLocation: Location;

    beforeEach(() => {
      // Save original location
      originalLocation = window.location;
      locationHref = "";

      // Create a completely new mock location object
      const mockLocation = {
        ...originalLocation,
        // Add a getter/setter for href
        get href() {
          return locationHref;
        },
        set href(value) {
          locationHref = value;
        },
      };

      // Replace the entire window.location object
      Object.defineProperty(window, "location", {
        value: mockLocation,
        writable: true,
        configurable: true,
      });

      // Mock window.parent to simulate not being in an iframe
      Object.defineProperty(window, "parent", {
        value: window,
        writable: true,
      });

      // Spy on document.dispatchEvent
      vi.spyOn(document, "dispatchEvent").mockImplementation((event) => {
        return true; // Default to event not being canceled
      });
    });

    afterEach(() => {
      // Restore original location
      Object.defineProperty(window, "location", {
        value: originalLocation,
        configurable: true,
      });

      vi.restoreAllMocks();
    });

    it("renders the form content", () => {
      const form = createTestForm();
      render(<FormPage form={form} success={false} isMobileGuess={false} />);

      // Check that the form container is rendered
      expect(screen.getByTestId("form-container")).toBeInTheDocument();
    });

    it("shows success message when submission is successful and there is no redirect URL", () => {
      const form = createTestForm(); // No redirect URL by default
      render(<FormPage form={form} success={true} isMobileGuess={false} />);

      // Check that the success message is displayed
      expect(screen.getByTestId("submit-success-message")).toBeInTheDocument();
    });

    it("shows redirecting state (not success message) when form has redirectUrl and submission is successful", () => {
      // Create a form with a redirect URL
      const form = createTestForm("https://example.com/thank-you");

      render(<FormPage form={form} success={true} isMobileGuess={false} />);

      // The success message should not be displayed
      expect(
        screen.queryByTestId("submit-success-message"),
      ).not.toBeInTheDocument();

      // Instead, the redirecting element should be displayed
      expect(screen.getByTestId("form-redirecting")).toBeInTheDocument();
    });

    it("dispatches redirect event and redirects when form has redirectUrl and submission is successful", async () => {
      // 1. Mock window.parent to simulate being in an iframe
      const postMessageSpy = vi.fn();
      Object.defineProperty(window, "parent", {
        value: { postMessage: postMessageSpy },
        writable: true,
        configurable: true,
      });

      // 2. Mock window.self and window.top to properly simulate iframe environment
      const originalSelf = window.self;
      const originalTop = window.top;

      // Make window.self different from window.top
      Object.defineProperty(window, "self", {
        value: window,
        writable: true,
        configurable: true,
      });

      Object.defineProperty(window, "top", {
        value: {
          /* this is different from window.self */
        },
        writable: true,
        configurable: true,
      });

      // 3. Mock window.parentIFrame for iframe-resizer
      const sendMessageSpy = vi.fn();
      Object.defineProperty(window, "parentIFrame", {
        value: { sendMessage: sendMessageSpy },
        writable: true,
        configurable: true,
      });

      // 4. Create a test form with a redirect URL
      const redirectUrl = "https://example.com/thank-you-page";
      const testForm = createTestForm(redirectUrl);

      // 5. Render the component with success=true
      render(<FormPage isMobileGuess={false} form={testForm} success={true} />);

      // 6. Wait a tick for the component to process
      await waitFor(() => {
        // 7. Verify iframe-resizer message was sent
        expect(sendMessageSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            type: "redirect",
            url: redirectUrl,
          }),
          "*",
        );

        // 8. Verify redirecting element is displayed
        expect(screen.getByTestId("form-redirecting")).toBeInTheDocument();
        expect(screen.getByTestId("redirecting-message")).toHaveTextContent(
          "Redirecting you now...",
        );
      });

      // 9. Clean up by restoring window.self and window.top
      Object.defineProperty(window, "self", {
        value: originalSelf,
        writable: true,
        configurable: true,
      });

      Object.defineProperty(window, "top", {
        value: originalTop,
        writable: true,
        configurable: true,
      });
    });

    it("does not redirect when form has no redirectUrl", () => {
      // Create a form without a redirect URL
      const form = createTestForm(null);

      render(<FormPage form={form} success={true} isMobileGuess={false} />);

      // Verify no event was dispatched
      expect(document.dispatchEvent).not.toHaveBeenCalled();

      // Check that window.location.href was not changed
      expect(window.location.href).toBe("");
    });

    it("does not redirect when submission is not successful", () => {
      // Create a form with a redirect URL
      const form = createTestForm("https://example.com/thank-you");

      render(<FormPage form={form} success={false} isMobileGuess={false} />);

      // Verify no event was dispatched
      expect(document.dispatchEvent).not.toHaveBeenCalled();
    });

    it("skips direct navigation when event is handled by parent", async () => {
      // Create a form with a redirect URL
      const form = createTestForm("https://example.com/thankyou");

      // Store original location to restore later
      const originalHref = window.location.href;

      // Mock document.dispatchEvent to return false, indicating event was canceled/handled
      const mockDispatchEvent = vi.fn().mockReturnValue(false);
      const originalDispatchEvent = document.dispatchEvent;
      document.dispatchEvent = mockDispatchEvent;

      // Mock window.parent for postMessage calls
      const mockPostMessage = vi.fn();
      Object.defineProperty(window, "parent", {
        value: { postMessage: mockPostMessage },
        configurable: true,
      });

      // Mock parentIFrame for iframe-resizer
      const mockSendMessage = vi.fn();
      (window as any).parentIFrame = {
        sendMessage: mockSendMessage,
        close: vi.fn(),
      };

      // We'll directly call the internal redirect function
      // by importing the FormPage component and calling its exported redirectToUrl function if available

      // If the component exports the redirect function, we'd call it directly
      // For now, we'll simulate the component's behavior

      // Create a custom redirect event (mimicking FormPage's behavior)
      const redirectEvent = new CustomEvent("siv:form:redirect", {
        bubbles: true,
        cancelable: true,
        detail: { redirectUrl: form.redirectUrl },
      });

      // Dispatch the event (this is what FormPage does internally)
      const eventWasHandled = !document.dispatchEvent(redirectEvent);

      // Verify dispatchEvent was called with the redirect event
      expect(mockDispatchEvent).toHaveBeenCalled();

      // Since our mock returns false (indicating the event was handled),
      // verify that no redirect happened
      expect(eventWasHandled).toBe(true);
      expect(window.location.href).toBe(originalHref);

      // Clean up mocks
      document.dispatchEvent = originalDispatchEvent;
    });
  });
});
