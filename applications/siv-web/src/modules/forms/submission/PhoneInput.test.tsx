import { describe, expect, it, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { EmbeddedForm } from "./EmbeddedForm";
import { formFactory } from "../../admin/forms/form-test-factories";
import intlTelInput from "intl-tel-input";
import "intl-tel-input/build/css/intlTelInput.css";
import { useFormState } from "../components/useFormState";
import { FC } from "react";
import { createTestField } from "@/modules/admin/forms/new-form-test-factories";

// Create a wrapper component that uses the real form state hook
const TestForm: FC = () => {
  const form = formFactory.build({
    fields: [
      createTestField({
        type: "PHONE",
        label: "Phone Number",
        required: true,
        width: "full",
        order: 0,
      }),
      createTestField({
        type: "EMAIL",
        label: "Email",
        required: true,
        width: "full",
        order: 1,
      }),
    ],
  });

  const { values, handleChange } = useFormState({});

  return <EmbeddedForm form={form} values={values} onChange={handleChange} />;
};

describe("Phone Input Field", () => {
  const renderPhoneField = () => {
    return render(<TestForm />);
  };

  // Helper function to get all relevant phone inputs
  const getPhoneInputs = () => {
    const visibleInput = screen.getByTestId("input-PHONE") as HTMLInputElement;
    const hiddenPhoneInput = document.querySelector(
      'input[name="PHONE"]',
    ) as HTMLInputElement;
    const hiddenCountryInput = document.querySelector(
      'input[name="PHONE_COUNTRY"]',
    ) as HTMLInputElement;

    return {
      visibleInput,
      hiddenPhoneInput,
      hiddenCountryInput,
    };
  };

  // Helper function to verify phone number state
  const verifyPhoneState = async (expectedDigits: string) => {
    const { visibleInput, hiddenPhoneInput } = getPhoneInputs();

    await waitFor(
      () => {
        // Get the iti instance
        const iti = (visibleInput as any).iti;
        if (!iti) return false;

        // Get the formatted number and strip non-digits
        const formattedNumber = iti.getNumber();
        const strippedFormatted = formattedNumber.replace(/\D/g, "");
        const strippedExpected = expectedDigits.replace(/\D/g, "");

        // Verify both visible and hidden inputs
        return (
          strippedFormatted === strippedExpected &&
          hiddenPhoneInput.value.replace(/\D/g, "") === strippedExpected
        );
      },
      { timeout: 1000 },
    );
  };

  // Helper function to verify country selection
  const verifyCountrySelection = async (expectedCountry: string) => {
    const { visibleInput, hiddenCountryInput } = getPhoneInputs();

    await waitFor(
      () => {
        // Get the iti instance
        const iti = (visibleInput as any).iti;
        if (!iti) return false;

        // Get the selected country data
        const selectedCountry = iti.getSelectedCountryData();

        // Verify both the flag and hidden input
        const flagElement = visibleInput
          .closest(".iti")
          ?.querySelector(".iti__flag");
        const hasCorrectFlag = flagElement?.classList.contains(
          `iti__${expectedCountry}`,
        );

        return hasCorrectFlag && hiddenCountryInput.value === expectedCountry;
      },
      { timeout: 1000 },
    );
  };

  it("should maintain focus and state when typing numbers", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    await user.click(visibleInput);
    await user.type(visibleInput, "303");

    await verifyPhoneState("303");
  });

  it("should maintain focus and state when typing dashes", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    await user.click(visibleInput);
    await user.type(visibleInput, "303-");

    await verifyPhoneState("303");
  });

  it("should maintain focus and state when typing periods", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    await user.click(visibleInput);
    await user.type(visibleInput, "303.");

    await verifyPhoneState("303");
  });

  it("should not truncate numbers when switching fields and maintain state in both inputs", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    const emailInput = screen.getByTestId("input-EMAIL") as HTMLInputElement;

    // Type a complete phone number with dash
    await user.click(visibleInput);
    await user.type(visibleInput, "************");

    // Switch to email field
    await user.click(emailInput);

    // Verify phone number is complete in both visible and hidden inputs
    await verifyPhoneState("3035551234");
  });

  it("should handle backspace correctly and update both inputs", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    await user.click(visibleInput);
    await user.type(visibleInput, "303-");
    await user.type(visibleInput, "{Backspace}");

    await verifyPhoneState("303");
  });

  it("should handle paste operations and update both inputs", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    await user.click(visibleInput);

    // Simulate paste
    const pasteData = "************";
    await user.paste(pasteData);

    await verifyPhoneState("3035551234");
  });

  it("should handle different phone number formats and maintain consistent state", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();
    const testCases = [
      { input: "(*************", expected: "3035551234" },
      { input: "************", expected: "3035551234" },
      { input: "************", expected: "3035551234" },
      { input: "3035551234", expected: "3035551234" },
    ];

    for (const { input, expected } of testCases) {
      // Instead of clearing, set value directly and trigger input event
      visibleInput.value = "";
      visibleInput.dispatchEvent(new Event("input", { bubbles: true }));

      await user.click(visibleInput);
      await user.keyboard(input);

      await verifyPhoneState(expected);
    }
  });

  it("should maintain selected country after typing a phone number", async () => {
    const user = userEvent.setup();
    renderPhoneField();

    const { visibleInput } = getPhoneInputs();

    // First verify the UI shows US as selected
    await waitFor(() => {
      const flagElement = visibleInput
        .closest(".iti")
        ?.querySelector(".iti__flag");
      expect(flagElement).toBeTruthy();
      expect(flagElement).toHaveClass("iti__us");

      const selectedCountryButton = visibleInput
        .closest(".iti")
        ?.querySelector(".iti__selected-country");
      expect(selectedCountryButton).toBeTruthy();
      expect(selectedCountryButton).toHaveAttribute(
        "title",
        expect.stringContaining("United States"),
      );
    });

    // Click the country selector to open the dropdown
    const countrySelector = visibleInput
      .closest(".iti")
      ?.querySelector(".iti__selected-country");
    if (!countrySelector) throw new Error("Country selector not found");
    await user.click(countrySelector);

    // Find and click the UK option
    const ukOption = screen.getByText("United Kingdom");
    await user.click(ukOption);

    // Verify UK is selected
    await verifyCountrySelection("gb");

    // Type a phone number
    await user.click(visibleInput);
    await user.type(visibleInput, "7911123456");

    // Verify the number is entered correctly
    await verifyPhoneState("7911123456");

    // Verify UK is still selected
    await verifyCountrySelection("gb");
  });
});
