import { describe, it, expect } from "vitest";
import {
  createLeadFromFormSubmission,
  validateFormSubmission,
  ValidFormSubmission,
} from "./form-submission-domain";
import { randomUUID } from "crypto";
import { FormField } from "../../builder/types";

describe("createLeadFromFormSubmission", () => {
  const formId = randomUUID();
  const baseFields: FormField[] = [
    {
      id: randomUUID(),
      formId,
      type: "EMAIL",
      label: "Email",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 0,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "FIRST_NAME",
      label: "First Name",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 1,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "LAST_NAME",
      label: "Last Name",
      required: true,
      width: "full",
      rowBreakAfter: false,
      hideForEventTypes: [],
      order: 2,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "PHONE",
      label: "Phone",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 3,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "COMPANY",
      label: "Company",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 4,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "CITY",
      label: "City",
      required: true,
      width: "full",
      rowBreakAfter: false,
      hideForEventTypes: [],
      order: 5,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "STATE",
      label: "State",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 6,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "POSTAL_CODE",
      label: "Postal Code",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 7,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "COUNTRY",
      label: "Country",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 8,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "EVENT_TYPE",
      label: "Event Type",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 9,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "EVENT_NAME",
      label: "Event Name",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 10,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "EVENT_NEEDS",
      label: "Event Needs",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 11,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "START_DATE",
      label: "Start Date",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 12,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "END_DATE",
      label: "End Date",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 13,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "GUEST_COUNT",
      label: "Guest Count",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 14,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "ROOM_COUNT",
      label: "Room Count",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 15,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "MEAL_COUNT",
      label: "Meal Count",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 16,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "BUDGET",
      label: "Budget",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 17,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "EVENT_DESCRIPTION",
      label: "Event Description",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 18,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "FLEXIBLE_DATES",
      label: "Flexible Dates",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 19,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "MARKETING_CONSENT",
      label: "Marketing Consent",
      required: true,
      width: "full",
      hideForEventTypes: [],
      order: 20,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
    {
      id: randomUUID(),
      formId,
      type: "COUNTRY",
      label: "Country",
      required: false,
      width: "full",
      hideForEventTypes: [],
      order: 21,
      rowBreakAfter: false,
      createdAt: new Date(),
      updatedAt: null,
    },
  ];

  const baseParams = {
    propertyId: randomUUID(),
    sourceId: randomUUID(),
  };

  it("should create a lead with all fields populated", () => {
    const startDate = "2049-10-25";
    const endDate = "2049-11-23";
    const formData = {
      EMAIL: "<EMAIL>",
      FIRST_NAME: "John",
      LAST_NAME: "Doe",
      PHONE: "+13033213633",
      COMPANY: "Test Corp",
      CITY: "Test City",
      STATE: "NSW",
      COUNTRY: "AU",
      POSTAL_CODE: "12345",
      EVENT_TYPE: "corporate_meeting",
      EVENT_NEEDS: ["MEETING_SPACE", "CATERING"],
      START_DATE: startDate,
      END_DATE: endDate,
      GUEST_COUNT: "100",
      ROOM_COUNT: "50",
      MEAL_COUNT: "75",
      BUDGET: "10000",
      EVENT_DESCRIPTION: "Test Event",
      EVENT_NAME: "Annual Conference 2024",
      FLEXIBLE_DATES: true,
      MARKETING_CONSENT: true,
    };

    const validationResult = validateFormSubmission(
      baseFields,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validation: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result).toEqual({
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      phone: "+13033213633",
      company: "Test Corp",
      country: "AU",
      city: "Test City",
      state: "NSW",
      postalCode: "12345",
      eventType: "corporate_meeting",
      eventNeeds: ["MEETING_SPACE", "CATERING"],
      startDate: startDate,
      endDate: endDate,
      guestCount: 100,
      roomCount: 50,
      mealCount: 75,
      budget: 10000,
      eventDescription: "Test Event",
      eventName: "Annual Conference 2024",
      flexibleDates: true,
      marketingConsent: true,
      source: "form_submission",
      propertyId: baseParams.propertyId,
      sourceId: baseParams.sourceId,
      assignedAt: null,
      assignedSalesRepEmail: null,
      highlevelContactId: null,
      highlevelOpportunityId: null,
    });
  });

  it("should handle empty form data with all optional fields", () => {
    const formData = {
      // Required fields
      EMAIL: "<EMAIL>",
      FIRST_NAME: "John",
      LAST_NAME: "Doe",
      PHONE: "+13033213633",
      CITY: "Test City",
      COUNTRY: "US",
      STATE: "CA",
      POSTAL_CODE: "12345",
      EVENT_TYPE: "corporate_meeting",
      MARKETING_CONSENT: true,
    };

    const validationResult = validateFormSubmission(
      baseFields,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validation result: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result).toEqual({
      // Required fields should have values
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      phone: "+13033213633",
      city: "Test City",
      state: "CA",
      country: "US",
      postalCode: "12345",
      eventType: "corporate_meeting",
      marketingConsent: true,
      // Optional fields should be null/empty
      company: null,
      eventNeeds: [],
      startDate: null,
      endDate: null,
      guestCount: null,
      roomCount: null,
      mealCount: null,
      budget: null,
      eventDescription: null,
      eventName: null,
      flexibleDates: false,
      // Standard fields
      source: "form_submission",
      propertyId: baseParams.propertyId,
      sourceId: baseParams.sourceId,
      assignedAt: null,
      assignedSalesRepEmail: null,
      highlevelContactId: null,
      highlevelOpportunityId: null,
    });
  });

  it("should handle empty strings as null", () => {
    // Create a form schema with only optional fields for testing empty string handling
    const optionalFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "COMPANY",
        label: "Company",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "EVENT_DESCRIPTION",
        label: "Event Description",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "EVENT_NAME",
        label: "Event Name",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 2,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      COMPANY: "",
      EVENT_DESCRIPTION: "",
      EVENT_NAME: "",
    };

    const validationResult = validateFormSubmission(
      optionalFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validationResult: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    // All empty strings should be converted to null
    expect(result.company).toBeNull();
    expect(result.eventDescription).toBeNull();
    expect(result.eventName).toBeNull();

    // Other fields should default to their type's empty value
    expect(result.eventNeeds).toEqual([]);
    expect(result.flexibleDates).toBe(false);
    expect(result.marketingConsent).toBe(false);
    expect(result.startDate).toBeNull();
    expect(result.endDate).toBeNull();
    expect(result.guestCount).toBeNull();
    expect(result.roomCount).toBeNull();
    expect(result.mealCount).toBeNull();
    expect(result.budget).toBeNull();
  });

  it("should handle numeric fields correctly", () => {
    const numericFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "GUEST_COUNT",
        label: "Guest Count",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "ROOM_COUNT",
        label: "Room Count",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "MEAL_COUNT",
        label: "Meal Count",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 2,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "BUDGET",
        label: "Budget",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 3,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      GUEST_COUNT: "0",
      ROOM_COUNT: "0",
      MEAL_COUNT: "0",
      BUDGET: "0",
    };

    const validationResult = validateFormSubmission(
      numericFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validationResult ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result.guestCount).toBe(0);
    expect(result.roomCount).toBe(0);
    expect(result.mealCount).toBe(0);
    expect(result.budget).toBe(0);
  });

  it("should handle date fields correctly", () => {
    const dateFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "START_DATE",
        label: "Start Date",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "END_DATE",
        label: "End Date",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    // Use ISO format date strings (YYYY-MM-DD) with leading zeros for month and day
    const futureStartDate = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000); // 2 day in the future
    const futureEndDate = new Date(Date.now() + 6 * 24 * 60 * 60 * 1000); // 6 days in the future
    const startDate = futureStartDate.toISOString().slice(0, 10);
    const endDate = futureEndDate.toISOString().slice(0, 10);
    const formData = {
      START_DATE: startDate,
      END_DATE: endDate,
    };

    const validationResult = validateFormSubmission(
      dateFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `result: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    // The dates should be strings in YYYY-MM-DD format
    expect(result.startDate).toBe(startDate);
    expect(result.endDate).toBe(endDate);
  });

  it("should handle date fields as strings correctly", () => {
    const dateFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "START_DATE",
        label: "Start Date",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "END_DATE",
        label: "End Date",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    // Test with date strings in ISO format
    const futureStartDateStr = new Date(Date.now() + 24 * 2 * 60 * 60 * 1000); // 1 day in the future
    const futureEndDateStr = new Date(Date.now() + 6 * 24 * 60 * 60 * 1000); // 5 days in the future
    const startDateStr = futureStartDateStr.toISOString().slice(0, 10);
    const endDateStr = futureEndDateStr.toISOString().slice(0, 10);
    const formData = {
      START_DATE: startDateStr,
      END_DATE: endDateStr,
    };

    const validationResult = validateFormSubmission(
      dateFieldsSchema,
      formId,
      formData,
    );
    expect(validationResult.isOk()).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    // The dates should be strings in YYYY-MM-DD format
    expect(result.startDate).toBe(startDateStr);
    expect(result.endDate).toBe(endDateStr);
  });

  it("should handle boolean fields correctly", () => {
    const booleanFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "FLEXIBLE_DATES",
        label: "Flexible Dates",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "MARKETING_CONSENT",
        label: "marketing consent",
        required: true, // Making this optional for the test
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      FLEXIBLE_DATES: false,
      MARKETING_CONSENT: true,
    };

    const validationResult = validateFormSubmission(
      booleanFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validationResult: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result.flexibleDates).toBe(false);
    expect(result.marketingConsent).toBe(true);
  });

  it("should handle event needs array correctly", () => {
    const eventNeedsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "EVENT_NEEDS",
        label: "Event Needs",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      EVENT_NEEDS: ["MEETING_SPACE"],
    };

    const validationResult = validateFormSubmission(
      eventNeedsSchema,
      formId,
      formData,
    );
    expect(validationResult.isOk()).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result.eventNeeds).toEqual(["MEETING_SPACE"]);

    // Test with empty array
    const emptyArrayValidation = validateFormSubmission(
      eventNeedsSchema,
      formId,
      {
        EVENT_NEEDS: [],
      },
    );
    expect(emptyArrayValidation.isOk()).toBe(true);
    const validEmptySubmission = emptyArrayValidation._unsafeUnwrap();

    const resultEmptyArray = createLeadFromFormSubmission({
      ...baseParams,
      submission: validEmptySubmission,
    });
    expect(resultEmptyArray.eventNeeds).toEqual([]);

    // Test with non-array value
    const nonArrayValidation = validateFormSubmission(
      eventNeedsSchema,
      formId,
      {
        EVENT_NEEDS: "MEETING_SPACE",
      },
    );
    expect(nonArrayValidation.isOk()).toBe(true);
    const validNonArraySubmission = nonArrayValidation._unsafeUnwrap();

    const resultNonArray = createLeadFromFormSubmission({
      ...baseParams,
      submission: validNonArraySubmission,
    });
    expect(resultNonArray.eventNeeds).toEqual(["MEETING_SPACE"]);
  });

  it("should handle null and undefined values as actual null", () => {
    const optionalFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "COMPANY",
        label: "Company",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "EVENT_DESCRIPTION",
        label: "Event Description",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "EVENT_NAME",
        label: "Event Name",
        required: false,
        width: "full",
        hideForEventTypes: [],
        order: 2,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      COMPANY: "",
      EVENT_DESCRIPTION: "",
      EVENT_NAME: "",
    };

    const validationResult = validateFormSubmission(
      optionalFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validationResult: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    // These should be actual null values, not string "null"
    expect(result.company).toBe(null);
    expect(result.eventDescription).toBe(null);
    expect(result.eventName).toBe(null);

    // Verify the type is actually null
    expect(result.company).not.toBe("null");
    expect(typeof result.company).toBe("object");
  });

  it("should handle empty numeric fields as null, not zero", () => {
    const numericFieldsSchema: FormField[] = [
      {
        id: randomUUID(),
        formId,
        type: "GUEST_COUNT",
        label: "Guest Count",
        hideForEventTypes: [],
        required: false,
        width: "full",
        order: 0,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "ROOM_COUNT",
        label: "Room Count",
        required: false,
        hideForEventTypes: [],
        width: "full",
        order: 1,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "MEAL_COUNT",
        label: "Meal Count",
        required: false,
        hideForEventTypes: [],
        width: "full",
        order: 2,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
      {
        id: randomUUID(),
        formId,
        type: "BUDGET",
        label: "Budget",
        required: false,
        hideForEventTypes: [],
        width: "full",
        order: 3,
        rowBreakAfter: false,
        createdAt: new Date(),
        updatedAt: null,
      },
    ];

    const formData = {
      GUEST_COUNT: "", // Empty string
      ROOM_COUNT: undefined, // Undefined
      MEAL_COUNT: undefined, // undefined
      BUDGET: "   ", // Whitespace
    };

    const validationResult = validateFormSubmission(
      numericFieldsSchema,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validationResult: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    // All empty/undefined numeric fields should be null, not 0
    expect(result.guestCount).toBeNull();
    expect(result.roomCount).toBeNull();
    expect(result.mealCount).toBeNull();
    expect(result.budget).toBeNull();

    // Verify types
    expect(result.guestCount).not.toBe(0);
    expect(result.roomCount).not.toBe(0);
    expect(result.mealCount).not.toBe(0);
    expect(result.budget).not.toBe(0);
  });

  it("should correctly extract country data from submission", () => {
    const formData = {
      EMAIL: "<EMAIL>",
      FIRST_NAME: "John",
      LAST_NAME: "Doe",
      PHONE: "+13033213633",
      COMPANY: "Test Corp",
      COUNTRY: "CA", // Canada
      CITY: "Toronto",
      STATE: "ON", // Ontario
      POSTAL_CODE: "12345",
      EVENT_TYPE: "corporate_meeting",
      MARKETING_CONSENT: true,
    };

    const validationResult = validateFormSubmission(
      baseFields,
      formId,
      formData,
    );
    expect(
      validationResult.isOk(),
      `validation result: ${JSON.stringify(validationResult)}`,
    ).toBe(true);
    const validSubmission = validationResult._unsafeUnwrap();

    const result = createLeadFromFormSubmission({
      ...baseParams,
      submission: validSubmission,
    });

    expect(result).toMatchObject({
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      country: "CA",
      city: "Toronto",
      state: "ON",
      // ... other fields
    });
  });
});
