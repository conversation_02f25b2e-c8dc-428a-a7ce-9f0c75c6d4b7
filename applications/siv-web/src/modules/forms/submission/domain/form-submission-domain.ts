import { z } from "zod";
import { err, ok, Result } from "neverthrow";
import { eventType, leadSource } from "@/drizzle/schema";
import { <PERSON>Field } from "../../builder/types";
import {
  FieldIdentifier,
  PREDEFINED_FIELDS,
  validateFormSubmission as validateFormSubmissionZod,
} from "../../builder/fieldTypes";
import { NewLead } from "@/modules/leads/domain/types";

// Create a type that maps field identifiers to their expected types
export type FormSubmissionData = {
  [K in FieldIdentifier]?: (typeof PREDEFINED_FIELDS)[K]["baseSchema"] extends {
    type: "simple";
    schema: z.ZodType<any>;
  }
    ? z.infer<(typeof PREDEFINED_FIELDS)[K]["baseSchema"]["schema"]>
    : (typeof PREDEFINED_FIELDS)[K]["baseSchema"] extends {
          type: "compound";
          fields: Record<string, z.ZodType<any>>;
        }
      ? {
          [P in keyof (typeof PREDEFINED_FIELDS)[K]["baseSchema"]["fields"]]: z.infer<
            (typeof PREDEFINED_FIELDS)[K]["baseSchema"]["fields"][P]
          >;
        }
      : never;
};

// Pure domain types
export interface ValidFormSubmission {
  formId: string;
  data: FormSubmissionData;
}

export interface ValidationError {
  type: "validation";
  message: string;
  errors: Record<string, string[]>;
}

// Pure domain functions
function zodErrorToValidationError(
  zodError: z.ZodError,
  message: string,
): ValidationError {
  const errors: Record<string, string[]> = {};
  zodError.errors.forEach((err) => {
    const field = err.path[0] as string;
    if (!errors[field]) {
      errors[field] = [];
    }
    errors[field].push(err.message);
  });
  return {
    type: "validation",
    message,
    errors,
  };
}

export function validateFormSubmission(
  formFields: FormField[],
  formId: string,
  formData: Record<string, unknown>,
): Result<ValidFormSubmission, ValidationError> {
  // Generate schema based on the form fields and validate the data
  const formValidation = validateFormSubmissionZod(formFields, formData);

  if (!formValidation.success) {
    return err(
      zodErrorToValidationError(formValidation.error, "Invalid form data"),
    );
  }

  return ok({
    formId,
    data: formValidation.data,
  });
}

export function createLeadFromFormSubmission(params: {
  submission: ValidFormSubmission;
  propertyId: string;
  sourceId: string;
}): NewLead {
  const stringOrNull = (value: unknown) =>
    value == null ||
    value === undefined ||
    (typeof value === "string" && value.trim() === "")
      ? null
      : String(value);

  return {
    email: stringOrNull(params.submission.data.EMAIL),
    firstName: stringOrNull(params.submission.data.FIRST_NAME),
    lastName: stringOrNull(params.submission.data.LAST_NAME),
    phone: stringOrNull(params.submission.data.PHONE),
    company: stringOrNull(params.submission.data.COMPANY),
    city: stringOrNull(params.submission.data.CITY),
    state: stringOrNull(params.submission.data.STATE),
    country: stringOrNull(params.submission.data.COUNTRY),
    postalCode: stringOrNull(params.submission.data.POSTAL_CODE),
    eventType:
      (params.submission.data.EVENT_TYPE as
        | (typeof eventType.enumValues)[number]
        | undefined) ?? null,
    eventName: stringOrNull(params.submission.data.EVENT_NAME),
    eventNeeds: Array.isArray(params.submission.data.EVENT_NEEDS)
      ? params.submission.data.EVENT_NEEDS
      : [],
    startDate: stringOrNull(params.submission.data.START_DATE),
    endDate: stringOrNull(params.submission.data.END_DATE),
    guestCount:
      typeof params.submission.data.GUEST_COUNT === "number"
        ? params.submission.data.GUEST_COUNT
        : null,
    roomCount:
      typeof params.submission.data.ROOM_COUNT === "number"
        ? params.submission.data.ROOM_COUNT
        : null,
    mealCount:
      typeof params.submission.data.MEAL_COUNT === "number"
        ? params.submission.data.MEAL_COUNT
        : null,
    budget:
      typeof params.submission.data.BUDGET === "number"
        ? params.submission.data.BUDGET
        : null,
    eventDescription: stringOrNull(params.submission.data.EVENT_DESCRIPTION),
    flexibleDates: Boolean(params.submission.data.FLEXIBLE_DATES ?? null),
    marketingConsent: Boolean(params.submission.data.MARKETING_CONSENT ?? null),
    source: "form_submission",
    propertyId: params.propertyId,
    sourceId: params.sourceId,
    // Fields we explicitly set to null for form submissions
    assignedAt: null,
    assignedSalesRepEmail: null,
    highlevelContactId: null,
    highlevelOpportunityId: null,
  };
}
