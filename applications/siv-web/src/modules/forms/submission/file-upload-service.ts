import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { s3UploadConfig } from "./s3-upload-config";
import { v4 as uuidv4 } from "uuid";
import { Result, err, ok } from "neverthrow";
import { env } from "@/config/env";
// Request type for presigned URL generation
export interface GeneratePresignedUploadUrlRequest {
  fileName?: string; // File name (for more accurate Content-Type)
  fileType?: string; // File MIME type
  fileSize?: number; // File size in bytes
  formId: string;
  uploadGroupId: string;
}

// Information about a single presigned URL
export interface FileUploadResponse {
  fileId: string;
  presignedUrl: string;
  fullKey: string;
  keyWithoutStatusPrefix: string;
  uploadGroupId: string;
}

export interface FileUploadError {
  type: "internal_error" | "validation_error";
  message: string;
  details?: string;
}

export class UploadUrlGenerator {
  constructor(private readonly s3Client: S3Client) {}

  /**
   * Generates a single presigned URL for file upload to S3
   * The response structure is kept the same for compatibility,
   * but only one URL is generated.
   */
  async generateUploadUrl(
    params: GeneratePresignedUploadUrlRequest,
  ): Promise<Result<FileUploadResponse, FileUploadError>> {
    try {
      const bucket = s3UploadConfig.bucket;
      if (!bucket || bucket.includes("/")) {
        throw new Error(`Invalid bucket name: ${bucket}`);
      }

      const fileId = uuidv4();
      const keyWithoutStatusPrefix = `${params.formId}/${params.uploadGroupId}/${fileId}`;
      const key = `pending/${keyWithoutStatusPrefix}`;

      const command = new PutObjectCommand({
        Bucket: bucket,
        Key: key,
        ContentType: params.fileType || "application/octet-stream",
      });

      const presignedUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn: 900, // 15 minutes
      });

      const urlInfo: FileUploadResponse = {
        fileId,
        presignedUrl,
        fullKey: key,
        keyWithoutStatusPrefix,
        uploadGroupId: params.uploadGroupId,
      };

      return ok(urlInfo);
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Unknown error occurred while generating upload URL";

      return err({
        type: "internal_error",
        message: "Failed to generate upload URL",
        details: errorMessage,
      } as FileUploadError);
    }
  }
}
