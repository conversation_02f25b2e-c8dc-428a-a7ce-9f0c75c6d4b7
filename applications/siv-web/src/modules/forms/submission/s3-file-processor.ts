import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { Result, err, ok } from "neverthrow";
import { s3UploadConfig } from "./s3-upload-config";

export interface FileKey {
  s3Key: string;
  s3KeyWithoutStatusPrefix: string;
}

export interface MovedFileInfo {
  originalKey: string;
  submittedKey: string;
  keyWithoutStatusPrefix: string;
}

export interface S3ProcessingError {
  type: "s3_error";
  message: string;
  details?: string;
}

export class S3FileProcessor {
  constructor(private readonly s3Client: S3Client) {}

  /**
   * Moves files from the pending/ prefix to the submitted/ prefix in S3
   *
   * @param fileKeys Array of file keys to move
   * @returns Result with moved file information or error
   */
  async moveFilesToSubmittedPrefix(
    fileKeys: FileKey[],
  ): Promise<Result<MovedFileInfo[], S3ProcessingError>> {
    try {
      if (fileKeys.length === 0) {
        return ok([]);
      }

      const movedFiles: MovedFileInfo[] = [];

      // Process each file: copy to new location, then delete original
      for (const file of fileKeys) {
        // 1. Copy file to submitted/ prefix
        const submittedKey = `${s3UploadConfig.submittedUploadsPrefix}${file.s3KeyWithoutStatusPrefix}`;

        const copyCommand = new CopyObjectCommand({
          Bucket: s3UploadConfig.bucket,
          CopySource: `${s3UploadConfig.bucket}/${file.s3Key}`,
          Key: submittedKey,
        });

        await this.s3Client.send(copyCommand);

        // 2. Delete file from pending/ prefix
        const deleteCommand = new DeleteObjectCommand({
          Bucket: s3UploadConfig.bucket,
          Key: file.s3Key,
        });

        await this.s3Client.send(deleteCommand);

        // Track moved file info
        movedFiles.push({
          originalKey: file.s3Key,
          submittedKey,
          keyWithoutStatusPrefix: file.s3KeyWithoutStatusPrefix,
        });
      }

      return ok(movedFiles);
    } catch (error) {
      console.error("Error processing S3 files:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Unknown error occurred while processing S3 files";

      return err({
        type: "s3_error",
        message: "Failed to process S3 files",
        details: errorMessage,
      });
    }
  }
}
