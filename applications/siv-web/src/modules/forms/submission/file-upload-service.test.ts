import { beforeEach, describe, expect, it, vi } from "vitest";
import { UploadUrlGenerator } from "./file-upload-service";
import { s3Client } from "@/s3Client";
import { S3Client } from "@aws-sdk/client-s3";

describe("UploadUrlGenerator", () => {
  let service: UploadUrlGenerator;

  beforeEach(() => {
    service = new UploadUrlGenerator(s3Client);
  });

  describe("generateUploadUrl", () => {
    it("should generate a presigned URL for file upload", async () => {
      const result = await service.generateUploadUrl({
        formId: "test-form-id",
        uploadGroupId: "test-upload-group-id",
        fileName: "test.pdf",
        fileType: "application/pdf",
        fileSize: 1024,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // Check structure of response
        expect(result.value.uploadGroupId).toEqual("test-upload-group-id");
        expect(result.value.fileId.length).toBeGreaterThan(1);

        expect(result.value.presignedUrl.length).toBeGreaterThan(1);
        expect(
          result.value.presignedUrl.includes(
            "test-lead-form-file-uploads/pending/test-form-id/test-upload-group-id/",
          ),
        ).toEqual(true);
        expect(
          result.value.fullKey.includes(
            "pending/test-form-id/test-upload-group-id/",
          ),
        ).toEqual(true);
        expect(
          result.value.keyWithoutStatusPrefix.includes(
            "test-form-id/test-upload-group-id/",
          ),
        ).toEqual(true);

        // Test uploading to the URL
        const testContent = "Test file content for upload";
        const uploadResponse = await fetch(result.value.presignedUrl, {
          method: "PUT",
          headers: {
            "Content-Type": "application/pdf",
            "Content-Length": String(testContent.length),
          },
          body: testContent,
        });

        expect(uploadResponse.ok).toBe(true);
      }
    });

    it("should handle missing file metadata gracefully", async () => {
      const result = await service.generateUploadUrl({
        formId: "test-form-id",
        uploadGroupId: "test-upload-group-id",
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.presignedUrl).toBeDefined();
      }
    });

    it("should handle errors gracefully", async () => {
      // Create a mock S3 client that always throws an error
      const mockErrorClient = {
        send: vi.fn().mockRejectedValue(new Error("Mock S3 error")),
      } as unknown as S3Client;

      const badService = new UploadUrlGenerator(mockErrorClient);

      const result = await badService.generateUploadUrl({
        formId: "test-form-id",
        uploadGroupId: "test-upload-group-id",
        fileName: "test.pdf",
        fileType: "application/pdf",
        fileSize: 1024,
      });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toEqual("internal_error");
      }
    });
  });
});
