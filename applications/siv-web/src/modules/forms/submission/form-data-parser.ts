/**
 * Parses FormData into a structured object, handling:
 * - Regular fields
 * - Array fields (field[])
 * - Rails-style object arrays (field[0][property])
 */
export function parseFormData(formData: FormData): Record<string, any> {
  const data: Record<string, any> = {};

  for (const [key, value] of formData.entries()) {
    // Handle Rails-style object arrays: field[0][property]
    const arrayObjectMatch = key.match(/^([^\[]+)\[(\d+)\]\[([^\]]+)\]$/);
    if (arrayObjectMatch) {
      const [, fieldName, indexStr, property] = arrayObjectMatch;
      const index = parseInt(indexStr, 10);

      if (!data[fieldName]) data[fieldName] = [];
      if (!data[fieldName][index]) data[fieldName][index] = {};

      const parsedValue = parseValue(value);
      data[fieldName][index][property] = parsedValue;
      continue;
    }

    // Handle simple array notation: field[]
    const arrayMatch = key.match(/^([^\[]+)\[\]$/);
    if (arrayMatch) {
      const fieldName = arrayMatch[1];
      if (!data[fieldName]) data[fieldName] = [];

      const parsedValue = parseValue(value);
      data[fieldName].push(parsedValue);
      continue;
    }

    // Handle existing fields (convert to array if needed)
    if (key in data) {
      if (Array.isArray(data[key])) {
        data[key].push(parseValue(value));
      } else {
        data[key] = [data[key], parseValue(value)];
      }
      continue;
    }

    // Handle regular fields
    data[key] = parseValue(value);
  }

  return data;
}

/**
 * Parses a form value into the appropriate type
 */
function parseValue(value: FormDataEntryValue): unknown {
  if (value instanceof File) return value;

  const stringValue = value.toString();

  // Only parse booleans, keep numbers as strings
  if (stringValue === "true") return true;
  if (stringValue === "false") return false;

  // Keep all other values as strings
  return stringValue;
}
