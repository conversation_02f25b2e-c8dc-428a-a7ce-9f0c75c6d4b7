import { env } from "@/config/env";

// S3 configuration for form file uploads
export const s3UploadConfig = {
  // Use the same bucket as file-routes.tsx for consistency
  bucket: env.S3_LEAD_FORM_FILE_UPLOAD_BUCKET,
  // Prefix for pending uploads that haven't been associated with a submission yet
  pendingUploadsPrefix: "pending/",
  // Prefix for uploads that have been associated with a submission
  submittedUploadsPrefix: "submitted/",
  // Maximum file size in bytes (5MB)
  maxFileSize: 5 * 1024 * 1024,
  // Allowed file types
  allowedFileTypes: [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/heic",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ],
};
