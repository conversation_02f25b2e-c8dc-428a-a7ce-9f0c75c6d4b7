import { db } from "@/db/dbclient";
import { formSubmission, lead, leadFile } from "@/drizzle/schema";
import logger from "@/logger";
import { Lead } from "@/modules/leads/domain/types";
import { Form, FormSubmissionData } from "../builder/types";
import type { InferInsertModel } from "drizzle-orm";
import { err, ok, Result } from "neverthrow";
import {
  createLeadFromFormSubmission,
  validateFormSubmission,
  ValidationError as DomainValidationError,
} from "@/modules/forms/submission/domain/form-submission-domain";
import { FormRepo } from "@/modules/forms/FormRepo";
import { LeadCreatedEventPublisher } from "@/modules/leads/domain/events";
import { S3Client } from "@aws-sdk/client-s3";
import {
  FileUploadError,
  GeneratePresignedUploadUrlRequest,
  FileUploadResponse,
  UploadUrlGenerator,
} from "./file-upload-service";
import { PgTransaction } from "drizzle-orm/pg-core";
import {
  S3FileProcessor,
  FileKey,
  S3ProcessingError,
  MovedFileInfo,
} from "./s3-file-processor";
import { eq } from "drizzle-orm";
import { s3UploadConfig } from "./s3-upload-config";

export interface FormSubmissionSuccess {
  leadId: string;
  form: Form;
}

export interface ValidationError extends DomainValidationError {
  form: Form;
}

export interface DatabaseError {
  type: "database";
  message: string;
  cause?: unknown;
  form: Form;
}

export interface WorkflowError {
  type: "workflow";
  message: string;
  cause?: unknown;
  form: Form;
}

export interface NotFoundError {
  type: "not_found";
  message: string;
}

export type FormSubmissionFailure =
  | ValidationError
  | DatabaseError
  | WorkflowError
  | NotFoundError;

type LeadInsert = InferInsertModel<typeof lead>;

function transformToDbFormSubmission(data: FormSubmissionData) {
  console.log("transformToDbFormSubmission - input data:", data);

  // Create a deep copy to avoid modifying the original
  const transformedData = JSON.parse(JSON.stringify(data)) as Record<
    string,
    any
  >;

  // Helper function to extract YYYY-MM-DD from any date value
  const extractDateString = (dateValue: unknown): string | unknown => {
    console.log("extractDateString - input:", dateValue);
    console.log("extractDateString - type:", typeof dateValue);

    if (dateValue instanceof Date) {
      // For Date objects, use local date components to match the user's input
      const year = dateValue.getFullYear();
      const month = String(dateValue.getMonth() + 1).padStart(2, "0"); // +1 because months are 0-indexed
      const day = String(dateValue.getDate()).padStart(2, "0");
      const result = `${year}-${month}-${day}`;
      console.log("extractDateString - Date result:", result);
      return result;
    } else if (typeof dateValue === "string") {
      // For strings, preserve the exact format - just take the date part if it's an ISO string
      const result = dateValue.includes("T")
        ? dateValue.split("T")[0]
        : dateValue;
      console.log("extractDateString - string result:", result);
      return result;
    }
    return dateValue; // Return as is for other types
  };

  // Convert date fields to YYYY-MM-DD format for database storage
  if ("START_DATE" in transformedData && transformedData.START_DATE) {
    transformedData.START_DATE = extractDateString(transformedData.START_DATE);
  }

  if ("END_DATE" in transformedData && transformedData.END_DATE) {
    transformedData.END_DATE = extractDateString(transformedData.END_DATE);
  }

  return transformedData;
}

export class FormSubmissionApplicationService {
  private uploadUrlGenerator: UploadUrlGenerator;
  private s3FileProcessor: S3FileProcessor;

  constructor(
    private readonly leadCreatedEventPublisher: LeadCreatedEventPublisher,
    private readonly formRepo: FormRepo,
    s3Client: S3Client,
  ) {
    this.uploadUrlGenerator = new UploadUrlGenerator(s3Client);
    this.s3FileProcessor = new S3FileProcessor(s3Client);
  }

  private async findFormWithRelations(
    formId: string,
  ): Promise<Result<Form, NotFoundError>> {
    const form = await this.formRepo.findFormWithRelations(formId);

    if (!form) {
      return err({
        type: "not_found",
        message: "Form not found",
      });
    }

    return ok(form);
  }

  async generateFileUploadUrl(
    presigendUploadUrlRequest: GeneratePresignedUploadUrlRequest,
  ): Promise<Result<FileUploadResponse, FileUploadError>> {
    return this.uploadUrlGenerator.generateUploadUrl(presigendUploadUrlRequest);
  }

  async submitForm(
    formId: string,
    formData: Record<string, unknown>,
  ): Promise<Result<FormSubmissionSuccess, FormSubmissionFailure>> {
    // Debug log for date handling
    console.log("Form submission data:", formData);
    const formResult = await this.findFormWithRelations(formId);
    if (formResult.isErr()) {
      return err(formResult.error);
    }
    const form = formResult.value;

    // 2. Validate form submission
    const validationResult = validateFormSubmission(
      form.fields,
      formId,
      formData,
    );

    if (validationResult.isErr()) {
      return err({
        ...validationResult.error,
        form,
      });
    }

    const validSubmission = validationResult.value;

    try {
      const formSubmissionToSave = transformToDbFormSubmission(
        validSubmission.data,
      );
      console.log(
        "transformToDbFormSubmission - input data:",
        validSubmission.data,
      );

      const { createdFormSubmission, createdLead, fileUploads } =
        await db.transaction(async (tx) => {
          // Store form submission with transformed data
          const [submission] = await tx
            .insert(formSubmission)
            .values({
              formId: validSubmission.formId,
              data: formSubmissionToSave, // Use the transformed data
              metadata: {},
            })
            .returning();

          const validatedLead = createLeadFromFormSubmission({
            propertyId: form.propertyId,
            submission: validSubmission,
            sourceId: submission.id,
          });
          const [newLead] = await tx
            .insert(lead)
            .values(validatedLead as LeadInsert)
            .returning();

          // Process file uploads and insert into lead_file table
          const files = await this.processFileUploads({
            fileUploads: validSubmission.data.FILE_UPLOAD as any,
            newLeadId: newLead.id,
          });

          try {
            if (files && files.length > 0) {
              await tx.insert(leadFile).values(files).returning();
            }
          } catch (fileInsertError) {
            logger.error(fileInsertError, "Failed to insert file records");
            throw fileInsertError;
          }
          return {
            createdFormSubmission: submission,
            createdLead: newLead,
            fileUploads: files,
          };
        });

      // After successful transaction, move files in S3 from pending to submitted
      if (fileUploads && fileUploads.length > 0) {
        const fileKeys: FileKey[] = fileUploads.map(
          (file: { s3Key: string; s3KeyWithoutStatusPrefix: string }) => ({
            s3Key: file.s3Key,
            s3KeyWithoutStatusPrefix: file.s3KeyWithoutStatusPrefix,
          }),
        );

        const moveResult =
          await this.s3FileProcessor.moveFilesToSubmittedPrefix(fileKeys);

        if (moveResult.isErr()) {
          logger.error(
            { leadId: createdLead.id, error: moveResult.error },
            "Failed to move files from pending to submitted. Files will remain in pending status.",
          );
          // Don't fail the submission just because file moving failed
          // The files are still accessible in the pending prefix
        } else {
          // Update database records with new S3 paths
          try {
            const movedFiles = moveResult.value;
            await db.transaction(async (tx) => {
              // Create all updates without awaiting each one
              const updates = movedFiles.map((file) =>
                tx
                  .update(leadFile)
                  .set({
                    s3Key: file.submittedKey,
                    updatedAt: new Date().toISOString(),
                  })
                  .where(eq(leadFile.s3Key, file.originalKey)),
              );

              // Execute all updates in parallel within the transaction
              await Promise.all(updates);
            });
          } catch (dbError) {
            logger.error(
              { leadId: createdLead.id, error: dbError },
              "Failed to update file records with new S3 paths. Database records may be out of sync with S3.",
            );
            // Don't fail the submission just because DB updates failed
            // The files were successfully moved in S3, but DB records may be out of sync
          }
        }
      }

      const domainLead: Lead = {
        ...createdLead,
        startDate: createdLead.startDate,
        endDate: createdLead.endDate,
        createdAt: new Date(createdLead.createdAt!),
        updatedAt: createdLead.updatedAt
          ? new Date(createdLead.updatedAt)
          : null,
        budget: createdLead.budget ? Number(createdLead.budget) : null,
      };

      try {
        await this.leadCreatedEventPublisher.publish({
          name: "lead.created",
          data: {
            leadId: domainLead.id,
            propertyId: domainLead.propertyId,
          },
          id: `lead-created-${domainLead.id}`,
        });

        return ok({
          leadId: domainLead.id,
          form,
        });
      } catch (error) {
        logger.error("Failed to start lead sync workflow", {
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return err({
          type: "workflow",
          message: "Failed to start lead sync workflow",
          cause: error,
          form,
        });
      }
    } catch (error) {
      logger.error("Failed to save form submission", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
      return err({
        type: "database",
        message: "Failed to save form submission",
        cause: error,
        form,
      });
    }
  }

  async getForm(formId: string): Promise<Result<Form, NotFoundError>> {
    return this.findFormWithRelations(formId);
  }

  private async processFileUploads({
    fileUploads,
    newLeadId,
  }: {
    fileUploads: any;
    newLeadId: string;
  }) {
    return fileUploads?.map((file: any) => {
      const keyWithoutStatusPrefix = file.keyWithoutStatusPrefix;
      return {
        leadId: newLeadId,
        fileName: file.fileName,
        contentType: file.fileType,
        s3Key: file.uploadKey,
        s3KeyWithoutStatusPrefix: keyWithoutStatusPrefix,
      };
    });
  }
}
