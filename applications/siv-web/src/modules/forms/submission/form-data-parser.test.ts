import { describe, it, expect } from "vitest";
import { parseFormData } from "./form-data-parser";

describe("Form Data Parser", () => {
  describe("parseFormData", () => {
    it("should parse basic form fields", () => {
      const formData = new FormData();
      formData.append("firstName", "John");
      formData.append("lastName", "Doe");
      formData.append("age", "30");

      const result = parseFormData(formData);

      expect(result).toEqual({
        firstName: "John",
        lastName: "Doe",
        age: "30",
      });
    });

    it("should handle duplicate field names as arrays", () => {
      const formData = new FormData();
      formData.append("tags", "javascript");
      formData.append("tags", "react");
      formData.append("tags", "typescript");

      const result = parseFormData(formData);

      expect(result).toEqual({
        tags: ["javascript", "react", "typescript"],
      });
    });

    it("should handle array notation fields", () => {
      const formData = new FormData();
      formData.append("colors[]", "red");
      formData.append("colors[]", "blue");
      formData.append("colors[]", "green");

      const result = parseFormData(formData);

      expect(result).toEqual({
        colors: ["red", "blue", "green"],
      });
    });

    it("should handle Rails-style object arrays", () => {
      const formData = new FormData();
      formData.append(
        "FILE_UPLOAD[0][fileId]",
        "123e4567-e89b-12d3-a456-426614174000",
      );
      formData.append("FILE_UPLOAD[0][fileName]", "document.pdf");
      formData.append("FILE_UPLOAD[0][fileSize]", "1024");
      formData.append("FILE_UPLOAD[0][fileType]", "application/pdf");

      formData.append(
        "FILE_UPLOAD[1][fileId]",
        "123e4567-e89b-12d3-a456-426614174001",
      );
      formData.append("FILE_UPLOAD[1][fileName]", "image.jpg");
      formData.append("FILE_UPLOAD[1][fileSize]", "2048");
      formData.append("FILE_UPLOAD[1][fileType]", "image/jpeg");

      const result = parseFormData(formData);

      expect(result).toEqual({
        FILE_UPLOAD: [
          {
            fileId: "123e4567-e89b-12d3-a456-426614174000",
            fileName: "document.pdf",
            fileSize: "1024",
            fileType: "application/pdf",
          },
          {
            fileId: "123e4567-e89b-12d3-a456-426614174001",
            fileName: "image.jpg",
            fileSize: "2048",
            fileType: "image/jpeg",
          },
        ],
      });
    });

    it("should handle sparse Rails-style object arrays", () => {
      const formData = new FormData();
      formData.append(
        "FILE_UPLOAD[0][fileId]",
        "123e4567-e89b-12d3-a456-426614174000",
      );
      formData.append("FILE_UPLOAD[0][fileName]", "document.pdf");

      formData.append(
        "FILE_UPLOAD[2][fileId]",
        "123e4567-e89b-12d3-a456-426614174002",
      );
      formData.append("FILE_UPLOAD[2][fileName]", "spreadsheet.xlsx");

      const result = parseFormData(formData);

      expect(result).toEqual({
        FILE_UPLOAD: [
          {
            fileId: "123e4567-e89b-12d3-a456-426614174000",
            fileName: "document.pdf",
          },
          undefined,
          {
            fileId: "123e4567-e89b-12d3-a456-426614174002",
            fileName: "spreadsheet.xlsx",
          },
        ],
      });
    });

    it("should handle mixed field types", () => {
      const formData = new FormData();
      formData.append("name", "John Doe");
      formData.append(
        "FILE_UPLOAD[0][fileId]",
        "123e4567-e89b-12d3-a456-426614174000",
      );
      formData.append("FILE_UPLOAD[0][fileName]", "document.pdf");
      formData.append("tags[]", "resume");
      formData.append("tags[]", "personal");

      const result = parseFormData(formData);

      expect(result).toEqual({
        name: "John Doe",
        FILE_UPLOAD: [
          {
            fileId: "123e4567-e89b-12d3-a456-426614174000",
            fileName: "document.pdf",
          },
        ],
        tags: ["resume", "personal"],
      });
    });

    it("should handle booleans", () => {
      const formData = new FormData();
      formData.append("isActive", "true");
      formData.append("isDeleted", "false");

      const result = parseFormData(formData);

      expect(result).toEqual({
        isActive: true,
        isDeleted: false,
      });
    });
  });
});
