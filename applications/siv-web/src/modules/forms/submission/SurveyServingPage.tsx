"use client";
import * as SurveyReact from "survey-react-ui";
import * as SurveyCore from "survey-core";
import "survey-core/defaultV2.min.css";
import { useEffect, useState } from "react";
import { ClientOnly } from "@/components/client-only";

interface SurveyServingPageProps {
  survey: InstanceType<typeof SurveyCore.Model>;
}

export default function SurveyServingPage({ survey }: SurveyServingPageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [clientSurvey, setClientSurvey] = useState<InstanceType<
    typeof SurveyCore.Model
  > | null>(null);

  useEffect(() => {
    // Create a new model instance using the survey data directly
    const model = new SurveyCore.Model(survey);
    setClientSurvey(model);
    setIsLoaded(true);
    console.log("SurveyReact available:", !!SurveyReact);
    console.log("SurveyReact contents:", SurveyReact);
  }, [survey]);

  return (
    <ClientOnly>
      {isLoaded && clientSurvey && <SurveyReact.Survey model={clientSurvey} />}
    </ClientOnly>
  );
}
