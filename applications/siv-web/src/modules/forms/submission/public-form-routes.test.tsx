import { beforeEach, describe, expect, it, vi } from "vitest";
import { Hono } from "hono";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import { lead, leadFile } from "@/drizzle/schema";
import { formFactory } from "@/modules/admin/forms/form-test-factories";
import { createPublicFormRoutes } from "./public-form-routes";
import { eq } from "drizzle-orm";
import { testRenderer } from "@/test/setup";
import { parseHTML } from "@/test/test-utils";
import { getByTestId } from "@testing-library/dom";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { randomUUID } from "crypto";
import { FormSubmissionApplicationService } from "@/modules/forms/submission/form-submission-application-service";
import { FormRepo } from "@/modules/forms/FormRepo";
import { InMemoryEventPublisher } from "@/modules/leads/infrastructure/InMemoryEventPublisher";
import { LeadCreatedEvent } from "@/modules/leads/domain/events";
import { s3Client } from "@/s3Client";
import {
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { s3UploadConfig } from "./s3-upload-config";

// Create mock before any imports
const mockIframeResizer = vi.hoisted(() => vi.fn());
vi.mock("@iframe-resizer/child", () => ({
  __esModule: true,
  iframeResizer: mockIframeResizer,
}));

function createApp() {
  const leadCreatedEventPublisher =
    new InMemoryEventPublisher<LeadCreatedEvent>();
  const app = new Hono<{ Variables: Variables }>().use("*", testRenderer).route(
    "/",
    createPublicFormRoutes({
      formSumbissionApplicationService: new FormSubmissionApplicationService(
        leadCreatedEventPublisher,
        new FormRepo(),
        s3Client,
      ),
    }),
  );

  return { app, leadCreatedEventPublisher };
}

describe("Public Form Routes", () => {
  withTransactionalTests();

  describe("GET /:formId", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it("should render the form page with all fields", async () => {
      const { app } = createApp();

      const testForm = await formFactory.create(
        {
          name: "Test Contact Form",
        },
        {
          transient: {
            fields: [
              {
                type: "FIRST_NAME",
                label: "First Name Field",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "EMAIL",
                label: "Email Field",
                required: true,
                width: "full",
                order: 1,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByTestId(document, "label-FIRST_NAME")).toHaveTextContent(
        "First Name",
      );
      expect(getByTestId(document, "label-EMAIL")).toHaveTextContent("Email");
    });

    it("should render form with custom theme", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            theme: {
              name: "Custom Theme",
              primaryColor: "#ff0000",
              backgroundColor: "#f1f5f9",
              textColor: "#000000",
              borderColor: "#cccccc",
              buttonBorderRadius: "9999px",
              padding: "2.5rem",
              font: "Arial, sans-serif",
              buttonAlignment: "left",
              inputBorderRadius: "1rem",
              buttonTextColor: "#f8fafc",
              fieldBackgroundColor: "#f8f9fa",
              inputTextColor: "#2d3748",
              formBorderRadius: "2rem",
            },
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      const formElement = getByTestId(document, "form-container");
      const submitWrapper = document.querySelector(".ef-submit-wrapper");

      // Check all CSS variables are set correctly
      // Colors
      expect(formElement.style.getPropertyValue("--ef-primary")).toBe(
        "#ff0000",
      );
      expect(formElement.style.getPropertyValue("--ef-background")).toBe(
        "#f1f5f9",
      );
      expect(formElement.style.getPropertyValue("--ef-text")).toBe("#000000");
      expect(formElement.style.getPropertyValue("--ef-border")).toBe("#cccccc");
      expect(formElement.style.getPropertyValue("--ef-button-text")).toBe(
        "#f8fafc",
      );
      expect(formElement.style.getPropertyValue("--ef-field-background")).toBe(
        "#f8f9fa",
      );
      expect(formElement.style.getPropertyValue("--ef-input-text")).toBe(
        "#2d3748",
      );

      // Spacing and borders
      expect(formElement.style.getPropertyValue("--ef-spacing")).toBe("2.5rem");
      expect(formElement.style.getPropertyValue("--ef-form-radius")).toBe(
        "2rem",
      );
      expect(formElement.style.getPropertyValue("--ef-input-radius")).toBe(
        "1rem",
      );
      expect(formElement.style.getPropertyValue("--ef-button-radius")).toBe(
        "9999px",
      );

      // Typography and layout
      expect(formElement.style.getPropertyValue("--ef-font")).toBe(
        "Arial, sans-serif",
      );
      expect(submitWrapper).toHaveAttribute("data-alignment", "left");
    });

    it("should render form with custom font and button alignment", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            theme: {
              name: "Font Theme",
              primaryColor: "#3b82f6",
              backgroundColor: "#ffffff",
              textColor: "#000000",
              borderColor: "#e2e8f0",
              buttonBorderRadius: "0.375rem",
              padding: "1rem",
              font: "Arial, sans-serif",
              buttonAlignment: "left",
              inputBorderRadius: "0.375rem",
              buttonTextColor: "#ffffff",
              fieldBackgroundColor: "#ffffff",
              inputTextColor: "#000000",
              formBorderRadius: "0.75rem",
            },
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      const formElement = getByTestId(document, "form-container");
      const submitWrapper = document.querySelector(".ef-submit-wrapper");

      expect(formElement.style.getPropertyValue("--ef-font")).toBe(
        "Arial, sans-serif",
      );
      expect(submitWrapper).toHaveAttribute("data-alignment", "left");
    });

    it("should render form with custom input styling", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            theme: {
              name: "Input Theme",
              primaryColor: "#3b82f6",
              backgroundColor: "#ffffff",
              textColor: "#000000",
              borderColor: "#e2e8f0",
              buttonBorderRadius: "0.375rem",
              padding: "1rem",
              font: "system-ui, -apple-system, sans-serif",
              buttonAlignment: "right",
              inputBorderRadius: "1rem",
              buttonTextColor: "#ffffff",
              fieldBackgroundColor: "#f8f9fa",
              inputTextColor: "#2d3748",
              formBorderRadius: "0.75rem",
            },
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      const formElement = getByTestId(document, "form-container");

      expect(formElement.style.getPropertyValue("--ef-input-radius")).toBe(
        "1rem",
      );
      expect(formElement.style.getPropertyValue("--ef-field-background")).toBe(
        "#f8f9fa",
      );
      expect(formElement.style.getPropertyValue("--ef-input-text")).toBe(
        "#2d3748",
      );
    });

    it("should render form with custom button styling", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            theme: {
              name: "Button Theme",
              primaryColor: "#3b82f6",
              backgroundColor: "#ffffff",
              textColor: "#000000",
              borderColor: "#e2e8f0",
              buttonBorderRadius: "9999px",
              padding: "1rem",
              font: "system-ui, -apple-system, sans-serif",
              buttonAlignment: "right",
              inputBorderRadius: "0.375rem",
              buttonTextColor: "#f8fafc",
              fieldBackgroundColor: "#ffffff",
              inputTextColor: "#000000",
              formBorderRadius: "0.75rem",
            },
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      const formElement = getByTestId(document, "form-container");

      expect(formElement.style.getPropertyValue("--ef-button-radius")).toBe(
        "9999px",
      );
      expect(formElement.style.getPropertyValue("--ef-button-text")).toBe(
        "#f8fafc",
      );
    });

    it("should render form with custom form container styling", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            theme: {
              name: "Container Theme",
              primaryColor: "#3b82f6",
              backgroundColor: "#f1f5f9",
              textColor: "#000000",
              borderColor: "#e2e8f0",
              buttonBorderRadius: "0.375rem",
              padding: "2.5rem",
              font: "system-ui, -apple-system, sans-serif",
              buttonAlignment: "right",
              inputBorderRadius: "0.375rem",
              buttonTextColor: "#ffffff",
              fieldBackgroundColor: "#ffffff",
              inputTextColor: "#000000",
              formBorderRadius: "2rem",
            },
          },
        },
      );

      const res = await app.request(`/${testForm.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      const formElement = getByTestId(document, "form-container");

      expect(formElement.style.getPropertyValue("--ef-form-radius")).toBe(
        "2rem",
      );
      expect(formElement.style.getPropertyValue("--ef-background")).toBe(
        "#f1f5f9",
      );
      expect(formElement.style.getPropertyValue("--ef-spacing")).toBe("2.5rem");
    });

    it("should return 404 for non-existent form", async () => {
      const { app } = createApp();
      const res = await app.request(`/${randomUUID()}`);
      expect(res.status).toBe(404);
      expect(await res.text()).toBe("Form not found");
    });
  });

  describe("POST /:formId/files/presign", () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it("should generate presigned URLs for file upload", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create();

      const res = await app.request(`/${testForm.id}/files/presign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ uploadGroupId: "abc-123-upload-id" }),
      });

      const data = await res.json();
      expect(res.status).toBe(200);
      expect(data).toEqual({
        fileId: expect.any(String),
        fullKey: expect.stringMatching(/^pending\/[^/]+\/[^/]+\/[^/]+$/),
        keyWithoutStatusPrefix: expect.stringMatching(/^[^/]+\/[^/]+\/[^/]+$/),
        presignedUrl: expect.stringMatching(
          /^http:\/\/(localhost|minio):9000\/test-lead-form-file-uploads\/pending\/.+/,
        ), //localhost for local testing; minio for ci
        uploadGroupId: "abc-123-upload-id",
      });
    });
  });

  describe("POST /:formId/submission", () => {
    it("should create lead from form submission and publish a lead created event", async () => {
      const { app, leadCreatedEventPublisher } = createApp();
      const testForm = await formFactory.create(
        {
          redirectUrl: "https://example.com/thank-you",
        },
        {
          transient: {
            fields: [
              {
                type: "FIRST_NAME",
                label: "First Name",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "LAST_NAME",
                label: "Last Name",
                required: true,
                width: "full",
                order: 1,
                rowBreakAfter: false,
              },
              {
                type: "EMAIL",
                label: "Email",
                required: true,
                width: "full",
                order: 2,
                rowBreakAfter: false,
              },
              {
                type: "PHONE",
                label: "Phone",
                required: false,
                width: "full",
                order: 3,
                rowBreakAfter: false,
              },
              {
                type: "START_DATE",
                label: "Start Date",
                required: false,
                width: "full",
                order: 4,
                rowBreakAfter: false,
              },
              {
                type: "END_DATE",
                label: "End Date",
                required: false,
                width: "full",
                order: 5,
                rowBreakAfter: false,
              },
              {
                type: "FILE_UPLOAD",
                label: "Documents",
                required: false,
                width: "full",
                order: 6,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const uploadGroupId = "123e4567-e89b-12d3-a456-426614174000";
      const fileName = "test-document.pdf";
      const fileSize = "1024";
      const fileType = "application/pdf";
      const fileId = "123e4567-e89b-12d3-a456-426614174001";
      const uploadKey = `pending/${uploadGroupId}/test-document.pdf`;
      const keyWithoutStatusPrefix = `${uploadGroupId}/test-document.pdf`;

      // Create form data with file upload references
      const formData = new FormData();
      formData.append("FIRST_NAME", "John");
      formData.append("LAST_NAME", "Doe");
      formData.append("EMAIL", "<EMAIL>");
      formData.append("FILE_UPLOAD[0][fileId]", fileId);
      formData.append("FILE_UPLOAD[0][fileName]", fileName);
      formData.append("FILE_UPLOAD[0][fileSize]", fileSize);
      formData.append("FILE_UPLOAD[0][fileType]", fileType);
      formData.append("FILE_UPLOAD[0][uploadKey]", uploadKey);
      formData.append(
        "FILE_UPLOAD[0][keyWithoutStatusPrefix]",
        keyWithoutStatusPrefix,
      );

      const res = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);
      const document = parseHTML(await res.text());
      expect(
        document.querySelector('[data-testid="form-redirecting"]'),
      ).toBeTruthy();
      expect(
        document.querySelector('[data-testid="redirecting-message"]'),
      ).toHaveTextContent("Redirecting you now...");

      // Verify lead created event was published
      expect(leadCreatedEventPublisher.getEvents()).toHaveLength(1);
      expect(leadCreatedEventPublisher.getEvents()[0].name).toBe(
        "lead.created",
      );
    });

    it("should handle missing required fields", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "FIRST_NAME",
                label: "First Name",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FIRST_NAME", ""); // Send empty string instead of omitting the field

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 200) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);

      const document = parseHTML(await response.text());
      const firstNameField = getByTestId(document, "label-FIRST_NAME").closest(
        ".ef-field",
      );

      // Check error message
      expect(firstNameField).toHaveTextContent("Required");
      expect(firstNameField).toHaveClass("has-error");

      // Verify no lead was created
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(0);
    });

    it("should handle flexible dates checkbox submission correctly", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "FLEXIBLE_DATES",
                label: "Are your dates flexible?",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FLEXIBLE_DATES", "true");

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 200) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(200);

      // Verify the lead was created with correct boolean value
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].flexibleDates).toBe(true);

      // Try with false value
      const formData2 = new FormData();
      formData2.append("FLEXIBLE_DATES", "false");

      const response2 = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData2,
      });

      if (response2.status !== 200) {
        const errorClone2 = response2.clone();
        console.log("Response status:", response2.status);
        console.log("Response text:", await errorClone2.text());
      }

      expect(response2.status).toBe(200);

      // Verify the second lead was created with correct boolean value
      const leads2 = await db
        .select()
        .from(lead)
        .where(eq(lead.flexibleDates, false));
      expect(leads2[0].flexibleDates).toBe(false);
    });

    it("should handle submission without redirect URL", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          redirectUrl: null,
        },
        {
          transient: {
            fields: [
              {
                type: "FIRST_NAME",
                label: "First Name",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FIRST_NAME", "John");

      const res = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);
      const document = parseHTML(await res.text());

      // Look for success message by class instead of test ID
      const successMessage = document.querySelector(
        '[data-testid="submit-success-message"]',
      );
      expect(successMessage).toBeTruthy();
      expect(successMessage).toHaveTextContent(
        "Thank you for your submission! A member of our team will reach out to you soon.",
      );
    });

    it("should return 404 for submission to non-existent form", async () => {
      const { app } = createApp();
      const formData = new FormData();
      formData.append("FIRST_NAME", "John");

      const res = await app.request(`/${randomUUID()}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(404);
      expect(await res.text()).toBe("Form not found");
    });

    it("should reject form submission when required marketing consent is not checked", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "FIRST_NAME",
                label: null,
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "MARKETING_CONSENT",
                label: null,
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FIRST_NAME", "John");

      const res = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 422) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(422);
      const html = await res.text();
      expect(html).toContain("You must agree to receive communications");
    });

    it("should accept form submission when required marketing consent is checked", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "MARKETING_CONSENT",
                label: null,
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("MARKETING_CONSENT", "true");

      const res = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);
      const html = await res.text();
      expect(html).toContain("Thank you for your submission");
    });

    it("should handle event type and event needs submission correctly", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "EVENT_TYPE",
                label: "Event Type",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 1,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_TYPE", "corporate_meeting");
      formData.append("EVENT_NEEDS", "MEETING_SPACE");
      formData.append("EVENT_NEEDS", "CATERING");

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 200) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(200);

      // Verify the lead was created with correct values
      const leads = await db.select().from(lead).orderBy(lead.createdAt);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventType).toBe("corporate_meeting");
      expect(leads[0].eventNeeds).toEqual(["MEETING_SPACE", "CATERING"]);

      // Try with single event need
      const formData2 = new FormData();
      formData2.append("EVENT_TYPE", "wedding");
      formData2.append("EVENT_NEEDS", "CATERING");

      const response2 = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData2,
      });

      if (response2.status !== 200) {
        const errorClone2 = response2.clone();
        console.log("Response status:", response2.status);
        console.log("Response text:", await errorClone2.text());
      }

      expect(response2.status).toBe(200);

      // Verify the second lead was created with correct values
      const leads2 = await db
        .select()
        .from(lead)
        .where(eq(lead.eventType, "wedding"));
      expect(leads2).toHaveLength(1);
      expect(leads2[0].eventType).toBe("wedding");
      expect(leads2[0].eventNeeds).toEqual(["CATERING"]);

      // Try with no event needs
      const formData3 = new FormData();
      formData3.append("EVENT_TYPE", "celebration");

      const response3 = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData3,
      });

      if (response3.status !== 200) {
        const errorClone3 = response3.clone();
        console.log("Response status:", response3.status);
        console.log("Response text:", await errorClone3.text());
      }

      expect(response3.status).toBe(200);

      // Verify the third lead was created with correct values
      const leads3 = await db
        .select()
        .from(lead)
        .where(eq(lead.eventType, "celebration"));
      expect(leads3[0].eventType).toBe("celebration");
      expect(leads3[0].eventNeeds).toEqual([]);
    });

    it("should reject invalid event type values", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "EVENT_TYPE",
                label: "Event Type",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_TYPE", "invalid_event_type");

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 422) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);

      // Verify no lead was created
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(0);
    });

    it("should reject invalid event needs values", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_NEEDS", "INVALID_NEED");

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 422) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);

      // Verify no lead was created
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(0);
    });

    it("should validate date fields correctly", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "START_DATE",
                label: "Start Date",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "END_DATE",
                label: "End Date",
                required: true,
                width: "full",
                order: 1,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      // Test past date validation
      const pastDate = "1989-12-25";

      const formData = new FormData();
      formData.append("START_DATE", pastDate);

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 422) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);
      const document = parseHTML(await response.text());
      expect(document.querySelector(".ef-field-error")).toHaveTextContent(
        "Date must be in the future",
      );

      // Test valid dates
      const futureDate1 = "2300-12-22";
      const futureDate2 = "2300-12-24";

      const validFormData = new FormData();
      validFormData.append("START_DATE", futureDate1);
      validFormData.append("END_DATE", futureDate2);

      const validResponse = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: validFormData,
      });

      if (validResponse.status !== 200) {
        const errorClone = validResponse.clone();
        console.log("Response status:", validResponse.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(validResponse.status).toBe(200);
    });

    it("should validate number fields correctly", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "GUEST_COUNT",
                label: "Guest Count",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "MEAL_COUNT",
                label: "Meal Count",
                required: true,
                width: "full",
                order: 1,
                rowBreakAfter: false,
              },
              {
                type: "ROOM_COUNT",
                label: "Room Count",
                required: true,
                width: "full",
                order: 2,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      // Test invalid guest count
      const formData = new FormData();
      formData.append("GUEST_COUNT", "-1");
      formData.append("MEAL_COUNT", "1");
      formData.append("ROOM_COUNT", "1");

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 422) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);
      const document = parseHTML(await response.text());
      expect(document).toHaveTextContent("Cannot be negative");

      // Test invalid meal count
      const formData2 = new FormData();
      formData2.append("GUEST_COUNT", "1");
      formData2.append("MEAL_COUNT", "-1");
      formData2.append("ROOM_COUNT", "1");

      const response2 = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData2,
      });

      if (response2.status !== 422) {
        const errorClone2 = response2.clone();
        console.log("Response status:", response2.status);
        console.log("Response text:", await errorClone2.text());
      }

      expect(response2.status).toBe(422);
      const document2 = parseHTML(await response2.text());
      expect(document2.querySelector(".ef-field-error")).toHaveTextContent(
        "Cannot be negative",
      );

      // Test valid numbers
      const validFormData = new FormData();
      validFormData.append("GUEST_COUNT", "10");
      validFormData.append("MEAL_COUNT", "10");
      validFormData.append("ROOM_COUNT", "5");

      const validResponse = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: validFormData,
      });

      if (validResponse.status !== 200) {
        const errorClone = validResponse.clone();
        console.log("Response status:", validResponse.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(validResponse.status).toBe(200);

      // Verify the lead was created with correct values
      const leads = await db.select().from(lead).orderBy(lead.createdAt);
      expect(leads).toHaveLength(1);
      expect(leads[0].guestCount).toBe(10);
    });

    it("should validate text field lengths correctly", async () => {
      const { app } = createApp();
      const testForm = await formFactory.create(
        {
          name: "Test form",
        },
        {
          transient: {
            fields: [
              {
                type: "EVENT_DESCRIPTION",
                label: "Event Description",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      // Test too long event description
      const formData = new FormData();
      formData.append("EVENT_DESCRIPTION", "a".repeat(2001));

      const response = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (response.status !== 422) {
        const errorClone = response.clone();
        console.log("Response status:", response.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(response.status).toBe(422);
      const document = parseHTML(await response.text());
      expect(document).toHaveTextContent(
        "String must contain at most 2000 character(s)",
      );

      // Test valid lengths
      const validFormData = new FormData();
      validFormData.append("EVENT_DESCRIPTION", "Valid description");

      const validResponse = await app.request(`/${testForm.id}/submission`, {
        method: "POST",
        body: validFormData,
      });

      if (validResponse.status !== 200) {
        const errorClone = validResponse.clone();
        console.log("Response status:", validResponse.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(validResponse.status).toBe(200);
    });

    it("should move uploaded files from pending to submitted S3 prefix after form submission", async () => {
      const form = await createTestFormWithFileUpload();

      const uploadGroupId = "123e4567-e89b-12d3-a456-426614174000";
      const fileName = "test-document.pdf";
      const fileSize = "1024";
      const fileType = "application/pdf";
      const fileId = "123e4567-e89b-12d3-a456-426614174001";
      const uploadKey = `pending/${uploadGroupId}/test-document.pdf`;
      const keyWithoutStatusPrefix = `${uploadGroupId}/test-document.pdf`;
      const targetSubmittedKey = `submitted/${keyWithoutStatusPrefix}`;

      const testContent = await createTestFileInS3(uploadKey, fileType);
      const formData = createFormDataWithFile({
        fileId,
        fileName,
        fileSize,
        fileType,
        uploadKey,
        keyWithoutStatusPrefix,
      });

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(1);

      await verifyFileMovedToSubmitted(
        uploadKey,
        targetSubmittedKey,
        testContent,
      );

      // Verify that the lead_file records are updated with the new S3 key
      const updatedFileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(updatedFileRecords).toHaveLength(1);
      expect(updatedFileRecords[0].s3Key).toBe(targetSubmittedKey);
      expect(updatedFileRecords[0].s3KeyWithoutStatusPrefix).toBe(
        keyWithoutStatusPrefix,
      );
    });

    it("should save file references to lead_file table when form is submitted with file uploads", async () => {
      const form = await createTestFormWithFileUpload();

      const fileId = "123e4567-e89b-12d3-a456-426614174001";
      const fileName = "test-document.pdf";
      const fileSize = "1024";
      const fileType = "application/pdf";
      const uploadKey =
        "pending/123e4567-e89b-12d3-a456-426614174000/test-document.pdf";
      const keyWithoutStatusPrefix =
        "123e4567-e89b-12d3-a456-426614174000/test-document.pdf";
      const targetSubmittedKey = `submitted/${keyWithoutStatusPrefix}`;

      const testContent = await createTestFileInS3(uploadKey, fileType);
      const formData = createFormDataWithFile({
        fileId,
        fileName,
        fileSize,
        fileType,
        uploadKey,
        keyWithoutStatusPrefix,
      });

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(1);

      await verifyFileMovedToSubmitted(
        uploadKey,
        targetSubmittedKey,
        testContent,
      );

      // Verify that the lead_file records are updated with the new S3 key
      const updatedFileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(updatedFileRecords).toHaveLength(1);
      expect(updatedFileRecords[0].s3Key).toBe(targetSubmittedKey);
      expect(updatedFileRecords[0].s3KeyWithoutStatusPrefix).toBe(
        keyWithoutStatusPrefix,
      );
    });

    it("should save multiple file references when multiple files are uploaded", async () => {
      const form = await createTestFormWithFileUpload();
      const uploadGroupId = "123e4567-e89b-12d3-a456-426614174000";

      const fileData = [
        {
          fileId: "123e4567-e89b-12d3-a456-426614174001",
          fileName: "document1.pdf",
          fileSize: "1024",
          fileType: "application/pdf",
          uploadKey: `pending/${uploadGroupId}/document1.pdf`,
          keyWithoutStatusPrefix: `${uploadGroupId}/document1.pdf`,
        },
        {
          fileId: "123e4567-e89b-12d3-a456-426614174002",
          fileName: "document2.jpg",
          fileSize: "2048",
          fileType: "image/jpeg",
          uploadKey: `pending/${uploadGroupId}/document2.jpg`,
          keyWithoutStatusPrefix: `${uploadGroupId}/document2.jpg`,
        },
      ];

      const formData = new FormData();
      formData.append("FIRST_NAME", "John");
      formData.append("LAST_NAME", "Doe");
      formData.append("EMAIL", "<EMAIL>");

      fileData.forEach((file, index) => {
        formData.append(`FILE_UPLOAD[${index}][fileId]`, file.fileId);
        formData.append(`FILE_UPLOAD[${index}][fileName]`, file.fileName);
        formData.append(`FILE_UPLOAD[${index}][fileSize]`, file.fileSize);
        formData.append(`FILE_UPLOAD[${index}][fileType]`, file.fileType);
        formData.append(`FILE_UPLOAD[${index}][uploadKey]`, file.uploadKey);
        formData.append(
          `FILE_UPLOAD[${index}][keyWithoutStatusPrefix]`,
          file.keyWithoutStatusPrefix,
        );
      });

      // Create test files in S3
      for (const file of fileData) {
        await createTestFileInS3(file.uploadKey, file.fileType);
      }

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(2);

      // Wait for file movement to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Verify the database records are updated with the new S3 keys
      const updatedFileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(updatedFileRecords).toHaveLength(2);

      for (const file of fileData) {
        const targetSubmittedKey = `submitted/${file.keyWithoutStatusPrefix}`;

        // Verify file exists in the submitted location
        try {
          const submittedFileResponse = await s3Client.send(
            new GetObjectCommand({
              Bucket: s3UploadConfig.bucket,
              Key: targetSubmittedKey,
            }),
          );
          expect(submittedFileResponse).toBeDefined();

          // Verify original file is gone
          try {
            await s3Client.send(
              new GetObjectCommand({
                Bucket: s3UploadConfig.bucket,
                Key: file.uploadKey,
              }),
            );
            expect(false).toBe(true); // Should not reach here
          } catch (error: any) {
            expect(error.name).toBe("NoSuchKey");
          }

          // Clean up the test file
          await s3Client.send(
            new DeleteObjectCommand({
              Bucket: s3UploadConfig.bucket,
              Key: targetSubmittedKey,
            }),
          );
        } catch (e) {
          console.error("Error checking or cleaning up test file:", e);
        }

        // Verify the file record has been updated with the new path
        const fileRecord = updatedFileRecords.find(
          (record) =>
            record.fileName === file.fileName &&
            record.contentType === file.fileType,
        );

        expect(fileRecord).toBeDefined();
        expect(fileRecord!.s3Key).toBe(targetSubmittedKey);
        expect(fileRecord!.s3KeyWithoutStatusPrefix).toBe(
          file.keyWithoutStatusPrefix,
        );
      }
    });

    it("should handle form submission with no files when field is optional", async () => {
      const form = await createTestFormWithFileUpload();

      const formData = createFormDataWithFile();

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(0);
    });

    it("should reject form submission when required file upload is missing", async () => {
      const form = await createTestFormWithFileUpload({ fileRequired: true });

      const formData = createFormDataWithFile();

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(422);

      const document = parseHTML(await res.text());
      const fileUploadField = getByTestId(
        document,
        "label-FILE_UPLOAD",
      ).closest(".ef-field");
      expect(fileUploadField).toHaveTextContent("Required");
      expect(fileUploadField).toHaveClass("has-error");

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(0);
    });
  });

  describe("Form Submission", () => {
    it("should handle single checkbox fields correctly", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "FLEXIBLE_DATES",
                label: "Flexible Dates",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FLEXIBLE_DATES", "true");

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);
    });

    it("should handle unchecked single checkbox fields correctly", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "FLEXIBLE_DATES",
                label: "Flexible Dates",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("SOME_JUNK", "false");
      // Don't append anything for FLEXIBLE_DATES - simulating unchecked box

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);
      const document = parseHTML(await res.text());
      expect(
        document.querySelector('[data-testid="submit-success-message"]'),
      ).toBeInTheDocument();
    });

    it("should handle select fields", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_TYPE",
                label: "Event Type",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_TYPE", "wedding");
      // Since event type can only be a single value, we should only submit one
      // This test should actually be moved to event needs which supports multiple values

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with correct values
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventType).toBe("wedding");
    });

    it("should handle checkbox group fields with multiple selections", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_NEEDS", "CATERING");
      formData.append("EVENT_NEEDS", "MEETING_SPACE");

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with correct values
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventNeeds).toEqual(["CATERING", "MEETING_SPACE"]);
    });

    it("should handle checkbox group fields with single selection", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_NEEDS", "CATERING");

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with correct value
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventNeeds).toEqual(["CATERING"]);
    });

    it("should handle checkbox group fields with no selections", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("SOME_OTHER_DATA", "");
      // Don't append anything for EVENT_NEEDS - simulating no selections

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with no event needs
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventNeeds).toEqual([]);
    });

    it("should handle multiple event needs selections correctly", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("EVENT_NEEDS", "MEETING_SPACE");
      formData.append("EVENT_NEEDS", "CATERING");

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with correct values
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].eventNeeds).toEqual(["MEETING_SPACE", "CATERING"]);
    });

    it("should handle FLEXIBLE_DATES checkbox when checked", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "FLEXIBLE_DATES",
                label: "Are your dates flexible?",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("FLEXIBLE_DATES", "true");

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with flexibleDates set to true
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].flexibleDates).toBe(true);
    });

    it("should handle FLEXIBLE_DATES checkbox when unchecked", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "FLEXIBLE_DATES",
                label: "Are your dates flexible?",
                required: false,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
            ],
          },
        },
      );

      const formData = new FormData();
      formData.append("SOME_OTHER_DATA", "hello");
      // Don't append FLEXIBLE_DATES to simulate unchecked state

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      if (res.status !== 200) {
        const errorClone = res.clone();
        console.log("Response status:", res.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(res.status).toBe(200);

      // Verify the lead was created with flexibleDates set to false
      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);
      expect(leads[0].flexibleDates).toBe(false);
    });

    it("should handle form submission with conditional fields correctly", async () => {
      const form = await formFactory.create(
        {},
        {
          transient: {
            fields: [
              {
                type: "EVENT_TYPE",
                label: "Event Type",
                required: true,
                width: "full",
                order: 0,
                rowBreakAfter: false,
              },
              {
                type: "GUEST_COUNT",
                label: "Guest Count",
                required: true,
                width: "full",
                order: 1,
                rowBreakAfter: false,
                hideForEventTypes: ["corporate_meeting"],
              },
              {
                type: "ROOM_COUNT",
                label: "Room Count",
                required: true,
                width: "full",
                order: 2,
                rowBreakAfter: false,
                hideForEventTypes: ["wedding"],
              },
            ],
          },
        },
      );

      // Test wedding submission - GUEST_COUNT required, ROOM_COUNT hidden
      const weddingData = new FormData();
      weddingData.append("EVENT_TYPE", "wedding");
      weddingData.append("GUEST_COUNT", "100");
      // ROOM_COUNT omitted since it's hidden for weddings

      const { app } = createApp();
      const weddingRes = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: weddingData,
      });

      if (weddingRes.status !== 200) {
        const errorClone = weddingRes.clone();
        console.log("Response status:", weddingRes.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(weddingRes.status).toBe(200);

      // Verify wedding lead was created with correct values
      const weddingLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.eventType, "wedding"));
      expect(weddingLeads).toHaveLength(1);
      expect(weddingLeads[0]).toMatchObject({
        eventType: "wedding",
        guestCount: 100,
        roomCount: null, // Hidden field should be null
      });

      // Test corporate meeting submission - ROOM_COUNT required, GUEST_COUNT hidden
      const corporateData = new FormData();
      corporateData.append("EVENT_TYPE", "corporate_meeting");
      corporateData.append("ROOM_COUNT", "50");
      // GUEST_COUNT omitted since it's hidden for corporate meetings

      const corporateRes = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: corporateData,
      });

      if (corporateRes.status !== 200) {
        const errorClone = corporateRes.clone();
        console.log("Response status:", corporateRes.status);
        console.log("Response text:", await errorClone.text());
      }

      expect(corporateRes.status).toBe(200);

      // Verify corporate lead was created with correct values
      const corporateLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.eventType, "corporate_meeting"));
      expect(corporateLeads).toHaveLength(1);
      expect(corporateLeads[0]).toMatchObject({
        eventType: "corporate_meeting",
        roomCount: 50,
        guestCount: null, // Hidden field should be null
      });
    });
  });

  describe("Form Submission with Files", () => {
    it("should save file references to lead_file table when form is submitted with file uploads", async () => {
      const form = await createTestFormWithFileUpload();

      const fileId = "123e4567-e89b-12d3-a456-426614174001";
      const fileName = "test-document.pdf";
      const fileSize = "1024";
      const fileType = "application/pdf";
      const uploadKey =
        "pending/123e4567-e89b-12d3-a456-426614174000/test-document.pdf";
      const keyWithoutStatusPrefix =
        "123e4567-e89b-12d3-a456-426614174000/test-document.pdf";
      const targetSubmittedKey = `submitted/${keyWithoutStatusPrefix}`;

      const testContent = await createTestFileInS3(uploadKey, fileType);
      const formData = createFormDataWithFile({
        fileId,
        fileName,
        fileSize,
        fileType,
        uploadKey,
        keyWithoutStatusPrefix,
      });

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(1);

      await verifyFileMovedToSubmitted(
        uploadKey,
        targetSubmittedKey,
        testContent,
      );

      // Verify that the lead_file records are updated with the new S3 key
      const updatedFileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(updatedFileRecords).toHaveLength(1);
      expect(updatedFileRecords[0].s3Key).toBe(targetSubmittedKey);
      expect(updatedFileRecords[0].s3KeyWithoutStatusPrefix).toBe(
        keyWithoutStatusPrefix,
      );
    });

    it("should save multiple file references when multiple files are uploaded", async () => {
      const form = await createTestFormWithFileUpload();
      const uploadGroupId = "123e4567-e89b-12d3-a456-426614174000";

      const fileData = [
        {
          fileId: "123e4567-e89b-12d3-a456-426614174001",
          fileName: "document1.pdf",
          fileSize: "1024",
          fileType: "application/pdf",
          uploadKey: `pending/${uploadGroupId}/document1.pdf`,
          keyWithoutStatusPrefix: `${uploadGroupId}/document1.pdf`,
        },
        {
          fileId: "123e4567-e89b-12d3-a456-426614174002",
          fileName: "document2.jpg",
          fileSize: "2048",
          fileType: "image/jpeg",
          uploadKey: `pending/${uploadGroupId}/document2.jpg`,
          keyWithoutStatusPrefix: `${uploadGroupId}/document2.jpg`,
        },
      ];

      const formData = new FormData();
      formData.append("FIRST_NAME", "John");
      formData.append("LAST_NAME", "Doe");
      formData.append("EMAIL", "<EMAIL>");

      fileData.forEach((file, index) => {
        formData.append(`FILE_UPLOAD[${index}][fileId]`, file.fileId);
        formData.append(`FILE_UPLOAD[${index}][fileName]`, file.fileName);
        formData.append(`FILE_UPLOAD[${index}][fileSize]`, file.fileSize);
        formData.append(`FILE_UPLOAD[${index}][fileType]`, file.fileType);
        formData.append(`FILE_UPLOAD[${index}][uploadKey]`, file.uploadKey);
        formData.append(
          `FILE_UPLOAD[${index}][keyWithoutStatusPrefix]`,
          file.keyWithoutStatusPrefix,
        );
      });

      // Create test files in S3
      for (const file of fileData) {
        await createTestFileInS3(file.uploadKey, file.fileType);
      }

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(2);

      // Wait for file movement to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Verify the database records are updated with the new S3 keys
      const updatedFileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(updatedFileRecords).toHaveLength(2);

      for (const file of fileData) {
        const targetSubmittedKey = `submitted/${file.keyWithoutStatusPrefix}`;

        // Verify file exists in the submitted location
        try {
          const submittedFileResponse = await s3Client.send(
            new GetObjectCommand({
              Bucket: s3UploadConfig.bucket,
              Key: targetSubmittedKey,
            }),
          );
          expect(submittedFileResponse).toBeDefined();

          // Verify original file is gone
          try {
            await s3Client.send(
              new GetObjectCommand({
                Bucket: s3UploadConfig.bucket,
                Key: file.uploadKey,
              }),
            );
            expect(false).toBe(true); // Should not reach here
          } catch (error: any) {
            expect(error.name).toBe("NoSuchKey");
          }

          // Clean up the test file
          await s3Client.send(
            new DeleteObjectCommand({
              Bucket: s3UploadConfig.bucket,
              Key: targetSubmittedKey,
            }),
          );
        } catch (e) {
          console.error("Error checking or cleaning up test file:", e);
        }

        // Verify the file record has been updated with the new path
        const fileRecord = updatedFileRecords.find(
          (record) =>
            record.fileName === file.fileName &&
            record.contentType === file.fileType,
        );

        expect(fileRecord).toBeDefined();
        expect(fileRecord!.s3Key).toBe(targetSubmittedKey);
        expect(fileRecord!.s3KeyWithoutStatusPrefix).toBe(
          file.keyWithoutStatusPrefix,
        );
      }
    });

    it("should handle form submission with no files when field is optional", async () => {
      const form = await createTestFormWithFileUpload();

      const formData = createFormDataWithFile();

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(200);

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(1);

      const fileRecords = await db
        .select()
        .from(leadFile)
        .where(eq(leadFile.leadId, leads[0].id));
      expect(fileRecords).toHaveLength(0);
    });

    it("should reject form submission when required file upload is missing", async () => {
      const form = await createTestFormWithFileUpload({ fileRequired: true });

      const formData = createFormDataWithFile();

      const { app } = createApp();
      const res = await app.request(`/${form.id}/submission`, {
        method: "POST",
        body: formData,
      });

      expect(res.status).toBe(422);

      const document = parseHTML(await res.text());
      const fileUploadField = getByTestId(
        document,
        "label-FILE_UPLOAD",
      ).closest(".ef-field");
      expect(fileUploadField).toHaveTextContent("Required");
      expect(fileUploadField).toHaveClass("has-error");

      const leads = await db.select().from(lead);
      expect(leads).toHaveLength(0);
    });
  });
});

async function createTestFormWithFileUpload(
  options: { fileRequired?: boolean } = {},
) {
  return formFactory.create(
    {},
    {
      transient: {
        fields: [
          {
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            order: 0,
            rowBreakAfter: false,
          },
          {
            type: "LAST_NAME",
            label: "Last Name",
            required: true,
            width: "full",
            order: 1,
            rowBreakAfter: false,
          },
          {
            type: "EMAIL",
            label: "Email",
            required: true,
            width: "full",
            order: 2,
            rowBreakAfter: false,
          },
          {
            type: "FILE_UPLOAD",
            label: "Documents",
            required: options.fileRequired ?? false,
            width: "full",
            order: 3,
            rowBreakAfter: false,
          },
        ],
      },
    },
  );
}

// Test helper to create a test file in S3
async function createTestFileInS3(uploadKey: string, fileType: string) {
  const testContent = "Test PDF File Contents";
  const buffer = Buffer.from(testContent);

  await s3Client.send(
    new PutObjectCommand({
      Bucket: s3UploadConfig.bucket,
      Key: uploadKey,
      Body: buffer,
      ContentType: fileType,
    }),
  );

  return testContent;
}

// Test helper to create form data with basic fields and optional file
function createFormDataWithFile(fileInfo?: {
  fileId: string;
  fileName: string;
  fileSize: string;
  fileType: string;
  uploadKey: string;
  keyWithoutStatusPrefix: string;
}) {
  const formData = new FormData();
  formData.append("FIRST_NAME", "John");
  formData.append("LAST_NAME", "Doe");
  formData.append("EMAIL", "<EMAIL>");

  if (fileInfo) {
    formData.append("FILE_UPLOAD[0][fileId]", fileInfo.fileId);
    formData.append("FILE_UPLOAD[0][fileName]", fileInfo.fileName);
    formData.append("FILE_UPLOAD[0][fileSize]", fileInfo.fileSize);
    formData.append("FILE_UPLOAD[0][fileType]", fileInfo.fileType);
    formData.append("FILE_UPLOAD[0][uploadKey]", fileInfo.uploadKey);
    formData.append(
      "FILE_UPLOAD[0][keyWithoutStatusPrefix]",
      fileInfo.keyWithoutStatusPrefix,
    );
  }

  return formData;
}

// Helper to verify file was moved from pending to submitted prefix
async function verifyFileMovedToSubmitted(
  uploadKey: string,
  targetSubmittedKey: string,
  expectedContent: string,
) {
  // Wait a bit for async operations to complete
  await new Promise((resolve) => setTimeout(resolve, 100));

  try {
    try {
      await s3Client.send(
        new GetObjectCommand({
          Bucket: s3UploadConfig.bucket,
          Key: uploadKey,
        }),
      );

      expect(false).toBe(true);
    } catch (error: any) {
      expect(error.name).toBe("NoSuchKey");
    }

    const submittedFileResponse = await s3Client.send(
      new GetObjectCommand({
        Bucket: s3UploadConfig.bucket,
        Key: targetSubmittedKey,
      }),
    );

    expect(submittedFileResponse).toBeDefined();

    if (submittedFileResponse.Body) {
      const chunks = [];
      for await (const chunk of submittedFileResponse.Body as any) {
        chunks.push(Buffer.from(chunk));
      }
      const content = Buffer.concat(chunks).toString("utf-8");
      expect(content).toBe(expectedContent);
    }
  } finally {
    try {
      await s3Client.send(
        new DeleteObjectCommand({
          Bucket: s3UploadConfig.bucket,
          Key: targetSubmittedKey,
        }),
      );
    } catch (e) {
      // Ignore errors during cleanup
    }
  }
}
