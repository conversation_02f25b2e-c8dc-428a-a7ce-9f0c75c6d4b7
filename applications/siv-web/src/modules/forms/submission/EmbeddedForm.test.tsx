import { describe, expect, it, vi } from "vitest";
import {
  render,
  screen,
  waitFor,
  within,
  fireEvent,
} from "@testing-library/react";
import { EmbeddedForm, type FormValue } from "./EmbeddedForm";
import { formFactory } from "../../admin/forms/form-test-factories";
import { type FieldDefinition, PREDEFINED_FIELDS } from "../builder/fieldTypes";
import userEvent from "@testing-library/user-event";
import { useState, useEffect } from "react";
import type { Form, FormField } from "../builder/types";
import { createTestField } from "@/modules/admin/forms/new-form-test-factories";
import { z } from "zod";

// Add the type definition for intlTelInput
declare global {
  interface Window {
    intlTelInput: any;
  }

  var intlTelInput: any;
}

const mockOnChange = vi.fn();

// Extend PREDEFINED_FIELDS to allow adding custom fields in tests
declare module "../builder/fieldTypes" {
  interface PredefinedFieldsType {
    [key: string]: FieldDefinition;
  }
}

// Add the isCheckboxField function
function isCheckboxField(field: FieldDefinition): field is FieldDefinition & {
  inputType: "CHECKBOX";
  defaultCheckboxText: string;
} {
  return field.inputType === "CHECKBOX";
}

type TestContainer = Document | HTMLElement;

function getAllVisibleFields(
  container: TestContainer = document,
): NodeListOf<Element> {
  return container.querySelectorAll('[data-testid^="field-"]');
}

function getFieldsInRow(fields: Element[], startIndex: number): Element[] {
  const result = [];
  let currentIndex = startIndex;

  // Add the first field in this logical row
  const firstField = fields[currentIndex];
  result.push(firstField);
  currentIndex++;

  // If this field has row-break-after, we're done with this row
  if (firstField.getAttribute("data-row-break-after") === "true") {
    return result;
  }

  // If it's full-width, it's always a row by itself
  if (firstField.getAttribute("data-width") === "full") {
    return result;
  }

  // If we have more fields, and the next one doesn't have row-break before it
  // (indicated by the previous field having row-break-after), add it to the row
  while (
    currentIndex < fields.length &&
    fields[currentIndex - 1].getAttribute("data-row-break-after") !== "true" &&
    result.length < 2 && // Maximum of 2 half-width fields per row
    fields[currentIndex].getAttribute("data-width") === "half"
  ) {
    result.push(fields[currentIndex]);
    currentIndex++;

    // If this field has row-break-after, we're done with this row
    if (
      fields[currentIndex - 1].getAttribute("data-row-break-after") === "true"
    ) {
      break;
    }
  }

  return result;
}

// Helper function to get all logical rows based on CSS rules
function getLogicalRows(container: TestContainer = document): Element[][] {
  const fields = Array.from(getAllVisibleFields(container));
  const rows = [];
  let currentIndex = 0;

  while (currentIndex < fields.length) {
    const fieldsInRow = getFieldsInRow(fields, currentIndex);
    rows.push(fieldsInRow);
    currentIndex += fieldsInRow.length;
  }

  return rows;
}

// Helper function to find a logical row containing the specified field
function findRowWithField(
  fieldType: string,
  container: TestContainer = document,
): Element[] | undefined {
  const rows = getLogicalRows(container);
  return rows.find((row) =>
    row.some(
      (field) => field.getAttribute("data-testid") === `field-${fieldType}`,
    ),
  );
}

describe("EmbeddedForm", () => {
  describe("Label Display Logic", () => {
    it("should display label for non-checkbox field when provided", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: "Custom First Name",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-FIRST_NAME")).toHaveTextContent(
        "Custom First Name",
      );
    });

    it("should display default label for non-checkbox field when label is null", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: null,
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-FIRST_NAME")).toHaveTextContent(
        "First Name",
      );
    });

    it("should not display label text for checkbox when label is null", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "MARKETING_CONSENT",
            label: null,
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const label = screen.queryByTestId("label-MARKETING_CONSENT");
      expect(label?.textContent).toEqual("");
      expect(screen.getByRole("checkbox")).toBeInTheDocument();
    });

    it("should display custom label for checkbox when provided", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "MARKETING_CONSENT",
            label: "Custom Marketing Label",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-MARKETING_CONSENT")).toHaveTextContent(
        "Custom Marketing Label",
      );
    });

    it("should display default label for FLEXIBLE_DATES when label is null", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FLEXIBLE_DATES",
            label: null,
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-FLEXIBLE_DATES")).toHaveTextContent(
        "Are your dates flexible?",
      );
    });

    it("should display custom label for FLEXIBLE_DATES when provided", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FLEXIBLE_DATES",
            label: "Custom Flexible Label",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-FLEXIBLE_DATES")).toHaveTextContent(
        "Custom Flexible Label",
      );
    });
  });

  describe("Required Field Indicators", () => {
    it("should show required indicator for required fields", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const label = screen.getByTestId("label-FIRST_NAME");
      expect(label).toContainHTML("*");
    });

    it("should show required indicator inline before checkbox text for required checkbox fields", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "MARKETING_CONSENT",
            label: null,
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);

      // Verify that the required indicator exists
      const requiredIndicator = screen.getByTestId(
        "required-indicator-MARKETING_CONSENT",
      );
      expect(requiredIndicator).toBeInTheDocument();
      expect(requiredIndicator).toHaveTextContent("*");

      // Verify the indicator is within the label
      const checkboxLabel = screen.getByTestId(
        "checkbox-label-MARKETING_CONSENT",
      );
      expect(checkboxLabel).toContainElement(requiredIndicator);
    });

    it("should position asterisk correctly in required checkbox fields with no labels", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "MARKETING_CONSENT",
            label: null,
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);

      // Get the checkbox label element using the test ID
      const checkboxLabel = screen.getByTestId(
        "checkbox-label-MARKETING_CONSENT",
      );

      // Get the required indicator using its test ID
      const requiredIndicator = screen.getByTestId(
        "required-indicator-MARKETING_CONSENT",
      );

      // Verify the required indicator exists and contains the asterisk
      expect(requiredIndicator).toBeInTheDocument();
      expect(requiredIndicator).toHaveTextContent("*");

      // Verify it's inside the checkbox label
      expect(checkboxLabel).toContainElement(requiredIndicator);

      // Verify text order - get the full text content and check positions
      const labelText = checkboxLabel.textContent || "";
      const asteriskIndex = labelText.indexOf("*");
      const checkboxTextIndex = labelText.indexOf(
        isCheckboxField(PREDEFINED_FIELDS.MARKETING_CONSENT)
          ? PREDEFINED_FIELDS.MARKETING_CONSENT.defaultCheckboxText
          : "",
      );

      // Asterisk should come before the checkbox text
      expect(asteriskIndex).toBeLessThan(checkboxTextIndex);
    });

    it("should not show required indicator for optional fields", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DESCRIPTION",
            label: "Description",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const label = screen.getByTestId("label-EVENT_DESCRIPTION");
      expect(label).not.toContainHTML("*");
    });

    it("should show required indicator for optional checkbox fields", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "MARKETING_CONSENT",
            label: null,
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);

      // Get the checkbox label
      const checkboxLabel = screen.getByTestId(
        "checkbox-label-MARKETING_CONSENT",
      );

      // Verify the required indicator doesn't exist
      expect(
        screen.queryByTestId("required-indicator-MARKETING_CONSENT"),
      ).not.toBeInTheDocument();

      // Verify the label doesn't contain an asterisk
      expect(checkboxLabel).not.toHaveTextContent("*");
    });
  });

  describe("Field Width Layout", () => {
    it("should render full-width fields with appropriate class", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const field = screen
        .getByTestId("form-container")
        .querySelector(".ef-field");
      expect(field).toHaveAttribute("data-width", "full");
    });

    it("should render half-width fields with appropriate class", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "half",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const field = screen
        .getByTestId("form-container")
        .querySelector(".ef-field");
      expect(field).toHaveAttribute("data-width", "half");
    });
  });

  describe("Success State", () => {
    it("should render success message when success prop is true", () => {
      const form = formFactory.build();
      render(<EmbeddedForm form={form} success={true} />);
      expect(screen.getByTestId("submit-success-message")).toBeInTheDocument();
    });

    it("should not render form fields when in success state", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} success={true} />);
      expect(screen.queryByTestId("label-FIRST_NAME")).not.toBeInTheDocument();
    });
  });

  describe("Error Display", () => {
    it("should display field-level errors", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EMAIL",
            label: "Email",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      const errors = {
        EMAIL: ["Please enter a valid email address"],
      };

      render(<EmbeddedForm form={form} errors={errors} />);
      expect(
        screen.getByText("Please enter a valid email address"),
      ).toBeInTheDocument();
    });

    it("should deduplicate duplicate error messages", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "PHONE",
            label: "Phone Number",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Create errors with duplicate messages
      const errors = {
        PHONE: [
          "Please enter a valid US phone number",
          "Please enter a valid US phone number", // Duplicate message
        ],
      };

      render(<EmbeddedForm form={form} errors={errors} />);

      // The error message should only appear once
      const errorMessages = screen.getAllByText(
        "Please enter a valid US phone number",
      );
      expect(errorMessages).toHaveLength(1);
    });

    it("should deduplicate errors across START_DATE and END_DATE fields in desktop view", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Event Dates",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Create errors with the same message for both START_DATE and END_DATE
      const errors = {
        START_DATE: ["Invalid date"],
        END_DATE: ["Invalid date"],
      };

      // Set isMobileGuess to false to ensure desktop mode
      render(
        <EmbeddedForm form={form} errors={errors} isMobileGuess={false} />,
      );

      // The error message should only appear once in desktop view
      const errorMessages = screen.getAllByText("Invalid date");
      expect(errorMessages).toHaveLength(1);
    });

    it("should display form-level errors", () => {
      const form = formFactory.build();
      const errors = {
        _form: ["Something went wrong with your submission"],
      };

      render(<EmbeddedForm form={form} errors={errors} />);
      expect(
        screen.getByText("Something went wrong with your submission"),
      ).toBeInTheDocument();
    });
  });

  describe("Theme Application", () => {
    it("should apply theme styles to form container", () => {
      const form = formFactory.build({
        theme: {
          ...formFactory.build().theme,
          primaryColor: "#ff0000",
          backgroundColor: "#ffffff",
          textColor: "#000000",
        },
      });

      render(<EmbeddedForm form={form} />);
      const container = screen.getByTestId("form-container");
      const styles = window.getComputedStyle(container);

      expect(container).toHaveStyle({
        "--ef-primary": "#ff0000",
        "--ef-background": "#ffffff",
        "--ef-text": "#000000",
      });
    });

    it("should apply button alignment from theme", () => {
      const form = formFactory.build({
        theme: {
          ...formFactory.build().theme,
          buttonAlignment: "left" as const,
        },
      });

      render(<EmbeddedForm form={form} />);
      const submitWrapper = screen
        .getByTestId("form-container")
        .querySelector(".ef-submit-wrapper");
      expect(submitWrapper).toHaveAttribute("data-alignment", "left");
    });
  });

  describe("Checkbox Value Handling", () => {
    it('should check single checkbox when value is "on"', () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FLEXIBLE_DATES",
            label: "Flexible Dates",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} values={{ FLEXIBLE_DATES: "on" }} />);
      const checkbox = screen.getByRole("checkbox") as HTMLInputElement;
      expect(checkbox.checked).toBe(true);
    });

    it('should check single checkbox when value is "true"', () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FLEXIBLE_DATES",
            label: "Flexible Dates",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} values={{ FLEXIBLE_DATES: "true" }} />);
      const checkbox = screen.getByRole("checkbox") as HTMLInputElement;
      expect(checkbox.checked).toBe(true);
    });

    it('should set value="true" on single checkboxes', () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "FLEXIBLE_DATES",
            label: "Flexible Dates",
            required: false,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const checkbox = screen.getByRole("checkbox") as HTMLInputElement;
      expect(checkbox.value).toBe("true");
    });
  });

  describe("Date Range Field", () => {
    it("should render date range picker for EVENT_DATE_RANGE field", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Event Dates",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("input-EVENT_DATE_RANGE")).toBeInTheDocument();
      expect(
        screen.getByTestId("date-range-picker-EVENT_DATE_RANGE"),
      ).toBeInTheDocument();
      expect(screen.getByTestId("date-range-picker-button")).toHaveTextContent(
        "Pick a date range",
      );
    });

    it("should show required indicator for required date range field", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Event Dates",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      const label = screen.getByTestId("label-EVENT_DATE_RANGE");
      expect(label).toContainHTML("*");
    });

    it("should display field-level errors for date range field", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Event Dates",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      const errors = {
        EVENT_DATE_RANGE: ["End date must be on or after start date"],
      };

      render(<EmbeddedForm form={form} errors={errors} />);
      expect(
        screen.getByText("End date must be on or after start date"),
      ).toBeInTheDocument();
    });

    it("should display custom label for date range field when provided", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Custom Date Range Label",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toHaveTextContent(
        "Custom Date Range Label",
      );
    });

    it("should display default label for date range field when label is null", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: null,
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      render(<EmbeddedForm form={form} />);
      expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toHaveTextContent(
        "Event Dates",
      );
    });

    it("should retain date values when form is resubmitted with errors", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_DATE_RANGE",
            label: "Event Dates",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Create a date for testing (use fixed dates to avoid test flakiness)
      const startDate = new Date("2025-01-15T12:00:00.000Z");
      const endDate = new Date("2025-01-20T12:00:00.000Z");

      // Mock values that would be returned from server after validation failure
      const values = {
        START_DATE: startDate.toISOString(),
        END_DATE: endDate.toISOString(),
      };

      // Mock errors
      const errors = {
        EMAIL: ["Please enter a valid email address"],
      };

      // Render form with values and errors
      render(<EmbeddedForm form={form} values={values} errors={errors} />);

      // In desktop view the button should show the selected date range
      return waitFor(
        () => {
          const dateRangeButton = screen.getByTestId(
            "date-range-picker-button",
          );
          expect(dateRangeButton).toHaveTextContent(
            "Jan 15, 2025 - Jan 20, 2025",
          );
        },
        { timeout: 2000 },
      );
    });
  });

  describe("Conditional Field Visibility", () => {
    const TestWrapper = ({
      form,
      initialEventType,
    }: {
      form: Form;
      initialEventType: string;
    }) => {
      const [values, setValues] = useState<Record<string, FormValue>>({
        EVENT_TYPE: initialEventType,
      });

      const handleChange = (name: string, value: FormValue) => {
        setValues((prev) => ({ ...prev, [name]: value }));
      };

      return (
        <EmbeddedForm form={form} values={values} onChange={handleChange} />
      );
    };

    it("should hide fields based on selected event type", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
          createTestField({
            type: "ROOM_COUNT",
            label: "Number of Rooms",
            required: true,
            width: "full",
            order: 2,
            hideForEventTypes: ["wedding"],
          }),
        ],
      });

      // Initial render with wedding event type
      const { rerender } = render(
        <EmbeddedForm form={form} values={{ EVENT_TYPE: "wedding" }} />,
      );
      expect(screen.getByTestId("label-GUEST_COUNT")).toBeInTheDocument();
      expect(screen.queryByTestId("label-ROOM_COUNT")).not.toBeInTheDocument();

      // Re-render with corporate meeting event type
      rerender(
        <EmbeddedForm
          form={form}
          values={{ EVENT_TYPE: "corporate_meeting" }}
        />,
      );
      expect(screen.queryByTestId("label-GUEST_COUNT")).not.toBeInTheDocument();
      expect(screen.getByTestId("label-ROOM_COUNT")).toBeInTheDocument();
    });

    it("should clear values of hidden fields", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
        ],
      });

      // Initial render with wedding event type and guest count value
      const { rerender } = render(
        <EmbeddedForm
          form={form}
          values={{
            EVENT_TYPE: "wedding",
            GUEST_COUNT: "100",
          }}
        />,
      );

      // Verify guest count is shown with value
      const guestInput = screen.getByTestId(
        "input-GUEST_COUNT",
      ) as HTMLInputElement;
      expect(guestInput.value).toBe("100");

      // Change to corporate meeting
      rerender(
        <EmbeddedForm
          form={form}
          values={{
            EVENT_TYPE: "corporate_meeting",
            GUEST_COUNT: "100", // This should be ignored since field is hidden
          }}
        />,
      );

      // Verify guest count field is not rendered
      expect(screen.queryByTestId("input-GUEST_COUNT")).not.toBeInTheDocument();
    });

    it("should always show EVENT_TYPE field regardless of hide rules", () => {
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: ["wedding", "corporate_meeting"], // These should be ignored
          }),
        ],
      });

      render(<EmbeddedForm form={form} values={{ EVENT_TYPE: "wedding" }} />);
      expect(screen.getByTestId("label-EVENT_TYPE")).toBeInTheDocument();
    });

    it("should update field visibility when user changes event type", async () => {
      const user = userEvent.setup();
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "GUEST_COUNT",
            label: "Number of Guests",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: ["corporate_meeting"],
          }),
          createTestField({
            type: "ROOM_COUNT",
            label: "Number of Rooms",
            required: true,
            width: "full",
            order: 2,
            hideForEventTypes: ["wedding"],
          }),
        ],
      });

      render(<TestWrapper form={form} initialEventType="" />);

      // Initially all fields should be visible since no event type is selected
      expect(screen.getByTestId("label-EVENT_TYPE")).toBeInTheDocument();
      expect(screen.getByTestId("label-GUEST_COUNT")).toBeInTheDocument();
      expect(screen.getByTestId("label-ROOM_COUNT")).toBeInTheDocument();

      // Select 'Wedding' event type using selectOptions
      const eventTypeSelect = screen.getByTestId("input-EVENT_TYPE");
      await user.selectOptions(eventTypeSelect, "wedding");

      // Guest count should be visible, room count should be hidden
      await waitFor(() => {
        expect(screen.getByTestId("label-GUEST_COUNT")).toBeInTheDocument();
        expect(
          screen.queryByTestId("label-ROOM_COUNT"),
        ).not.toBeInTheDocument();
      });

      // Change to 'Corporate Meeting'
      await user.selectOptions(eventTypeSelect, "corporate_meeting");

      // Guest count should be hidden, room count should be visible
      await waitFor(() => {
        expect(
          screen.queryByTestId("label-GUEST_COUNT"),
        ).not.toBeInTheDocument();
        expect(screen.getByTestId("label-ROOM_COUNT")).toBeInTheDocument();
      });
    });

    // New tests for conditional event needs
    describe("Conditional Event Needs Options", () => {
      it("should display wedding-specific options when wedding event type is selected", async () => {
        const form = formFactory.build({
          fields: [
            createTestField({
              type: "EVENT_TYPE",
              label: "Event Type",
              required: true,
              width: "full",
              order: 0,
            }),
            createTestField({
              type: "EVENT_NEEDS",
              label: "Event Needs",
              required: false,
              width: "full",
              order: 1,
            }),
          ],
        });

        render(<EmbeddedForm form={form} values={{ EVENT_TYPE: "wedding" }} />);

        // Wedding options should be visible
        expect(screen.getByText("Rehearsal")).toBeInTheDocument();
        expect(screen.getByText("Rehearsal Dinner")).toBeInTheDocument();

        // Corporate options should NOT be visible
        expect(screen.queryByText("Meeting Space")).not.toBeInTheDocument();
      });

      it("should display corporate options for corporate_meeting event type", async () => {
        const form = formFactory.build({
          fields: [
            createTestField({
              type: "EVENT_TYPE",
              label: "Event Type",
              required: true,
              width: "full",
              order: 0,
            }),
            createTestField({
              type: "EVENT_NEEDS",
              label: "Event Needs",
              required: false,
              width: "full",
              order: 1,
            }),
          ],
        });

        render(
          <EmbeddedForm
            form={form}
            values={{ EVENT_TYPE: "corporate_meeting" }}
          />,
        );

        // Corporate options should be visible
        expect(screen.getByText("Meeting Space")).toBeInTheDocument();
        expect(screen.getByText("Catering")).toBeInTheDocument();

        // Wedding options should NOT be visible
        expect(screen.queryByText("Rehearsal")).not.toBeInTheDocument();
      });

      it("should filter out invalid options when event type changes", async () => {
        const user = userEvent.setup();

        // Create a component that tracks state
        const TestEventNeedsWrapper = () => {
          const [values, setValues] = useState<Record<string, FormValue>>({
            EVENT_TYPE: "wedding",
            EVENT_NEEDS: ["REHEARSAL", "REHEARSAL_DINNER"],
          });

          const handleChange = (name: string, value: FormValue) => {
            setValues((prev) => ({ ...prev, [name]: value }));
          };

          const form = formFactory.build({
            fields: [
              createTestField({
                type: "EVENT_TYPE",
                label: "Event Type",
                required: true,
                width: "full",
                order: 0,
              }),
              createTestField({
                type: "EVENT_NEEDS",
                label: "Event Needs",
                required: false,
                width: "full",
                order: 1,
              }),
            ],
          });

          return (
            <EmbeddedForm form={form} values={values} onChange={handleChange} />
          );
        };

        render(<TestEventNeedsWrapper />);

        // Initially wedding options should be selected
        expect(screen.getByText("Rehearsal")).toBeInTheDocument();
        const rehearsalCheckbox = screen
          .getByLabelText("Rehearsal")
          .closest("input") as HTMLInputElement;
        expect(rehearsalCheckbox.checked).toBe(true);

        // Change to corporate meeting
        const eventTypeSelect = screen.getByTestId("input-EVENT_TYPE");
        await user.selectOptions(eventTypeSelect, "corporate_meeting");

        // Wedding needs should be filtered out and corporate needs should be visible
        await waitFor(() => {
          expect(screen.queryByText("Rehearsal")).not.toBeInTheDocument();
          expect(screen.getByText("Meeting Space")).toBeInTheDocument();
        });
      });
    });
  });
});

describe("Field Visibility", () => {
  it("should respect hideForEventTypes when grouping fields", () => {
    // Create a form with appropriate row breaks and hidden fields
    const formFields = [
      createTestField({
        id: "1",
        type: "EVENT_TYPE",
        label: "Event Type",
        width: "full", // Full width field breaks row
        order: 0,
      }),
      createTestField({
        id: "2",
        type: "FIRST_NAME",
        label: "First Name",
        width: "half",
        order: 1,
        // No row break, should pair with LAST_NAME
      }),
      createTestField({
        id: "3",
        type: "LAST_NAME",
        label: "Last Name",
        width: "half",
        order: 2,
        rowBreakAfter: true, // End row after this field
      }),
      createTestField({
        id: "4",
        type: "ROOM_COUNT",
        label: "Room Count",
        width: "half",
        order: 3,
        // Hide this field for 'corporate_meeting' event type
        hideForEventTypes: ["corporate_meeting"],
        // No row break, would pair with BUDGET
      }),
      createTestField({
        id: "5",
        type: "BUDGET",
        label: "Budget",
        width: "half",
        order: 4,
        // Last field, no row break needed
      }),
    ];

    const form = formFactory.build({
      fields: formFields,
    });

    // Render with corporate_meeting as the event type
    render(
      <EmbeddedForm form={form} values={{ EVENT_TYPE: "corporate_meeting" }} />,
    );

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Row 1: Should contain EVENT_TYPE
    const row1 = logicalRows[0];
    expect(row1.length).toBe(1);
    expect(row1[0].getAttribute("data-testid")).toBe("field-EVENT_TYPE");

    // Row 2: Should contain FIRST_NAME and LAST_NAME
    const row2 = logicalRows[1];
    expect(row2.length).toBe(2);
    expect(row2[0].getAttribute("data-testid")).toBe("field-FIRST_NAME");
    expect(row2[1].getAttribute("data-testid")).toBe("field-LAST_NAME");

    // Row 3: Should contain only BUDGET (ROOM_COUNT should be hidden)
    const row3 = logicalRows[2];
    expect(row3.length).toBe(1);
    expect(row3[0].getAttribute("data-testid")).toBe("field-BUDGET");
    expect(screen.queryByTestId("field-ROOM_COUNT")).not.toBeInTheDocument();
  });
});

describe("EmbeddedForm with specific row configurations", () => {
  // Helper to create test fields with specified widths and row breaks
  const createFieldsWithExplicitRows = () => {
    return [
      // Row 1: Single half-width field alone in a row
      createTestField({
        id: "1",
        type: "FIRST_NAME",
        label: "First Name",
        width: "half",
        order: 0,
        rowBreakAfter: true, // Force a row break after this field
      }),

      // Row 2: Full-width field in its own row
      createTestField({
        id: "2",
        type: "EMAIL",
        label: "Email",
        width: "full", // Full width fields always break rows
        order: 1,
      }),

      // Row 3: Two half-width fields in the same row
      createTestField({
        id: "3",
        type: "CITY",
        label: "City",
        width: "half",
        order: 2,
      }),
      createTestField({
        id: "4",
        type: "STATE",
        label: "State",
        width: "half",
        order: 3,
        rowBreakAfter: true, // Force a row break after this field
      }),

      // Row 4: Another single half-width field alone in a row
      createTestField({
        id: "5",
        type: "PHONE",
        label: "Phone",
        width: "half",
        order: 4,
        rowBreakAfter: true, // Force a row break after this field
      }),

      // Row 5: Full-width field after solo half-width
      createTestField({
        id: "6",
        type: "EVENT_DESCRIPTION",
        label: "Comments",
        width: "full", // Full width fields always break rows
        order: 5,
      }),
    ];
  };

  it("should render a solo half-width field in its own row", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Check that the first row contains only the first name field
    const firstRow = logicalRows[0];
    expect(firstRow.length).toBe(1);
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-FIRST_NAME");

    // Verify the field maintains its half-width property
    expect(firstRow[0]).toHaveAttribute("data-width", "half");
  });

  it("should render a full-width field in its own row", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Check that the second row contains only the email field
    const secondRow = logicalRows[1];
    expect(secondRow.length).toBe(1);
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-EMAIL");

    // Verify the field maintains its full-width property
    expect(secondRow[0]).toHaveAttribute("data-width", "full");
  });

  it("should render two half-width fields in the same row", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Check that the third row contains both city and state fields
    const thirdRow = logicalRows[2];
    expect(thirdRow.length).toBe(2);
    expect(thirdRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(thirdRow[1].getAttribute("data-testid")).toBe("field-STATE");

    // Verify both fields maintain their half-width property
    expect(thirdRow[0]).toHaveAttribute("data-width", "half");
    expect(thirdRow[1]).toHaveAttribute("data-width", "half");
  });

  it("should correctly render a second solo half-width field in its own row", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Check that the fourth row contains only the phone field
    const fourthRow = logicalRows[3];
    expect(fourthRow.length).toBe(1);
    expect(fourthRow[0].getAttribute("data-testid")).toBe("field-PHONE");

    // Verify the field maintains its half-width property
    expect(fourthRow[0]).toHaveAttribute("data-width", "half");
  });

  it("should correctly render a sequence of different row types", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Verify we have 5 rows total in the exact order specified
    expect(logicalRows).toHaveLength(5);

    // Check the sequence of rows matches our field definitions
    expect(logicalRows[0][0].getAttribute("data-testid")).toBe(
      "field-FIRST_NAME",
    ); // Solo half-width
    expect(logicalRows[1][0].getAttribute("data-testid")).toBe("field-EMAIL"); // Full-width
    expect(logicalRows[2][0].getAttribute("data-testid")).toBe("field-CITY"); // First of two half-width
    expect(logicalRows[2][1].getAttribute("data-testid")).toBe("field-STATE"); // Second of two half-width
    expect(logicalRows[3][0].getAttribute("data-testid")).toBe("field-PHONE"); // Solo half-width
    expect(logicalRows[4][0].getAttribute("data-testid")).toBe(
      "field-EVENT_DESCRIPTION",
    ); // Full-width
  });

  it("should preserve field width attributes regardless of row configuration", () => {
    const fields = createFieldsWithExplicitRows();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Check all fields maintain their width attributes
    const firstNameField = screen.getByTestId("field-FIRST_NAME");
    const emailField = screen.getByTestId("field-EMAIL");
    const cityField = screen.getByTestId("field-CITY");
    const stateField = screen.getByTestId("field-STATE");
    const phoneField = screen.getByTestId("field-PHONE");
    const commentsField = screen.getByTestId("field-EVENT_DESCRIPTION");

    expect(firstNameField).toHaveAttribute("data-width", "half");
    expect(emailField).toHaveAttribute("data-width", "full");
    expect(cityField).toHaveAttribute("data-width", "half");
    expect(stateField).toHaveAttribute("data-width", "half");
    expect(phoneField).toHaveAttribute("data-width", "half");
    expect(commentsField).toHaveAttribute("data-width", "full");
  });
});

describe("EmbeddedForm with rowBreakAfter functionality", () => {
  // Helper to create test fields with rowBreakAfter properties
  const createFieldsWithRowBreaks = () => {
    return [
      // First field: half-width with rowBreakAfter=true
      createTestField({
        id: "1",
        type: "FIRST_NAME",
        label: "First Name",
        width: "half",
        order: 0,
        rowBreakAfter: true,
      }),

      // Second field: half-width, should start a new row due to previous rowBreakAfter
      createTestField({
        id: "2",
        type: "LAST_NAME",
        label: "Last Name",
        width: "half",
        order: 1,
      }),

      // Third field: half-width, should pair with previous field
      createTestField({
        id: "3",
        type: "EMAIL",
        label: "Email",
        width: "half",
        order: 2,
        rowBreakAfter: true,
      }),

      // Fourth field: full-width, should start a new row due to rowBreakAfter
      createTestField({
        id: "4",
        type: "COMPANY",
        label: "Company",
        width: "full",
        order: 3,
      }),

      // Fifth field: half-width
      createTestField({
        id: "5",
        type: "CITY",
        label: "City",
        width: "half",
        order: 4,
      }),

      // Sixth field: half-width, should pair with previous field
      createTestField({
        id: "6",
        type: "STATE",
        label: "State",
        width: "half",
        order: 5,
      }),
    ];
  };

  it("should force a new row after a half-width field with rowBreakAfter=true", () => {
    const fields = createFieldsWithRowBreaks();
    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // Get the logical rows based on CSS layout
    const logicalRows = getLogicalRows();
    expect(logicalRows).toHaveLength(4);

    // First row should contain FIRST_NAME
    const firstRow = logicalRows[0];
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-FIRST_NAME");
    expect(firstRow.length).toBe(1);

    // Second row should contain LAST_NAME and EMAIL
    const secondRow = logicalRows[1];
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-LAST_NAME");
    expect(secondRow[1].getAttribute("data-testid")).toBe("field-EMAIL");
    expect(secondRow.length).toBe(2);

    // Third row should contain COMPANY
    const thirdRow = logicalRows[2];
    expect(thirdRow[0].getAttribute("data-testid")).toBe("field-COMPANY");
    expect(thirdRow.length).toBe(1);

    // Fourth row should contain CITY and STATE
    const fourthRow = logicalRows[3];
    expect(fourthRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(fourthRow[1].getAttribute("data-testid")).toBe("field-STATE");
    expect(fourthRow.length).toBe(2);
  });

  it("should place two half-width fields in the same row when no rowBreakAfter is set", () => {
    // Create a simpler form with just two half-width fields
    const fields = [
      createTestField({
        id: "1",
        type: "CITY",
        label: "City",
        width: "half",
        order: 0,
        rowBreakAfter: false,
      }),
      createTestField({
        id: "2",
        type: "STATE",
        label: "State",
        width: "half",
        order: 1,
      }),
    ];

    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // We should have just one row
    const logicalRows = getLogicalRows();
    expect(logicalRows).toHaveLength(1);

    // The row should contain both fields
    const row = logicalRows[0];
    expect(row[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(row[1].getAttribute("data-testid")).toBe("field-STATE");
    expect(row.length).toBe(2);
  });

  it("should handle full-width fields with rowBreakAfter correctly", () => {
    // Create a form with full-width fields that have rowBreakAfter
    const fields = [
      createTestField({
        id: "1",
        type: "EMAIL",
        label: "Email",
        width: "full",
        order: 0,
        rowBreakAfter: true, // This is redundant but should work
      }),
      createTestField({
        id: "2",
        type: "PHONE",
        label: "Phone",
        width: "half",
        order: 1,
      }),
      createTestField({
        id: "3",
        type: "COMPANY",
        label: "Company",
        width: "half",
        order: 2,
      }),
    ];

    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // We should have two rows
    const logicalRows = getLogicalRows();
    expect(logicalRows).toHaveLength(2);

    // First row should contain only EMAIL
    const firstRow = logicalRows[0];
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-EMAIL");
    expect(firstRow.length).toBe(1);

    // Second row should contain PHONE and COMPANY
    const secondRow = logicalRows[1];
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-PHONE");
    expect(secondRow[1].getAttribute("data-testid")).toBe("field-COMPANY");
    expect(secondRow.length).toBe(2);
  });

  it("should handle the case when the second-to-last field is half-width", () => {
    // Edge case: second-to-last field is half-width
    const fields = [
      createTestField({
        id: "1",
        type: "EMAIL",
        label: "Email",
        width: "full",
        order: 0,
      }),
      createTestField({
        id: "2",
        type: "CITY",
        label: "City",
        width: "half",
        order: 1,
        // This is the second-to-last field
      }),
      createTestField({
        id: "3",
        type: "STATE",
        label: "State",
        width: "half",
        order: 2,
        // This is the last field
      }),
    ];

    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // We should have two rows
    const logicalRows = getLogicalRows();
    expect(logicalRows).toHaveLength(2);

    // First row should contain only EMAIL
    const firstRow = logicalRows[0];
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-EMAIL");
    expect(firstRow.length).toBe(1);

    // Second row should contain CITY and STATE
    const secondRow = logicalRows[1];
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(secondRow[1].getAttribute("data-testid")).toBe("field-STATE");
    expect(secondRow.length).toBe(2);
  });

  it("should handle complex form with multiple half-width fields including second-to-last field", () => {
    // A more complex case with multiple rows and second-to-last field is half-width
    const fields = [
      // Row 1: Two half-width fields
      createTestField({
        id: "1",
        type: "FIRST_NAME",
        label: "First Name",
        width: "half",
        order: 0,
      }),
      createTestField({
        id: "2",
        type: "LAST_NAME",
        label: "Last Name",
        width: "half",
        order: 1,
      }),
      // Row 2: One full-width field
      createTestField({
        id: "3",
        type: "EMAIL",
        label: "Email",
        width: "full",
        order: 2,
      }),
      // Row 3: Two half-width fields including second-to-last and last fields
      createTestField({
        id: "4",
        type: "CITY",
        label: "City",
        width: "half",
        order: 3,
        // This is the second-to-last field
      }),
      createTestField({
        id: "5",
        type: "STATE",
        label: "State",
        width: "half",
        order: 4,
        // This is the last field
      }),
    ];

    const form = formFactory.build({ fields });

    render(<EmbeddedForm form={form} />);

    // We should have three rows
    const logicalRows = getLogicalRows();
    expect(logicalRows).toHaveLength(3);

    // First row should have two fields: FIRST_NAME and LAST_NAME
    const firstRow = logicalRows[0];
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-FIRST_NAME");
    expect(firstRow[1].getAttribute("data-testid")).toBe("field-LAST_NAME");
    expect(firstRow.length).toBe(2);

    // Second row should have EMAIL
    const secondRow = logicalRows[1];
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-EMAIL");
    expect(secondRow.length).toBe(1);

    // Third row should have CITY and STATE
    const thirdRow = logicalRows[2];
    expect(thirdRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(thirdRow[1].getAttribute("data-testid")).toBe("field-STATE");
    expect(thirdRow.length).toBe(2);
  });
});

describe("field width and row breaking behavior", () => {
  it("should correctly display all fields when the postal code field is half-width or full-width", () => {
    // Create a form with city and state (half-width), postal code (initially half-width) and event dates
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "CITY",
          label: "City",
          required: true,
          width: "half",
          order: 0,
        }),
        createTestField({
          type: "STATE",
          label: "State/Province",
          required: true,
          width: "half",
          order: 1,
        }),
        createTestField({
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          width: "half" as const, // Initially half-width
          order: 2,
        }),
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          width: "full" as const,
          order: 3,
        }),
      ],
    });

    // First test with Postal Code as half-width
    const { rerender } = render(<EmbeddedForm form={form} />);

    // Verify all fields are displayed
    expect(screen.getByTestId("label-CITY")).toBeInTheDocument();
    expect(screen.getByTestId("label-STATE")).toBeInTheDocument();
    expect(screen.getByTestId("label-POSTAL_CODE")).toBeInTheDocument();
    expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toBeInTheDocument();

    // Check row organization using our CSS-based helpers
    const logicalRows = getLogicalRows();
    expect(logicalRows.length).toBe(3);

    // First row should contain city and state
    const firstRow = logicalRows[0];
    expect(firstRow.length).toBe(2);
    expect(firstRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(firstRow[1].getAttribute("data-testid")).toBe("field-STATE");

    // Second row should contain postal code (half-width)
    const secondRow = logicalRows[1];
    expect(secondRow.length).toBe(1);
    expect(secondRow[0].getAttribute("data-testid")).toBe("field-POSTAL_CODE");

    // Third row should contain event date range
    const thirdRow = logicalRows[2];
    expect(thirdRow.length).toBe(1);
    expect(thirdRow[0].getAttribute("data-testid")).toBe(
      "field-EVENT_DATE_RANGE",
    );

    // Now test with Postal Code as full-width
    const updatedForm = {
      ...form,
      fields: form.fields.map((field) =>
        field.type === "POSTAL_CODE"
          ? { ...field, width: "full" as const }
          : field,
      ),
    };

    rerender(<EmbeddedForm form={updatedForm} />);

    // Verify all fields are still displayed
    expect(screen.getByTestId("label-CITY")).toBeInTheDocument();
    expect(screen.getByTestId("label-STATE")).toBeInTheDocument();
    expect(screen.getByTestId("label-POSTAL_CODE")).toBeInTheDocument();
    expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toBeInTheDocument();

    // Check updated row organization with full-width postal code
    const updatedRows = getLogicalRows();
    expect(updatedRows.length).toBe(3);

    // First row should still contain city and state
    const updatedFirstRow = updatedRows[0];
    expect(updatedFirstRow.length).toBe(2);
    expect(updatedFirstRow[0].getAttribute("data-testid")).toBe("field-CITY");
    expect(updatedFirstRow[1].getAttribute("data-testid")).toBe("field-STATE");

    // Second row should now contain postal code (full-width)
    const updatedSecondRow = updatedRows[1];
    expect(updatedSecondRow.length).toBe(1);
    expect(updatedSecondRow[0].getAttribute("data-testid")).toBe(
      "field-POSTAL_CODE",
    );
    expect(updatedSecondRow[0].getAttribute("data-width")).toBe("full");

    // Third row should still contain event date range
    const updatedThirdRow = updatedRows[2];
    expect(updatedThirdRow.length).toBe(1);
    expect(updatedThirdRow[0].getAttribute("data-testid")).toBe(
      "field-EVENT_DATE_RANGE",
    );
  });

  it("should handle the edge case with half-width second-to-last field followed by a full-width last field", () => {
    // This specifically tests the case shown in the screenshots where the Postal Code is half-width
    // and is followed by Event Dates which is full-width
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "EVENT_NEEDS",
          label: "Event Needs",
          required: false,
          width: "full",
          order: 0,
        }),
        createTestField({
          type: "ROOM_COUNT",
          label: "Number of Rooms Needed",
          required: false,
          width: "full",
          order: 1,
        }),
        createTestField({
          type: "MEAL_COUNT",
          label: "Number of Meals",
          required: false,
          width: "full",
          order: 2,
        }),
        createTestField({
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "full",
          order: 3,
        }),
        createTestField({
          type: "CITY",
          label: "City",
          required: true,
          width: "half",
          order: 4,
        }),
        createTestField({
          type: "STATE",
          label: "State/Province",
          required: true,
          width: "half",
          order: 5,
        }),
        createTestField({
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          width: "half" as const, // This is the second-to-last field
          order: 6,
        }),
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          width: "full" as const, // This is the last field
          order: 7,
        }),
      ],
    });

    render(<EmbeddedForm form={form} />);

    // Verify all fields are displayed, including the Event Dates field
    expect(screen.getByTestId("label-EVENT_NEEDS")).toBeInTheDocument();
    expect(screen.getByTestId("label-ROOM_COUNT")).toBeInTheDocument();
    expect(screen.getByTestId("label-MEAL_COUNT")).toBeInTheDocument();
    expect(screen.getByTestId("label-PHONE")).toBeInTheDocument();
    expect(screen.getByTestId("label-CITY")).toBeInTheDocument();
    expect(screen.getByTestId("label-STATE")).toBeInTheDocument();
    expect(screen.getByTestId("label-POSTAL_CODE")).toBeInTheDocument();
    expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toBeInTheDocument();

    // Check if EVENT_DATE_RANGE is properly displayed in its own row using our CSS-based layout helpers
    const eventDatesField = screen.getByTestId("field-EVENT_DATE_RANGE");
    const postalCodeField = screen.getByTestId("field-POSTAL_CODE");

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Find the row containing POSTAL_CODE and EVENT_DATE_RANGE
    const postalCodeRow = logicalRows.find((row) =>
      Array.from(row).some(
        (field) => field.getAttribute("data-testid") === "field-POSTAL_CODE",
      ),
    );

    const eventDateRow = logicalRows.find((row) =>
      Array.from(row).some(
        (field) =>
          field.getAttribute("data-testid") === "field-EVENT_DATE_RANGE",
      ),
    );

    // POSTAL_CODE and EVENT_DATE_RANGE should be in different rows
    expect(postalCodeRow).not.toBe(eventDateRow);
    expect(eventDateRow).toContain(eventDatesField);
  });

  it("should fix the issue with EVENT_DATE_RANGE not showing when POSTAL_CODE is half-width", () => {
    // Create a minimal form with just POSTAL_CODE (half-width) and EVENT_DATE_RANGE (full-width)
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "POSTAL_CODE",
          label: "Postal Code",
          required: true,
          width: "half" as const, // Half-width postal code
          order: 0,
        }),
        createTestField({
          type: "EVENT_DATE_RANGE",
          label: "Event Dates",
          required: true,
          width: "full" as const,
          order: 1,
        }),
      ],
    });

    render(<EmbeddedForm form={form} />);

    // Both fields should be present and visible
    expect(screen.getByTestId("label-POSTAL_CODE")).toBeInTheDocument();
    expect(screen.getByTestId("label-EVENT_DATE_RANGE")).toBeInTheDocument();

    // Check that EVENT_DATE_RANGE is in a separate row from POSTAL_CODE
    const postalCodeField = screen.getByTestId("field-POSTAL_CODE");
    const eventDateField = screen.getByTestId("field-EVENT_DATE_RANGE");

    // Get all logical rows
    const logicalRows = getLogicalRows();

    // Find the rows containing our fields
    const postalCodeRow = logicalRows.find((row) =>
      Array.from(row).some(
        (field) => field.getAttribute("data-testid") === "field-POSTAL_CODE",
      ),
    );

    const eventDateRow = logicalRows.find((row) =>
      Array.from(row).some(
        (field) =>
          field.getAttribute("data-testid") === "field-EVENT_DATE_RANGE",
      ),
    );

    // Ensure each field is in its own row
    expect(postalCodeRow).not.toBe(eventDateRow);
    expect(postalCodeRow).toContain(postalCodeField);
    expect(eventDateRow).toContain(eventDateField);
  });
});

describe("Currency Input Formatting", () => {
  it("displays formatted currency values with $ and commas while maintaining numeric values in hidden input", async () => {
    // Create a form with a budget field
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "BUDGET",
          label: "Budget",
          required: false,
          order: 0,
          width: "full",
        }),
      ],
    });

    // Create a wrapper component to track state
    function TestWrapper() {
      const [values, setValues] = useState<Record<string, string>>({
        BUDGET: "1000.25",
      });

      return (
        <>
          <div data-testid="current-value">{values.BUDGET}</div>
          <EmbeddedForm
            form={form}
            values={values}
            onChange={(fieldType: string, value: string | string[] | File) => {
              setValues((prev) => ({ ...prev, [fieldType]: value as string }));
            }}
          />
        </>
      );
    }

    // Render the form
    render(<TestWrapper />);

    // Check that the visible value is formatted with $ and commas
    const visibleBudgetInput = screen.getByLabelText("Budget");
    expect(visibleBudgetInput).toHaveValue("$1,000.25");

    // Check that the hidden input has the numeric value without formatting

    expect(screen.getByTestId("hidden-field-BUDGET")).toHaveValue("1000.25");
    const user = userEvent.setup();

    // Using fireEvent instead of userEvent to set the value all at once
    fireEvent.change(visibleBudgetInput, { target: { value: "$1,234.56" } });

    // Check that the value in state has $ and commas removed
    const currentValue = screen.getByTestId("current-value");
    expect(currentValue).toHaveTextContent("1234.56");

    // Check that the displayed value is formatted with $ and commas
    expect(visibleBudgetInput).toHaveValue("$1,234.56");

    // Check that the hidden input has the numeric value without formatting
    expect(screen.getByTestId("hidden-field-BUDGET")).toHaveValue("1234.56");
  });
});

describe("Phone Input Initialization", () => {
  it("should initialize intlTelInput on phone fields", () => {
    // Create a form with a phone field
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "full",
          order: 0,
        }),
      ],
    });

    // Render the form
    render(<EmbeddedForm form={form} />);

    // Get the phone input element
    const phoneInput = screen.getByTestId("input-PHONE");

    // The React component should render the input with appropriate class
    expect(phoneInput).toBeInTheDocument();
    // The React component uses a different class structure than vanilla js
    expect(phoneInput).toHaveClass("ef-input");
  });

  it("should use PHONE_COUNTRY value for initialCountry", () => {
    // Create a form with a phone field
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "full",
          order: 0,
        }),
      ],
    });

    const values = {
      PHONE_COUNTRY: "de", // Set Germany as the initial country
    };

    // Render the form with values
    render(<EmbeddedForm form={form} values={values} />);

    // Find the flag element within the React IntlTelInput component
    const fieldContainer = screen.getByTestId("field-PHONE");
    // With the React component, the flag has a different structure
    const flagElement = fieldContainer.querySelector(".iti__flag");

    // Verify the country code is set to Germany (de)
    expect(flagElement).toHaveClass("iti__de");
  });

  it('should default to "us" when no PHONE_COUNTRY is provided', () => {
    // Create a form with a phone field
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "full",
          order: 0,
        }),
      ],
    });

    // Render the form without any values
    render(<EmbeddedForm form={form} />);

    // Find the flag element in the React component
    const fieldContainer = screen.getByTestId("field-PHONE");
    const flagElement = fieldContainer.querySelector(".iti__flag");

    // Verify default country is US
    expect(flagElement).toHaveClass("iti__us");
  });

  it("should maintain phone field value after interacting with other fields", async () => {
    // This test captures the bug where the phone field gets cleared when interacting with other fields
    const user = userEvent.setup();

    // Create a form with a phone field and a name field
    const form = formFactory.build({
      fields: [
        createTestField({
          type: "PHONE",
          label: "Phone Number",
          required: true,
          width: "full",
          order: 0,
        }),
        createTestField({
          type: "FIRST_NAME",
          label: "Full Name",
          required: true,
          width: "full",
          order: 1,
        }),
      ],
    });

    // Track the values to verify they are preserved
    const formValues: Record<string, string | string[] | File> = {};
    const handleChange = vi.fn(
      (name: string, value: string | string[] | File) => {
        formValues[name] = value;
      },
    );

    // Render the form with the onChange handler
    render(
      <EmbeddedForm form={form} onChange={handleChange} values={formValues} />,
    );

    // Verify the handler was called with proper values when interacting with fields

    const phoneField = screen.getByTestId("field-PHONE");
    expect(phoneField).toBeInTheDocument();
    const phoneInput = screen.getByTestId("input-PHONE");

    await user.type(phoneInput, "1234567890");

    expect(screen.getByTestId("input-PHONE")).toHaveValue("1 (234) 567-890");
    // Verify we can interact with the name field
    const nameInput = screen.getByTestId("input-FIRST_NAME");
    await user.type(nameInput, "John Doe");

    expect(screen.getByTestId("input-PHONE")).toHaveValue("1 (234) 567-890");
  });

  describe("Phone input state syncing", () => {
    it("should sync phone input value with React state when value is changed", async () => {
      const user = userEvent.setup();

      // Create a form with a phone field
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "PHONE",
            label: "Phone Number",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Track the values to verify they are updated correctly
      const formValues: Record<string, string | string[] | File> = {};
      const handleChange = vi.fn(
        (name: string, value: string | string[] | File) => {
          formValues[name] = value;
        },
      );

      // Render the form with the onChange handler
      render(
        <EmbeddedForm
          form={form}
          onChange={handleChange}
          values={formValues}
        />,
      );

      // Get the phone input
      const phoneInput = screen.getByTestId("input-PHONE");

      // Type a phone number
      await user.type(phoneInput, "5551234567");

      // Verify the input value was updated
      expect(phoneInput).toHaveValue("(*************");
    });

    it("should maintain phone input value when component rerenders", async () => {
      const user = userEvent.setup();

      // Create a form with a phone field and other fields
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "PHONE",
            label: "Phone Number",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "EVENT_TYPE",
            label: "Event Type",
            required: true,
            width: "full",
            order: 1,
          }),
          createTestField({
            type: "FIRST_NAME",
          }),
        ],
      });

      // Track the values and create a wrapper that will rerender the component with updated values
      const TestWrapper = () => {
        const [formValues, setFormValues] = useState<
          Record<string, string | string[] | File>
        >({});

        const handleChange = (
          name: string,
          value: string | string[] | File,
        ) => {
          setFormValues((prev) => ({ ...prev, [name]: value }));
        };

        return (
          <EmbeddedForm
            form={form}
            onChange={handleChange}
            values={formValues}
          />
        );
      };

      // Render the test wrapper
      render(<TestWrapper />);

      // Get the phone input and event type select
      const phoneInput = screen.getByTestId("input-PHONE");
      const eventTypeSelect = screen.getByTestId("field-EVENT_TYPE");

      // Type a phone number
      await user.type(phoneInput, "5551234567");

      // Verify the input value was updated
      expect(phoneInput).toHaveValue("(*************");

      // Change the event type to trigger a rerender
      await user.type(screen.getByTestId("input-FIRST_NAME"), "Jimbo");

      // Verify the phone input still has the correct value
      expect(phoneInput).toHaveValue("(*************");
    });

    it("should initialize phone input with value from props", async () => {
      // Create a form with a phone field
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "PHONE",
            label: "Phone Number",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Pre-populate values
      const formValues: Record<string, string | string[] | File> = {
        PHONE: "5551234567",
      };

      // Render the form with pre-populated values
      render(<EmbeddedForm form={form} values={formValues} />);

      // Get the phone input
      const phoneInput = screen.getByTestId("input-PHONE");

      // Verify the input was initialized with the correct value
      expect(phoneInput).toHaveValue("(*************");
    });

    it("should update phone input value when values prop changes", async () => {
      // Create a form with a phone field
      const form = formFactory.build({
        fields: [
          createTestField({
            type: "PHONE",
            label: "Phone Number",
            required: true,
            width: "full",
            order: 0,
          }),
        ],
      });

      // Use a wrapper component to update values prop
      const TestWrapperWithUpdatingProps = () => {
        const [phone, setPhone] = useState("");

        // Update the phone value after component mounts
        useEffect(() => {
          setTimeout(() => {
            setPhone("5551234567");
          }, 100);
        }, []);

        return <EmbeddedForm form={form} values={{ PHONE: phone }} />;
      };

      // Render the wrapper
      render(<TestWrapperWithUpdatingProps />);

      // Get the phone input
      const phoneInput = screen.getByTestId("input-PHONE");

      // Verify the input starts empty
      expect(phoneInput).toHaveValue("");

      // Wait for the value to be updated
      await waitFor(
        () => {
          expect(phoneInput).toHaveValue("5551234567");
        },
        { timeout: 500 },
      );
    });
  });
});

describe("Country-State Selection", () => {
  // Helper function to select a country using the ComboBox by clicking and selecting
  const selectCountry = async (
    user: ReturnType<typeof userEvent.setup>,
    countryCode: string,
  ) => {
    // Click the button to open the dropdown
    const countryButton = screen.getByTestId("COUNTRY-button");
    await user.click(countryButton);

    // Wait for the dropdown to open
    await waitFor(() => {
      expect(screen.getByTestId("COUNTRY-option-list")).toBeInTheDocument();
    });

    // Find and click the country option - use waitFor to handle timing issues
    await waitFor(async () => {
      const option = screen.getByTestId(`COUNTRY-option-${countryCode}`);
      await user.click(option);
    });
  };

  it("should update state options based on the selected country", async () => {
    const user = userEvent.setup();

    // Create a wrapper component that tracks state
    const CountryStateTestWrapper = () => {
      const [formValues, setFormValues] = useState<Record<string, string>>({});

      const handleChange = (name: string, value: string | string[] | File) => {
        if (typeof value === "string") {
          setFormValues((prev) => ({ ...prev, [name]: value }));
        }
      };

      const form = formFactory.build({
        fields: [
          createTestField({
            type: "COUNTRY",
            label: "Country",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "STATE",
            label: "State/Province",
            required: true,
            width: "full",
            order: 1,
          }),
        ],
      });

      return (
        <>
          <div data-testid="current-country">
            {formValues.COUNTRY || "none"}
          </div>
          <div data-testid="current-state">{formValues.STATE || "none"}</div>
          <EmbeddedForm
            form={form}
            values={formValues}
            onChange={handleChange}
          />
        </>
      );
    };

    // Render our test component
    render(<CountryStateTestWrapper />);

    // Verify initial state - no country or state selected
    expect(screen.getByTestId("current-country")).toHaveTextContent("none");
    expect(screen.getByTestId("current-state")).toHaveTextContent("none");

    // First, let's select US (which has states)
    await selectCountry(user, "US");

    // Verify the country was updated
    await waitFor(() => {
      expect(screen.getByTestId("current-country")).toHaveTextContent("US");
    });

    // Verify STATE field is visible
    expect(screen.getByTestId("field-STATE")).toBeInTheDocument();

    // Select a state - open the state dropdown and select California
    const stateButton = screen.getByTestId("STATE-button");
    await user.click(stateButton);

    // Wait for state dropdown to appear with options
    await waitFor(() => {
      expect(screen.getByTestId("STATE-option-list")).toBeInTheDocument();
    });

    // Find and click a US state
    await waitFor(async () => {
      const option = screen.getByTestId("STATE-option-CA");
      await user.click(option);
    });

    // Verify the state was updated
    await waitFor(() => {
      expect(screen.getByTestId("current-state")).toHaveTextContent("CA");
    });
  });

  // Additional test to verify state field is hidden for countries without states
  it("should hide state field for countries without states", async () => {
    const user = userEvent.setup();

    // Create a wrapper component
    const CountryStateVisibilityWrapper = () => {
      const [formValues, setFormValues] = useState<Record<string, string>>({});

      const handleChange = (name: string, value: string | string[] | File) => {
        if (typeof value === "string") {
          setFormValues((prev) => ({ ...prev, [name]: value }));
        }
      };

      const form = formFactory.build({
        fields: [
          createTestField({
            type: "COUNTRY",
            label: "Country",
            required: true,
            width: "full",
            order: 0,
          }),
          createTestField({
            type: "STATE",
            label: "State/Province",
            required: true,
            width: "full",
            order: 1,
          }),
        ],
      });

      return (
        <EmbeddedForm form={form} values={formValues} onChange={handleChange} />
      );
    };

    // Render our test component
    render(<CountryStateVisibilityWrapper />);

    // Verify initial state - STATE field should be visible but empty
    expect(screen.getByTestId("field-STATE")).toBeInTheDocument();

    // Select a country without states (e.g., Vatican City - VA)
    await selectCountry(user, "VA");

    // Verify the STATE field is now hidden
    await waitFor(() => {
      expect(screen.queryByTestId("field-STATE")).not.toBeInTheDocument();
    });

    // Change to a country with states (US)
    await selectCountry(user, "US");

    // Verify STATE field is visible again
    await waitFor(() => {
      expect(screen.getByTestId("field-STATE")).toBeInTheDocument();
    });
  });
});

describe("ComboBox Field", () => {
  // Helper function to create form with custom combobox field
  const createFormWithComboBox = (selectedValue?: string) => {
    const options = [
      { value: "apple", label: "Apple" },
      { value: "banana", label: "Banana" },
      { value: "orange", label: "Orange" },
    ];

    // Create a field that will use COMBO_BOX rendering
    const field = createTestField({
      // Use an existing field type that's already in PREDEFINED_FIELDS
      type: "COMPANY",
      label: "Favorite Fruit",
      required: true,
      width: "full",
      order: 0,
    });

    // Create a mock version of the field definition
    const originalFieldDef = PREDEFINED_FIELDS.COMPANY;
    PREDEFINED_FIELDS.COMPANY = {
      ...originalFieldDef,
      inputType: "COMBO_BOX",
      options,
    };

    // Create form with our field
    const form = formFactory.build({
      fields: [field],
    });

    // Values to pass to form
    const formValues = selectedValue ? { COMPANY: selectedValue } : {};

    return {
      form,
      values: formValues as Record<string, string | string[] | File>,
      options,
      cleanup: () => {
        // Restore original field definition after test
        PREDEFINED_FIELDS.COMPANY = originalFieldDef;
      },
    };
  };

  it("renders correctly with default value in COMBO_BOX field", async () => {
    const { form, values, cleanup } = createFormWithComboBox("banana");

    try {
      // Pass in a pre-selected value
      render(<EmbeddedForm form={form} values={values} />);

      // Verify the combobox is rendered
      const combobox = screen.getByTestId("input-COMPANY");
      expect(combobox).toBeInTheDocument();

      // Check the hidden input has the correct value
      const hiddenInput = screen.getByTestId("hidden-field-COMPANY");
      expect(hiddenInput).toHaveValue("banana");
    } finally {
      cleanup();
    }
  });

  it("allows selection of a new value in COMBO_BOX field", async () => {
    const { form, values, options, cleanup } = createFormWithComboBox();

    try {
      const handleChange = vi.fn();

      // Render with no pre-selected value
      const { rerender } = render(
        <EmbeddedForm form={form} onChange={handleChange} values={values} />,
      );

      // Set up user interactions
      const user = userEvent.setup();

      // Click the chevron button to open the dropdown
      const comboboxButton = screen.getByTestId("COMPANY-button");
      await user.click(comboboxButton);

      // Wait for the dropdown to appear
      const optionsList = await waitFor(
        () => {
          const list = screen.getByTestId("COMPANY-option-list");
          if (!list) throw new Error("COMPANY-option-list not found");
          return list;
        },
        { timeout: 1000 },
      );

      // Click an option
      const bananaOption = await waitFor(
        () => {
          const option = within(optionsList).getByTestId(
            "COMPANY-option-banana",
          );
          if (!option) throw new Error("COMPANY-option-banana not found");
          return option;
        },
        { timeout: 1000 },
      );

      await user.click(bananaOption);

      // Verify onChange was called with the correct value
      expect(handleChange).toHaveBeenCalledWith("COMPANY", "banana");
    } finally {
      cleanup();
    }
  });

  it("shows the selected value after selecting from dropdown", async () => {
    const { form, options, cleanup } = createFormWithComboBox();

    try {
      // Create a component that tracks its own state to test the full interaction
      const ComboBoxTestWrapper = () => {
        const [values, setValues] = useState<Record<string, string>>({});

        const handleChange = (
          name: string,
          value: string | string[] | File,
        ) => {
          if (typeof value === "string") {
            setValues((prev) => ({ ...prev, [name]: value }));
          }
        };

        // Mock field definition for test
        const originalFieldDef = PREDEFINED_FIELDS.COMPANY;
        PREDEFINED_FIELDS.COMPANY = {
          ...originalFieldDef,
          inputType: "COMBO_BOX",
          options,
        };

        // Create form with mock field
        const field = createTestField({
          type: "COMPANY",
          label: "Favorite Fruit",
          required: true,
          width: "full",
          order: 0,
        });

        const form = formFactory.build({
          fields: [field],
        });

        return (
          <>
            <div data-testid="current-value">{values.COMPANY || "none"}</div>
            <EmbeddedForm form={form} values={values} onChange={handleChange} />
          </>
        );
      };

      // Render the wrapper component
      render(<ComboBoxTestWrapper />);

      // Set up user interactions
      const user = userEvent.setup();

      // Verify initial state
      expect(screen.getByTestId("current-value")).toHaveTextContent("none");

      // Click the chevron button to open dropdown
      const comboboxButton = screen.getByTestId("COMPANY-button");
      await user.click(comboboxButton);

      // Select an option
      const optionsList = await waitFor(
        () => {
          const list = screen.getByTestId("COMPANY-option-list");
          if (!list) throw new Error("COMPANY-option-list not found");
          return list;
        },
        { timeout: 1000 },
      );

      const bananaOption = await waitFor(
        () => {
          const option = within(optionsList).getByTestId(
            "COMPANY-option-banana",
          );
          if (!option) throw new Error("COMPANY-option-banana not found");
          return option;
        },
        { timeout: 1000 },
      );

      await user.click(bananaOption);

      // Verify the state was updated
      await waitFor(() => {
        expect(screen.getByTestId("current-value")).toHaveTextContent("banana");
      });

      // Check the hidden input has the correct value too
      expect(screen.getByTestId("hidden-field-COMPANY")).toHaveValue("banana");
    } finally {
      cleanup();
    }
  });
});
