import { Hono } from "hono";
import { Variables } from "@/auth";
import FormPage from "./FormPage";
import logger from "@/logger";
import {
  FormSubmissionApplicationService,
  FormSubmissionFailure,
  ValidationError,
} from "./form-submission-application-service";
import { UAParser } from "ua-parser-js";
import { parseFormData } from "./form-data-parser";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

const fileUploadRequestSchema = z.object({
  fileName: z.string().optional(),
  fileType: z.string().optional(),
  fileSize: z.number().int().positive().optional(),
  uploadGroupId: z.string(),
});

export interface Dependencies {
  formSumbissionApplicationService: FormSubmissionApplicationService;
}

function isValidationError(
  error: FormSubmissionFailure,
): error is ValidationError {
  return error.type === "validation";
}

function isMobileFromUA(userAgent: string): boolean {
  const parser = new UAParser(userAgent);
  const device = parser.getDevice();
  return device.type === "mobile" || device.type === "tablet";
}

export function createPublicFormRoutes(deps: Dependencies) {
  const formSubmissionService = deps.formSumbissionApplicationService;
  const routes = new Hono<{ Variables: Variables }>()
    .get("/:formId", async (c) => {
      const formId = c.req.param("formId");
      const result = await formSubmissionService.getForm(formId);

      if (result.isErr()) {
        return c.text(result.error.message, 404);
      }

      // Get user agent from request headers and determine if mobile
      const userAgent = c.req.header("user-agent") || "";
      const isMobileGuess = isMobileFromUA(userAgent);

      return c.render(
        <FormPage
          success={false}
          form={result.value}
          isMobileGuess={isMobileGuess}
        />,
      );
    })
    .post(
      "/:formId/files/presign",
      zValidator("json", fileUploadRequestSchema),
      async (c) => {
        const formId = c.req.param("formId");
        const requestBody = c.req.valid("json");
        const result = await formSubmissionService.generateFileUploadUrl({
          formId,
          ...requestBody,
        });

        return result.match(
          (success) => c.json(success),
          (error) => {
            logger.error(
              {
                formId,
                errorType: error.type,
                message: error.message,
                details: error.details,
              },
              "File upload error",
            );

            return c.json(
              {
                error: error.message,
                details: error.details,
              },
              500,
            );
          },
        );
      },
    )
    .post("/:formId/submission", async (c) => {
      const formId = c.req.param("formId");
      const formData = await c.req.formData();

      // Get user agent and determine if mobile
      const userAgent = c.req.header("user-agent") || "";
      const isMobileGuess = isMobileFromUA(userAgent);

      // Log raw form data entries
      logger.debug(
        {
          formId,
          rawEntries: Array.from(formData.entries()),
        },
        "Raw form data entries",
      );

      // Use the form data parser to handle complex form structures
      const data = parseFormData(formData);

      // Log processed form data
      logger.debug(
        {
          formId,
          processedData: data,
        },
        "Processed form data",
      );

      // Submit form using application service
      const submissionResult = await formSubmissionService.submitForm(
        formId,
        data,
      );

      return submissionResult.match(
        // Success case
        (result) => {
          // Log successful submission data
          logger.debug(
            {
              formId,
              submissionData: data,
              success: true,
              hasRedirectUrl: !!result.form.redirectUrl,
            },
            "Form submission successful",
          );

          return c.render(
            <FormPage
              form={result.form}
              success={true}
              isMobileGuess={isMobileGuess}
            />,
          );
        },
        // Error case
        (error) => {
          if (error.type === "not_found") {
            return c.text(error.message, 404);
          }

          if (isValidationError(error)) {
            logger.debug(
              {
                formId,
                validationErrors: error.errors,
                submittedData: data,
                phoneCountry: data["PHONE_COUNTRY"],
              },
              "Form validation failed",
            );

            c.status(422);
            return c.render(
              <FormPage
                success={false}
                form={error.form}
                errors={error.errors}
                values={data}
                isMobileGuess={isMobileGuess}
              />,
            );
          }

          // Handle database or workflow errors
          logger.error(
            {
              formId,
              errorType: error.type,
              message: error.message,
              data: data,
            },
            "Failed to process form submission",
          );

          c.status(500);
          return c.render(
            <FormPage
              success={false}
              form={error.form}
              errors={{
                _form: ["Failed to save form submission. Please try again."],
              }}
              values={data}
              isMobileGuess={isMobileGuess}
            />,
          );
        },
      );
    });

  return routes;
}

export type PublicFormRoutes = ReturnType<typeof createPublicFormRoutes>;
