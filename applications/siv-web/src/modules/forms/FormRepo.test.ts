import { describe, expect, it } from "vitest";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { FormRepo } from "./FormRepo";

describe("FormRepo", () => {
  withTransactionalTests();

  const formRepo = new FormRepo();

  describe("createForm and findFormWithRelations", () => {
    it("should create a form with theme and fields and retrieve it correctly", async () => {
      // Create a test property
      const testProperty = await propertyFactory.create();

      // Create a form with theme and fields
      const createdForm = await formRepo.createForm({
        name: "Test Form",
        description: "Test Description",
        propertyId: testProperty.id!!,
        theme: {
          name: "Test Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            hideForEventTypes: ["wedding"],
            order: 0,
            rowBreakAfter: false,
          },
          {
            type: "EMAIL",
            label: "Email",
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 1,
            rowBreakAfter: true,
          },
          {
            type: "MARKETING_CONSENT",
            label: "Marketing Consent",
            required: true,
            width: "full",
            hideForEventTypes: [],
            order: 2,
            rowBreakAfter: true,
            checkboxText: "I agree to the terms and conditions",
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Verify the created form
      expect(createdForm).toBeDefined();
      expect(createdForm.name).toBe("Test Form");
      expect(createdForm.theme.name).toBe("Test Theme");
      expect(createdForm.fields).toHaveLength(3);
      expect(createdForm.fields[0].type).toBe("FIRST_NAME");
      expect(createdForm.fields[1].type).toBe("EMAIL");
      expect(createdForm.fields[2].type).toBe("MARKETING_CONSENT");
      expect(createdForm.fields[2].checkboxText).toBe(
        "I agree to the terms and conditions",
      );

      // Explicitly verify rowBreakAfter field
      expect(createdForm.fields[0].rowBreakAfter).toBe(false);
      expect(createdForm.fields[1].rowBreakAfter).toBe(true);
      expect(createdForm.fields[2].rowBreakAfter).toBe(true);

      // Now try to retrieve it using findFormWithRelations
      const retrievedForm = await formRepo.findFormWithRelations(
        createdForm.id,
      );

      // Verify the retrieved form matches the created one
      expect(retrievedForm).toBeDefined();
      expect(retrievedForm?.id).toBe(createdForm.id);
      expect(retrievedForm?.name).toBe(createdForm.name);
      expect(retrievedForm?.theme.name).toBe(createdForm.theme.name);
      expect(retrievedForm?.fields).toHaveLength(3);
      expect(retrievedForm?.fields[0].type).toBe("FIRST_NAME");
      expect(retrievedForm?.fields[1].type).toBe("EMAIL");
      expect(retrievedForm?.fields[2].type).toBe("MARKETING_CONSENT");
      expect(retrievedForm?.fields[2].checkboxText).toBe(
        "I agree to the terms and conditions",
      );
      expect(retrievedForm?.fields[0].hideForEventTypes).toEqual(["wedding"]);

      // Explicitly verify rowBreakAfter is retrieved correctly
      expect(retrievedForm?.fields[0].rowBreakAfter).toBe(false);
      expect(retrievedForm?.fields[1].rowBreakAfter).toBe(true);
      expect(retrievedForm?.fields[2].rowBreakAfter).toBe(true);

      // Verify fields are ordered correctly
      expect(retrievedForm?.fields[0].order).toBe(0);
      expect(retrievedForm?.fields[1].order).toBe(1);
      expect(retrievedForm?.fields[2].order).toBe(2);
    });

    it("should return null when trying to find a non-existent form", async () => {
      const nonExistentForm = await formRepo.findFormWithRelations(
        "00000000-0000-0000-0000-000000000000",
      );
      expect(nonExistentForm).toBeNull();
    });
  });

  describe("updateForm", () => {
    it("should update form metadata, theme, and fields while preserving IDs", async () => {
      const testProperty = await propertyFactory.create();

      // First create a form
      const originalForm = await formRepo.createForm({
        name: "Original Form",
        description: "Original Description",
        propertyId: testProperty.id!!,
        theme: {
          name: "Original Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "Original First Name",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: true,
          },
          {
            type: "MARKETING_CONSENT",
            label: "Original Checkbox",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: [],
            rowBreakAfter: false,
            checkboxText: "Original checkbox text",
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Verify original rowBreakAfter values
      expect(originalForm.fields[0].rowBreakAfter).toBe(true);
      expect(originalForm.fields[1].rowBreakAfter).toBe(false);

      // Store original IDs
      const originalThemeId = originalForm.theme.id;
      const originalFormId = originalForm.id;

      // Now update the form with new data and flipped rowBreakAfter values
      const updatedForm = await formRepo.updateForm(originalForm.id, {
        name: "Updated Form",
        description: "Updated Description",
        propertyId: testProperty.id!!,
        theme: {
          name: "Updated Theme",
          primaryColor: "#ff0000",
          placeholderColor: "#999999",
          backgroundColor: "#f5f5f5",
          textColor: "#333333",
          borderColor: "#cccccc",
          inputBorderRadius: "0.5rem",
          buttonBorderRadius: "0.5rem",
          padding: "2rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#fafafa",
          inputTextColor: "#222222",
          formBorderRadius: "1rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "Updated First Name",
            required: false,
            width: "half",
            order: 1,
            hideForEventTypes: ["wedding"],
            rowBreakAfter: false,
          },
          {
            type: "EMAIL",
            label: "New Email Field",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: true,
          },
          {
            type: "MARKETING_CONSENT",
            label: "",
            required: false,
            width: "full",
            order: 2,
            hideForEventTypes: [],
            rowBreakAfter: true,
            checkboxText: "Updated checkbox text",
          },
        ],
        redirectUrl: "https://example.com",
        allowedDomains: ["example.com"],
      });

      // Verify form metadata was updated
      expect(updatedForm.id).toBe(originalFormId);
      expect(updatedForm.name).toBe("Updated Form");
      expect(updatedForm.description).toBe("Updated Description");
      expect(updatedForm.redirectUrl).toBe("https://example.com");
      expect(updatedForm.allowedDomains).toEqual(["example.com"]);

      // Verify theme was updated but ID preserved
      expect(updatedForm.theme.id).toBe(originalThemeId);
      expect(updatedForm.theme.name).toBe("Updated Theme");
      expect(updatedForm.theme.primaryColor).toBe("#ff0000");
      expect(updatedForm.theme.backgroundColor).toBe("#f5f5f5");
      expect(updatedForm.theme.buttonAlignment).toBe("left");
      expect(updatedForm.theme.font).toBe("Arial, sans-serif");

      // Verify fields were updated
      expect(updatedForm.fields).toHaveLength(3);

      // Fields should be in order
      const [firstField, secondField, thirdField] = updatedForm.fields;

      // Verify email field (order 0)
      expect(firstField.type).toBe("EMAIL");
      expect(firstField.label).toBe("New Email Field");
      expect(firstField.required).toBe(true);
      expect(firstField.width).toBe("full");
      expect(firstField.order).toBe(0);
      expect(firstField.hideForEventTypes).toEqual([]);
      expect(firstField.rowBreakAfter).toBe(true);

      // Verify first name field (order 1)
      expect(secondField.type).toBe("FIRST_NAME");
      expect(secondField.label).toBe("Updated First Name");
      expect(secondField.required).toBe(false);
      expect(secondField.width).toBe("half");
      expect(secondField.order).toBe(1);
      expect(secondField.hideForEventTypes).toEqual(["wedding"]);
      expect(secondField.rowBreakAfter).toBe(false);

      // Verify checkbox field (order 2)
      expect(thirdField.type).toBe("MARKETING_CONSENT");
      expect(thirdField.required).toBe(false);
      expect(thirdField.checkboxText).toBe("Updated checkbox text");
      expect(thirdField.order).toBe(2);
      expect(thirdField.rowBreakAfter).toBe(true);

      // Verify the changes persist
      const retrievedForm =
        await formRepo.findFormWithRelations(originalFormId);
      expect(retrievedForm).toEqual(updatedForm);
    });

    it("should handle checkbox fields without checkbox text", async () => {
      const testProperty = await propertyFactory.create();

      // Create a form with a checkbox field but no checkbox text
      const form = await formRepo.createForm({
        name: "Checkbox Form",
        description: "Testing checkbox without text",
        propertyId: testProperty.id!!,
        theme: {
          name: "Test Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "MARKETING_CONSENT",
            label: "Checkbox without text",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: true,
            // No checkboxText property
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Verify the form was created
      expect(form).toBeDefined();
      expect(form.fields).toHaveLength(1);
      expect(form.fields[0].type).toBe("MARKETING_CONSENT");
      expect(form.fields[0].checkboxText).toBeUndefined();
      expect(form.fields[0].rowBreakAfter).toBe(true);

      // Retrieve and verify that default checkbox text is applied
      const retrievedForm = await formRepo.findFormWithRelations(form.id);
      expect(retrievedForm).toBeDefined();
      expect(retrievedForm?.fields[0].type).toBe("MARKETING_CONSENT");
      // Default checkbox text should be applied when retrieving
      expect(retrievedForm?.fields[0].checkboxText).toBe(
        "I consent to receiving marketing communications.",
      );
      expect(retrievedForm?.fields[0].rowBreakAfter).toBe(true);
    });

    it("should throw error when updating non-existent form", async () => {
      const testProperty = await propertyFactory.create();
      const nonExistentId = "00000000-0000-0000-0000-000000000000";

      await expect(
        formRepo.updateForm(nonExistentId, {
          name: "Test Form",
          description: null,
          propertyId: testProperty.id!!,
          theme: {
            name: "Test Theme",
            primaryColor: "#000000",
            placeholderColor: "#cfcfcf",
            backgroundColor: "#ffffff",
            textColor: "#000000",
            borderColor: "#000000",
            inputBorderRadius: "0.375rem",
            buttonBorderRadius: "0.375rem",
            padding: "1rem",
            buttonTextColor: "#ffffff",
            buttonAlignment: "right",
            font: "system-ui, -apple-system, sans-serif",
            fieldBackgroundColor: "#ffffff",
            inputTextColor: "#000000",
            formBorderRadius: "0.75rem",
          },
          fields: [],
          redirectUrl: null,
          allowedDomains: [],
        }),
      ).rejects.toThrow("Form not found");
    });
  });

  describe("upsertForm", () => {
    it("should create a new form when the ID does not exist", async () => {
      const testProperty = await propertyFactory.create();
      const newFormId = "00000000-0000-0000-0000-000000000001";

      // Create a form with a specific ID that doesn't exist yet
      const createdForm = await formRepo.upsertForm(newFormId, {
        name: "New Upserted Form",
        description: "Created via upsert",
        propertyId: testProperty.id!!,
        theme: {
          name: "Upsert Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "First Name",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: false,
          },
          {
            type: "EMAIL",
            label: "Email",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: [],
            rowBreakAfter: true,
          },
          {
            type: "MARKETING_CONSENT",
            label: "Marketing Consent",
            required: false,
            width: "full",
            order: 2,
            hideForEventTypes: [],
            rowBreakAfter: false,
            checkboxText: "I agree to receive marketing communications",
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Verify the created form
      expect(createdForm).toBeDefined();
      expect(createdForm.id).toBe(newFormId);
      expect(createdForm.name).toBe("New Upserted Form");
      expect(createdForm.description).toBe("Created via upsert");
      expect(createdForm.propertyId).toBe(testProperty.id);
      expect(createdForm.redirectUrl).toBeNull();
      expect(createdForm.allowedDomains).toEqual([]);

      // Verify theme properties
      expect(createdForm.theme).toBeDefined();
      expect(createdForm.theme.name).toBe("Upsert Theme");
      expect(createdForm.theme.primaryColor).toBe("#000000");
      expect(createdForm.theme.backgroundColor).toBe("#ffffff");
      expect(createdForm.theme.buttonAlignment).toBe("right");
      expect(createdForm.theme.placeholderColor).toBe("#cfcfcf");

      // Verify fields
      expect(createdForm.fields).toHaveLength(3);
      expect(createdForm.fields[0].type).toBe("FIRST_NAME");
      expect(createdForm.fields[0].label).toBe("First Name");
      expect(createdForm.fields[0].required).toBe(true);
      expect(createdForm.fields[0].width).toBe("full");
      expect(createdForm.fields[0].rowBreakAfter).toBe(false);
      expect(createdForm.fields[0].hideForEventTypes).toEqual([]);

      expect(createdForm.fields[1].type).toBe("EMAIL");
      expect(createdForm.fields[1].label).toBe("Email");
      expect(createdForm.fields[1].required).toBe(true);
      expect(createdForm.fields[1].rowBreakAfter).toBe(true);

      expect(createdForm.fields[2].type).toBe("MARKETING_CONSENT");
      expect(createdForm.fields[2].checkboxText).toBe(
        "I agree to receive marketing communications",
      );

      // Verify it exists in the database with the same properties
      const retrievedForm = await formRepo.findFormWithRelations(newFormId);
      expect(retrievedForm).not.toBeNull();
      expect(retrievedForm?.id).toBe(newFormId);
      expect(retrievedForm?.name).toBe("New Upserted Form");
      expect(retrievedForm?.description).toBe("Created via upsert");
      expect(retrievedForm?.theme.name).toBe("Upsert Theme");
      expect(retrievedForm?.fields).toHaveLength(3);
      expect(retrievedForm?.fields[0].type).toBe("FIRST_NAME");
      expect(retrievedForm?.fields[1].type).toBe("EMAIL");
      expect(retrievedForm?.fields[2].type).toBe("MARKETING_CONSENT");
      expect(retrievedForm?.fields[2].checkboxText).toBe(
        "I agree to receive marketing communications",
      );
    });

    it("should update an existing form when the ID exists", async () => {
      const testProperty = await propertyFactory.create();

      // First create a form
      const originalForm = await formRepo.createForm({
        name: "Original Upsert Form",
        description: "Original Description",
        propertyId: testProperty.id!!,
        theme: {
          name: "Original Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "Original First Name",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: false,
          },
          {
            type: "MARKETING_CONSENT",
            label: "Original Marketing Consent",
            required: true,
            width: "full",
            order: 1,
            hideForEventTypes: [],
            rowBreakAfter: false,
            checkboxText: "Original checkbox text",
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Store original IDs
      const originalThemeId = originalForm.theme.id;
      const originalFormId = originalForm.id;

      // Verify original form properties
      expect(originalForm.fields[0].rowBreakAfter).toBe(false);
      expect(originalForm.fields[1].type).toBe("MARKETING_CONSENT");
      expect(originalForm.fields[1].checkboxText).toBe(
        "Original checkbox text",
      );

      // Now upsert the form with the same ID but different data
      const upsertedForm = await formRepo.upsertForm(originalFormId, {
        name: "Updated via Upsert",
        description: "Updated Description",
        propertyId: testProperty.id!!,
        theme: {
          name: "Updated Theme",
          primaryColor: "#ff0000",
          placeholderColor: "#999999",
          backgroundColor: "#f5f5f5",
          textColor: "#333333",
          borderColor: "#cccccc",
          inputBorderRadius: "0.5rem",
          buttonBorderRadius: "0.5rem",
          padding: "2rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "left",
          font: "Arial, sans-serif",
          fieldBackgroundColor: "#fafafa",
          inputTextColor: "#222222",
          formBorderRadius: "1rem",
        },
        fields: [
          {
            type: "FIRST_NAME",
            label: "Updated First Name",
            required: false,
            width: "half",
            order: 1,
            hideForEventTypes: ["wedding"],
            rowBreakAfter: false,
          },
          {
            type: "EMAIL",
            label: "New Email Field",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: true,
          },
          {
            type: "MARKETING_CONSENT",
            label: "Updated Marketing Consent",
            required: false,
            width: "full",
            order: 2,
            hideForEventTypes: [],
            rowBreakAfter: true,
            checkboxText: "Updated checkbox text",
          },
        ],
        redirectUrl: "https://example.com",
        allowedDomains: ["example.com"],
      });

      // Verify form metadata was updated
      expect(upsertedForm.id).toBe(originalFormId);
      expect(upsertedForm.name).toBe("Updated via Upsert");
      expect(upsertedForm.description).toBe("Updated Description");
      expect(upsertedForm.redirectUrl).toBe("https://example.com");
      expect(upsertedForm.allowedDomains).toEqual(["example.com"]);

      // Verify theme was updated but ID preserved
      expect(upsertedForm.theme.id).toBe(originalThemeId);
      expect(upsertedForm.theme.name).toBe("Updated Theme");
      expect(upsertedForm.theme.primaryColor).toBe("#ff0000");
      expect(upsertedForm.theme.backgroundColor).toBe("#f5f5f5");
      expect(upsertedForm.theme.buttonAlignment).toBe("left");
      expect(upsertedForm.theme.placeholderColor).toBe("#999999");
      expect(upsertedForm.theme.padding).toBe("2rem");

      // Verify fields were updated
      expect(upsertedForm.fields).toHaveLength(3);

      // Fields should be sorted by order
      expect(upsertedForm.fields[0].type).toBe("EMAIL");
      expect(upsertedForm.fields[0].label).toBe("New Email Field");
      expect(upsertedForm.fields[0].required).toBe(true);
      expect(upsertedForm.fields[0].width).toBe("full");
      expect(upsertedForm.fields[0].rowBreakAfter).toBe(true);

      expect(upsertedForm.fields[1].type).toBe("FIRST_NAME");
      expect(upsertedForm.fields[1].label).toBe("Updated First Name");
      expect(upsertedForm.fields[1].required).toBe(false);
      expect(upsertedForm.fields[1].width).toBe("half");
      expect(upsertedForm.fields[1].hideForEventTypes).toEqual(["wedding"]);

      expect(upsertedForm.fields[2].type).toBe("MARKETING_CONSENT");
      expect(upsertedForm.fields[2].label).toBe("Updated Marketing Consent");
      expect(upsertedForm.fields[2].checkboxText).toBe("Updated checkbox text");
      expect(upsertedForm.fields[2].rowBreakAfter).toBe(true);

      // Verify the form was actually updated in the database
      const retrievedForm =
        await formRepo.findFormWithRelations(originalFormId);
      expect(retrievedForm).not.toBeNull();
      expect(retrievedForm?.name).toBe("Updated via Upsert");
      expect(retrievedForm?.theme.name).toBe("Updated Theme");
      expect(retrievedForm?.fields).toHaveLength(3);
      expect(retrievedForm?.fields[0].type).toBe("EMAIL");
      expect(retrievedForm?.fields[1].type).toBe("FIRST_NAME");
      expect(retrievedForm?.fields[2].type).toBe("MARKETING_CONSENT");
      expect(retrievedForm?.fields[2].checkboxText).toBe(
        "Updated checkbox text",
      );
    });

    it("should handle checkbox fields without checkbox text when upserting", async () => {
      const testProperty = await propertyFactory.create();
      const newFormId = "00000000-0000-0000-0000-000000000002";

      // Create a form with a checkbox field but no checkbox text via upsert
      const form = await formRepo.upsertForm(newFormId, {
        name: "Checkbox Upsert Form",
        description: "Testing checkbox without text via upsert",
        propertyId: testProperty.id!!,
        theme: {
          name: "Test Theme",
          primaryColor: "#000000",
          placeholderColor: "#cfcfcf",
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderColor: "#000000",
          inputBorderRadius: "0.375rem",
          buttonBorderRadius: "0.375rem",
          padding: "1rem",
          buttonTextColor: "#ffffff",
          buttonAlignment: "right",
          font: "system-ui, -apple-system, sans-serif",
          fieldBackgroundColor: "#ffffff",
          inputTextColor: "#000000",
          formBorderRadius: "0.75rem",
        },
        fields: [
          {
            type: "MARKETING_CONSENT",
            label: "Checkbox without text",
            required: true,
            width: "full",
            order: 0,
            hideForEventTypes: [],
            rowBreakAfter: true,
            // No checkboxText property
          },
        ],
        redirectUrl: null,
        allowedDomains: [],
      });

      // Verify the form was created
      expect(form).toBeDefined();
      expect(form.id).toBe(newFormId);
      expect(form.fields).toHaveLength(1);
      expect(form.fields[0].type).toBe("MARKETING_CONSENT");
      expect(form.fields[0].checkboxText).toBeUndefined();
      expect(form.fields[0].rowBreakAfter).toBe(true);

      // Retrieve and verify that default checkbox text is applied
      const retrievedForm = await formRepo.findFormWithRelations(form.id);
      expect(retrievedForm).toBeDefined();
      expect(retrievedForm?.fields[0].type).toBe("MARKETING_CONSENT");
      // Default checkbox text should be applied when retrieving
      expect(retrievedForm?.fields[0].checkboxText).toBe(
        "I consent to receiving marketing communications.",
      );
      expect(retrievedForm?.fields[0].rowBreakAfter).toBe(true);
    });
  });
});
