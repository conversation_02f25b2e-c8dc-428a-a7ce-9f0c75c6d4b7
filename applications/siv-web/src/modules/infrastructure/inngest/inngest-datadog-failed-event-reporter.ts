import { client, v1 } from "@datadog/datadog-api-client";
import { SivInngestClient } from "@/inngest-client";

const configuration = client.createConfiguration();
const apiInstance = new v1.EventsApi(configuration);

export default function createDatadogFunctionFailedReporter(
  inngest: SivInngestClient,
) {
  inngest.createFunction(
    {
      name: "Send failures to Datadog",
      id: "send-failed-function-events-to-datadog",
    },
    { event: "inngest/function.failed" },
    async ({ event, step }) => {
      // This is a normal Inngest function, so we can use steps as we normally do:
      await step.run("send-event-to-datadog", async () => {
        const error = event.data.error;

        // Create the Datadog event body using information about the failed function:
        const params: v1.EventsApiCreateEventRequest = {
          body: {
            title: "Inngest Function Failed",
            alertType: "error",
            text: `The ${event.data.function_id} function (runId=${event.data.run_id} failed with the error: ${error.message}`,
            tags: [
              // Add a tag with the Inngest function id:
              `inngest_function_id:${event.data.function_id}`,
            ],
          },
        };

        // Send to Datadog:
        const data = await apiInstance.createEvent(params);

        // Return the data to Inngest for viewing in function logs:
        return { message: "Event sent successfully", data };
      });
    },
  );
}
