import { eventType } from "@/drizzle/schema";

// Type to get the database enum values
export type EventTypeValue = (typeof eventType.enumValues)[number];

// Form display mapping (what users see in the form)
export const EVENT_TYPE_DISPLAY_MAPPING: Record<EventTypeValue, string> = {
  corporate_meeting: "Corporate Meeting",
  corporate_retreat: "Corporate Retreat",
  association_event: "Association Event",
  wedding: "Wedding",
  family_reunion: "Family Reunion",
  celebration: "Celebration",
  social_or_sport_club: "Social or Sport Club",
} as const;

// Function to get display name for a database value
export function getEventTypeDisplayName(value: EventTypeValue): string {
  return EVENT_TYPE_DISPLAY_MAPPING[value];
}

// Function to get database value from display name
export function getEventTypeDatabaseValue(
  displayName: string,
): EventTypeValue | null {
  const entry = Object.entries(EVENT_TYPE_DISPLAY_MAPPING).find(
    ([_, label]) => label === displayName,
  );
  return entry ? (entry[0] as EventTypeValue) : null;
}

// Get all options in the format needed for form fields
export function getEventTypeOptions() {
  return Object.entries(EVENT_TYPE_DISPLAY_MAPPING).map(([value, label]) => ({
    value,
    label,
  }));
}
