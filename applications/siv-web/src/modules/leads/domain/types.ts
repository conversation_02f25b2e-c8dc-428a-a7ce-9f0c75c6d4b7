import {
  lead,
  eventNeed,
  eventType,
  leadSource,
  leadFile,
} from "@/drizzle/schema";
import { InferInsertModel, InferSelectModel } from "drizzle-orm";

export type Lead = Omit<
  InferSelectModel<typeof lead>,
  "startDate" | "endDate" | "createdAt" | "updatedAt" | "budget"
> & {
  budget: number | null;
  startDate?: string | null;
  endDate?: string | null;
  createdAt: Date;
  updatedAt: Date | null;
};

export type LeadWithFiles = Lead & { files: LeadFile[] };

export type NewLead = Omit<Lead, "id" | "createdAt" | "updatedAt">;

export const LeadEventTypeValues = eventType.enumValues;

export type LeadFile = InferSelectModel<typeof leadFile>;
export type NewLeadFile = InferInsertModel<typeof leadFile>;
