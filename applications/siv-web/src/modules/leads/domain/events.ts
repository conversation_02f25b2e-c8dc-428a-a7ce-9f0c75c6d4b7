import { LeadWithFiles } from "./types";

// Base event data
type BaseEventData = {
  leadId: string;
};

// Lead assigned event data needs to include the full lead data and files
export type LeadAssignedEventData = {
  leadId: string;
  leadData: LeadWithFiles;
};

// Create the final event types
export type LeadCreatedEvent = {
  name: "lead.created";
  id: string;
  data: BaseEventData & {
    propertyId: string;
  };
};

export type LeadAssignedEvent = {
  name: "lead.assigned";
  id: string;
  data: LeadAssignedEventData;
};

export type LeadSyncedToHighlevelEventPayload = BaseEventData & {
  highLevelContactId: string;
  highLevelOpportunityId: string;
  // HighLevel authentication details
  highLevelAuthToken: string;
  highLevelLocationId: string;
  fileUploads: Array<{
    fileId: string;
    s3Key: string;
    keyWithoutStatusPrefix: string;
    fileName: string;
    contentType: string;
  }>;
};

// Lead synced to HighLevel event
export type LeadSyncedToHighlevelEvent = {
  name: "lead.synced-to-highlevel";
  id: string;
  data: LeadSyncedToHighlevelEventPayload;
};

export type LeadEventTypes =
  | LeadCreatedEvent
  | LeadAssignedEvent
  | LeadSyncedToHighlevelEvent;

// Complete event type definitions derived from existing types
export type LeadEventsSchema = {
  [K in LeadEventTypes["name"]]: Extract<LeadEventTypes, { name: K }>;
};

export interface LeadAssignedEventPublisher {
  publish(event: LeadAssignedEvent): Promise<void>;
}

export interface LeadCreatedEventPublisher {
  publish(event: LeadCreatedEvent): Promise<void>;
}
