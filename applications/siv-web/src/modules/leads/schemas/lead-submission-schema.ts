import { z } from "@hono/zod-openapi";
import { eventType, eventNeed } from "@/drizzle/schema";

// API submission uses different enum format than database
export const EventType = z
  .enum([
    "wedding",
    "corporate_meeting",
    "corporate_retreat",
    "association_event",
    "family_reunion",
    "celebration",
    "social_or_sport_club",
  ])
  .openapi({
    description: "Type of event",
    example: "wedding",
  });

const EventNeedsSchema = z.array(z.enum(eventNeed.enumValues)).openapi({
  description: "Specific event needs",
  example: ["WEDDING_CEREMONY", "WEDDING_RECEPTION"],
});

// Define country schema
const CountrySchema = z.string().length(2).openapi({
  description: "Valid 2-letter ISO country code",
  example: "US",
});

// Helper function to validate dates
const getToday = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
};

// Main lead submission schema - simplified for the API
export const LeadSubmissionSchema = z
  .object({
    // Contact Information
    firstName: z.string().min(1, "First name is required").max(100).openapi({
      description: "Contact's first name",
      example: "<PERSON>",
    }),
    lastName: z.string().min(1, "Last name is required").max(100).openapi({
      description: "Contact's last name",
      example: "Smith",
    }),
    email: z.string().email("Invalid email format").openapi({
      description: "Contact's email address",
      example: "<EMAIL>",
    }),
    phone: z.string().min(7, "Phone number is required").max(20).openapi({
      description: "Contact's phone number in E.161 format",
      example: "+14155552671",
    }),
    company: z.string().optional().openapi({
      description: "Company name",
      example: "Acme Corp",
    }),
    country: CountrySchema.min(2, "Country is required").openapi({
      description: "Country code",
      example: "US",
    }),
    city: z.string().min(1, "City is required").openapi({
      description: "City name",
      example: "San Francisco",
    }),
    state: z.string().optional().openapi({
      description: "State/province/region",
      example: "CA",
    }),
    postalCode: z.string().optional().openapi({
      description: "Postal/ZIP code",
      example: "94103",
    }),

    // Event Details
    eventType: EventType,
    eventName: z.string().optional().openapi({
      description: "Name of the event",
      example: "Smith-Johnson Wedding",
    }),
    startDate: z
      .string()
      .refine((date) => {
        const inputDate = new Date(date);
        return !isNaN(inputDate.getTime()); // Validates the date format
      }, "Invalid date format")
      .refine((date) => {
        const inputDate = new Date(date);
        const today = getToday();
        return inputDate >= today;
      }, "Date must be in the future")
      .openapi({
        description: "Event start date in ISO 8601 format",
        example: "2024-12-15",
      }),
    endDate: z
      .string()
      .refine((date) => {
        const inputDate = new Date(date);
        return !isNaN(inputDate.getTime()); // Validates the date format
      }, "Invalid date format")
      .optional()
      .openapi({
        description: "Event end date in ISO 8601 format",
        example: "2024-12-17",
      }),
    guestCount: z
      .number()
      .min(1, "Guest count must be at least 1")
      .max(10000)
      .openapi({
        description: "Number of guests",
        example: 150,
      }),
    roomCount: z.number().int().positive().max(20000).optional().openapi({
      description: "Number of rooms needed",
      example: 45,
    }),
    mealCount: z.number().int().positive().optional().openapi({
      description: "Number of meals needed",
      example: 250,
    }),
    budget: z.number().positive().optional().openapi({
      description: "Estimated budget",
      example: 25000,
    }),
    flexibleDates: z.boolean().optional().default(false).openapi({
      description: "Whether dates are flexible",
      example: true,
    }),
    eventNeeds: EventNeedsSchema.optional(),
    eventDescription: z.string().max(2000).optional().openapi({
      description: "Description of the event",
      example: "Looking for a beautiful venue for our winter wedding",
    }),
    marketingConsent: z.boolean().optional().default(false).openapi({
      description: "Consent to receive marketing",
      example: true,
    }),
  })
  .refine((data) => data.email || data.phone, {
    message: "At least one contact method (email or phone) must be provided",
    path: ["_form"],
  })
  .refine(
    (data) => {
      if (!data.endDate) return true;

      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      return endDate >= startDate;
    },
    {
      message: "End date must be on or after start date",
      path: ["endDate"],
    },
  )
  .openapi("LeadSubmission");

// Type for the validated lead data
export type LeadSubmission = z.infer<typeof LeadSubmissionSchema>;

// Response schemas
export const LeadSubmissionResponseSchema = z
  .object({
    id: z.string().openapi({
      description: "Unique identifier for the lead",
      example: "lead_123456",
    }),
    status: z.literal("success").openapi({
      description: "Status of the submission",
      example: "success",
    }),
    createdAt: z.string().openapi({
      description: "Timestamp when the lead was created",
      example: "2023-09-15T14:23:45Z",
    }),
    propertyId: z.string().openapi({
      description: "ID of the property the lead was submitted to",
      example: "property_123",
    }),
  })
  .openapi("LeadSubmissionResponse");

export type LeadSubmissionResponse = z.infer<
  typeof LeadSubmissionResponseSchema
>;

export const ErrorResponseSchema = z
  .object({
    errorType: z.string().openapi({
      description: "Type of error that occurred",
      example: "field_validation_error",
    }),
    errors: z
      .record(z.array(z.string()))
      .optional()
      .openapi({
        description: "Detailed error information by field",
        example: {
          email: ["Please enter a valid email address"],
          phone: ["Please enter a valid phone number with country code"],
        },
      }),
    message: z.string().optional().openapi({
      description: "General error message",
      example: "Validation failed",
    }),
  })
  .openapi("ErrorResponse");
