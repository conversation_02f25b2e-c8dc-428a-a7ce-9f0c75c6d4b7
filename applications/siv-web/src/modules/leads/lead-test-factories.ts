import { Factory } from "fishery";
import {
  eventType,
  lead as leadTable,
  leadFile as leadFileTable,
} from "@/drizzle/schema";
import { faker } from "@faker-js/faker";
import { db } from "@/db/dbclient";
import { randomUUID } from "crypto";
import { Lead, LeadFile, NewLead } from "@/modules/leads/domain/types";
import { toDomain } from "@/modules/leads/infrastructure/lead-db-data-mapper";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";

function futureDateString() {
  const startDate = faker.date.future();
  return startDate.toISOString().split("T")[0];
}

// Generate an end date that is guaranteed to be after the provided start date
function generateEndDate(startDateStr: string) {
  const startDate = new Date(startDateStr);
  // Add 1-7 days to the start date
  const daysToAdd = faker.number.int({ min: 1, max: 7 });
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + daysToAdd);
  return endDate.toISOString().split("T")[0];
}

export const newLeadFactory = Factory.define<NewLead>(() => {
  const now = new Date();

  // Create a valid E.161 format phone number
  const generateValidPhone = () => {
    // Generate a random country code (1-9)
    const countryCode = faker.number.int({ min: 1, max: 9 });
    // Generate a random subscriber number (10 digits)
    const subscriberNumber = faker.string.numeric(10);
    return `+${countryCode}${subscriberNumber}`;
  };

  // Generate start date first
  const startDate = futureDateString();
  // Then generate end date based on start date
  const endDate = generateEndDate(startDate);

  const data: NewLead = {
    propertyId: propertyFactory.build().id!,
    eventName: "My cool event",
    email: faker.internet.email(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    phone: generateValidPhone(),
    company: faker.company.name(),
    city: faker.location.city(),
    state: faker.location.state({ abbreviated: true }),
    country: "US",
    postalCode: faker.location.zipCode(),
    eventType: faker.helpers.arrayElement(
      eventType.enumValues,
    ) as (typeof eventType.enumValues)[number],
    startDate: startDate,
    endDate: endDate,
    guestCount: faker.number.int({ min: 10, max: 700 }),
    roomCount: faker.number.int({ min: 0, max: 600 }),
    budget: faker.number.float({ min: 1000, max: 100000 }),
    eventDescription: faker.lorem.paragraph(),
    flexibleDates: faker.datatype.boolean(),
    marketingConsent: faker.datatype.boolean(),
    highlevelContactId: null,
    highlevelOpportunityId: null,
    assignedSalesRepEmail: faker.internet.email(),
    assignedAt: now.toISOString(),
    source: "form_submission",
    sourceId: randomUUID(),
    eventNeeds: null,
    mealCount: null,
  };
  return data;
});

export class LeadFactoryClass extends Factory<Lead> {
  async withCreatedProperty() {
    const property = await propertyFactory.create();
    return this.params({
      propertyId: property.id,
    });
  }
}

export class LeadFileFactoryClass extends Factory<LeadFile> {
  async withCreatedLead() {
    const leadFactoryWithProperty = await leadFactory.withCreatedProperty();
    const lead = await leadFactoryWithProperty.create();
    return this.params({
      leadId: lead.id,
    });
  }
}

export const leadFactory = LeadFactoryClass.define(({ onCreate }) => {
  onCreate(async (lead) => {
    const dbLead = {
      ...lead,
      startDate: lead.startDate,
      endDate: lead.endDate,
      updatedAt: lead.updatedAt?.toISOString(),
      createdAt: lead.createdAt.toISOString(),
      budget: lead.budget?.toString(),
    };
    const [result] = await db.insert(leadTable).values(dbLead).returning();
    return toDomain(result);
  });

  const now = new Date();
  const baseData = newLeadFactory.build();
  const data: Lead = {
    ...baseData,
    id: randomUUID(),
    createdAt: now,
    updatedAt: now,
  };
  return data;
});

export const leadFileFactory = LeadFileFactoryClass.define(({ onCreate }) => {
  onCreate(async (leadFile) => {
    const [result] = await db
      .insert(leadFileTable)
      .values({
        ...leadFile,
        createdAt: leadFile.createdAt,
        updatedAt: leadFile.updatedAt,
      })
      .returning();
    return result as LeadFile;
  });

  const now = new Date().toISOString();
  const data: LeadFile = {
    id: randomUUID(),
    leadId: randomUUID(),
    fileName: faker.system.fileName(),
    s3Key: `pending/${randomUUID()}/${faker.system.fileName()}`,
    s3KeyWithoutStatusPrefix: `${randomUUID()}/${faker.system.fileName()}`,
    contentType: faker.helpers.arrayElement([
      "application/pdf",
      "image/jpeg",
      "image/png",
    ]),
    createdAt: now,
    updatedAt: now,
  };
  return data;
});
