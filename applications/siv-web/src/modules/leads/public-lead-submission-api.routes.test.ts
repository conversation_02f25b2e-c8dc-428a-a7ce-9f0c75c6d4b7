import { beforeEach, describe, expect, it } from "vitest";
import {
  createPublicLeadSubmissionRoutes,
  LATEST_API_VERSION,
  LeadSubmissionResponseSchema,
  RelaxedLeadSubmissionResponseSchema,
  ErrorResponseSchema,
  excludeSensitiveFields,
} from "./public-lead-submission-api.routes";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import { lead } from "@/drizzle/schema";
import { eq } from "drizzle-orm";
import { LeadCreatedEvent } from "@/modules/leads/domain/events";
import { InMemoryEventPublisher } from "./infrastructure/InMemoryEventPublisher";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import {
  managementGroupApiKeyFactory,
  managementGroupFactory,
} from "@/modules/admin/management-groups/management-group-test-factories";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { newLeadFactory } from "./lead-test-factories";
import { OpenAPIHono } from "@hono/zod-openapi";
import { addDays, addMonths, addYears, format, subDays } from "date-fns";

async function createTestEntities() {
  const group = await managementGroupFactory.create();
  const apiKey = await managementGroupApiKeyFactory.create({
    managementGroupId: group.id,
  });
  const property = await propertyFactory.create({
    managementGroupId: group.id,
  });

  return { group, apiKey, property };
}

async function createNamedTestEntities(groupName = "Test Management Group") {
  const group = await managementGroupFactory.create({ name: groupName });
  const apiKey = await managementGroupApiKeyFactory.create({
    managementGroupId: group.id,
    name: `${groupName} API Key`,
  });
  const property = await propertyFactory.create({
    managementGroupId: group.id,
    name: `${groupName} Property`,
  });

  return { group, apiKey, property };
}

function formatDateString(date: Date): string {
  return format(date, "yyyy-MM-dd");
}

function prepareLeadSubmissionData(
  leadData: Record<string, any>,
): Record<string, any> {
  // Remove fields not needed for submission
  const {
    propertyId,
    source,
    sourceId,
    highlevelContactId,
    highlevelOpportunityId,
    assignedSalesRepEmail,
    assignedAt,
    ...submissionData
  } = leadData;

  // Ensure dates are in correct ISO format
  if (submissionData.startDate) {
    const date = new Date(submissionData.startDate);
    submissionData.startDate = formatDateString(date);
  }

  if (submissionData.endDate) {
    const date = new Date(submissionData.endDate);
    submissionData.endDate = formatDateString(date);

    // Ensure end date is after start date if both are present
    if (
      submissionData.startDate &&
      submissionData.endDate < submissionData.startDate
    ) {
      const startDate = new Date(submissionData.startDate);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 3);
      submissionData.endDate = formatDateString(endDate);
    }
  }

  return submissionData;
}

function validateResponseAgainstSchema(response: any, isRelaxed = false) {
  // Determine which schema to use based on response status and if it's relaxed
  let validationResult;

  if (response.status === 201) {
    // Success response
    const schema = isRelaxed
      ? RelaxedLeadSubmissionResponseSchema
      : LeadSubmissionResponseSchema;
    validationResult = schema.safeParse(response.json);
  } else {
    // Error response
    validationResult = ErrorResponseSchema.safeParse(response.json);
  }

  // If validation failed, log the errors for debugging
  if (!validationResult.success) {
    console.error(
      "Response validation failed:",
      validationResult.error.flatten(),
    );
    throw new Error(
      `Response doesn't conform to the schema: ${JSON.stringify(validationResult.error.format())}`,
    );
  }

  return validationResult.success;
}

async function submitLead(
  app: OpenAPIHono<{ Variables: Variables }>,
  propertyId: string | undefined,
  apiKey: string,
  data: Record<string, any>,
  isRelaxed = false,
) {
  if (!propertyId) {
    throw new Error("PropertyId is required");
  }

  const endpoint = isRelaxed ? "leads/relaxed" : "leads";
  const url = `/properties/${propertyId}/${endpoint}`;

  const res = await app.request(url, {
    method: "POST",
    headers: {
      "X-API-Key": apiKey,
      "X-API-Version": LATEST_API_VERSION,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const responseJson = await res.json();

  // Validate the response against the schema
  validateResponseAgainstSchema(
    { status: res.status, json: responseJson },
    isRelaxed,
  );

  return {
    status: res.status,
    json: responseJson,
  };
}

async function verifyLeadCreated(
  propertyId: string | undefined,
  data: Record<string, any>,
  extraChecks: Record<string, any> = {},
) {
  if (!propertyId) {
    throw new Error("PropertyId is required");
  }

  // Find the created lead
  const createdLeads = await db
    .select()
    .from(lead)
    .where(eq(lead.propertyId, propertyId));

  expect(createdLeads).toHaveLength(1);
  const createdLead = createdLeads[0];

  // Build expected fields by combining submitted data with defaults
  const expectedFields = {
    id: createdLead.id, // We can't know this in advance
    propertyId,
    email: data.email ?? null,
    firstName: data.firstName ?? null,
    lastName: data.lastName ?? null,
    phone: data.phone ?? null,
    company: data.company ?? null,
    city: data.city ?? null,
    state: data.state ?? null,
    postalCode: data.postalCode ?? null,
    eventType: data.eventType,
    startDate: data.startDate ?? null,
    endDate: data.endDate ?? null,
    guestCount: data.guestCount ?? null,
    roomCount: data.roomCount ?? null,
    mealCount: data.mealCount ?? null,
    budget: data.budget ? String(data.budget) : null,
    eventDescription: data.eventDescription ?? null,
    flexibleDates: data.flexibleDates ?? false,
    marketingConsent: data.marketingConsent ?? false,
    eventName: data.eventName ?? null,
    country: data.country ?? null,
    source: "api",
    sourceId: createdLead.sourceId, // We can't know this in advance
    eventNeeds: data.eventNeeds ?? null,
    createdAt: createdLead.createdAt, // We can't know this in advance
    updatedAt: null,
    assignedAt: null,
    assignedSalesRepEmail: null,
    highlevelContactId: null,
    highlevelOpportunityId: null,
    ...extraChecks,
  };

  // Check all fields match
  expect(createdLead).toEqual(expectedFields);

  return createdLead;
}

function verifyEventPublished(
  publisher: InMemoryEventPublisher<LeadCreatedEvent>,
  leadId: string,
) {
  const publishedEvents = publisher.getEvents();
  expect(publishedEvents).toHaveLength(1);
  expect(publishedEvents[0].data).toHaveProperty("leadId", leadId);
}

describe("Public Lead Submission API", () => {
  withTransactionalTests();

  // Create an in-memory event publisher for lead created events
  const leadCreatedEventPublisher =
    new InMemoryEventPublisher<LeadCreatedEvent>();

  function buildTestSetup() {
    const app = new OpenAPIHono<{ Variables: Variables }>();

    const routes = createPublicLeadSubmissionRoutes({
      leadCreatedEventPublisher,
    });

    app.route("/", routes);

    return { app };
  }

  beforeEach(() => {
    leadCreatedEventPublisher.clear();
  });

  describe("POST /properties/{propertyId}/leads", () => {
    it("should successfully create a lead with valid data", async () => {
      const { app } = buildTestSetup();

      // Create test entities
      const { group, apiKey, property } = await createNamedTestEntities();

      // Create lead submission data from factory
      const leadData = newLeadFactory.build({
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "+14155552671", // Valid E.161 format
        eventType: "wedding",
        country: "US",
        state: "NJ",
        city: "Jersey City",
      });

      // Prepare submission data
      const submissionData = prepareLeadSubmissionData(leadData);

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        submissionData,
      );

      // Verify lead in database
      const createdLead = await verifyLeadCreated(property.id, submissionData);

      // Verify response
      const responseJson = await res.json;
      expect(res.status, `${JSON.stringify(responseJson)}`).toBe(201);
      expect(responseJson).toHaveProperty("propertyId", property.id);

      // Verify lead object is included in response
      expect(responseJson).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(responseJson.lead).toEqual(
        JSON.parse(JSON.stringify(sanitizedLead)),
      );

      // Explicitly verify sensitive fields are not present
      expect(responseJson.lead).not.toHaveProperty("highlevelContactId");
      expect(responseJson.lead).not.toHaveProperty("highlevelOpportunityId");
      expect(responseJson.lead).not.toHaveProperty("assignedSalesRepEmail");
      expect(responseJson.lead).not.toHaveProperty("assignedAt");

      // Verify api_lead_submissions record
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should create a lead with minimal required data", async () => {
      const { app } = buildTestSetup();

      // Create test entities
      const { apiKey, property } = await createTestEntities();

      // Create minimal lead data
      const minimalLeadData = {
        email: "<EMAIL>",
        eventType: "wedding",
      };

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        minimalLeadData,
      );

      // Verify the lead was created in database
      const createdLead = await verifyLeadCreated(property.id, minimalLeadData);

      // Verify response
      const responseJson = await res.json;
      expect(res.status, `${JSON.stringify(responseJson)}`).toBe(201);
      expect(responseJson).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(responseJson.lead).toEqual(
        JSON.parse(JSON.stringify(sanitizedLead)),
      );

      // Explicitly verify sensitive fields are not present
      expect(responseJson.lead).not.toHaveProperty("highlevelContactId");
      expect(responseJson.lead).not.toHaveProperty("highlevelOpportunityId");

      // Verify api_lead_submissions record
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should create a lead with phone as the only contact method", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      // Create lead data from factory with only phone
      const phoneOnlyData = newLeadFactory.build({
        email: undefined, // Use empty string instead of undefined
        phone: "+15551234567", // Valid E.161 format
        firstName: "Phone",
        lastName: "Only",
        eventType: "wedding",
        country: "US",
        city: "Chicago",
      });

      // Prepare submission data
      const submissionData = prepareLeadSubmissionData(phoneOnlyData);

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        submissionData,
      );

      // Verify response and database
      expect(res.status).toBe(201);
      await verifyLeadCreated(property.id, submissionData);
    });

    it("should create a lead with event needs array", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      // Lead with event needs
      const leadData = newLeadFactory.build({
        firstName: "Event",
        lastName: "Needs",
        email: "<EMAIL>",
        eventType: "corporate_meeting",
      });

      // Prepare submission data with event needs
      const submissionData = {
        firstName: leadData.firstName,
        lastName: leadData.lastName,
        email: leadData.email,
        eventType: leadData.eventType,
        country: "US",
        city: "Dallas",
        eventNeeds: ["MEETING_SPACE", "CATERING", "GUESTROOMS"],
      };

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        submissionData,
      );

      // Verify response and database
      expect(res.status).toBe(201);
      await verifyLeadCreated(property.id, submissionData);
    });

    it("should create a wedding lead with wedding-specific event needs", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      const today = new Date();
      const weddingDate = addMonths(today, 6); // 6 months from now
      const weddingDateStr = format(weddingDate, "yyyy-MM-dd");

      // Create wedding lead with specific event needs
      const weddingData = {
        firstName: "Wedding",
        lastName: "Couple",
        email: "<EMAIL>",
        phone: "+14155559876",
        eventType: "wedding",
        country: "US",
        city: "New Orleans",
        state: "LA",
        eventName: "Smith-Johnson Wedding",
        startDate: weddingDateStr,
        guestCount: 150,
        budget: 35000,
        eventDescription: "Destination wedding with a focus on local cuisine",
        eventNeeds: [
          "WEDDING_CEREMONY",
          "WEDDING_RECEPTION",
          "REHEARSAL_DINNER",
          "SENDOFF_BRUNCH",
        ],
      };

      // Submit the lead
      const res = await submitLead(app, property.id, apiKey.key, weddingData);

      // Verify the lead was created in the database
      const createdLead = await verifyLeadCreated(property.id, weddingData);

      // Verify response
      expect(res.status).toBe(201);
      expect(res.json).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(res.json.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));

      // Verify that the wedding-specific event needs were properly saved
      expect(createdLead.eventNeeds).toEqual([
        "WEDDING_CEREMONY",
        "WEDDING_RECEPTION",
        "REHEARSAL_DINNER",
        "SENDOFF_BRUNCH",
      ]);
    });

    it("should create a comprehensive lead with all possible wedding details", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      const today = new Date();
      const startDate = addYears(today, 1); // Next year
      const startDateStr = format(startDate, "yyyy-MM-dd");

      const endDate = addDays(startDate, 4); // 4-day wedding weekend
      const endDateStr = format(endDate, "yyyy-MM-dd");

      // Comprehensive wedding lead with all possible fields
      const comprehensiveWeddingData = {
        firstName: "Complete",
        lastName: "Wedding",
        email: "<EMAIL>",
        phone: "+***********",
        company: "Family Business",
        country: "US",
        city: "Aspen",
        state: "CO",
        postalCode: "81611",
        eventType: "wedding",
        eventName: "Complete Wedding Experience",
        startDate: startDateStr,
        endDate: endDateStr,
        guestCount: 200,
        roomCount: 75,
        mealCount: 450, // Multiple meals over several days
        budget: 75000,
        flexibleDates: true,
        eventNeeds: [
          "WEDDING_CEREMONY",
          "WEDDING_RECEPTION",
          "REHEARSAL",
          "REHEARSAL_DINNER",
          "SENDOFF_BRUNCH",
          "BACHELOR_PARTY",
        ],
        eventDescription:
          "Luxury mountain destination wedding with all activities and amenities included for a full weekend celebration.",
        marketingConsent: true,
      };

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        comprehensiveWeddingData,
      );

      // Verify the lead was created in the database
      const createdLead = await verifyLeadCreated(
        property.id,
        comprehensiveWeddingData,
      );

      // Verify response
      expect(res.status).toBe(201);
      expect(res.json).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(res.json.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));
    });

    it("should return 401 with invalid API key", async () => {
      const { app } = buildTestSetup();

      // Create test property (without creating an API key)
      const { group, property } = await createTestEntities();

      // Prepare test data
      const testData = newLeadFactory.build();
      const submissionData = prepareLeadSubmissionData(testData);

      // Submit with invalid API key
      const res = await submitLead(
        app,
        property.id,
        "invalid_api_key_123",
        submissionData,
      );

      // Verify response
      expect(res.status).toBe(401);
      expect(res.json.errorType).toBe("authentication_error");

      // Verify no lead was created
      const createdLeads = await db.select().from(lead);
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 404 when property does not exist", async () => {
      const { app } = buildTestSetup();

      // Create management group and API key
      const { group, apiKey } = await createTestEntities();

      // Prepare test data
      const testData = newLeadFactory.build();
      const submissionData = prepareLeadSubmissionData(testData);

      // Submit to non-existent property
      const nonExistentPropertyId = "12345678-1234-1234-1234-123456789abc";
      const res = await submitLead(
        app,
        nonExistentPropertyId,
        apiKey.key,
        submissionData,
      );

      // Verify response
      expect(res.status).toBe(404);
      expect(res.json.errorType).toBe("not_found_error");

      // Verify no lead was created
      const createdLeads = await db.select().from(lead);
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 403 when property does not belong to API key's management group", async () => {
      const { app } = buildTestSetup();

      // Create two management groups
      const group1 = await managementGroupFactory.create();
      const group2 = await managementGroupFactory.create();

      // Create API key for first group
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: group1.id,
      });

      // Create property for second group
      const testProperty = await propertyFactory.create({
        managementGroupId: group2.id,
      });

      // Prepare test data
      const testData = newLeadFactory.build();
      const submissionData = prepareLeadSubmissionData(testData);

      // Try to submit lead with API key from wrong management group
      const res = await submitLead(
        app,
        testProperty.id,
        apiKey.key,
        submissionData,
      );

      // Verify response
      expect(res.status).toBe(403);
      expect(res.json.errorType).toBe("authorization_error");

      // Verify no lead was created
      const createdLeads = await db.select().from(lead);
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 400 when neither email nor phone is provided", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      // Lead with no contact methods
      const invalidData = {
        firstName: "No",
        lastName: "Contact",
        eventType: "wedding",
        country: "US",
        city: "Seattle",
      };

      // Submit the lead
      const res = await submitLead(app, property.id, apiKey.key, invalidData);

      // Verify response
      expect(res.status).toBe(400);

      // In our new implementation, we should get field_validation_error
      expect(res.json.errorType).toBe("field_validation_error");

      // Verify no lead was created
      const createdLeads = await db.select().from(lead);
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 400 when wedding-specific event needs are used with a non-wedding event type", async () => {
      const { app } = buildTestSetup();

      // Create test setup
      const { apiKey, property } = await createTestEntities();

      // Calculate future dates
      const today = new Date();
      const startDate = new Date(today);
      startDate.setDate(today.getDate() + 30); // 30 days from now
      const startDateStr = formatDateString(startDate);

      // Create corporate meeting with wedding-specific event needs
      const invalidEventNeedsData = {
        firstName: "Invalid",
        lastName: "EventNeeds",
        email: "<EMAIL>",
        eventType: "corporate_meeting", // Non-wedding event type
        country: "US",
        city: "Boston",
        startDate: startDateStr,
        guestCount: 100,
        eventNeeds: [
          "MEETING_SPACE", // Valid for corporate meeting
          "WEDDING_RECEPTION", // Should be invalid for corporate meeting
          "CATERING", // Valid for corporate meeting
        ],
      };

      // Submit the lead
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        invalidEventNeedsData,
      );

      // Verify response
      expect(res.status).toBe(400);
      expect(res.json.errorType).toBe("field_validation_error");

      // Check that the error is specifically about the eventNeeds field
      expect(res.json.errors).toBeDefined();
      expect(res.json.errors.eventNeeds).toBeDefined();

      // Verify the error message mentions the invalid wedding event need
      const errorMessage = res.json.errors.eventNeeds.join(" ");
      expect(errorMessage).toEqual(
        "Wedding-specific event needs can only be used with the wedding event type",
      );

      // Verify no lead was created
      const createdLeads = await db.select().from(lead);
      expect(createdLeads).toHaveLength(0);
    });

    it("should not allow a past start date (>2 days ago to account for timezones)", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Use date-fns to create a date 3 days in the past
      const today = new Date();
      const pastDate = subDays(today, 3);
      const pastDateStr = format(pastDate, "yyyy-MM-dd");

      const invalidData = {
        email: "<EMAIL>", // Unique email
        eventType: "wedding",
        startDate: pastDateStr,
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.startDate).toContain(
        "Must be a valid date in the future",
      );
    });

    it("should return 400 for end date before start date", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 5); // 5 days from now
      const startDateStr = futureDate.toISOString().split("T")[0];

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() + 3); // 3 days from now (before start date)
      const endDateStr = pastDate.toISOString().split("T")[0];

      const invalidData = {
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: startDateStr,
        endDate: endDateStr,
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.endDate).toContain(
        "End date must be on or after start date",
      );
    });

    it("should return 400 for invalid date format", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: "2024/12/15", // Invalid format
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.startDate).toContain(
        "Must be a valid date in the future",
      );
    });

    it("should return 400 for invalid eventType", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "birthday_party", // Not a valid enum value
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.eventType).toBeDefined();
    });

    it("should return 400 for invalid eventNeeds value", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "corporate_meeting",
        eventNeeds: ["CATERING", "INVALID_NEED"], // Contains an invalid enum value
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.eventNeeds).toEqual([
        "Invalid enum value. Expected 'MEETING_SPACE' | 'CATERING' | 'GUESTROOMS' | 'ACTIVITIES' | 'WEDDING_CEREMONY' | 'WEDDING_RECEPTION' | 'REHEARSAL' | 'REHEARSAL_DINNER' | 'SENDOFF_BRUNCH' | 'HONEYMOON' | 'BACHELOR_PARTY', received 'INVALID_NEED'",
      ]);
    });

    it("should return 400 for firstName exceeding max length", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "celebration",
        firstName: "a".repeat(256),
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.firstName).toBeDefined(); // Zod should catch max length defined in schema
    });

    it("should return 400 for guestCount exceeding max value", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "celebration",
        guestCount: 500001, // Max is 500,000
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.guestCount).toBeDefined();
    });

    it("should return 400 for guestCount below min value", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "celebration",
        guestCount: 0, // Min is 1
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.guestCount).toBeDefined();
    });

    it("should return 400 for invalid country code format", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidData = {
        email: "<EMAIL>",
        eventType: "celebration",
        country: "USA", // Invalid format (should be 2 letters)
      };

      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(invalidData),
      });

      expect(res.status).toBe(400);
      const body = await res.json();
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors?.country).toBeDefined();
    });

    it("should accept dates 2 days in the past to account for timezone differences", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Use date-fns to create a date exactly 2 days in the past
      const today = new Date();
      const twoDaysAgo = subDays(today, 2);
      const twoDaysAgoStr = format(twoDaysAgo, "yyyy-MM-dd");

      // End date 1 day after start date
      const oneDayAfterStart = addDays(twoDaysAgo, 1);
      const oneDayAfterStartStr = format(oneDayAfterStart, "yyyy-MM-dd");

      // Create submission with dates 2 days in the past - using strict endpoint
      const submissionData = {
        firstName: "Timezone",
        lastName: "Difference",
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: twoDaysAgoStr,
        endDate: oneDayAfterStartStr,
        guestCount: 100,
      };

      // Submit the lead to the strict endpoint
      const res = await app.request(`/properties/${testProperty.id}/leads`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey.key,
        },
        body: JSON.stringify(submissionData),
      });

      // Should be successful (201)
      expect(res.status).toBe(201);
      const responseBody = await res.json();

      // Verify the dates were accepted
      expect(responseBody.lead).toMatchObject({
        startDate: twoDaysAgoStr,
        endDate: oneDayAfterStartStr,
      });

      // Verify the database record has the correct dates
      const createdLead = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email))
        .then((results) => results[0]);

      expect(createdLead).toBeDefined();
      expect(createdLead).toMatchObject({
        startDate: twoDaysAgoStr,
        endDate: oneDayAfterStartStr,
      });

      // Verify the API submission record metadata
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toMatchObject({
        relaxedValidations: false,
        passedStrictValidations: true,
      });

      // Verify the lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });
  });

  describe("POST /properties/{propertyId}/leads/relaxed", () => {
    it("should successfully create a lead with valid data", async () => {
      const { app } = buildTestSetup();

      // Create test entities
      const { apiKey, property } = await createNamedTestEntities();

      // Create lead data
      const leadData = newLeadFactory.build({
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "+14155552671",
        eventType: "wedding",
        country: "US",
        state: "NJ",
        city: "Jersey City",
      });

      // Prepare submission data
      const submissionData = prepareLeadSubmissionData(leadData);

      // Submit to the relaxed endpoint
      const res = await submitLead(
        app,
        property.id,
        apiKey.key,
        submissionData,
        true,
      );

      // Verify lead in database
      const createdLead = await verifyLeadCreated(property.id, submissionData);

      // Verify response
      expect(res.status).toBe(201);
      expect(res.json).toHaveProperty("propertyId", property.id);

      // Verify lead object is included in response
      expect(res.json).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(res.json.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));

      // Explicitly verify sensitive fields are not present
      expect(res.json.lead).not.toHaveProperty("highlevelContactId");
      expect(res.json.lead).not.toHaveProperty("highlevelOpportunityId");
      expect(res.json.lead).not.toHaveProperty("assignedSalesRepEmail");
      expect(res.json.lead).not.toHaveProperty("assignedAt");

      // Verify api_lead_submissions record
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should accept a past start date and return warnings", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Create data with past date using date-fns
      const today = new Date();
      const pastDate = subDays(today, 5); // 5 days ago
      const pastDateStr = format(pastDate, "yyyy-MM-dd");

      const submissionData = {
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: pastDateStr, // Date in the past
      };

      // Submit to relaxed endpoint
      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      // Verify the lead was created despite the warning
      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...submissionData,
        startDate: null,
      });

      // Verify response
      const body = await res.json();
      expect(res.status, `Response: ${JSON.stringify(body)}`).toBe(201);
      expect(body).toHaveProperty("warnings");
      expect(body.warnings).toBeInstanceOf(Object);

      // Verify lead object is included in response
      expect(body).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(body.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));

      // Verify the specific warning about past date
      expect(body.warnings).toHaveProperty("startDate");
      expect(body.warnings.startDate).toBeInstanceOf(Array);
      expect(body.warnings.startDate[0]).toContain(
        "Must be a valid date in the future",
      );

      // Add more exhaustive test - check the full warnings object
      expect(body.warnings).toEqual({
        startDate: ["Must be a valid date in the future"],
      });

      // Verify api_lead_submissions record
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should accept invalid guest count and return warnings", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const submissionData = {
        email: "<EMAIL>",
        eventType: "wedding",
        guestCount: -5, // Negative guest count, invalid in strict validation
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      // Verify the lead was created
      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...submissionData,
        guestCount: null,
      });

      const body = await res.json();
      expect(res.status).toBe(201);
      expect(body).toHaveProperty("warnings");
      expect(body.warnings).toBeInstanceOf(Object);

      // Check for lead object in response
      expect(body).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(body.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));

      // Check for warning about guest count
      expect(body.warnings).toHaveProperty("guestCount");

      // Add more exhaustive test - check the full warnings object
      expect(body.warnings).toEqual({
        guestCount: ["Number must be greater than or equal to 1"],
      });
    });

    it("should still reject missing email and phone", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const noContactData = {
        firstName: "No",
        lastName: "Contact",
        eventType: "wedding",
        // Missing both email and phone
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(noContactData),
        },
      );

      const body = await res.json();
      expect(res.status).toBe(400);
      expect(body.errorType).toBe("field_validation_error");

      // Message should indicate email or phone is required
      expect(body.message).toContain("At least one contact method");

      // Verify no lead was created
      const createdLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.firstName, "No"));
      expect(createdLeads).toHaveLength(0);
    });

    it("should still reject invalid eventType", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const invalidEventTypeData = {
        email: "<EMAIL>",
        eventType: "invalid_event_type", // Not in the enum
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(invalidEventTypeData),
        },
      );

      const body = await res.json();
      expect(res.status).toBe(400);
      expect(body.errorType).toBe("field_validation_error");
      expect(body.errors).toHaveProperty("eventType");

      // Verify no lead was created
      const createdLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.email, invalidEventTypeData.email));
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 401 with invalid API key", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const submissionData = {
        email: "<EMAIL>",
        eventType: "wedding",
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": "invalid_api_key_123",
          },
          body: JSON.stringify(submissionData),
        },
      );

      expect(res.status).toBe(401);
      const responseJson = await res.json();
      expect(responseJson.errorType).toBe("authentication_error");

      // Verify no lead was created
      const createdLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email));
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 404 when property does not exist", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });

      const submissionData = {
        email: "<EMAIL>",
        eventType: "wedding",
      };

      const nonExistentPropertyId = "12345678-1234-1234-1234-123456789abc";

      const res = await app.request(
        `/properties/${nonExistentPropertyId}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      expect(res.status).toBe(404);
      const responseJson = await res.json();
      expect(responseJson.errorType).toBe("not_found_error");

      // Verify no lead was created
      const createdLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email));
      expect(createdLeads).toHaveLength(0);
    });

    it("should return 403 when property does not belong to API key's management group", async () => {
      const { app } = buildTestSetup();

      // Create two separate management groups
      const group1 = await managementGroupFactory.create();
      const group2 = await managementGroupFactory.create();

      // Create API key for first group
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: group1.id,
      });

      // Create property for second group
      const testProperty = await propertyFactory.create({
        managementGroupId: group2.id,
      });

      const submissionData = {
        email: "<EMAIL>",
        eventType: "wedding",
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      expect(res.status).toBe(403);
      const responseJson = await res.json();
      expect(responseJson.errorType).toBe("authorization_error");

      // Verify no lead was created
      const createdLeads = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email));
      expect(createdLeads).toHaveLength(0);
    });

    it("should accept multiple validation issues and return all warnings", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Create past dates
      const pastDate = subDays(new Date(), 4);
      const pastDateStr = pastDate.toISOString().split("T")[0];

      const endDate = subDays(pastDate, 3); // End date before start date
      const endDateStr = endDate.toISOString().split("T")[0];

      const multiIssueData = {
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: pastDateStr, // Past date
        endDate: endDateStr, // End date before start date
        guestCount: -10, // Negative guest count
        roomCount: -5, // Negative room count
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(multiIssueData),
        },
      );

      // Verify the lead was created with the problematic values
      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...multiIssueData,
        guestCount: null,
        roomCount: null,
        endDate: null,
        startDate: null,
      });

      const body = await res.json();
      expect(res.status).toBe(201);
      expect(body).toHaveProperty("warnings");
      expect(body.warnings).toBeInstanceOf(Object);

      // Check for lead object in response
      expect(body).toHaveProperty("lead");

      // Get sanitized version of the created lead
      const sanitizedLead = excludeSensitiveFields(createdLead);

      expect(body.lead).toEqual(JSON.parse(JSON.stringify(sanitizedLead)));

      // Should have multiple warnings
      expect(Object.keys(body.warnings).length).toBeGreaterThanOrEqual(2);

      // Check for specific warnings
      expect(body.warnings).toHaveProperty("startDate");
      expect(body.warnings).toHaveProperty("endDate");
      expect(body.warnings).toHaveProperty("guestCount");

      // Verify the API submission record was created
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should accept submission with past date and return specific warning", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Create a date in the past
      const pastDate = subDays(new Date(), 10);
      const pastDateStr = pastDate.toISOString().split("T")[0];

      const submissionData = {
        email: "<EMAIL>",
        firstName: "Past",
        lastName: "Date",
        eventType: "wedding",
        startDate: pastDateStr, // Past date should trigger a warning
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      // Verify the lead was created despite the warning
      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...submissionData,
        startDate: null,
      });

      // Verify response
      const body = await res.json();
      expect(res.status).toBe(201);
      expect(body).toHaveProperty("warnings");
      expect(body.warnings).toBeInstanceOf(Object);

      // Verify the specific warning about past date
      expect(body.warnings).toHaveProperty("startDate");
      expect(body.warnings.startDate).toBeInstanceOf(Array);
      expect(body.warnings.startDate[0]).toContain(
        "Must be a valid date in the future",
      );

      // Add more exhaustive test - check the full warnings object
      expect(body.warnings).toEqual({
        startDate: ["Must be a valid date in the future"],
      });

      // Verify api_lead_submissions record shows relaxed validation with warnings
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should accept submission with invalid dates and return specific warnings", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      const submissionData = {
        email: "<EMAIL>",
        firstName: "Past",
        lastName: "Date",
        eventType: "wedding",
        startDate: "9/29/2023", // Bad date format; needs to be dashed
        endDate: "not-a-date-at-all",
      };

      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      const responseBody = await res.json();
      expect(res.status, `response: ${JSON.stringify(responseBody)}`).toBe(201);

      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...submissionData,
        startDate: null,
        endDate: null,
      });

      expect(responseBody).toHaveProperty("warnings");

      expect(responseBody.warnings).toEqual({
        startDate: [
          "Invalid date format",
          "Must be a valid date in the future",
        ],
        endDate: [
          "Invalid date format",
          "Must be a valid date in the future",
          "End date must be on or after start date",
        ],
      });

      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();
      expect(apiSubmission?.relaxedValidations).toEqual(true);
      expect(apiSubmission?.passedStrictValidations).toEqual(false);

      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should display warnings about invalid fields but not reject the submission", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Create submission with multiple invalid fields
      const submissionData = {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "************",
        eventType: "wedding",
        startDate: "2020-01-01", // Past date (invalid)
        endDate: "2020-01-01", // Same as start date (not ideal)
        guestCount: -5, // Negative guest count (invalid)
        budget: -1000, // Negative budget (invalid)
        timeOfDay: "invalid_time", // Invalid time of day
        weddingDetails: {
          ceremonyLocation: "", // Empty string
          receptionLocation: 123, // Wrong type (should be string)
        },
        eventNeeds: ["CATERING"], // Contains invalid need
        message: "A".repeat(2000), // Excessively long message
        referralSource: "INVALID_SOURCE", // Invalid referral source
        contactPreference: "carrier_pigeon", // Invalid contact preference
        countryCode: "INVALID", // Invalid country code
      };

      // Submit the lead
      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      // Add detailed error logging if it fails
      if (res.status !== 201) {
        const errorBody = await res.clone().json();
        console.error("Relaxed lead submission failed:", {
          status: res.status,
          body: errorBody,
          submissionData,
        });
      }

      const responseBody = await res.json();
      // Verify successful response with warnings
      expect(res.status, `response: ${JSON.stringify(responseBody)}`).toBe(201);
      expect(responseBody).toHaveProperty("warnings");

      // Should have multiple warnings for different fields
      expect(Object.keys(responseBody.warnings).length).toBeGreaterThanOrEqual(
        3,
      );

      // Check for specific field warnings
      expect(responseBody.warnings).toHaveProperty("startDate");

      // Add more exhaustive test - check for multiple warnings fields
      // The exact warning messages might vary, so we'll verify some key fields exist
      const warningsObj = responseBody.warnings;
      expect(warningsObj).toBeDefined();

      // Make sure the warnings object has the expected structure
      // These are the fields we expect to have validation warnings
      const expectedWarningFields = ["startDate", "guestCount", "budget"];
      expectedWarningFields.forEach((field) => {
        expect(warningsObj).toHaveProperty(field);
        expect(Array.isArray(warningsObj[field])).toBe(true);
        expect(warningsObj[field].length).toBeGreaterThan(0);
      });

      const createdLead = await verifyLeadCreated(testProperty.id, {
        ...submissionData,
        budget: null,
        guestCount: null,
        startDate: null,
        endDate: null,
      });
      // Verify the API submission record was created
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toBeDefined();

      // Verify that a lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should nullify invalid fields in the response while returning warnings", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Past date (invalid)
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);
      const pastDateStr = pastDate.toISOString().split("T")[0];

      // Create submission with multiple types of invalid fields
      const submissionData = {
        firstName: "Null",
        lastName: "Fields",
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: pastDateStr, // Past date (invalid)
        endDate: "2020-01-01", // Past date (invalid)
        guestCount: -50, // Negative guest count (invalid)
        roomCount: -10, // Negative room count (invalid)
        budget: -5000, // Negative budget (invalid)
        country: "USA", // Invalid country code (should be 2 letters)
      };

      // Submit the lead
      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      expect(res.status).toBe(201);
      const responseBody = await res.json();

      // Check that warnings were returned for invalid fields
      expect(responseBody).toHaveProperty("warnings");
      expect(Object.keys(responseBody.warnings).length).toBeGreaterThanOrEqual(
        5,
      );

      // Verify the lead object in response has invalid fields nullified
      expect(responseBody.lead).toMatchObject({
        firstName: submissionData.firstName,
        lastName: submissionData.lastName,
        email: submissionData.email,
        eventType: submissionData.eventType,
        startDate: null,
        endDate: null,
        guestCount: null,
        roomCount: null,
        budget: null,
        country: null,
      });

      // Verify the database also has the lead with invalid fields stored as null
      const createdLead = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email))
        .then((results) => results[0]);

      expect(createdLead).toBeDefined();
      expect(createdLead).toMatchObject({
        firstName: submissionData.firstName,
        lastName: submissionData.lastName,
        email: submissionData.email,
        eventType: submissionData.eventType,
        startDate: null,
        endDate: null,
        guestCount: null,
        roomCount: null,
        budget: null,
        country: null,
      });

      // Verify the API submission record metadata
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toMatchObject({
        relaxedValidations: true,
        passedStrictValidations: false,
      });

      // Verify the lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });

    it("should accept dates 2 days in the past in relaxed mode", async () => {
      const { app } = buildTestSetup();
      const testGroup = await managementGroupFactory.create();
      const apiKey = await managementGroupApiKeyFactory.create({
        managementGroupId: testGroup.id,
      });
      const testProperty = await propertyFactory.create({
        managementGroupId: testGroup.id,
      });

      // Date 2 days in the past (should be valid due to timezone differences)
      const currentDate = new Date();
      const relaxedTwoDaysAgo = subDays(currentDate, 2);
      const relaxedTwoDaysAgoStr = format(relaxedTwoDaysAgo, "yyyy-MM-dd");

      // End date 1 day after start date
      const relaxedOneDayAfterStart = addDays(relaxedTwoDaysAgo, 1);
      const relaxedOneDayAfterStartStr = format(
        relaxedOneDayAfterStart,
        "yyyy-MM-dd",
      );

      // Create submission with dates 2 days in the past
      const submissionData = {
        firstName: "Timezone",
        lastName: "Difference",
        email: "<EMAIL>",
        eventType: "wedding",
        startDate: relaxedTwoDaysAgoStr,
        endDate: relaxedOneDayAfterStartStr,
        guestCount: 100,
      };

      // Submit the lead to the relaxed endpoint
      const res = await app.request(
        `/properties/${testProperty.id}/leads/relaxed`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": apiKey.key,
          },
          body: JSON.stringify(submissionData),
        },
      );

      expect(res.status).toBe(201);
      const responseBody = await res.json();

      // Check that there are no warnings for the dates
      expect(
        responseBody,
        `response body full: ${JSON.stringify(responseBody)}`,
      ).toHaveProperty("warnings");
      const warnings = responseBody.warnings || {};
      expect(warnings.startDate).toBeUndefined();
      expect(warnings.endDate).toBeUndefined();

      // Verify the dates were preserved in the response and not nullified
      expect(responseBody.lead).toMatchObject({
        startDate: relaxedTwoDaysAgoStr,
        endDate: relaxedOneDayAfterStartStr,
      });

      // Verify the database record has the correct dates
      const createdLead = await db
        .select()
        .from(lead)
        .where(eq(lead.email, submissionData.email))
        .then((results) => results[0]);

      expect(createdLead).toBeDefined();
      expect(createdLead).toMatchObject({
        startDate: relaxedTwoDaysAgoStr,
        endDate: relaxedOneDayAfterStartStr,
      });

      // Verify the API submission record shows relaxed validation but passed strict validation
      const apiSubmission = await db.query.apiLeadSubmissions.findFirst({
        where: (als, { eq }) => eq(als.id, createdLead.sourceId),
      });
      expect(apiSubmission).toMatchObject({
        relaxedValidations: true,
        passedStrictValidations: true, // Should pass strict validation despite being 2 days in past
      });

      // Verify the lead created event was published
      verifyEventPublished(leadCreatedEventPublisher, createdLead.id);
    });
  });
});
