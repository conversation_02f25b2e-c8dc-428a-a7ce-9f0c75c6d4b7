import logger from "@/logger";
import { SivInngestClient } from "@/inngest-client";
import { GetEvents } from "inngest";
import { SendEventPayload } from "inngest/helpers/types";

type SivInngestEventPayload = SendEventPayload<GetEvents<SivInngestClient>>;

export class InngestEventPublisher<T extends SivInngestEventPayload> {
  constructor(private readonly inngest: SivInngestClient) {}

  async publish(event: T): Promise<void> {
    await this.inngest.send(event);
    logger.info(event, "inngest event published");
  }
}
