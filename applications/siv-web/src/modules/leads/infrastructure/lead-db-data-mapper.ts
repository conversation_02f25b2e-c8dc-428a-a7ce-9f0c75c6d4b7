import { InferSelectModel } from "drizzle-orm";
import { lead } from "@/drizzle/schema";
import { Lead } from "@/modules/leads/domain/types";

type DbRecordType = InferSelectModel<typeof lead>;
export function toDomain(leadRecord: DbRecordType): Lead {
  const lead: Lead = {
    ...leadRecord,
    highlevelContactId: leadRecord.highlevelContactId ?? null,
    startDate: leadRecord.startDate ? leadRecord.startDate : null,
    endDate: leadRecord.endDate ? leadRecord.endDate : null,
    budget: leadRecord.budget ? parseFloat(leadRecord.budget) : null,
    createdAt: new Date(leadRecord.createdAt!),
    updatedAt: leadRecord.updatedAt ? new Date(leadRecord.updatedAt) : null,
    eventNeeds: leadRecord.eventNeeds ?? null,
  };
  return lead;
}
