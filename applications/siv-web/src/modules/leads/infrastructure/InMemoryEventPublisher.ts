/**
 * An in-memory event publisher for testing
 */
export class InMemoryEventPublisher<
  T extends { name: string; data: any; id: string },
> {
  private events: T[] = [];

  /**
   * Publish an event
   */
  async publish(event: T): Promise<void> {
    this.events.push(event);
  }

  /**
   * Get all published events
   */
  getEvents(): T[] {
    return this.events;
  }

  /**
   * Clear all events
   */
  clear(): void {
    this.events = [];
  }
}
