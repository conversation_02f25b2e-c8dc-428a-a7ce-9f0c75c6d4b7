import { create<PERSON>out<PERSON>, OpenAPIH<PERSON>, z } from "@hono/zod-openapi";
import { Variables } from "@/auth";
import { apiKeyAuth } from "@/modules/auth/api-key-middleware";
import { rateLimiter } from "hono-rate-limiter";
import { db } from "@/db/dbclient";
import {
  apiLeadSubmissions,
  eventNeed,
  eventType,
  lead,
  managementGroupApiKey,
  property,
} from "@/drizzle/schema"; // Removed unused insertApiLeadSubmissionSchema
import { eq, InferSelectModel } from "drizzle-orm";
import logger from "@/logger";
import { LeadCreatedEventPublisher } from "@/modules/leads/domain/events";
import { subDays, startOfDay, parseISO } from "date-fns";

const API_VERSIONS = ["2025-04-14"] as const;
export const LATEST_API_VERSION = API_VERSIONS[API_VERSIONS.length - 1];

const apiVersionHeaderSchema = z
  .enum(API_VERSIONS)
  .nullish()
  .default(LATEST_API_VERSION)
  .openapi({
    description: `API version. Defaults to the latest released API version. Valid values are: ${API_VERSIONS.join(" | ")}`,
    example: API_VERSIONS[0],
  });

// Event Type schema that matches database schema
export const EventType = z.enum(eventType.enumValues).openapi({
  description: "Type of event",
  example: "wedding",
});

const EventNeedsSchema = z.array(z.enum(eventNeed.enumValues)).openapi({
  description: "Specific event needs",
  example: ["WEDDING_CEREMONY", "WEDDING_RECEPTION"],
});

// Define country schema
const CountrySchema = z.string().length(2).openapi({
  description: "Valid 2-letter ISO country code",
  example: "US",
});

// Define wedding-specific event needs
const WEDDING_SPECIFIC_EVENT_NEEDS = [
  "WEDDING_CEREMONY",
  "WEDDING_RECEPTION",
  "REHEARSAL",
  "REHEARSAL_DINNER",
  "SENDOFF_BRUNCH",
  "HONEYMOON",
  "BACHELOR_PARTY",
];

// Helper function to validate dates
const getToday = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return today;
};

// Helper functions for schema field creation to reduce duplication
// This helps centralize field descriptions and examples
const schemaFields = {
  createStringField: (
    description: string,
    example: string,
    options?: { maxLength?: number },
  ) => {
    // Simplified implementation to avoid type issues
    const field = options?.maxLength
      ? z.string().max(options.maxLength).nullish()
      : z.string().nullish();

    return field.openapi({
      description,
      example,
    });
  },
  createNumberField: (
    description: string,
    example: number,
    options?: {
      min?: number;
      max?: number;
      int?: boolean;
      positive?: boolean;
    },
  ) => {
    let field = z.number();
    if (options?.int) {
      field = field.int();
    }
    if (options?.positive) {
      field = field.positive();
    }
    if (options?.min !== undefined) {
      field = field.min(options.min);
    }
    if (options?.max !== undefined) {
      field = field.max(options.max);
    }
    return field.nullish().openapi({
      description,
      example,
    });
  },
  createBooleanField: (
    description: string,
    example: boolean,
    defaultValue?: boolean,
  ) => {
    const field = z.boolean().nullish();
    if (defaultValue !== undefined) {
      return field.default(defaultValue).openapi({
        description,
        example,
      });
    }
    return field.openapi({
      description,
      example,
    });
  },
};

const dateValidators = {
  isValidDate: (date: string | null | undefined) => {
    if (!date) return true; // Skip validation if date is not provided
    if (date.match("^[0-9]{4}-[0-9]{2}-[0-9]{2}$") === null) return false;
    const inputDate = new Date(date);
    return !isNaN(inputDate.getTime()); // Validates the date format
  },
  isFutureDate: (date: string | null | undefined) => {
    if (!date) return true; // Skip validation if date is not provided

    const inputDate = parseISO(date);

    const today = startOfDay(new Date());

    const twoDaysAgo = subDays(today, 2);

    return inputDate >= twoDaysAgo;
  },
};

// Field descriptions and examples - centralized to avoid duplication
const fieldMetadata = {
  firstName: {
    description: "Contact's first name.",
    example: "Jane",
  },
  lastName: {
    description: "Contact's last name.",
    example: "Smith",
  },
  email: {
    description: "Contact's email address. Either email or phone is required.",
    example: "<EMAIL>",
  },
  phone: {
    description:
      "Contact's phone number in E.161 format (e.g., +14155552671). Either email or phone is required.",
    example: "+14155552671",
  },
  company: {
    description: "Company name.",
    example: "Acme Corp",
  },
  country: {
    description:
      "Country code. Must be a valid 2-letter ISO code. Invalid codes are treated as null in relaxed mode.",
    strictDescription: "Country code. Must be a valid 2-letter ISO code.",
    example: "US",
  },
  city: {
    description: "City name.",
    example: "San Francisco",
  },
  state: {
    description:
      "State/province/region abbreviation. Invalid codes are treated as null in relaxed mode.",
    strictDescription: "State/province/region abbreviation.",
    example: "CA",
  },
  postalCode: {
    description:
      "Postal/ZIP code. Obviously invalid codes are treated as null in relaxed mode.",
    example: "94103",
  },
  eventType: {
    description: "Type of event. Required field. Determines valid eventNeeds.",
    example: "wedding",
  },
  eventName: {
    description: "Name of the event.",
    example: "Smith-Johnson Wedding",
  },
  startDate: {
    description:
      "Event start date. Should be in YYYY-MM-DD format. Should be in YYYY-MM-DD format and cannot be in the past. Invalid dates or past dates are treated as null in relaxed mode. \n\n _*technically, to account for timezone differences a date may be in the past relative to your client location; we err on the side of permitting all possible dates in the world at a given moment_*",
    example: "2024-12-15",
    strictDescription:
      "Event start date. Must be in YYYY-MM-DD format and cannot be in the past.\n\n _*technically, to account for timezone differences a date may be in the past relative to your client location; we err on the side of permitting all possible dates in the world at a given moment_*",
  },
  endDate: {
    description:
      "Event end date. Should be in YYYY-MM-DD format and cannot be in the past. Invalid dates or past dates are treated as null in relaxed mode. \n\n _*technically, to account for timezone differences a date may be in the past relative to your client location; we err on the side of permitting all possible dates in the world at a given moment_*",
    example: "2024-12-17",
    strictDescription:
      "Event end date. Must be in YYYY-MM-DD format. If provided, must be on or after startDate. \n\n _*technically, to account for timezone differences a date may be in the past relative to your client location; we err on the side of permitting all possible dates in the world at a given moment_",
  },
  guestCount: {
    description: "Number of guests.",
    example: 150,
  },
  roomCount: {
    description: "Number of rooms needed.",
    example: 45,
  },
  mealCount: {
    description: "Number of meals needed.",
    example: 250,
  },
  budget: {
    description: "Estimated budget. Negative numbers are treated as null.",
    strictDescription: "Estimated budget. Must be a positive number.",
    example: 25000,
  },
  flexibleDates: {
    description: "Whether dates are flexible.",
    example: true,
  },
  eventNeeds: {
    description: `Specific event needs. Must be an array of valid codes. 
        - If \`eventType\` is 'wedding', only the following needs are valid: ${WEDDING_SPECIFIC_EVENT_NEEDS.map((n) => `\`${n}\``).join(", ")}.
        - If \`eventType\` is *not* 'wedding', only the following needs are valid: ${eventNeed.enumValues
          .filter((n) => !WEDDING_SPECIFIC_EVENT_NEEDS.includes(n))
          .map((n) => `\`${n}\``)
          .join(", ")}.`,
    example: ["WEDDING_CEREMONY", "WEDDING_RECEPTION"],
  },
  eventDescription: {
    description: "Description of the event.",
    example: "Looking for a beautiful venue for our winter wedding",
  },
  marketingConsent: {
    description: "Consent to receive marketing.",
    example: true,
  },
};

function applyCommonRefinements<T extends z.ZodTypeAny>(schema: T) {
  return schema
    .refine(
      (data: any) => {
        // Check for either a non-empty email or any phone
        return (
          (data.email && data.email.trim().length > 0) ||
          data.phone !== undefined
        );
      },
      {
        message:
          "At least one contact method (email or phone) must be provided",
        path: ["_form"],
      },
    )
    .refine(
      (data: any) => {
        if (!data.eventNeeds || data.eventNeeds.length === 0) return true;
        if (data.eventType !== "wedding") {
          const hasWeddingNeeds = data.eventNeeds.some((need: string) =>
            WEDDING_SPECIFIC_EVENT_NEEDS.includes(need),
          );
          return !hasWeddingNeeds;
        }
        return true;
      },
      {
        message:
          "Wedding-specific event needs can only be used with the wedding event type",
        path: ["eventNeeds"],
      },
    );
}

// Fix type issues with OpenAPI metadata for event type and event needs
const eventTypeMetadata = {
  description: fieldMetadata.eventType.description,
  example: fieldMetadata.eventType.example as "wedding", // Type assertion to match enum
};

const eventNeedsMetadata = {
  description: fieldMetadata.eventNeeds.description,
  example: fieldMetadata.eventNeeds.example as [
    "WEDDING_CEREMONY",
    "WEDDING_RECEPTION",
  ], // Type assertion
};

const strictEventNeedsMetadata = {
  description: fieldMetadata.eventNeeds.description,
  example: fieldMetadata.eventNeeds.example as [
    "WEDDING_CEREMONY",
    "WEDDING_RECEPTION",
  ], // Type assertion
};

// Base lead submission schema with common fields (no max length constraints)
const BaseLeadSubmissionSchemaObject = z.object({
  // Contact Information
  firstName: schemaFields.createStringField(
    fieldMetadata.firstName.description,
    fieldMetadata.firstName.example,
  ),
  lastName: schemaFields.createStringField(
    fieldMetadata.lastName.description,
    fieldMetadata.lastName.example,
  ),
  email: schemaFields.createStringField(
    fieldMetadata.email.description,
    fieldMetadata.email.example,
  ),
  phone: schemaFields.createStringField(
    fieldMetadata.phone.description,
    fieldMetadata.phone.example,
  ),
  company: schemaFields.createStringField(
    fieldMetadata.company.description,
    fieldMetadata.company.example,
  ),
  country: schemaFields.createStringField(
    fieldMetadata.country.description,
    fieldMetadata.country.example,
  ),
  city: schemaFields.createStringField(
    fieldMetadata.city.description,
    fieldMetadata.city.example,
  ),
  state: schemaFields.createStringField(
    fieldMetadata.state.description,
    fieldMetadata.state.example,
  ),
  postalCode: schemaFields.createStringField(
    fieldMetadata.postalCode.description,
    fieldMetadata.postalCode.example,
  ),

  // Event Details
  eventType: EventType.openapi(eventTypeMetadata),
  eventName: schemaFields.createStringField(
    fieldMetadata.eventName.description,
    fieldMetadata.eventName.example,
  ),
  startDate: schemaFields.createStringField(
    fieldMetadata.startDate.description,
    fieldMetadata.startDate.example,
  ),
  endDate: schemaFields.createStringField(
    fieldMetadata.endDate.description,
    fieldMetadata.endDate.example,
  ),
  guestCount: schemaFields.createNumberField(
    fieldMetadata.guestCount.description,
    fieldMetadata.guestCount.example,
  ),
  roomCount: schemaFields.createNumberField(
    fieldMetadata.roomCount.description,
    fieldMetadata.roomCount.example,
  ),
  mealCount: schemaFields.createNumberField(
    fieldMetadata.mealCount.description,
    fieldMetadata.mealCount.example,
  ),
  budget: schemaFields.createNumberField(
    fieldMetadata.budget.description,
    fieldMetadata.budget.example,
  ),
  flexibleDates: schemaFields.createBooleanField(
    fieldMetadata.flexibleDates.description,
    fieldMetadata.flexibleDates.example,
    false,
  ),
  eventNeeds: EventNeedsSchema.optional()
    .nullable()
    .openapi(eventNeedsMetadata),
  eventDescription: schemaFields.createStringField(
    fieldMetadata.eventDescription.description,
    fieldMetadata.eventDescription.example,
  ),
  marketingConsent: schemaFields.createBooleanField(
    fieldMetadata.marketingConsent.description,
    fieldMetadata.marketingConsent.example,
    false,
  ),
});

// Fix issues with relaxed schema
const RelaxedLeadSubmissionSchemaObject = BaseLeadSubmissionSchemaObject.extend(
  {
    firstName: schemaFields.createStringField(
      fieldMetadata.firstName.description,
      fieldMetadata.firstName.example,
      { maxLength: 255 },
    ),
    lastName: schemaFields.createStringField(
      fieldMetadata.lastName.description,
      fieldMetadata.lastName.example,
      { maxLength: 255 },
    ),
    email: schemaFields.createStringField(
      fieldMetadata.email.description,
      fieldMetadata.email.example,
      { maxLength: 255 },
    ),
    phone: schemaFields.createStringField(
      fieldMetadata.phone.description,
      fieldMetadata.phone.example,
      { maxLength: 255 },
    ),
    company: schemaFields.createStringField(
      fieldMetadata.company.description,
      fieldMetadata.company.example,
      { maxLength: 255 },
    ),
    city: schemaFields.createStringField(
      fieldMetadata.city.description,
      fieldMetadata.city.example,
      { maxLength: 255 },
    ),
    state: schemaFields.createStringField(
      fieldMetadata.state.description,
      fieldMetadata.state.example,
      { maxLength: 255 },
    ),
    postalCode: schemaFields.createStringField(
      fieldMetadata.postalCode.description,
      fieldMetadata.postalCode.example,
      { maxLength: 255 },
    ),
    eventName: schemaFields.createStringField(
      fieldMetadata.eventName.description,
      fieldMetadata.eventName.example,
      { maxLength: 255 },
    ),
    eventDescription: schemaFields.createStringField(
      fieldMetadata.eventDescription.description,
      fieldMetadata.eventDescription.example,
      { maxLength: 255 },
    ),
  },
);

// Fix issues with strict schema - recreating it to avoid duplicate declaration
const StrictSchema = BaseLeadSubmissionSchemaObject.extend({
  // Override fields with stricter validation and specific max lengths
  firstName: schemaFields.createStringField(
    fieldMetadata.firstName.description,
    fieldMetadata.firstName.example,
    { maxLength: 255 },
  ),
  lastName: schemaFields.createStringField(
    fieldMetadata.lastName.description,
    fieldMetadata.lastName.example,
    { maxLength: 255 },
  ),
  email: z
    .string()
    .max(255)
    .refine(
      (val) =>
        !val ||
        val.trim().length === 0 ||
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      {
        message: "Invalid email format",
      },
    )
    .nullish()
    .openapi({
      description: fieldMetadata.email.description,
      example: fieldMetadata.email.example,
    }),
  phone: z
    .string()
    .min(7, "Phone number is required")
    .max(20)
    .nullish()
    .openapi({
      description: fieldMetadata.phone.description,
      example: fieldMetadata.phone.example,
    }),
  company: schemaFields.createStringField(
    fieldMetadata.company.description,
    fieldMetadata.company.example,
    { maxLength: 100 },
  ),
  country: CountrySchema.nullish().openapi({
    description: fieldMetadata.country.description,
    example: fieldMetadata.country.example,
  }),
  city: schemaFields.createStringField(
    fieldMetadata.city.description,
    fieldMetadata.city.example,
    { maxLength: 100 },
  ),
  state: schemaFields.createStringField(
    fieldMetadata.state.description,
    fieldMetadata.state.example,
    { maxLength: 5 },
  ),
  postalCode: schemaFields.createStringField(
    fieldMetadata.postalCode.description,
    fieldMetadata.postalCode.example,
    { maxLength: 20 },
  ),
  eventName: schemaFields.createStringField(
    fieldMetadata.eventName.description,
    fieldMetadata.eventName.example,
    { maxLength: 255 },
  ),
  startDate: z
    .string()
    .nullish()
    .refine(dateValidators.isValidDate, "Invalid date format")
    .refine(dateValidators.isFutureDate, "Must be a valid date in the future")
    .openapi({
      description: fieldMetadata.startDate.strictDescription,
      example: fieldMetadata.startDate.example,
    }),
  endDate: z
    .string()
    .nullish()
    .refine(dateValidators.isValidDate, "Invalid date format")
    .refine(dateValidators.isFutureDate, "Must be a valid date in the future")
    .openapi({
      description: fieldMetadata.endDate.strictDescription,
      example: fieldMetadata.endDate.example,
    }),
  guestCount: schemaFields.createNumberField(
    fieldMetadata.guestCount.description,
    fieldMetadata.guestCount.example,
    {
      int: true,
      min: 1,
      max: 500000,
    },
  ),
  roomCount: schemaFields.createNumberField(
    fieldMetadata.roomCount.description,
    fieldMetadata.roomCount.example,
    {
      int: true,
      positive: true,
      max: 20000,
    },
  ),
  mealCount: schemaFields.createNumberField(
    fieldMetadata.mealCount.description,
    fieldMetadata.mealCount.example,
    {
      int: true,
      positive: true,
    },
  ),
  budget: schemaFields.createNumberField(
    fieldMetadata.budget.description,
    fieldMetadata.budget.example,
    { positive: true },
  ),
  eventDescription: schemaFields.createStringField(
    fieldMetadata.eventDescription.description,
    fieldMetadata.eventDescription.example,
    { maxLength: 2000 },
  ),
  eventNeeds: EventNeedsSchema.optional()
    .nullable()
    .openapi(strictEventNeedsMetadata),
});

// Apply strict-specific refinements
export const LeadSubmissionSchema = applyCommonRefinements(StrictSchema)
  .refine(
    (data: any) => {
      if (!data.startDate || !data.endDate) return true;
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      return endDate >= startDate;
    },
    { message: "End date must be on or after start date", path: ["endDate"] },
  )
  .openapi("LeadSubmission", {
    description: `Represents a lead submission via the API.

**Important Validation:**
*   At least one contact method (**email** or **phone**) must be provided.
*   If providing dates, **startDate** must be a valid date and cannot be in the past.
*   If providing both **startDate** and **endDate**, **endDate** must be a valid date and on or after **startDate**.
*   Wedding-specific **eventNeeds** can only be used when **eventType** is 'wedding'. See the \`eventNeeds\` field description for details.`,
  });

// Relaxed validation schema - apply common refinements
export const RelaxedLeadSubmissionSchema = applyCommonRefinements(
  RelaxedLeadSubmissionSchemaObject,
).openapi("RelaxedLeadSubmission", {
  description: `Represents a lead submission via the API using relaxed validation rules.

**Important Validation:**
*   At least one contact method (**email** or **phone**) must be provided.
*   **eventType** must be a valid value.
*   Wedding-specific **eventNeeds** can only be used when **eventType** is 'wedding'. See the \`eventNeeds\` field description for details.
*   Other fields (like dates, email format) are not strictly validated but may result in warnings if they fail strict validation.`,
});

// Type for the validated lead data
export type LeadSubmission = z.infer<typeof LeadSubmissionSchema>;
export type RelaxedLeadSubmission = z.infer<typeof RelaxedLeadSubmissionSchema>;

// Define a string literal union for error types
export const API_ERROR_TYPES = [
  "field_validation_error",
  "authentication_error",
  "authorization_error",
  "not_found_error",
  "rate_limit_error",
  "server_error",
] as const;

export type ApiErrorType = (typeof API_ERROR_TYPES)[number];

// Create a structured object with descriptions for each error type
export const API_ERROR_DESCRIPTIONS: Record<ApiErrorType, string> = {
  field_validation_error:
    "The submitted data failed validation rules (only for the strict endpoint). See the `errors` field for details.",
  authentication_error: "Invalid or missing API key.",
  authorization_error:
    "API key is valid but not authorized for the specified property (ensure the API key's management group matches the property's management group).",
  not_found_error: "The specified property ID does not exist.",
  rate_limit_error: "The request rate limit has been exceeded.",
  server_error: "An unexpected error occurred on the server.",
};

// Helper function to generate documentation string for error types
function getErrorTypeDocumentation(): string {
  return (
    "Type of error that occurred. Possible values:\n" +
    Object.entries(API_ERROR_DESCRIPTIONS)
      .map(([type, description]) => `- \`${type}\`: ${description}`)
      .join("\n")
  );
}

const leadResponseSchema = z
  .object({
    propertyId: z.string().openapi({
      description: "ID of the property",
      example: "3960a31a-1077-4231-8338-b6f1bedb5055",
    }),
    email: z.string().email().nullable().openapi({
      description: fieldMetadata.email.description,
      example: fieldMetadata.email.example,
    }),
    firstName: z.string().nullable().openapi({
      description: fieldMetadata.firstName.description,
      example: fieldMetadata.firstName.example,
    }),
    lastName: z.string().nullable().openapi({
      description: fieldMetadata.lastName.description,
      example: fieldMetadata.lastName.example,
    }),
    company: z.string().nullable().openapi({
      description: fieldMetadata.company.description,
      example: fieldMetadata.company.example,
    }),
    phone: z.string().nullable().openapi({
      description: fieldMetadata.phone.description,
      example: fieldMetadata.phone.example,
    }),
    city: z.string().nullable().openapi({
      description: fieldMetadata.city.description,
      example: fieldMetadata.city.example,
    }),
    state: z.string().nullable().openapi({
      description: fieldMetadata.state.description,
      example: fieldMetadata.state.example,
    }),
    postalCode: z.string().nullable().openapi({
      description: fieldMetadata.postalCode.description,
      example: fieldMetadata.postalCode.example,
    }),
    country: z.string().nullable().openapi({
      description: fieldMetadata.country.description,
      example: fieldMetadata.country.example,
    }),
    eventType: EventType.openapi({
      description: fieldMetadata.eventType.description,
      example: fieldMetadata.eventType.example as "wedding",
    }),
    eventName: z.string().nullable().openapi({
      description: fieldMetadata.eventName.description,
      example: fieldMetadata.eventName.example,
    }),
    startDate: z.string().nullable().openapi({
      description: fieldMetadata.startDate.description,
      example: fieldMetadata.startDate.example,
    }),
    endDate: z.string().nullable().openapi({
      description: fieldMetadata.endDate.description,
      example: fieldMetadata.endDate.example,
    }),
    guestCount: z.number().nullable().openapi({
      description: fieldMetadata.guestCount.description,
      example: fieldMetadata.guestCount.example,
    }),
    roomCount: z.number().nullable().openapi({
      description: fieldMetadata.roomCount.description,
      example: fieldMetadata.roomCount.example,
    }),
    mealCount: z.number().nullable().openapi({
      description: fieldMetadata.mealCount.description,
      example: fieldMetadata.mealCount.example,
    }),
    budget: z
      .string()
      .nullable()
      .openapi({
        description: fieldMetadata.budget.description,
        example: String(fieldMetadata.budget.example),
      }),
    flexibleDates: schemaFields.createBooleanField(
      fieldMetadata.flexibleDates.description,
      fieldMetadata.flexibleDates.example,
    ),
    eventNeeds: EventNeedsSchema.nullable().openapi({
      description: fieldMetadata.eventNeeds.description,
      example: ["WEDDING_CEREMONY", "WEDDING_RECEPTION"] as const,
    }),
    eventDescription: z.string().nullable().openapi({
      description: fieldMetadata.eventDescription.description,
      example: fieldMetadata.eventDescription.example,
    }),
    marketingConsent: schemaFields.createBooleanField(
      fieldMetadata.marketingConsent.description,
      fieldMetadata.marketingConsent.example,
    ),
  })
  .openapi("Lead");

export const LeadSubmissionResponseSchema = z
  .object({
    propertyId: z.string().uuid().openapi({
      description: "ID of the property the lead was submitted to",
      example: "prop_1234567890abcdef",
    }),
    lead: leadResponseSchema,
  })
  .openapi("LeadSubmissionResponse");

export const RelaxedLeadSubmissionResponseSchema =
  LeadSubmissionResponseSchema.extend({
    warnings: z.record(z.array(z.string())).openapi({
      description:
        "A map where keys are field names and values are arrays of validation messages indicating why the field failed strict validation. This is only present when relaxed validation was used and some fields failed strict validation.",
      example: {
        email: ["Invalid email format"],
        startDate: ["Must be a valid date in the future"],
      },
    }),
  }).openapi("RelaxedLeadSubmissionResponse");

// Union type for response
const LeadSubmissionResponseUnionSchema = z.union([
  LeadSubmissionResponseSchema,
  RelaxedLeadSubmissionResponseSchema,
]);

export type LeadSubmissionResponse = z.infer<
  typeof LeadSubmissionResponseUnionSchema
>;

export const ErrorResponseSchema = z
  .object({
    errorType: z.enum(API_ERROR_TYPES).openapi({
      description: getErrorTypeDocumentation(),
      example: "field_validation_error",
    }),
    errors: z
      .record(z.array(z.string()))
      .nullish()
      .openapi({
        description:
          "Detailed validation error messages by field. Only present when `errorType` is `field_validation_error`. Keys are field names, values are arrays of error messages.",
        example: {
          email: ["Invalid email format"],
          startDate: ["Must be a valid date in the future"],
          _form: [
            "At least one contact method (email or phone) must be provided",
          ],
        },
      }),
    message: z.string().nullish().openapi({
      description:
        "A general error message providing context, especially for non-validation errors or form-level validation errors.",
      example: "Validation failed",
    }),
  })
  .openapi("ErrorResponse");

const commonResponseHeaders = z.object({
  "X-API-Version": apiVersionHeaderSchema,
});

// Extend Variables to include managementGroupId
type ApiVariables = Variables & {
  managementGroupId: string;
};

const ONE_MINUTE_IN_MILLIS = 60 * 1000;
const REQUESTS_PER_MINUTE_LIMIT = 50;
/**
 * Configure rate limiter middleware
 */
export const rateLimiterMiddleware = rateLimiter({
  windowMs: ONE_MINUTE_IN_MILLIS,
  limit: REQUESTS_PER_MINUTE_LIMIT,
  standardHeaders: "draft-6", // Return rate limit info in headers
  keyGenerator: (c) => c.req.url, // Use URL as the key
});

function createBaseLeadSubmissionRouteConfig(options: {
  path: string;
  description: string;
  requestBodySchema: any;
  successResponseSchema: any;
}) {
  return {
    method: "post" as const,
    path: options.path,
    tags: ["Lead API"] as string[],
    description: options.description,
    request: {
      params: z.object({
        propertyId: z.string().uuid().openapi({
          description: "The unique identifier for the property",
          example: "123e4567-e89b-12d3-a456-************",
        }),
      }),
      headers: z.object({
        "X-API-Key": z.string().openapi({
          description: "API key for authentication",
          example: "grp_abc123def456",
        }),
        "X-API-Version": apiVersionHeaderSchema,
        "Content-Type": z.literal("application/json").openapi({
          description: "Content type must be application/json",
          example: "application/json",
        }),
      }),
      body: {
        content: {
          "application/json": {
            schema: options.requestBodySchema,
          },
        },
        required: true,
      },
    },
    responses: {
      201: {
        content: {
          "application/json": {
            schema: options.successResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: "Lead created successfully (Status Code: 201).",
      },
      400: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description:
          "Bad Request: Validation failed (only for strict endpoint). See response body for details.",
      },
      401: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: "Unauthorized: Invalid or missing API key.",
      },
      403: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: "Forbidden: API key is not authorized for this property.",
      },
      404: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: "Not Found: Property ID not found.",
      },
      429: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: `Too Many Requests: Rate limit exceeded. The API is rate limited to **${REQUESTS_PER_MINUTE_LIMIT} requests per minute** per property. Exceeding this limit will result in a \`429 Too Many Requests\` error.`,
      },
      500: {
        content: {
          "application/json": {
            schema: ErrorResponseSchema,
          },
        },
        headers: commonResponseHeaders,
        description: "Internal Server Error: An unexpected error occurred.",
      },
    },
  };
}

const standardValidationDescription = `This endpoint allows you to Create a lead for a property programmatically using strict validation.\n\n

You should use this endpoint unless you absolutely require less strict validation. 
E.g. When you are integrating with a third-party form that you cannot guarantee the validity of all input data, but do not want to lose leads.\n\n
    
 
 ### Authentication & Authorization\n\n
 Requires a valid API key associated with the property's management group via the \`X-API-Key\` header.
 
 ### Validation\n\n
 This endpoint enforces strict validation rules:
 *   At least one contact method (**email** or **phone**) must be provided.
 *   **startDate**, if provided, must be a valid date and cannot be in the past.
 *   **endDate**, if provided, must be a valid date and occur on or after **startDate**.
 *   **eventNeeds** must align with the specified **eventType** (see field description).
 *   Other fields have specific format or length requirements (see field descriptions).
 
 Failure to meet these rules will result in a \`400 Bad Request\` error with detailed validation messages in the response body. We recommend displaying these errors to the user.
 
 For less strict validation, use the \`/properties/{propertyId}/leads/relaxed\` endpoint.
  
 ### Rate Limiting \n\n 
 This endpoint is rate limited to **${REQUESTS_PER_MINUTE_LIMIT} requests per minute** per property. Exceeding this limit results in a \`429 Too Many Requests\` error. Contact Siv support if you require a higher limit.
 `;

const relaxedValidationDescription = `This endpoint allows you to Create a lead for a property programmatically using relaxed validation.\n\n

You should not use this endpoint unless you have a very good reason to prefer relaxed validations. 
E.g. When you are integrating with a third-party form that you cannot guarantee the validity of all input data, but do not want to lose leads.\n\n

 ### Authentication & Authorization\n\n
 Requires a valid API key associated with the property's management group via the \`X-API-Key\` header.

 ### Relaxed Validation\n\n
 This endpoint uses less strict validation than the standard (\`/properties/{propertyId}/leads\`) endpoint.
 *   It still requires at least one valid contact method (**email** or **phone**).
 *   It still requires a valid **eventType**.
 *   It still requires **eventNeeds** to align with the specified **eventType**.
 *   However, other fields (like date formats/logic, email format) are *not* strictly validated.\n\n
 
 
 If submitted data passes relaxed validation but fails the strict validation rules, the API will still create the lead but return a \`201 Created\` response with a status of \`success_with_warnings\` and include details about the strict validation failures in the \`warnings\` field.\n\n
 
 **Important**: Any fields that fail strict validation are **saved as null** rather than storing potentially invalid data.\n\n
 
 
 This endpoint is useful when you cannot guarantee the validity of all input data (e.g., integrating with a third-party form) but still want to capture the lead.
 
 ### Rate Limiting \n\n 
 This endpoint is rate limited to **${REQUESTS_PER_MINUTE_LIMIT} requests per minute** per property. Exceeding this limit results in a \`429 Too Many Requests\` error. Contact Siv support if you require a higher limit.
 `;

const leadSubmissionRoute = createRoute(
  createBaseLeadSubmissionRouteConfig({
    path: "/properties/{propertyId}/leads",
    description: standardValidationDescription,
    requestBodySchema: LeadSubmissionSchema,
    successResponseSchema: LeadSubmissionResponseSchema,
  }),
);

const relaxedLeadSubmissionRoute = createRoute(
  createBaseLeadSubmissionRouteConfig({
    path: "/properties/{propertyId}/leads/relaxed",
    description: relaxedValidationDescription,
    requestBodySchema: RelaxedLeadSubmissionSchema,
    successResponseSchema: RelaxedLeadSubmissionResponseSchema,
  }),
);

export function createPublicLeadSubmissionRoutes(deps: {
  leadCreatedEventPublisher: LeadCreatedEventPublisher;
}) {
  const app = new OpenAPIHono<{ Variables: ApiVariables }>({
    defaultHook: (result, c) => {
      if (!result.success) {
        // Format Zod validation errors
        const errors = result.error.flatten();

        logger.error("Lead submission validation failed", {
          errors,
        });

        // Check if there's a form-level error and use it in the message
        const formErrorMessage =
          errors.formErrors.length > 0
            ? errors.formErrors.join(", ")
            : errors.fieldErrors._form?.join(", "); // Get _form errors if present

        return c.json(
          {
            errorType: "field_validation_error" as ApiErrorType,
            errors: errors.fieldErrors,
            message:
              formErrorMessage ||
              API_ERROR_DESCRIPTIONS["field_validation_error"],
          },
          400,
        );
      }
    },
  });

  // Apply API key authentication middleware
  app.use("/properties/:propertyId/leads*", apiKeyAuth);

  // Apply rate limiting middleware
  app.use("/properties/:propertyId/leads*", rateLimiterMiddleware);

  // Shared implementation for both endpoints
  async function handleLeadSubmission(c: any, isRelaxed: boolean) {
    try {
      const requestBody = await c.req.json();
      const propertyId = c.req.param("propertyId");
      const apiKey = c.req.header("X-API-Key") || "";

      // Verify the property exists and belongs to the management group
      const verification = await verifyProperty(propertyId, apiKey);

      if (!verification.propertyFound) {
        return c.json(
          {
            errorType: "not_found_error" as ApiErrorType,
            message: API_ERROR_DESCRIPTIONS["not_found_error"],
          },
          404,
        );
      }

      if (verification.managementGroupId === null) {
        return c.json(
          {
            errorType: "authentication_error" as ApiErrorType,
            message: API_ERROR_DESCRIPTIONS["authentication_error"],
          },
          401,
        );
      }

      if (!verification.authorized) {
        return c.json(
          {
            errorType: "authorization_error" as ApiErrorType,
            message: API_ERROR_DESCRIPTIONS["authorization_error"],
          },
          403,
        );
      }

      // Create the lead with appropriate validation mode
      const result = await createLead(
        requestBody,
        propertyId,
        verification.managementGroupId!, // Safe to use non-null assertion here
        isRelaxed,
        deps.leadCreatedEventPublisher,
      );

      return c.json(result.responseBody, result.status);
    } catch (error) {
      return handleError(c, error);
    }
  }

  // Standard strict validation endpoint
  app.openapi(leadSubmissionRoute, async (c) => {
    return handleLeadSubmission(c, false);
  });

  // Relaxed validation endpoint
  app.openapi(relaxedLeadSubmissionRoute, async (c) => {
    return handleLeadSubmission(c, true);
  });

  return app;
}

async function verifyProperty(propertyId: string, apiKey: string) {
  // Use a JOIN query to verify both property existence and authorization in one go
  const results = await db
    .select({
      propertyId: property.id,
      propertyManagementGroupId: property.managementGroupId,
      apiKeyManagementGroupId: managementGroupApiKey.managementGroupId,
    })
    .from(property)
    .leftJoin(managementGroupApiKey, eq(managementGroupApiKey.key, apiKey))
    .where(eq(property.id, propertyId))
    .limit(1);

  if (results.length === 0) {
    logger.warn("Property not found during lead submission", { propertyId });
    return { propertyFound: false, authorized: false, managementGroupId: null };
  }

  const result = results[0];

  // Check if the property and API key belong to the same management group
  const authorized =
    result.propertyManagementGroupId === result.apiKeyManagementGroupId;

  if (!authorized) {
    logger.warn(
      "API key management group mismatch for property during lead submission",
      {
        propertyId,
        propertyManagementGroupId: result.propertyManagementGroupId,
        apiKeyManagementGroupId: result.apiKeyManagementGroupId,
      },
    );
  }

  return {
    propertyFound: true,
    authorized,
    managementGroupId: result.apiKeyManagementGroupId,
  };
}

async function createLead(
  requestBody: any,
  propertyId: string,
  managementGroupId: string,
  isRelaxed: boolean,
  leadCreatedEventPublisher: LeadCreatedEventPublisher,
) {
  // Use the appropriate schema based on relaxed flag
  const validationSchema = isRelaxed
    ? RelaxedLeadSubmissionSchema
    : LeadSubmissionSchema;

  // Validate the request body
  const parsed = validationSchema.safeParse(requestBody);
  if (!parsed.success) {
    const errors = parsed.error.flatten();
    logger.warn("Lead submission validation failed", {
      propertyId,
      managementGroupId,
      mode: isRelaxed ? "relaxed" : "strict",
      errors: errors.fieldErrors,
      formErrors: errors.formErrors,
    });
    throw {
      type: "validation_error",
      errors: errors,
    };
  }

  const leadData = parsed.data;

  // For relaxed validation, check against strict schema to find warnings
  const warnings: Record<string, string[]> = {};
  let invalidFields: Set<string> = new Set();

  if (isRelaxed) {
    const strictResult = LeadSubmissionSchema.safeParse(requestBody);
    if (!strictResult.success) {
      const strictErrors = strictResult.error.flatten();
      // Convert to Record<string, string[]> to ensure type safety
      for (const [field, messages] of Object.entries(
        strictErrors.fieldErrors,
      )) {
        if (Array.isArray(messages) && messages.length > 0) {
          warnings[field] = messages;
          invalidFields.add(field); // Track which fields are invalid
        }
      }

      logger.info(
        "Lead submission passed relaxed validation but failed strict validation (warnings generated)",
        {
          propertyId,
          managementGroupId,
          strictFieldErrors: strictErrors.fieldErrors,
        },
      );
    }
  }

  // Wrap lead creation and event publishing in a transaction
  return await db.transaction(async (tx) => {
    // 1. Create the API submission record
    const [apiSubmissionRecord] = await tx
      .insert(apiLeadSubmissions)
      .values({
        relaxedValidations: isRelaxed,
        passedStrictValidations:
          !warnings || Object.keys(warnings).length === 0,
      })
      .returning({ id: apiLeadSubmissions.id });

    if (!apiSubmissionRecord) {
      logger.error("Failed to create API submission record in transaction", {
        propertyId,
        managementGroupId,
      });
      throw new Error("Failed to create API submission record");
    }

    // 2. Create the lead record with sanitized data
    const insertData = {
      propertyId: propertyId,
      email:
        isRelaxed && invalidFields.has("email")
          ? null
          : (leadData.email ?? null),
      firstName:
        isRelaxed && invalidFields.has("firstName")
          ? null
          : (leadData.firstName ?? null),
      lastName:
        isRelaxed && invalidFields.has("lastName")
          ? null
          : (leadData.lastName ?? null),
      phone:
        isRelaxed && invalidFields.has("phone")
          ? null
          : (leadData.phone ?? null),
      company:
        isRelaxed && invalidFields.has("company")
          ? null
          : (leadData.company ?? null),
      city:
        isRelaxed && invalidFields.has("city") ? null : (leadData.city ?? null),
      state:
        isRelaxed && invalidFields.has("state")
          ? null
          : (leadData.state ?? null),
      postalCode:
        isRelaxed && invalidFields.has("postalCode")
          ? null
          : (leadData.postalCode ?? null),
      eventType: leadData.eventType, // eventType is required for both strict and relaxed
      startDate:
        (isRelaxed && invalidFields.has("startDate")) ||
        !dateValidators.isValidDate(leadData.startDate)
          ? null
          : leadData.startDate,
      endDate:
        (isRelaxed && invalidFields.has("endDate")) ||
        !dateValidators.isValidDate(leadData.endDate)
          ? null
          : leadData.endDate,
      guestCount:
        isRelaxed && invalidFields.has("guestCount")
          ? null
          : (leadData.guestCount ?? null),
      roomCount:
        isRelaxed && invalidFields.has("roomCount")
          ? null
          : (leadData.roomCount ?? null),
      mealCount:
        isRelaxed && invalidFields.has("mealCount")
          ? null
          : (leadData.mealCount ?? null),
      budget:
        isRelaxed && invalidFields.has("budget")
          ? null
          : leadData.budget != null
            ? String(leadData.budget)
            : null,
      eventDescription:
        isRelaxed && invalidFields.has("eventDescription")
          ? null
          : (leadData.eventDescription ?? null),
      flexibleDates:
        isRelaxed && invalidFields.has("flexibleDates")
          ? false
          : (leadData.flexibleDates ?? false),
      marketingConsent:
        isRelaxed && invalidFields.has("marketingConsent")
          ? false
          : (leadData.marketingConsent ?? false),
      eventName:
        isRelaxed && invalidFields.has("eventName")
          ? null
          : (leadData.eventName ?? null),
      country:
        isRelaxed && invalidFields.has("country")
          ? null
          : (leadData.country ?? null),
      eventNeeds:
        isRelaxed && invalidFields.has("eventNeeds")
          ? null
          : (leadData.eventNeeds ?? null),
      source: "api" as const,
      sourceId: apiSubmissionRecord.id,
    };

    const [createdLead] = await tx.insert(lead).values(insertData).returning();

    if (!createdLead || !createdLead.createdAt) {
      logger.error(
        "Failed to retrieve created lead details after insert in transaction",
        {
          propertyId,
          managementGroupId,
          apiSubmissionId: apiSubmissionRecord.id,
        },
      );
      throw new Error("Failed to retrieve created lead details after insert");
    }

    // 3. Publish the lead created event
    await leadCreatedEventPublisher.publish({
      id: `lead-created-${createdLead.id}`,
      name: "lead.created",
      data: {
        leadId: createdLead.id,
        propertyId: createdLead.propertyId,
      },
    });

    logger.info("Lead created successfully via API", {
      leadId: createdLead.id,
      propertyId: propertyId,
      managementGroupId: managementGroupId,
      mode: isRelaxed ? "relaxed" : "strict",
      passedStrict: !warnings || Object.keys(warnings).length === 0,
      apiSubmissionId: apiSubmissionRecord.id,
    });

    // 4. Construct response based on validation mode
    if (isRelaxed) {
      return {
        status: 201 as const,
        responseBody: {
          propertyId: propertyId,
          warnings: warnings,
          lead: excludeSensitiveFields(createdLead),
        },
      };
    } else {
      return {
        status: 201 as const,
        responseBody: {
          propertyId: propertyId,
          lead: excludeSensitiveFields(createdLead),
        },
      };
    }
  });
}

export function excludeSensitiveFields(lead: Record<string, any>) {
  // Create a copy of the lead to avoid modifying the original
  const sanitizedLead = { ...lead };

  // Remove sensitive fields
  delete sanitizedLead.highlevelContactId;
  delete sanitizedLead.highlevelOpportunityId;
  delete sanitizedLead.assignedSalesRepEmail;
  delete sanitizedLead.assignedAt;

  return sanitizedLead;
}

function handleError(c: any, error: any) {
  if (error.type === "validation_error") {
    // Check if there's a form-level error and use it in the message
    const formErrorMessage =
      error.errors.formErrors.length > 0
        ? error.errors.formErrors.join(", ")
        : error.errors.fieldErrors._form?.join(", "); // Get _form errors if present

    return c.json(
      {
        errorType: "field_validation_error" as ApiErrorType,
        errors: error.errors.fieldErrors,
        message:
          formErrorMessage || API_ERROR_DESCRIPTIONS["field_validation_error"],
      },
      400,
    );
  }

  // Log detailed error including potentially relevant context
  const errorContext = {
    propertyId: c.req.param("propertyId"),
    managementGroupId: c.get("managementGroupId"),
    errorMessage: error instanceof Error ? error.message : String(error),
    errorStack: error instanceof Error ? error.stack : undefined,
  };
  logger.error("Error processing lead submission request", errorContext);

  // Generic error response to client
  return c.json(
    {
      errorType: "server_error" as ApiErrorType,
      message: API_ERROR_DESCRIPTIONS["server_error"],
    },
    500,
  );
}
