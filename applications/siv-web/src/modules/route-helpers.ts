import { HonoRequest } from "hono";

export function routesBaseUrl<T extends string>(req: HonoRequest<T>) {
  const url = new URL(req.url);
  const forwardedProto = req.header()["x-forwarded-proto"];

  // If we have a forwarded protocol, use it instead of the request URL protocol
  if (forwardedProto) {
    url.protocol = `${forwardedProto}:`;
  }

  return url.toString().substring(0, url.toString().lastIndexOf(req.path));
}

export class ModuleBaseUrlFactory {
  constructor(private readonly componentMountedAtPath: string) {}

  public baseUrl(req: HonoRequest): string {
    const slashIfMissing = this.componentMountedAtPath.startsWith("/")
      ? ""
      : "/";
    return `${routesBaseUrl(req)}${slashIfMissing}${this.componentMountedAtPath}`;
  }

  factoryForNestedModule(nestedModuleMountedAtPath: string) {
    const slashIfMissing = nestedModuleMountedAtPath.startsWith("/") ? "" : "/";
    return new ModuleBaseUrlFactory(
      `${this.componentMountedAtPath}${slashIfMissing}${nestedModuleMountedAtPath}`,
    );
  }
}

export function indexUrl(urlWithIndexSuffix: string) {
  return urlWithIndexSuffix.replace(/\/index$/, "");
}
