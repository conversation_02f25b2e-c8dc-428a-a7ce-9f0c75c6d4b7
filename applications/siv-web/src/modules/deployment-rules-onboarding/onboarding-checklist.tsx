import { Check<PERSON><PERSON><PERSON>2, Circle } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

interface OnboardingChecklistProps {
  items: ChecklistItem[];
  onItemClick: (id: string) => void;
  onClose: () => void;
  title?: string;
}

export default function OnboardingChecklist({
  items,
  onItemClick,
  onClose,
  title = "Setup Checklist",
}: OnboardingChecklistProps) {
  const completedCount = items.filter((item) => item.completed).length;
  const progress = items.length > 0 ? (completedCount / items.length) * 100 : 0;

  return (
    <Card className="w-full max-w-md shadow-lg border-primary/10">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 px-2"
          >
            Close
          </Button>
        </div>
        <div className="w-full bg-muted rounded-full h-2 mt-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${progress}%` }}
          />
        </div>
        <div className="text-xs text-muted-foreground mt-1">
          {completedCount} of {items.length} completed
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <ul className="space-y-3">
          {items.map((item) => (
            <li
              key={item.id}
              className={cn(
                "flex items-start gap-3 p-2 rounded-md cursor-pointer transition-colors",
                item.completed ? "bg-primary/5" : "hover:bg-muted",
              )}
              onClick={() => onItemClick(item.id)}
            >
              <div className="mt-0.5">
                {item.completed ? (
                  <CheckCircle2 className="h-5 w-5 text-primary" />
                ) : (
                  <Circle className="h-5 w-5 text-muted-foreground" />
                )}
              </div>
              <div>
                <h3
                  className={cn(
                    "font-medium",
                    item.completed && "text-primary",
                  )}
                >
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {item.description}
                </p>
              </div>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
