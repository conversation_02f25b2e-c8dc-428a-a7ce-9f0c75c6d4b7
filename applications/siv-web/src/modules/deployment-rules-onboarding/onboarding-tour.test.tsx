import { render, screen, fireEvent } from "@testing-library/react";
import { vi, beforeEach, it, expect, describe } from "vitest";
import { toast } from "sonner";
import OnboardingTour from "./onboarding-tour";
import { ChecklistItemId } from "./types/lead-assignment";

// Mock sonner toast
vi.mock("sonner", () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

describe("OnboardingTour Warning Messages", () => {
  const defaultProps = {
    run: false,
    setRun: vi.fn(),
    onComplete: vi.fn(),
    onSkip: vi.fn(),
    tableOnly: true,
    completedActions: [],
    showChecklist: true,
    setShowChecklist: vi.fn(),
    hasTeams: false,
    hasIndividuals: false,
    hasCriteria: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock querySelector to avoid errors when trying to highlight elements
    vi.spyOn(document, "querySelector").mockReturnValue(null);
  });

  describe("Team Criteria Warnings", () => {
    it("shows team warning when no teams exist", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasTeams={false}
          hasCriteria={true}
        />,
      );

      // Find and click the team criteria checklist item
      const teamCriteriaItem = screen.getByText("Team Criteria");
      fireEvent.click(teamCriteriaItem.closest("li")!);

      expect(toast.error).toHaveBeenCalledWith("Please add a team first", {
        description:
          "You need to create at least one team before setting team criteria.",
      });
    });

    it("shows criteria warning when teams exist but no criteria defined", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasTeams={true}
          hasCriteria={false}
        />,
      );

      // Find and click the team criteria checklist item
      const teamCriteriaItem = screen.getByText("Team Criteria");
      fireEvent.click(teamCriteriaItem.closest("li")!);

      expect(toast.error).toHaveBeenCalledWith(
        "Please define your criteria first",
        {
          description:
            "You need to set up assignment criteria before configuring team rules.",
        },
      );
    });

    it("does not show warning when both teams and criteria exist", () => {
      const mockQuerySelector = vi.spyOn(document, "querySelector");
      mockQuerySelector.mockReturnValue({
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        scrollIntoView: vi.fn(),
      } as any);

      render(
        <OnboardingTour {...defaultProps} hasTeams={true} hasCriteria={true} />,
      );

      // Find and click the team criteria checklist item
      const teamCriteriaItem = screen.getByText("Team Criteria");
      fireEvent.click(teamCriteriaItem.closest("li")!);

      expect(toast.error).not.toHaveBeenCalled();
    });

    it("prioritizes team warning over criteria warning", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasTeams={false}
          hasCriteria={false}
        />,
      );

      // Find and click the team criteria checklist item
      const teamCriteriaItem = screen.getByText("Team Criteria");
      fireEvent.click(teamCriteriaItem.closest("li")!);

      // Should show team warning first, not criteria warning
      expect(toast.error).toHaveBeenCalledWith("Please add a team first", {
        description:
          "You need to create at least one team before setting team criteria.",
      });
      expect(toast.error).toHaveBeenCalledTimes(1);
    });
  });

  describe("Individual Criteria Warnings", () => {
    it("shows individual warning when no individuals exist", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasIndividuals={false}
          hasCriteria={true}
        />,
      );

      // Find and click the individual criteria checklist item
      const individualCriteriaItem = screen.getByText("Individual Criteria");
      fireEvent.click(individualCriteriaItem.closest("li")!);

      expect(toast.error).toHaveBeenCalledWith(
        "Please add an individual first",
        {
          description:
            "You need to add at least one individual before setting individual criteria.",
        },
      );
    });

    it("shows criteria warning when individuals exist but no criteria defined", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasIndividuals={true}
          hasCriteria={false}
        />,
      );

      // Find and click the individual criteria checklist item
      const individualCriteriaItem = screen.getByText("Individual Criteria");
      fireEvent.click(individualCriteriaItem.closest("li")!);

      expect(toast.error).toHaveBeenCalledWith(
        "Please define your criteria first",
        {
          description:
            "You need to set up assignment criteria before configuring individual rules.",
        },
      );
    });

    it("does not show warning when both individuals and criteria exist", () => {
      const mockQuerySelector = vi.spyOn(document, "querySelector");
      mockQuerySelector.mockReturnValue({
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        scrollIntoView: vi.fn(),
      } as any);

      render(
        <OnboardingTour
          {...defaultProps}
          hasIndividuals={true}
          hasCriteria={true}
        />,
      );

      // Find and click the individual criteria checklist item
      const individualCriteriaItem = screen.getByText("Individual Criteria");
      fireEvent.click(individualCriteriaItem.closest("li")!);

      expect(toast.error).not.toHaveBeenCalled();
    });

    it("prioritizes individual warning over criteria warning", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          hasIndividuals={false}
          hasCriteria={false}
        />,
      );

      // Find and click the individual criteria checklist item
      const individualCriteriaItem = screen.getByText("Individual Criteria");
      fireEvent.click(individualCriteriaItem.closest("li")!);

      // Should show individual warning first, not criteria warning
      expect(toast.error).toHaveBeenCalledWith(
        "Please add an individual first",
        {
          description:
            "You need to add at least one individual before setting individual criteria.",
        },
      );
      expect(toast.error).toHaveBeenCalledTimes(1);
    });
  });

  describe("Other Checklist Items", () => {
    it("does not show warnings for non-criteria checklist items", () => {
      const mockQuerySelector = vi.spyOn(document, "querySelector");
      mockQuerySelector.mockReturnValue({
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        scrollIntoView: vi.fn(),
      } as any);

      render(<OnboardingTour {...defaultProps} />);

      // Click on a different checklist item that shouldn't trigger warnings
      const defineCriteriaItem = screen.getByText("Define Your Criteria");
      fireEvent.click(defineCriteriaItem.closest("li")!);

      expect(toast.error).not.toHaveBeenCalled();
    });

    it("highlights elements when clicking valid checklist items", () => {
      const mockElement = {
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        scrollIntoView: vi.fn(),
      };
      const mockQuerySelector = vi.spyOn(document, "querySelector");
      mockQuerySelector.mockReturnValue(mockElement as any);

      render(<OnboardingTour {...defaultProps} />);

      // Click on a checklist item that should highlight
      const defineCriteriaItem = screen.getByText("Define Your Criteria");
      fireEvent.click(defineCriteriaItem.closest("li")!);

      expect(mockElement.classList.add).toHaveBeenCalledWith("highlight-pulse");
      expect(mockElement.scrollIntoView).toHaveBeenCalledWith({
        behavior: "smooth",
        block: "center",
      });
    });
  });

  describe("Checklist Rendering", () => {
    it("renders all expected checklist items", () => {
      render(<OnboardingTour {...defaultProps} />);

      // Check that key checklist items are rendered
      expect(screen.getByText("Define Your Criteria")).toBeInTheDocument();
      expect(screen.getByText("Team Criteria")).toBeInTheDocument();
      expect(screen.getByText("Individual Criteria")).toBeInTheDocument();
      expect(screen.getByText("Add Teams (Optional)")).toBeInTheDocument();
      expect(screen.getByText("Add Individuals")).toBeInTheDocument();
    });

    it("shows completed state for checklist items", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          completedActions={[
            ChecklistItemId.DefineCriteria,
            ChecklistItemId.AddTeams,
          ]}
        />,
      );

      // Find completed items by looking for the CheckCircle2 icon
      const completedIcons = screen.getAllByTestId("checklist-complete-icon");
      expect(completedIcons).toHaveLength(2);
    });

    it("shows incomplete state for pending checklist items", () => {
      render(
        <OnboardingTour
          {...defaultProps}
          completedActions={[ChecklistItemId.DefineCriteria]}
        />,
      );

      // Find incomplete items by looking for the Circle icon
      const incompleteIcons = screen.getAllByTestId(
        "checklist-incomplete-icon",
      );
      expect(incompleteIcons.length).toBeGreaterThan(0);
    });
  });

  describe("Props Integration", () => {
    it("correctly uses hasTeams, hasIndividuals, and hasCriteria props for validation", () => {
      // Test with all props true - should not show warnings
      const { rerender } = render(
        <OnboardingTour
          {...defaultProps}
          hasTeams={true}
          hasIndividuals={true}
          hasCriteria={true}
        />,
      );

      const mockElement = {
        classList: {
          add: vi.fn(),
          remove: vi.fn(),
        },
        scrollIntoView: vi.fn(),
      };
      const mockQuerySelector = vi.spyOn(document, "querySelector");
      mockQuerySelector.mockReturnValue(mockElement as any);

      // Click team criteria - should not show warning
      const teamCriteriaItem = screen.getByText("Team Criteria");
      fireEvent.click(teamCriteriaItem.closest("li")!);
      expect(toast.error).not.toHaveBeenCalled();

      // Clear previous calls
      vi.clearAllMocks();

      // Re-render with hasTeams false - should show team warning
      rerender(
        <OnboardingTour
          {...defaultProps}
          hasTeams={false}
          hasIndividuals={true}
          hasCriteria={true}
        />,
      );

      fireEvent.click(teamCriteriaItem.closest("li")!);
      expect(toast.error).toHaveBeenCalledWith("Please add a team first", {
        description:
          "You need to create at least one team before setting team criteria.",
      });
    });
  });
});
