import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ManageGeographyRegionsModal from "./manage-geography-regions-modal";
import RoomCountDefinitionModal from "./room-count-definition-modal";
import type { GeographyRegion, RoomCountRange } from "./types/lead-assignment";

describe("Warning handlers for removal of in-use items", () => {
  // No need to mock window.confirm anymore since we're using custom dialogs
  beforeEach(() => {
    // Reset any mocks
  });

  describe("ManageGeographyRegionsModal", () => {
    it("shows warning when deleting a region that is in use", async () => {
      // Mock callback that returns true (region is in use)
      const mockIsRegionInUse = vi.fn().mockImplementation(() => true);

      // Mock regions for testing
      const mockRegions: GeographyRegion[] = [
        {
          id: "region1",
          name: "Test Region",
          countries: ["US"],
          isPredefined: false,
        },
      ];

      render(
        <ManageGeographyRegionsModal
          open={true}
          onOpenChange={() => {}}
          geographyRegions={mockRegions}
          onSave={() => {}}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Find and select the region
      const regionCard = screen.getByText("Test Region");
      fireEvent.click(regionCard);

      // Find and click the delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Verify the call to isRegionInUse
      expect(mockIsRegionInUse).toHaveBeenCalledWith("region1");

      // Verify the warning dialog was shown
      await waitFor(() => {
        // Look for dialog using data-testid and warning text
        const confirmationDialog = screen.getByTestId("confirmation-dialog");
        expect(confirmationDialog).toBeInTheDocument();

        const warningText = screen.getByText((content) => {
          return content.includes("Warning") && content.includes("Test Region");
        });
        expect(warningText).toBeInTheDocument();
      });
    });

    it("doesn't show warning when deleting a region that is not in use", async () => {
      // Mock callback that returns false (region is not in use)
      const mockIsRegionInUse = vi.fn().mockImplementation(() => false);

      // Mock regions for testing
      const mockRegions: GeographyRegion[] = [
        {
          id: "region1",
          name: "Test Region",
          countries: ["US"],
          isPredefined: false,
        },
      ];

      render(
        <ManageGeographyRegionsModal
          open={true}
          onOpenChange={() => {}}
          geographyRegions={mockRegions}
          onSave={() => {}}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Find and select the region
      const regionCard = screen.getByText("Test Region");
      fireEvent.click(regionCard);

      // Find and click the delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Verify mockIsRegionInUse was called
      expect(mockIsRegionInUse).toHaveBeenCalledWith("region1");

      // Verify no warning dialog with "currently in use" text appears
      const warningText = screen.queryByText((content) => {
        return (
          content.includes("Warning") && content.includes("currently in use")
        );
      });
      expect(warningText).not.toBeInTheDocument();
    });

    it("cancels deletion when user declines the warning", async () => {
      // Mock callback that returns true (region is in use)
      const mockIsRegionInUse = vi.fn().mockImplementation(() => true);

      // Mock regions for testing
      const mockRegions: GeographyRegion[] = [
        {
          id: "region1",
          name: "Test Region",
          countries: ["US"],
          isPredefined: false,
        },
      ];

      const mockOnSave = vi.fn();

      render(
        <ManageGeographyRegionsModal
          open={true}
          onOpenChange={() => {}}
          geographyRegions={mockRegions}
          onSave={mockOnSave}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Find and select the region
      const regionCard = screen.getByText("Test Region");
      fireEvent.click(regionCard);

      // Find and click the delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Verify the warning dialog appears
      await waitFor(() => {
        const alertDialog = screen.getByRole("alertdialog");
        expect(alertDialog).toBeInTheDocument();
      });

      // Make sure mockIsRegionInUse was called but onSave was not
      expect(mockIsRegionInUse).toHaveBeenCalledWith("region1");
      expect(mockOnSave).not.toHaveBeenCalled();

      // Region should still be visible
      expect(screen.getByText("Test Region")).toBeInTheDocument();
    });
  });

  describe("RoomCountDefinitionModal", () => {
    it("shows warning when deleting a range that is in use", async () => {
      // Mock callback that returns true (range is in use)
      const mockIsRangeInUse = vi.fn().mockImplementation(() => true);

      // Mock ranges for testing
      const mockRanges: RoomCountRange[] = [
        {
          id: "range1",
          name: "Test Range",
          condition: "range",
          minValue: 1,
          maxValue: 50,
        },
      ];

      render(
        <RoomCountDefinitionModal
          open={true}
          onOpenChange={() => {}}
          ranges={mockRanges}
          onSave={() => {}}
          isRangeInUse={mockIsRangeInUse}
        />,
      );

      // Find and select the range
      const rangeRow = screen.getByText("Test Range");
      fireEvent.click(rangeRow);

      // Find and click the delete button
      const deleteButton = screen.getByText("Delete");
      fireEvent.click(deleteButton);

      // Verify the warning dialog was shown
      await waitFor(() => {
        // Look for dialog heading and warning text
        const dialogHeading = screen.getByRole("heading", {
          name: "Delete Bucket",
        });
        expect(dialogHeading).toBeInTheDocument();

        const warningText = screen.getByText((content) => {
          return content.includes("Warning") && content.includes("Test Range");
        });
        expect(warningText).toBeInTheDocument();
      });

      // Verify mockIsRangeInUse was called
      expect(mockIsRangeInUse).toHaveBeenCalledWith("range1");
    });

    it("doesn't show warning when deleting a range that is not in use", async () => {
      // Mock callback that returns false (range is not in use)
      const mockIsRangeInUse = vi.fn().mockImplementation(() => false);

      // Mock ranges for testing
      const mockRanges: RoomCountRange[] = [
        {
          id: "range1",
          name: "Test Range",
          condition: "range",
          minValue: 1,
          maxValue: 50,
        },
      ];

      render(
        <RoomCountDefinitionModal
          open={true}
          onOpenChange={() => {}}
          ranges={mockRanges}
          onSave={() => {}}
          isRangeInUse={mockIsRangeInUse}
        />,
      );

      // Find and select the range
      const rangeRow = screen.getByText("Test Range");
      fireEvent.click(rangeRow);

      // Find and click the delete button
      const deleteButton = screen.getByText("Delete");
      fireEvent.click(deleteButton);

      // Verify mockIsRangeInUse was called
      expect(mockIsRangeInUse).toHaveBeenCalledWith("range1");

      // Verify no warning dialog with "currently in use" text appears
      const warningText = screen.queryByText((content) => {
        return (
          content.includes("Warning") && content.includes("currently in use")
        );
      });
      expect(warningText).not.toBeInTheDocument();
    });

    it("cancels deletion when user declines the warning", async () => {
      // Mock callback that returns true (range is in use)
      const mockIsRangeInUse = vi.fn().mockImplementation(() => true);

      // Mock ranges for testing
      const mockRanges: RoomCountRange[] = [
        {
          id: "range1",
          name: "Test Range",
          condition: "range",
          minValue: 1,
          maxValue: 50,
        },
      ];

      const mockOnSave = vi.fn();

      render(
        <RoomCountDefinitionModal
          open={true}
          onOpenChange={() => {}}
          ranges={mockRanges}
          onSave={mockOnSave}
          isRangeInUse={mockIsRangeInUse}
        />,
      );

      // Find and select the range
      const rangeRow = screen.getByText("Test Range");
      fireEvent.click(rangeRow);

      // Find and click the delete button
      const deleteButton = screen.getByText("Delete");
      fireEvent.click(deleteButton);

      // Verify the warning dialog appears
      await waitFor(() => {
        const alertDialog = screen.getByRole("alertdialog");
        expect(alertDialog).toBeInTheDocument();
      });

      // Make sure mockIsRangeInUse was called but onSave was not
      expect(mockIsRangeInUse).toHaveBeenCalledWith("range1");
      expect(mockOnSave).not.toHaveBeenCalled();

      // Range should still be visible
      expect(screen.getByText("Test Range")).toBeInTheDocument();
    });
  });
});
