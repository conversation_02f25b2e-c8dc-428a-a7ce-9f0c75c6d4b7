"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type {
  CriteriaState,
  CriteriaTypeString,
  CriterionValue,
  Team,
} from "./types/lead-assignment";

interface ConfigureTeamModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  team?: Team | null;
  onSave: (team: Team) => void;
  criteria: CriteriaState;
  isCreating: boolean;
}

export default function ConfigureTeamModal({
  open,
  onOpenChange,
  team,
  onSave,
  criteria,
  isCreating,
}: ConfigureTeamModalProps) {
  const [name, setName] = useState("");
  const [teamCriteria, setTeamCriteria] = useState<
    Record<CriteriaTypeString, CriterionValue | null>
  >({
    geography: null,
    roomCount: null,
    eventType: null,
    industry: null,
    eventNeeds: null,
    dayOfMonth: null,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form fields when team or open state changes
  useEffect(() => {
    if (open && team) {
      setName(team.name || "");
      // Ensure all criteria types are initialized with proper CriterionValue or null
      setTeamCriteria({
        geography: team.criteria?.geography || null,
        roomCount: team.criteria?.roomCount || null,
        eventType: team.criteria?.eventType || null,
        industry: team.criteria?.industry || null,
        eventNeeds: team.criteria?.eventNeeds || null,
        dayOfMonth: team.criteria?.dayOfMonth || null,
      });
      setErrors({});
    } else if (open && isCreating) {
      // Clear form for new team
      setName("");
      // Initialize with null criteria (blank/not set)
      setTeamCriteria({
        geography: null,
        roomCount: null,
        eventType: null,
        industry: null,
        eventNeeds: null,
        dayOfMonth: null,
      });
      setErrors({});
    }
  }, [open, team, isCreating]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) newErrors.name = "Team name is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validate()) return;

    const updatedTeam: Team = {
      ...(team || { id: crypto.randomUUID(), exceptions: [] }),
      name,
      criteria: teamCriteria,
    };

    onSave(updatedTeam);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isCreating ? "Add New Team" : "Edit Team"}</DialogTitle>
          {isCreating && (
            <p className="text-sm text-muted-foreground mt-2">
              Creating teams is optional. Teams are useful when multiple
              individuals share common assignment rules, such as an entire team
              that only handles west coast leads or events with specific room
              count ranges.
            </p>
          )}
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="team-name">Team Name</Label>
            <Input
              id="team-name"
              data-testid="team-name-input"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={errors.name ? "border-red-500" : ""}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleSave();
                }
              }}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            data-testid="team-cancel-button"
          >
            Cancel
          </Button>
          <Button data-testid="team-save-button" onClick={handleSave}>
            {isCreating ? "Create Team" : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
