import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import type { CriterionValue } from "./types/lead-assignment";
import { CRITERION_ANY } from "./types/lead-assignment";

interface CriteriaOption {
  value: string;
  label: string;
}

interface CriteriaSelectionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  criteriaType: string;
  selectedCriterion?: CriterionValue | null;
  options: string[] | CriteriaOption[];
  onSave: (criterion: CriterionValue | null) => void;
  warningMessage?: {
    title: string;
    description: string;
  };
}

export default function CriteriaSelectionModal({
  open,
  onOpenChange,
  criteriaType,
  selectedCriterion,
  options,
  onSave,
  warningMessage,
}: CriteriaSelectionModalProps) {
  const [criterion, setCriterion] = useState<CriterionValue | null>(null);

  useEffect(() => {
    if (open) {
      // Initialize with the selected criterion (could be null)
      setCriterion(selectedCriterion ?? null);
    }
  }, [open, selectedCriterion]);

  const handleSave = () => {
    onSave(criterion);
    onOpenChange(false);
  };

  const criteriaInfo = {
    geography: {
      label: "Geography",
      explanation:
        "Assign leads based on their geographic origin. Ex. local, west coast, midwest",
    },
    roomCount: {
      label: "Room Count",
      explanation:
        "Assign leads based on number of rooms reserved on peak night, or number of total rooms for event. Ex. More than 50 rooms, between 10 and 20 rooms",
    },
    eventType: {
      label: "Event Type",
      explanation:
        "Assign leads based on the type of event. Ex. Wedding, Corporate Meetings",
    },
    industry: {
      label: "Industry",
      explanation:
        "Assign leads based on the industry of the organization hosting the meeting or event. Ex. SIC code lookup",
    },
    eventNeeds: {
      label: "Event Needs",
      explanation: null,
    },
    dayOfMonth: {
      label: "Day of Month",
      explanation: null,
    },
  } as const;

  const getCriteriaLabel = (type: string) => {
    return criteriaInfo[type as keyof typeof criteriaInfo]?.label || type;
  };

  const getCriteriaExplanation = (type: string) => {
    return criteriaInfo[type as keyof typeof criteriaInfo]?.explanation || null;
  };

  const handleAssignAnyChange = (checked: boolean) => {
    if (checked) {
      setCriterion(CRITERION_ANY);
    } else {
      // When unchecking "Any", set to null (no selection)
      setCriterion(null);
    }
  };

  const handleValueChange = (value: string, checked: boolean) => {
    if (criterion === null || criterion.type === "Any") {
      // If currently null or "Any", switch to specific with this value
      setCriterion({ type: "Specific", values: [value] });
    } else {
      // Update the values array
      const currentValues = criterion.values || [];
      if (checked) {
        setCriterion({ type: "Specific", values: [...currentValues, value] });
      } else {
        const newValues = currentValues.filter((v) => v !== value);
        // If no values left, set to null
        if (newValues.length === 0) {
          setCriterion(null);
        } else {
          setCriterion({ type: "Specific", values: newValues });
        }
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        aria-describedby="criteria-selection-desc"
        data-testid="criteria-selection-modal"
        onKeyDown={(e) => {
          if (e.key === "Enter" && !e.shiftKey && !warningMessage) {
            e.preventDefault();
            handleSave();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>Edit {getCriteriaLabel(criteriaType)}</DialogTitle>
          <p
            id="criteria-selection-desc"
            className="text-sm text-muted-foreground mt-2"
          >
            Select {getCriteriaLabel(criteriaType)} criteria for assignment
            rules.
          </p>
        </DialogHeader>
        {getCriteriaExplanation(criteriaType) && (
          <p className="text-sm text-muted-foreground italic">
            {getCriteriaExplanation(criteriaType)}
          </p>
        )}
        <div className="py-4">
          <div className="flex items-center space-x-2 mb-2">
            <Checkbox
              id={`any-${criteriaType}`}
              checked={criterion?.type === "Any"}
              onCheckedChange={(checked) =>
                handleAssignAnyChange(checked === true)
              }
            />
            <Label htmlFor={`any-${criteriaType}`}>
              Assign Any {getCriteriaLabel(criteriaType)}
            </Label>
          </div>

          <ScrollArea className="h-[200px] pr-4">
            {/* Show warning message if provided */}
            {warningMessage ? (
              <Alert variant="default" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>{warningMessage.title}</AlertTitle>
                <AlertDescription>
                  {warningMessage.description}
                </AlertDescription>
              </Alert>
            ) : null}

            <div className="space-y-2">
              {options.map((option) => {
                // Check if option is a string or an object
                const value =
                  typeof option === "string" ? option : option.value;
                const label =
                  typeof option === "string" ? option : option.label;

                return (
                  <div key={value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${criteriaType}-${value}`}
                      checked={
                        criterion?.type === "Specific" &&
                        criterion.values.includes(value)
                      }
                      onCheckedChange={(checked) =>
                        handleValueChange(value, checked === true)
                      }
                    />
                    <Label htmlFor={`${criteriaType}-${value}`}>{label}</Label>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {/* Show the appropriate action button based on whether we have a warning */}
          {warningMessage ? (
            <Button onClick={() => onOpenChange(false)}>Configure</Button>
          ) : (
            <Button onClick={handleSave}>Save</Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
