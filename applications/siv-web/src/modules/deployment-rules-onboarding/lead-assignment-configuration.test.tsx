import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from "vitest";
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react";
import { useState, useEffect } from "react";
import LeadAssignmentConfiguration from "./lead-assignment-configuration";
import ManageGeographyRegionsModal from "./manage-geography-regions-modal";
import RoomCountDefinitionModal from "./room-count-definition-modal";

// Mock the OnboardingTour component to prevent errors
vi.mock("./onboarding-tour", () => ({
  default: ({ children }: { children: any }) => (
    <div data-testid="mocked-tour">{children}</div>
  ),
}));

// Fix for pointer-events issues in testing
beforeAll(() => {
  // Save original pointer-events style
  const originalPointerEvents = document.body.style.pointerEvents;

  // Set pointer-events to auto to enable clicking
  document.body.style.pointerEvents = "auto";

  // Mock pointer capture methods to prevent issues
  Element.prototype.setPointerCapture = vi.fn();
  Element.prototype.releasePointerCapture = vi.fn();
  window.HTMLElement.prototype.hasPointerCapture = vi.fn();

  return () => {
    // Restore original pointer-events style
    document.body.style.pointerEvents = originalPointerEvents;
  };
});

// These tests are true integration tests that simulate real user flows
describe("LeadAssignmentConfiguration", () => {
  // Spy on console methods to debug issues
  let consoleErrorSpy: ReturnType<typeof vi.spyOn>;

  // Reset state before each test
  beforeEach(() => {
    vi.clearAllMocks();

    // Reset any DOM state that might affect tests
    document.body.innerHTML = "";

    // Spy on console.error to catch React warnings but filter noisy ones
    consoleErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation((message, ...args) => {
        // Filter out known warnings that aren't relevant to the test
        if (
          typeof message === "string" &&
          (message.includes("Missing `Description` or `aria-describedby") ||
            message.includes("React does not recognize the") ||
            message.includes("Invalid DOM property"))
        ) {
          return; // Ignore these warnings
        }
        // Log other errors normally for debugging
        console.info("Console error during test:", message, ...args);
      });
  });

  // Cleanup after each test
  afterEach(() => {
    // Cleanup any remaining event listeners or DOM side effects
    vi.restoreAllMocks();
    consoleErrorSpy.mockRestore();
  });

  // Direct unit tests for the confirmation dialog functionality
  describe("Confirmation Dialog Tests", () => {
    describe("Geography Region Confirmation", () => {
      it("shows warning when deleting a region that is in use", async () => {
        // Mock isRegionInUse to always return true (region is in use)
        const mockIsRegionInUse = vi.fn().mockReturnValue(true);

        // Render the ManageGeographyRegionsModal component directly with our mock
        render(
          <ManageGeographyRegionsModal
            open={true}
            onOpenChange={() => {}}
            geographyRegions={[
              { id: "test-region-id", name: "Test Region", countries: ["US"] },
            ]}
            onSave={() => {}}
            isRegionInUse={mockIsRegionInUse}
          />,
        );

        // Find the Test Region in the modal and select it
        const regionText = await screen.findByText("Test Region");
        fireEvent.click(regionText);

        // Find the delete button and click it
        const deleteButton = screen.getByTestId("delete-region-button");
        fireEvent.click(deleteButton);

        // The confirmation dialog should appear - verify it contains the warning
        await waitFor(() => {
          // Confirmation dialog should be in the document
          const confirmationDialog = screen.getByTestId("confirmation-dialog");
          expect(confirmationDialog).toBeInTheDocument();

          // Text should contain warning and region name
          const dialogText = within(confirmationDialog).getByText((content) => {
            return (
              content.includes("Warning") && content.includes("Test Region")
            );
          });
          expect(dialogText).toBeInTheDocument();
        });

        // Verify isRegionInUse was called with the correct ID
        expect(mockIsRegionInUse).toHaveBeenCalledWith("test-region-id");
      });

      it("does not show warning when deleting an unused region", async () => {
        // Mock isRegionInUse to return false (region is not in use)
        const mockIsRegionInUse = vi.fn().mockReturnValue(false);

        // Render the ManageGeographyRegionsModal component directly with our mock
        render(
          <ManageGeographyRegionsModal
            open={true}
            onOpenChange={() => {}}
            geographyRegions={[
              { id: "test-region-id", name: "Test Region", countries: ["US"] },
            ]}
            onSave={() => {}}
            isRegionInUse={mockIsRegionInUse}
          />,
        );

        // Find the Test Region in the modal and select it
        const regionText = await screen.findByText("Test Region");
        fireEvent.click(regionText);

        // Find the delete button and click it
        const deleteButton = screen.getByTestId("delete-region-button");
        fireEvent.click(deleteButton);

        // Verify no confirmation dialog appears (deleted directly)
        // Wait a bit to make sure no confirmation dialog appears
        await new Promise((resolve) => setTimeout(resolve, 100));

        // No confirmation dialog should be present
        expect(
          screen.queryByTestId("confirmation-dialog"),
        ).not.toBeInTheDocument();

        // Verify isRegionInUse was called
        expect(mockIsRegionInUse).toHaveBeenCalledWith("test-region-id");
      });

      it("cancels deletion when user clicks Cancel on warning", async () => {
        // Mock isRegionInUse to return true (region is in use)
        const mockIsRegionInUse = vi.fn().mockReturnValue(true);

        // Mock onSave to check that it's not called (no deletion)
        const mockSave = vi.fn();

        // Render the component
        render(
          <ManageGeographyRegionsModal
            open={true}
            onOpenChange={() => {}}
            geographyRegions={[
              { id: "test-region-id", name: "Test Region", countries: ["US"] },
            ]}
            onSave={mockSave}
            isRegionInUse={mockIsRegionInUse}
          />,
        );

        // Find the Test Region in the modal and select it
        const regionText = await screen.findByText("Test Region");
        fireEvent.click(regionText);

        // Find the delete button and click it
        const deleteButton = screen.getByTestId("delete-region-button");
        fireEvent.click(deleteButton);

        // Find the confirmation dialog and click Cancel
        await waitFor(() => {
          const confirmationDialog = screen.getByTestId("confirmation-dialog");
          const cancelButton = screen.getByTestId("confirmation-cancel-button");
          fireEvent.click(cancelButton);
        });

        // Verify the region still exists (was not deleted)
        expect(screen.getByText("Test Region")).toBeInTheDocument();
      });
    });

    describe("Room Count Range Confirmation", () => {
      it("shows warning when deleting a range that is in use", async () => {
        // Mock isRangeInUse to return true (range is in use)
        const mockIsRangeInUse = vi.fn().mockReturnValue(true);

        // Render the component directly
        render(
          <RoomCountDefinitionModal
            open={true}
            onOpenChange={() => {}}
            ranges={[
              {
                id: "test-range-id",
                name: "Test Range",
                condition: "less",
                maxValue: 100,
              },
            ]}
            onSave={() => {}}
            isRangeInUse={mockIsRangeInUse}
          />,
        );

        // Find the Test Range in the modal and select it
        const rangeText = await screen.findByText("Test Range");
        fireEvent.click(rangeText);

        // Find the delete button and click it
        const deleteButton = screen.getByText("Delete");
        fireEvent.click(deleteButton);

        // The confirmation dialog should appear
        await waitFor(() => {
          const confirmationDialog = screen.getByTestId("confirmation-dialog");
          expect(confirmationDialog).toBeInTheDocument();

          // Text should contain warning and range name
          const dialogText = within(confirmationDialog).getByText((content) => {
            return (
              content.includes("Warning") && content.includes("Test Range")
            );
          });
          expect(dialogText).toBeInTheDocument();
        });

        // Verify isRangeInUse was called with the correct ID
        expect(mockIsRangeInUse).toHaveBeenCalledWith("test-range-id");
      });

      it("does not show warning when deleting an unused range", async () => {
        // Mock isRangeInUse to return false (range is not in use)
        const mockIsRangeInUse = vi.fn().mockReturnValue(false);

        // Render the component directly
        render(
          <RoomCountDefinitionModal
            open={true}
            onOpenChange={() => {}}
            ranges={[
              {
                id: "test-range-id",
                name: "Test Range",
                condition: "less",
                maxValue: 100,
              },
            ]}
            onSave={() => {}}
            isRangeInUse={mockIsRangeInUse}
          />,
        );

        // Find the Test Range in the modal and select it
        const rangeText = await screen.findByText("Test Range");
        fireEvent.click(rangeText);

        // Find the delete button and click it
        const deleteButton = screen.getByText("Delete");
        fireEvent.click(deleteButton);

        // Verify no confirmation dialog appears (deleted directly)
        // Wait a bit to make sure no confirmation dialog appears
        await new Promise((resolve) => setTimeout(resolve, 100));

        // No confirmation dialog should be present
        expect(
          screen.queryByTestId("confirmation-dialog"),
        ).not.toBeInTheDocument();

        // Verify isRangeInUse was called
        expect(mockIsRangeInUse).toHaveBeenCalledWith("test-range-id");
      });

      it("cancels deletion when user clicks Cancel on the warning", async () => {
        // Mock isRangeInUse to return true (range is in use)
        const mockIsRangeInUse = vi.fn().mockReturnValue(true);

        // Mock onSave to check that it's not called (no deletion)
        const mockSave = vi.fn();

        // Render the component
        render(
          <RoomCountDefinitionModal
            open={true}
            onOpenChange={() => {}}
            ranges={[
              {
                id: "test-range-id",
                name: "Test Range",
                condition: "less",
                maxValue: 100,
              },
            ]}
            onSave={mockSave}
            isRangeInUse={mockIsRangeInUse}
          />,
        );

        // Find the Test Range in the modal and select it
        const rangeText = await screen.findByText("Test Range");
        fireEvent.click(rangeText);

        // Find the delete button and click it
        const deleteButton = screen.getByText("Delete");
        fireEvent.click(deleteButton);

        // Find the confirmation dialog and click Cancel
        await waitFor(() => {
          const confirmationDialog = screen.getByTestId("confirmation-dialog");
          const cancelButton = screen.getByTestId("confirmation-cancel-button");
          fireEvent.click(cancelButton);
        });

        // Verify the range still exists (was not deleted)
        expect(screen.getByText("Test Range")).toBeInTheDocument();
      });
    });
  });

  describe("Criteria Value Cleanup", () => {
    it("cleans up invalid geography region values when saving geography regions", async () => {
      // Mock the cleanupInvalidCriteriaValues function to verify it's called
      const mockCleanup = vi.fn();

      // Render ManageGeographyRegionsModal with onDefinitionsChanged prop
      render(
        <ManageGeographyRegionsModal
          open={true}
          onOpenChange={() => {}}
          geographyRegions={[
            { id: "region1", name: "Region 1", countries: ["US"] },
            { id: "region2", name: "Region 2", countries: ["CA"] },
          ]}
          onSave={() => {}}
          isRegionInUse={() => false}
          onDefinitionsChanged={mockCleanup}
        />,
      );

      // Find and click the "Save All Changes" button
      const saveButton = screen.getByTestId("save-all-changes-button");
      fireEvent.click(saveButton);

      // Verify the cleanup function was called with an array that includes our regions
      expect(mockCleanup).toHaveBeenCalled();
      const calledWith = mockCleanup.mock.calls[0][0];
      expect(calledWith).toContain("region1");
      expect(calledWith).toContain("region2");
    });

    it("cleans up invalid room count range values when saving room count ranges", async () => {
      // Mock the cleanupInvalidCriteriaValues function to verify it's called
      const mockCleanup = vi.fn();

      // Render RoomCountDefinitionModal with onDefinitionsChanged prop
      render(
        <RoomCountDefinitionModal
          open={true}
          onOpenChange={() => {}}
          ranges={[
            { id: "range1", name: "Range 1", condition: "less", maxValue: 100 },
            {
              id: "range2",
              name: "Range 2",
              condition: "greater",
              minValue: 100,
            },
          ]}
          onSave={() => {}}
          isRangeInUse={() => false}
          onDefinitionsChanged={mockCleanup}
        />,
      );

      // Find and click the "Save All Buckets" button
      const saveButton = screen.getByText("Save All Buckets");
      fireEvent.click(saveButton);

      // Verify the cleanup function was called with an array that includes our ranges
      expect(mockCleanup).toHaveBeenCalled();
      const calledWith = mockCleanup.mock.calls[0][0];
      expect(calledWith).toContain("range1");
      expect(calledWith).toContain("range2");
    });
  });

  describe("Assignment Summary Access", () => {
    it("shows error dialog when trying to access summary with overlapping rules", async () => {
      // We'll test the button logic by mocking the overlap detection to simulate overlaps
      // This is a more focused unit test approach

      // Spy on findOverlappingAssignments to force it to return overlaps

      // We need to actually import the module and mock it
      const overlapDetectionModule = await import("./utils/overlap-detection");
      vi.spyOn(
        overlapDetectionModule,
        "findOverlappingAssignments",
      ).mockImplementation(() => ({
        allOverlaps: [{
          type: "conflict",
          involvedEntityIds: ["1", "2"],
          overlappingRuleIds: ["rule1", "rule2"],
          entity1Name: "John Smith",
          entity2Name: "Test Individual",
          summary: "John Smith and Test Individual have overlapping rules"
        }],
        entityIdsWithConflicts: new Set(["1", "2"]),
        entityIdsWithRedundanciesOnly: new Set()
      }));

      // Render the main component after mocking
      render(<LeadAssignmentConfiguration />);

      // Add an individual to trigger overlap detection
      const addIndividualButton = screen.getByTestId("add-individual-button");
      fireEvent.click(addIndividualButton);

      await waitFor(() => {
        expect(screen.getByText("Add New Individual")).toBeInTheDocument();
      });

      // Fill out minimal form fields
      const firstNameInput = screen.getByLabelText("First Name");
      fireEvent.change(firstNameInput, { target: { value: "Test" } });

      const lastNameInput = screen.getByLabelText("Last Name");
      fireEvent.change(lastNameInput, { target: { value: "Individual" } });

      const titleInput = screen.getByLabelText("Title");
      fireEvent.change(titleInput, { target: { value: "Sales Rep" } });

      const emailInput = screen.getByLabelText("Email");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      const phoneInput = screen.getByLabelText("Phone");
      fireEvent.change(phoneInput, { target: { value: "555-1234" } });

      const saveButton = screen.getByText("Save");
      fireEvent.click(saveButton);

      // Wait for modal to close and overlap detection to run
      await waitFor(() => {
        expect(
          screen.queryByText("Add New Individual"),
        ).not.toBeInTheDocument();
      });

      // Give time for overlap detection to run
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Now try to click "View Assignment Summary" - this should show the error dialog
      const viewSummaryButton = screen.getByTestId("assignment-summary");
      fireEvent.click(viewSummaryButton);

      // Check if overlap alert dialog appears
      await waitFor(() => {
        expect(
          screen.getByText("Cannot View Assignment Summary"),
        ).toBeInTheDocument();
        expect(
          screen.getByText(/You have overlapping assignment rules/),
        ).toBeInTheDocument();
      });

      // Verify the dialog has a close button
      const closeButton = screen.getByText("Close");
      expect(closeButton).toBeInTheDocument();

      // Click close and verify dialog disappears
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(
          screen.queryByText("Cannot View Assignment Summary"),
        ).not.toBeInTheDocument();
      });

      // Verify the mock was called
      expect(overlapDetectionModule.findOverlappingAssignments).toHaveBeenCalled();
    });

    it("allows access to summary when no overlaps exist", async () => {
      // Render the main component
      render(<LeadAssignmentConfiguration />);

      // Since the default state has only one individual with no specific criteria,
      // there should be no overlaps initially

      // Click "View Assignment Summary" button
      const viewSummaryButton = screen.getByTestId("assignment-summary");
      fireEvent.click(viewSummaryButton);

      // Should not show error dialog, should navigate to summary
      // Wait to make sure no error dialog appears
      await new Promise((resolve) => setTimeout(resolve, 200));

      expect(
        screen.queryByText("Cannot View Assignment Summary"),
      ).not.toBeInTheDocument();

      // Should show assignment summary instead
      // Note: The actual summary component might show different content,
      // but we can verify we're not showing the error
      expect(
        screen.queryByText(/You have overlapping assignment rules/),
      ).not.toBeInTheDocument();
    });
  });

  describe("Team Rule Materialization", () => {
    it("automatically materializes team rules for individuals with empty rules on component mount", async () => {
      // Create a mock component that simulates the scenario where an individual
      // is assigned to a team but has empty rules (before materialization was added)
      const TestComponent = () => {
        const [individuals, setIndividuals] = useState([
          {
            id: "individual-1",
            name: "Test Individual",
            firstName: "Test",
            lastName: "Individual",
            title: "Test Title",
            email: "<EMAIL>",
            phone: "555-1234",
            teamId: "team-1",
            criteria: {
              geography: null,
              roomCount: null,
              eventType: null,
              industry: null,
              eventNeeds: null,
              dayOfMonth: null,
            },
            rules: [], // Empty rules - should get materialized
            exceptions: [],
          },
        ]);

        const [teams] = useState([
          {
            id: "team-1",
            name: "Test Team",
            criteria: {
              geography: null,
              roomCount: null,
              eventType: { type: "Specific", values: ["Wedding"] },
              industry: { type: "Specific", values: ["Technology"] },
              eventNeeds: null,
              dayOfMonth: null,
            },
            rules: [
              {
                id: "team-rule-1",
                criteria: {
                  eventType: { type: "Specific", values: ["Wedding"] },
                  industry: { type: "Specific", values: ["Technology"] },
                },
              },
            ],
            exceptions: [],
          },
        ]);

        // Simulate the materialization logic from the main component
        useEffect(() => {
          let needsUpdate = false;
          const updatedIndividuals = individuals.map((individual) => {
            if (individual.teamId) {
              const team = teams.find((t) => t.id === individual.teamId);
              if (team && team.rules && team.rules.length > 0) {
                const hasNoRules = !individual.rules || individual.rules.length === 0;
                const hasEmptyRules =
                  individual.rules &&
                  individual.rules.length > 0 &&
                  individual.rules.every((rule) =>
                    Object.values(rule.criteria).every(
                      (criterion) => criterion === null || criterion === undefined
                    )
                  );

                if (hasNoRules || hasEmptyRules) {
                  needsUpdate = true;
                  // Materialize team rules
                  const materializedRules = team.rules.map((teamRule) => ({
                    id: crypto.randomUUID(),
                    criteria: { ...teamRule.criteria },
                  }));

                  return {
                    ...individual,
                    rules: materializedRules,
                  };
                }
              }
            }
            return individual;
          });

          if (needsUpdate) {
            setIndividuals(updatedIndividuals);
          }
        }, [teams]);

        return (
          <div>
            <div data-testid="individual-name">{individuals[0].name}</div>
            <div data-testid="individual-rules-count">{individuals[0].rules.length}</div>
            {individuals[0].rules.length > 0 && (
              <div data-testid="materialized-event-type">
                {individuals[0].rules[0].criteria.eventType?.values?.[0] || "null"}
              </div>
            )}
            {individuals[0].rules.length > 0 && (
              <div data-testid="materialized-industry">
                {individuals[0].rules[0].criteria.industry?.values?.[0] || "null"}
              </div>
            )}
          </div>
        );
      };

      // Render the test component
      render(<TestComponent />);

      // Verify the individual name is displayed
      expect(screen.getByTestId("individual-name")).toHaveTextContent("Test Individual");

      // Wait for the useEffect to run and materialize the rules
      await waitFor(() => {
        // Should have materialized 1 rule from the team
        expect(screen.getByTestId("individual-rules-count")).toHaveTextContent("1");
      });

      // Verify the materialized rules contain the correct team criteria
      await waitFor(() => {
        expect(screen.getByTestId("materialized-event-type")).toHaveTextContent("Wedding");
        expect(screen.getByTestId("materialized-industry")).toHaveTextContent("Technology");
      });
    });

    it("does not materialize rules for individuals who already have non-empty rules", async () => {
      const TestComponent = () => {
        const [individuals, setIndividuals] = useState([
          {
            id: "individual-1",
            name: "Test Individual",
            firstName: "Test",
            lastName: "Individual", 
            title: "Test Title",
            email: "<EMAIL>",
            phone: "555-1234",
            teamId: "team-1",
            criteria: {
              geography: null,
              roomCount: null,
              eventType: null,
              industry: null,
              eventNeeds: null,
              dayOfMonth: null,
            },
            rules: [
              {
                id: "existing-rule",
                criteria: {
                  eventType: { type: "Specific", values: ["Conference"] },
                  industry: { type: "Specific", values: ["Finance"] },
                },
              },
            ], // Already has rules - should not be overwritten
            exceptions: [],
          },
        ]);

        const [teams] = useState([
          {
            id: "team-1",
            name: "Test Team",
            criteria: {
              geography: null,
              roomCount: null,
              eventType: { type: "Specific", values: ["Wedding"] },
              industry: { type: "Specific", values: ["Technology"] },
              eventNeeds: null,
              dayOfMonth: null,
            },
            rules: [
              {
                id: "team-rule-1",
                criteria: {
                  eventType: { type: "Specific", values: ["Wedding"] },
                  industry: { type: "Specific", values: ["Technology"] },
                },
              },
            ],
            exceptions: [],
          },
        ]);

        // Track if materialization was attempted
        const [materializationAttempted, setMaterializationAttempted] = useState(false);

        useEffect(() => {
          let needsUpdate = false;
          const updatedIndividuals = individuals.map((individual) => {
            if (individual.teamId) {
              const team = teams.find((t) => t.id === individual.teamId);
              if (team && team.rules && team.rules.length > 0) {
                const hasNoRules = !individual.rules || individual.rules.length === 0;
                const hasEmptyRules =
                  individual.rules &&
                  individual.rules.length > 0 &&
                  individual.rules.every((rule) =>
                    Object.values(rule.criteria).every(
                      (criterion) => criterion === null || criterion === undefined
                    )
                  );

                if (hasNoRules || hasEmptyRules) {
                  setMaterializationAttempted(true);
                  needsUpdate = true;
                  const materializedRules = team.rules.map((teamRule) => ({
                    id: crypto.randomUUID(),
                    criteria: { ...teamRule.criteria },
                  }));

                  return {
                    ...individual,
                    rules: materializedRules,
                  };
                }
              }
            }
            return individual;
          });

          if (needsUpdate) {
            setIndividuals(updatedIndividuals);
          }
        }, [teams]);

        return (
          <div>
            <div data-testid="individual-rules-count">{individuals[0].rules.length}</div>
            <div data-testid="materialization-attempted">{materializationAttempted.toString()}</div>
            <div data-testid="original-event-type">
              {individuals[0].rules[0].criteria.eventType?.values?.[0] || "null"}
            </div>
            <div data-testid="original-industry">
              {individuals[0].rules[0].criteria.industry?.values?.[0] || "null"}
            </div>
          </div>
        );
      };

      render(<TestComponent />);

      // Wait a moment for any effects to run
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Should still have the original 1 rule
      expect(screen.getByTestId("individual-rules-count")).toHaveTextContent("1");

      // Should not have attempted materialization
      expect(screen.getByTestId("materialization-attempted")).toHaveTextContent("false");

      // Should preserve original rule values
      expect(screen.getByTestId("original-event-type")).toHaveTextContent("Conference");
      expect(screen.getByTestId("original-industry")).toHaveTextContent("Finance");
    });
  });
});
