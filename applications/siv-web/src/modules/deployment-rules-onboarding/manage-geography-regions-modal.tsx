"use client";

import { useEffect, useRef, useState } from "react";
import { Country, State } from "country-state-city";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { AlertCircle, Info, Plus, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { SlideInPanel } from "./components/slide-in-panel";
import { ConfirmationDialog } from "./components/confirmation-dialog";
import { cn } from "@/lib/utils";
import type { GeographyRegion } from "./types/lead-assignment";
import { PRECONFIGURED_REGIONS } from "./constants/geography-templates";
import {
  calculateDynamicSystemRegions,
  createTemplateRegions,
  findOverlappingRegion,
  getRegionDescription,
  SYSTEM_REGION_TYPES,
  TEMPLATE_REGION_TYPES,
} from "./utils/geography-region-utils";

interface ManageGeographyRegionsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  geographyRegions: GeographyRegion[];
  onSave: (regions: GeographyRegion[]) => void;
  // Optional callback to check if a region is in use
  isRegionInUse?: (regionId: string) => boolean;
  // Optional callback when definitions change to clean up invalid values
  onDefinitionsChanged?: (validRegionIds: string[]) => void;
}

export default function ManageGeographyRegionsModal({
  open,
  onOpenChange,
  geographyRegions,
  onSave,
  isRegionInUse,
  onDefinitionsChanged,
}: ManageGeographyRegionsModalProps) {
  // Initialize with provided regions or defaults if none
  const [regions, setRegions] = useState<GeographyRegion[]>([]);

  // Selected region for editing
  const [selectedRegion, setSelectedRegion] = useState<GeographyRegion | null>(
    null,
  );

  // Form state
  const [regionName, setRegionName] = useState("");
  const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
  const [selectedUSStates, setSelectedUSStates] = useState<string[]>([]);
  const [showUSStates, setShowUSStates] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [helpMessage, setHelpMessage] = useState<string | null>(null);
  const [countrySearchOpen, setCountrySearchOpen] = useState(false);
  const [stateSearchOpen, setStateSearchOpen] = useState(false);
  const [countrySearch, setCountrySearch] = useState("");
  const [stateSearch, setStateSearch] = useState("");

  // DOM Refs for scroll handling
  const statesScrollAreaRef = useRef<HTMLDivElement>(null);
  const countriesScrollAreaRef = useRef<HTMLDivElement>(null);

  // State for keeping track of system regions and excluded regions
  const [excludedSystemRegions, setExcludedSystemRegions] = useState<string[]>(
    [],
  );

  // Get all countries and US states
  const allCountries = Country.getAllCountries();
  const allUSStates = State.getStatesOfCountry("US");

  // Placeholder comment to preserve line numbers
  // Old function replaced by the improved calculateExcludedSystemRegions

  // Excluded regions are now updated synchronously with regions state
  // We calculate excluded regions first and update both state variables at once
  // This helps avoid race conditions and makes tests more predictable

  // Initialize state when the modal opens or when geographyRegions change
  useEffect(() => {
    if (open) {
      // Start with a clean copy of the user-defined regions from geographyRegions
      const userDefinedRegions = geographyRegions.filter(
        (r) => !r.isPredefined,
      );

      // Update all regions (user-defined + system) and trigger UI refresh
      // This will add the necessary system regions and update excluded regions
      updateRegionsWithSystemRegions(userDefinedRegions);

      // Reset the form
      resetForm();
    }
  }, [open, geographyRegions]);

  // Update US states visibility when countries change
  useEffect(() => {
    const isUSOnly =
      selectedCountries.length === 1 && selectedCountries[0] === "US";

    // Only show US states selector if US is the only selected country
    setShowUSStates(isUSOnly);

    // If US is now the only country selected, show a helper message
    if (isUSOnly && selectedCountries.length > 0) {
      setHelpMessage(
        "You can now select specific US states/territories if desired. If none are selected, all US states/territories will be included.",
      );
      // Clear message after 5 seconds
      setTimeout(() => {
        setHelpMessage((prevMsg) =>
          prevMsg ===
          "You can now select specific US states/territories if desired. If none are selected, all US states/territories will be included."
            ? null
            : prevMsg,
        );
      }, 8000);
    }
    // If US is no longer the only selected country, clear selected states
    else if (!isUSOnly) {
      if (selectedUSStates.length > 0) {
        setSelectedUSStates([]);
        // Show warning message
        setHelpMessage(
          "US state/territory selections have been cleared because multiple countries are selected.",
        );
        // Clear message after 5 seconds
        setTimeout(() => {
          setHelpMessage((prevMsg) =>
            prevMsg ===
            "US state/territory selections have been cleared because multiple countries are selected."
              ? null
              : prevMsg,
          );
        }, 5000);
      }
    }
  }, [selectedCountries, selectedUSStates.length]);

  const resetForm = () => {
    setSelectedRegion(null);
    setRegionName("");
    setSelectedCountries([]);
    setSelectedUSStates([]);
    setValidationError(null);
    setHelpMessage(
      "Please define a new region by entering a name and selecting countries",
    );

    // Flash the form fields to indicate they've been reset
    const nameInput = document.getElementById("region-name");
    if (nameInput) {
      nameInput.focus();
      setTimeout(() => nameInput.blur(), 300);
    }
  };

  const selectRegionForEdit = (region: GeographyRegion) => {
    // Only user-defined regions can be edited
    if (region.isPredefined) return;
    setSelectedRegion(region);
    setRegionName(region.name);
    setSelectedCountries(region.countries || []);
    setSelectedUSStates(region.usStates || []);
    setValidationError(null);
    setHelpMessage(null);
    // Store the original region for reset
    setOriginalRegion(region);
  };

  // Store the original region state for reset
  const [originalRegion, setOriginalRegion] = useState<GeographyRegion | null>(
    null,
  );

  // Reset form to the original selected region's values
  const resetToOriginalRegion = () => {
    if (originalRegion) {
      setRegionName(originalRegion.name);
      setSelectedCountries(originalRegion.countries || []);
      setSelectedUSStates(originalRegion.usStates || []);
      setValidationError(null);
      setHelpMessage(null);
    }
  };

  // Handle adding a new preconfigured template
  const handleQuickAddTemplate = (
    template: (typeof PRECONFIGURED_REGIONS)[0],
  ) => {
    // Handle special dynamic templates
    let templateCountries = [...template.countries];
    let templateName = template.name;

    // Handle special dynamic templates
    if (template.dynamicTemplate === "ALL_NON_US_COUNTRIES") {
      // Get all countries from country-state-city
      const allCountries = Country.getAllCountries();
      templateCountries = allCountries
        .filter((country) => country.isoCode !== "US")
        .map((country) => country.isoCode);
    } else if (template.dynamicTemplate === "USA_ALL_STATES") {
      // This template is just USA with no states specified (meaning all states)
      templateCountries = ["US"];
      // Ensure usStates is empty array (means all states)
      template.usStates = [];
    }

    // Create new region from template
    const newRegion: GeographyRegion = {
      id: crypto.randomUUID(),
      name: templateName,
      countries: templateCountries,
      usStates: template.usStates || [],
      isPredefined: false,
    };

    // Get only non-predefined (user-defined) regions
    const userDefinedRegions = regions.filter((r) => !r.isPredefined);

    // Check for overlaps with other user-defined regions
    const overlappingRegion = findOverlappingRegion(
      newRegion,
      userDefinedRegions,
    );

    if (overlappingRegion) {
      setValidationError(
        `Template "${template.name}" overlaps with existing region "${overlappingRegion.name}". Cannot add.`,
      );
      return;
    }

    // Add the new region to the current user-defined regions
    const currentUserDefinedRegions = regions.filter((r) => !r.isPredefined);
    const updatedUserRegions = [...currentUserDefinedRegions, newRegion];

    // Calculate the new regions but don't update state yet
    const updatedRegions = updateRegionsWithSystemRegions(
      updatedUserRegions,
      false,
    );

    // Check for regions that would be affected by this change
    if (isRegionInUse) {
      const affectedRegionIds = findAffectedRegions(regions, updatedRegions);

      if (affectedRegionIds.length > 0) {
        // Get names of affected regions for the warning message
        const affectedRegionNames = affectedRegionIds.map((id) => {
          const region = regions.find((r) => r.id === id);
          return region ? region.name : id;
        });

        // Show custom confirmation dialog instead of window.confirm
        const confirmMessage = `Warning: Adding "${template.name}" will exclude the following regions that are currently in use by assignment rules: ${affectedRegionNames.join(", ")}. 
        This will remove these values from any assignment rules using them.`;

        // Use the showConfirmationDialog helper function
        showConfirmationDialog(
          "Warning: Regions In Use",
          confirmMessage,
          () => {
            // This will be executed if the user confirms
            setRegions(updatedRegions);
          },
        );

        // Return early - the action will be completed if the user confirms
        return;
      }
    }

    // Now update the state with the new regions
    setRegions(updatedRegions);
  };

  // Old overlap detection logic is no longer used
  // We now use the findOverlappingRegion function from geography-region-utils

  // Calculate the dynamic system regions based on current user-defined regions
  const calculateSystemRegions = (
    userDefinedRegions: GeographyRegion[],
  ): GeographyRegion[] => {
    // Get user-defined regions (non-predefined)
    const userRegions = userDefinedRegions.filter((r) => !r.isPredefined);

    // Use our utility function to calculate the dynamic system regions
    return calculateDynamicSystemRegions(userRegions);
  };

  // Update regions with the correct system regions and trigger UI refresh
  const updateRegionsWithSystemRegions = (
    userRegions: GeographyRegion[],
    updateState: boolean = true,
  ) => {
    // Filter to get only user-defined regions
    const onlyUserRegions = userRegions.filter((r) => !r.isPredefined);

    // Calculate the dynamic system regions based on user regions
    const dynamicSystemRegions = calculateDynamicSystemRegions(onlyUserRegions);

    // Create a new regions array with both user regions and system regions
    const updatedRegions = [...onlyUserRegions, ...dynamicSystemRegions];

    if (updateState) {
      // Calculate which system regions should be excluded first
      const updatedExcludedRegions = calculateExcludedSystemRegions(
        onlyUserRegions,
        updatedRegions,
      );

      // Then update both state variables at once to avoid race conditions
      setRegions(updatedRegions);
      setExcludedSystemRegions(updatedExcludedRegions);
      console.error("EXCLUDED SYSTEM REGIONS", updatedExcludedRegions);
    }

    return updatedRegions;
  };

  // Helper to calculate excluded system regions to ensure consistency
  const calculateExcludedSystemRegions = (
    userDefinedRegions: GeographyRegion[],
    allRegions: GeographyRegion[],
  ): string[] => {
    const excludedRegions: string[] = [];

    // Check if "All Non-US Countries" should be excluded
    const hasNonUSCountries = userDefinedRegions.some(
      (r) => r.countries && r.countries.some((country) => country !== "US"),
    );

    if (hasNonUSCountries) {
      excludedRegions.push(TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES);
    }

    // Check if "USA (All States)" should be excluded
    const hasUSRegions = userDefinedRegions.some(
      (r) => r.countries && r.countries.includes("US"),
    );

    if (hasUSRegions) {
      excludedRegions.push(TEMPLATE_REGION_TYPES.USA_ALL_STATES);

      // Only exclude "All Other US States" if we don't have any specific US states
      // OR we have the entire US (since there are no "other" states in that case)
      const hasSpecificUSStates = userDefinedRegions.some(
        (r) =>
          r.countries &&
          r.countries.includes("US") &&
          r.usStates &&
          r.usStates.length > 0,
      );

      const hasEntireUS = userDefinedRegions.some(
        (r) =>
          r.countries &&
          r.countries.includes("US") &&
          (!r.usStates || r.usStates.length === 0),
      );

      // Add to excluded regions only if the condition is met AND the region isn't already included
      if (
        (!hasSpecificUSStates || hasEntireUS) &&
        !allRegions.some(
          (r) => r.predefinedType === SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
        )
      ) {
        excludedRegions.push(SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES);
      }
    }

    // Check if "All Other Countries/Regions" should be excluded
    if (
      userDefinedRegions.length > 0 &&
      !allRegions.some(
        (r) => r.predefinedType === SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
      )
    ) {
      excludedRegions.push(SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES);
    }

    return excludedRegions;
  };

  // Validate form before saving
  const validateForm = (): boolean => {
    // Check required fields
    if (!regionName.trim()) {
      setValidationError("Region name is required");
      return false;
    }

    // Check for unique name among user-defined regions
    const duplicateName = regions.some(
      (r) =>
        !r.isPredefined &&
        r.name.toLowerCase() === regionName.trim().toLowerCase() &&
        (!selectedRegion || r.id !== selectedRegion.id),
    );

    if (duplicateName) {
      setValidationError("Region name must be unique");
      return false;
    }

    // Require at least one country
    if (selectedCountries.length === 0) {
      setValidationError("At least one country must be selected");
      return false;
    }

    // Create a GeographyRegion object from the form data
    const regionToValidate: GeographyRegion = {
      id: selectedRegion?.id || crypto.randomUUID(),
      name: regionName.trim(),
      countries: selectedCountries,
      usStates: selectedUSStates.length > 0 ? selectedUSStates : undefined,
      isPredefined: false,
    };

    // Get only non-predefined (user-defined) regions
    const userDefinedRegions = regions.filter((r) => !r.isPredefined);

    // Filter out the current region if we're editing
    const otherUserRegions = userDefinedRegions.filter(
      (r) => r.id !== regionToValidate.id,
    );

    // Check for overlaps with other user-defined regions
    const overlappingRegion = findOverlappingRegion(
      regionToValidate,
      otherUserRegions,
    );

    if (overlappingRegion) {
      setValidationError(
        `Region overlaps with existing region "${overlappingRegion.name}". Regions must be geographically distinct.`,
      );
      return false;
    }

    return true;
  };

  // Save the current form as a new/updated region
  const handleSaveRegion = () => {
    if (!validateForm()) return;

    const regionData: GeographyRegion = {
      id: selectedRegion?.id || crypto.randomUUID(),
      name: regionName.trim(),
      countries: selectedCountries,
      usStates: selectedUSStates.length > 0 ? selectedUSStates : undefined,
      isPredefined: false,
    };

    // Build the list of user-defined regions with the new/updated region
    let userDefinedRegionsWithUpdate: GeographyRegion[];

    if (selectedRegion) {
      // For editing, update the existing region
      userDefinedRegionsWithUpdate = regions
        .filter((r) => !r.isPredefined)
        .map((r) => (r.id === regionData.id ? regionData : r));
    } else {
      // For adding a new region
      userDefinedRegionsWithUpdate = [
        ...regions.filter((r) => !r.isPredefined),
        regionData,
      ];
    }

    // Calculate the new regions but don't update state yet
    const updatedRegions = updateRegionsWithSystemRegions(
      userDefinedRegionsWithUpdate,
      false,
    );

    // Check for regions that would be affected by this change
    if (isRegionInUse) {
      const affectedRegionIds = findAffectedRegions(regions, updatedRegions);

      if (affectedRegionIds.length > 0) {
        // Get names of affected regions for the warning message
        const affectedRegionNames = affectedRegionIds.map((id) => {
          const region = regions.find((r) => r.id === id);
          return region ? region.name : id;
        });

        // Show custom confirmation dialog instead of window.confirm
        const confirmMessage = `Warning: Saving this region will exclude the following regions that are currently in use by assignment rules: ${affectedRegionNames.join(", ")}. 
        This will remove these values from any assignment rules using them.`;

        // Use the showConfirmationDialog helper function
        showConfirmationDialog(
          "Warning: Regions In Use",
          confirmMessage,
          () => {
            // This will be executed if the user confirms
            setRegions(updatedRegions);
            resetForm();
          },
        );

        // Return early - the action will be completed if the user confirms
        return;
      }
    }

    // Now update the state with the new regions
    setRegions(updatedRegions);

    resetForm();
  };

  // Find which regions would be affected by a change that would exclude them
  const findAffectedRegions = (
    currentRegions: GeographyRegion[],
    updatedRegions: GeographyRegion[],
  ): string[] => {
    // Get the IDs of regions in current but not in updated regions
    const currentIds = new Set(currentRegions.map((r) => r.id));
    const updatedIds = new Set(updatedRegions.map((r) => r.id));

    // Find regions that exist in current but not in updated
    const potentiallyAffectedIds: string[] = [];
    currentIds.forEach((id) => {
      if (!updatedIds.has(id)) {
        potentiallyAffectedIds.push(id);
      }
    });

    // Filter to only those that are in use (if the callback is provided)
    if (isRegionInUse) {
      return potentiallyAffectedIds.filter((id) => isRegionInUse(id));
    }

    return potentiallyAffectedIds;
  };

  // State for confirmation dialogs
  const [deletionConfirmOpen, setDeletionConfirmOpen] = useState(false);
  const [deletionConfirmMessage, setDeletionConfirmMessage] = useState("");
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState("");
  const [confirmDialogTitle, setConfirmDialogTitle] = useState("");
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );

  // Helper function to show confirmation dialog
  const showConfirmationDialog = (
    title: string,
    message: string,
    onConfirm: () => void,
  ) => {
    setConfirmDialogTitle(title);
    setConfirmDialogMessage(message);
    setConfirmDialogAction(() => onConfirm);
    setConfirmDialogOpen(true);
  };

  // Prepare deletion by checking for affected regions and showing confirmation if needed
  const handleDeleteRegion = () => {
    if (selectedRegion && !selectedRegion.isPredefined) {
      // Get the user-defined regions without the deleted region
      const userDefinedRegionsAfterDelete = regions.filter(
        (r) => !r.isPredefined && r.id !== selectedRegion.id,
      );

      // Calculate the new regions array after deletion
      const updatedRegions = updateRegionsWithSystemRegions(
        userDefinedRegionsAfterDelete,
        false,
      );

      // Check if the deleted region or any system regions that would be excluded are in use
      const affectedRegions = isRegionInUse ? [selectedRegion.id] : [];

      // Only check for other affected regions if we have a way to determine usage
      if (isRegionInUse) {
        const potentiallyAffectedIds = findAffectedRegions(
          regions,
          updatedRegions,
        );
        affectedRegions.push(...potentiallyAffectedIds);

        // Filter to only include regions that are in use
        const regionsInUse = affectedRegions.filter((id) => isRegionInUse(id));

        if (regionsInUse.length > 0) {
          // Get names of affected regions for the warning message
          const affectedRegionNames = regionsInUse.map((id) => {
            const region = regions.find((r) => r.id === id);
            return region ? region.name : id;
          });

          // Show custom confirmation dialog instead of window.confirm
          const confirmMessage = `Warning: The following regions are currently in use by assignment rules: ${affectedRegionNames.join(", ")}. 
          Deleting this region will remove these values from any assignment rules using them.`;

          setDeletionConfirmMessage(confirmMessage);
          setDeletionConfirmOpen(true);
          return;
        }
      }

      // No confirmation needed, proceed with deletion directly
      confirmDeleteRegion();
    }
  };

  // Actual deletion logic after confirmation
  const confirmDeleteRegion = () => {
    if (selectedRegion && !selectedRegion.isPredefined) {
      // Get the user-defined regions without the deleted region
      const userDefinedRegionsAfterDelete = regions.filter(
        (r) => !r.isPredefined && r.id !== selectedRegion.id,
      );

      // Calculate the new regions array after deletion
      const updatedRegions = updateRegionsWithSystemRegions(
        userDefinedRegionsAfterDelete,
        false,
      );

      // Proceed with deletion
      setRegions(updatedRegions);
      resetForm();
      setDeletionConfirmOpen(false);
    }
  };

  // Save all changes and close modal
  const handleSaveAll = () => {
    // Get user-defined (non-predefined) regions
    const userRegions = regions.filter((r) => !r.isPredefined);

    // Create a fresh array for all system-managed regions (both static and dynamic)
    const allSystemRegions: GeographyRegion[] = [];

    // Add dynamically calculated regions for complete coverage
    const dynamicRegions = calculateDynamicSystemRegions(userRegions);

    // Only include dynamic regions that don't already exist in our system regions
    for (const dynamicRegion of dynamicRegions) {
      // Check if this dynamic region type already exists in our collected system regions
      if (
        !allSystemRegions.some(
          (r) => r.predefinedType === dynamicRegion.predefinedType,
        )
      ) {
        allSystemRegions.push(dynamicRegion);
      }
    }

    // Combine user-defined regions with ALL system regions
    const finalRegions = [...userRegions, ...allSystemRegions];

    // Edge case: Ensure we're not saving an empty set of regions
    if (finalRegions.length === 0) {
      // Add default static system regions if somehow we have none
      const defaultSystemRegions = createTemplateRegions();
      finalRegions.push(...defaultSystemRegions);
    }

    // Create list of all valid region IDs that should be kept in assignment rules
    const validRegionIds = finalRegions.map((region) => region.id);

    // Check for regions that would be affected by this change
    const originalRegions = geographyRegions;

    if (isRegionInUse) {
      // Find regions that are in the original set but not in the final set
      const affectedRegionIds = findAffectedRegions(
        originalRegions,
        finalRegions,
      );

      if (affectedRegionIds.length > 0) {
        // Get names of affected regions for the warning message
        const affectedRegionNames = affectedRegionIds.map((id) => {
          const region = originalRegions.find((r) => r.id === id);
          return region ? region.name : id;
        });

        // Show custom confirmation dialog instead of window.confirm
        const confirmMessage = `Warning: The following regions are currently in use by assignment rules: ${affectedRegionNames.join(", ")}. 
        Saving these changes will remove these values from any assignment rules using them.`;

        // Use the showConfirmationDialog helper function
        showConfirmationDialog(
          "Warning: Regions In Use",
          confirmMessage,
          () => {
            // This will be executed if the user confirms
            // Call onDefinitionsChanged if provided to clean up invalid entries
            if (onDefinitionsChanged) {
              onDefinitionsChanged(validRegionIds);
            }

            // Call onSave with the finalRegions array to update parent component state
            onSave(finalRegions);

            // Close the modal after saving
            onOpenChange(false);
          },
        );

        // Return early - the action will be completed if the user confirms
        return;
      }
    }

    // Call onDefinitionsChanged if provided to clean up invalid entries
    if (onDefinitionsChanged) {
      onDefinitionsChanged(validRegionIds);
    }

    // Call onSave with the finalRegions array to update parent component state
    onSave(finalRegions);

    // Close the modal after saving
    onOpenChange(false);
  };

  // Toggle a country in the selection
  const toggleCountry = (country: (typeof allCountries)[0]) => {
    const newSelected = [...selectedCountries];
    const index = newSelected.indexOf(country.isoCode);

    // Adding a country
    if (index === -1) {
      // Check if we already have US selected with states and are trying to add another country
      if (
        selectedCountries.length === 1 &&
        selectedCountries[0] === "US" &&
        selectedUSStates.length > 0 &&
        country.isoCode !== "US"
      ) {
        // Show custom confirmation dialog instead of window.confirm
        const confirmMessage =
          "Adding another country will clear your US state/territory selections. " +
          "If you want to specify states/territories, you should only select United States as the country.";

        // Use the showConfirmationDialog helper function
        showConfirmationDialog(
          "Warning: Clear State Selections",
          confirmMessage,
          () => {
            // This will be executed if the user confirms
            newSelected.push(country.isoCode);
            setSelectedCountries(newSelected);
            // Clear state selections since we're adding multiple countries
            setSelectedUSStates([]);
          },
        );

        // Return early - action will be completed if user confirms
        return;
      } else {
        newSelected.push(country.isoCode);
      }
    } else {
      newSelected.splice(index, 1);
    }

    setSelectedCountries(newSelected);
  };

  // Toggle a state in the selection
  const toggleState = (state: (typeof allUSStates)[0]) => {
    const newSelected = [...selectedUSStates];
    const index = newSelected.indexOf(state.isoCode);

    if (index === -1) {
      newSelected.push(state.isoCode);
    } else {
      newSelected.splice(index, 1);
    }

    setSelectedUSStates(newSelected);
  };

  // Select all US states
  const selectAllStates = () => {
    setSelectedUSStates(allUSStates.map((state) => state.isoCode));
  };

  // Deselect all US states
  const deselectAllStates = () => {
    setSelectedUSStates([]);
  };

  // Filtered countries based on search
  const filteredCountries = countrySearch
    ? allCountries.filter((country) =>
        country.name.toLowerCase().includes(countrySearch.toLowerCase()),
      )
    : allCountries;

  // Filtered states based on search
  const filteredStates = stateSearch
    ? allUSStates.filter((state) =>
        state.name.toLowerCase().includes(stateSearch.toLowerCase()),
      )
    : allUSStates;

  // Create the footer buttons
  const footerContent = (
    <>
      <Button
        variant="outline"
        onClick={() => onOpenChange(false)}
        data-testid="cancel-button"
      >
        Cancel
      </Button>
      <Button
        onClick={handleSaveAll}
        data-testid="footer-save-all-changes-button"
      >
        Save All Changes
      </Button>
    </>
  );

  // Create header action buttons
  const headerActions = (
    <Button onClick={handleSaveAll} data-testid="save-all-changes-button">
      Save All Changes
    </Button>
  );

  // Helper to get user-defined regions with the in-progress region included
  function getUserDefinedRegionsWithInProgress(
    regions: GeographyRegion[],
    regionName: string,
    selectedCountries: string[],
    selectedUSStates: string[],
    selectedRegion: GeographyRegion | null,
  ): GeographyRegion[] {
    const regionInProgress: GeographyRegion = {
      id: selectedRegion?.id || "in-progress",
      name: regionName.trim() || "In Progress",
      countries: selectedCountries,
      usStates: selectedUSStates.length > 0 ? selectedUSStates : undefined,
      isPredefined: false,
    };

    let userDefinedRegions = regions.filter((r) => !r.isPredefined);
    if (selectedRegion) {
      userDefinedRegions = userDefinedRegions.map((r) =>
        r.id === selectedRegion.id ? regionInProgress : r,
      );
    } else if (regionName.trim() && selectedCountries.length > 0) {
      userDefinedRegions = [...userDefinedRegions, regionInProgress];
    }
    return userDefinedRegions;
  }

  function getPreviewExcludedSystemRegions(
    regions: GeographyRegion[],
    regionName: string,
    selectedCountries: string[],
    selectedUSStates: string[],
    selectedRegion: GeographyRegion | null,
  ): string[] {
    const userDefinedRegions = getUserDefinedRegionsWithInProgress(
      regions,
      regionName,
      selectedCountries,
      selectedUSStates,
      selectedRegion,
    );
    const updatedRegions = [
      ...userDefinedRegions,
      ...regions.filter((r) => r.isPredefined),
    ];
    return calculateExcludedSystemRegions(userDefinedRegions, updatedRegions);
  }

  // Add this useEffect after other hooks
  useEffect(() => {
    setExcludedSystemRegions(
      getPreviewExcludedSystemRegions(
        regions,
        regionName,
        selectedCountries,
        selectedUSStates,
        selectedRegion,
      ),
    );
  }, [
    selectedCountries,
    selectedUSStates,
    regionName,
    selectedRegion,
    regions,
  ]);

  // State for "What are these" modal
  const [systemRegionsInfoOpen, setSystemRegionsInfoOpen] = useState(false);

  return (
    <SlideInPanel
      open={open}
      onOpenChange={onOpenChange}
      title="Manage Geography Regions"
      footerContent={footerContent}
      headerActions={headerActions}
    >
      {/* Description */}
      <p className="text-sm text-muted-foreground mb-6">
        Define and manage geographic regions for lead assignment rules. These
        regions will be available as options when configuring geography-based
        lead assignments.
      </p>

      {/* Display validation error */}
      {validationError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{validationError}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Panel - Region Management */}
        <div className="border rounded-lg p-5 bg-card">
          <h3 className="font-semibold text-lg mb-4">Region Management</h3>

          {/* Quick Add Templates */}
          <div className="mb-5">
            <h4 className="text-sm font-medium mb-3">Quick Add Templates</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {PRECONFIGURED_REGIONS.map((template, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="justify-start overflow-hidden text-ellipsis whitespace-nowrap"
                  onClick={() => handleQuickAddTemplate(template)}
                  data-testid={`quick-add-template-${template.name.toLowerCase().replace(/\s+/g, "-")}`}
                >
                  <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                  {template.name}
                </Button>
              ))}
            </div>
            <Button
              variant="default"
              className="mt-3 w-full flex items-center justify-center gap-1"
              onClick={() => {
                resetForm();
                setValidationError(null);
                // Flash the right panel to draw attention
                const rightPanel = document.querySelector("[data-right-panel]");
                if (rightPanel) {
                  rightPanel.classList.add(
                    "ring-2",
                    "ring-primary",
                    "ring-opacity-50",
                  );
                  setTimeout(() => {
                    rightPanel.classList.remove(
                      "ring-2",
                      "ring-primary",
                      "ring-opacity-50",
                    );
                  }, 1000);
                }
              }}
              data-testid="add-custom-region-button"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Custom Region
            </Button>
          </div>

          {/* User-Defined Regions */}
          <div className="mb-5" data-testid="user-defined-regions">
            <h4 className="text-sm font-medium mb-3">Your Regions</h4>
            <div className="h-[250px] border rounded-md p-3 overflow-auto bg-background">
              {regions.filter((r) => !r.isPredefined).length === 0 ? (
                <div className="text-center text-muted-foreground p-4">
                  No custom regions defined yet. Create your first one or use a
                  template.
                </div>
              ) : (
                <div className="space-y-2">
                  {regions
                    .filter((r) => !r.isPredefined)
                    .map((region) => (
                      <div
                        key={region.id}
                        className={cn(
                          "p-3 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors",
                          {
                            "bg-primary/10 border-primary/30 ring-1 ring-primary/30":
                              selectedRegion?.id === region.id,
                          },
                        )}
                        onClick={() => selectRegionForEdit(region)}
                        data-testid={`region-card-${region.id}`}
                      >
                        <div className="font-medium flex items-center justify-between">
                          <span data-testid={`region-name-${region.id}`}>
                            {region.name}
                          </span>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <Info className="h-4 w-4 text-muted-foreground" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-80 text-sm p-4"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <div className="font-semibold mb-2">
                                Geographic Coverage
                              </div>
                              <div className="text-muted-foreground">
                                {getRegionDescription(region)}
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {region.countries.length === 1 &&
                          region.countries[0] === "US" &&
                          region.usStates &&
                          region.usStates.length > 0
                            ? `US States/Territories (${region.usStates.length} selected)`
                            : `Countries: ${region.countries.length}`}
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>
          </div>

          {/* System-Managed Predefined Regions */}
          <div>
            <div className="flex items-center mb-3">
              <h4 className="text-sm font-medium mr-2">
                System-Managed Regions
              </h4>
              <button
                type="button"
                className="text-xs underline text-muted-foreground hover:text-primary focus:outline-none"
                onClick={() => setSystemRegionsInfoOpen(true)}
                data-testid="system-managed-regions-info-link"
              >
                what are these?
              </button>
            </div>
            <ScrollArea className="h-[300px] border rounded-md p-3 bg-background">
              <div className="space-y-2">
                <div data-testid="included-regions-container">
                  {/* Display predefined system regions that aren't excluded */}
                  {regions
                    .filter(
                      (r) =>
                        r.isPredefined &&
                        !excludedSystemRegions.includes(r.predefinedType || ""),
                    )
                    .map((region) => (
                      <div
                        key={region.id}
                        className="p-3 border rounded-md bg-muted/20 border-muted/30"
                        data-testid={`included-region-${region.predefinedType?.toLowerCase().replace(/_/g, "-")}`}
                      >
                        <div className="font-medium flex items-center justify-between">
                          <span
                            data-testid={`included-region-name-${region.predefinedType?.toLowerCase().replace(/_/g, "-")}`}
                          >
                            {region.name}
                          </span>
                          <div className="flex items-center gap-2">
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                >
                                  <Info className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-80 text-sm p-4">
                                <div className="font-semibold mb-2">
                                  Geographic Coverage
                                </div>
                                <div className="text-muted-foreground">
                                  {getRegionDescription(region)}
                                </div>
                                {region.predefinedType?.includes(
                                  "ALL_OTHER",
                                ) && (
                                  <div className="mt-2 text-xs bg-muted p-2 rounded">
                                    <span className="font-medium">Note:</span>{" "}
                                    This dynamic region automatically adjusts
                                    based on your custom regions to ensure
                                    comprehensive global coverage.
                                  </div>
                                )}
                              </PopoverContent>
                            </Popover>
                            <Badge variant="secondary" className="text-xs">
                              {region.predefinedType?.includes("ALL_OTHER")
                                ? "Dynamic"
                                : "Static"}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {region.predefinedType ===
                            TEMPLATE_REGION_TYPES.USA_ALL_STATES &&
                            "All US states/territories"}
                          {region.predefinedType ===
                            TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES &&
                            "All countries except USA"}
                          {region.predefinedType ===
                            SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES &&
                            "US states not in your custom regions"}
                          {region.predefinedType ===
                            SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES &&
                            "Countries not in your custom regions"}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Right Panel - Region Configuration Form */}
        <div
          className="border rounded-lg p-5 bg-card transition-all duration-300"
          data-right-panel
        >
          <div className="flex items-center gap-3 mb-4">
            <h3 className="font-semibold text-lg">
              {selectedRegion ? "Edit Region" : "Create New Region"}
            </h3>
          </div>

          {helpMessage && (
            <div
              className={cn(
                "p-4 rounded-md mb-5 text-sm border",
                helpMessage.includes("cleared") ||
                  helpMessage.includes("warning")
                  ? "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-300"
                  : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300",
              )}
            >
              {helpMessage.includes("cleared") ||
              helpMessage.includes("warning") ? (
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 flex-shrink-0 text-yellow-500 dark:text-yellow-400" />
                  <span>{helpMessage}</span>
                </div>
              ) : (
                <div className="flex items-start gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 flex-shrink-0 text-blue-500 dark:text-blue-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>{helpMessage}</span>
                </div>
              )}
            </div>
          )}

          <div className="space-y-4">
            {/* Region Name */}
            <div>
              <Label
                htmlFor="region-name"
                className="block text-sm font-medium mb-1"
              >
                Region Name
              </Label>
              <Input
                id="region-name"
                placeholder="e.g., Western Europe"
                value={regionName}
                onChange={(e) => setRegionName(e.target.value)}
                className="max-w-md"
                data-testid="region-name-input"
              />
            </div>

            {/* Country Selection */}
            <div>
              <Label className="block text-sm font-medium mb-1">
                Countries
              </Label>
              <Popover
                open={countrySearchOpen}
                onOpenChange={setCountrySearchOpen}
                modal={true}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={countrySearchOpen}
                    className="w-full justify-between"
                    data-testid="country-select-button"
                  >
                    {selectedCountries.length === 0
                      ? "Select countries..."
                      : `${selectedCountries.length} countries selected`}
                    <svg
                      className="ml-2 h-4 w-4 shrink-0 opacity-50"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[350px] p-4" align="start">
                  <div className="space-y-2">
                    <Input
                      placeholder="Search countries..."
                      value={countrySearch}
                      onChange={(e) => setCountrySearch(e.target.value)}
                      className="mb-2"
                    />
                    <ScrollArea className="h-[300px] w-full border rounded-md mt-1">
                      <div className="space-y-1 p-2">
                        {filteredCountries.map((country) => {
                          const isSelected = selectedCountries.includes(
                            country.isoCode,
                          );
                          return (
                            <div
                              key={country.isoCode}
                              className="flex items-center space-x-2 p-1 hover:bg-muted/50 rounded cursor-pointer"
                              onClick={() => toggleCountry(country)}
                              data-testid={`country-option-${country.isoCode}`}
                            >
                              <div className="flex items-center space-x-2 w-full">
                                <Checkbox
                                  checked={isSelected}
                                  className="pointer-events-none"
                                />
                                <span className="cursor-pointer flex-grow">
                                  {country.name}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Show selected countries as badges */}
              {selectedCountries.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {selectedCountries.map((countryCode) => {
                    const country = allCountries.find(
                      (c) => c.isoCode === countryCode,
                    );
                    return (
                      <Badge
                        key={countryCode}
                        variant="secondary"
                        className="flex items-center"
                      >
                        {country?.name || countryCode}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-1 p-0"
                          onClick={() => {
                            setSelectedCountries(
                              selectedCountries.filter(
                                (c) => c !== countryCode,
                              ),
                            );
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    );
                  })}
                </div>
              )}
            </div>

            {/* US States Selection - Only show if USA is the only selected country */}
            {showUSStates && (
              <div>
                <div className="flex items-center justify-between mb-1">
                  <Label className="block text-sm font-medium">
                    US States/Territories
                  </Label>
                  <div className="flex items-center text-xs gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs"
                      onClick={selectAllStates}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs"
                      onClick={deselectAllStates}
                    >
                      Deselect All
                    </Button>
                  </div>
                </div>

                <Popover
                  open={stateSearchOpen}
                  onOpenChange={setStateSearchOpen}
                  modal={true}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={stateSearchOpen}
                      className="w-full justify-between"
                    >
                      {selectedUSStates.length === 0
                        ? "Select states/territories..."
                        : `${selectedUSStates.length} states/territories selected`}
                      <svg
                        className="ml-2 h-4 w-4 shrink-0 opacity-50"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[350px] p-4" align="start">
                    <div className="space-y-2">
                      <Input
                        placeholder="Search US states/territories..."
                        value={stateSearch}
                        onChange={(e) => setStateSearch(e.target.value)}
                        className="mb-2"
                      />
                      <ScrollArea className="h-[300px] w-full border rounded-md mt-1">
                        <div className="space-y-1 p-2">
                          {filteredStates.map((state) => {
                            const isSelected = selectedUSStates.includes(
                              state.isoCode,
                            );
                            return (
                              <div
                                key={state.isoCode}
                                className="flex items-center space-x-2 p-1 hover:bg-muted/50 rounded cursor-pointer"
                                onClick={() => toggleState(state)}
                              >
                                <div className="flex items-center space-x-2 w-full">
                                  <Checkbox
                                    checked={isSelected}
                                    className="pointer-events-none"
                                  />
                                  <span className="cursor-pointer flex-grow">
                                    {state.name}
                                  </span>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </ScrollArea>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Show selected states as badges */}
                {selectedUSStates.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedUSStates.map((stateCode) => {
                      const state = allUSStates.find(
                        (s) => s.isoCode === stateCode,
                      );
                      return (
                        <Badge
                          key={stateCode}
                          variant="secondary"
                          className="flex items-center"
                        >
                          {state?.name || stateCode}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 ml-1 p-0"
                            onClick={() => {
                              setSelectedUSStates(
                                selectedUSStates.filter((s) => s !== stateCode),
                              );
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      );
                    })}
                  </div>
                )}

                {/* Information for empty state selection */}
                {selectedUSStates.length === 0 && (
                  <p className="text-xs text-muted-foreground mt-1">
                    If no states/territories are selected, this region covers
                    all of USA.
                  </p>
                )}
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-5 border-t mt-5">
              {selectedRegion && (
                <Button
                  variant="destructive"
                  onClick={handleDeleteRegion}
                  data-testid="delete-region-button"
                >
                  Delete Region
                </Button>
              )}
              {selectedRegion && (
                <Button
                  variant="outline"
                  onClick={resetToOriginalRegion}
                  data-testid="reset-form-to-original-button"
                >
                  Reset
                </Button>
              )}
              <Button
                variant="outline"
                onClick={resetForm}
                data-testid="cancel-edit-button"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleSaveRegion();
                  setHelpMessage(null);
                }}
                data-testid="save-region-button"
              >
                {selectedRegion ? "Update Region" : "Add Region"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* System-Managed Regions Info Modal */}
      <ConfirmationDialog
        open={systemRegionsInfoOpen}
        onOpenChange={setSystemRegionsInfoOpen}
        title="About System-Managed Regions"
        description={`System-managed regions are automatically generated to ensure that every possible country 
                    or US state/territory is covered by at least one region.
                     These regions fill in any gaps left by your custom regions, so that lead assignment rules always
                      have complete geographic coverage.\n\n
                     The system manage regions sometimes change when you add or remove regions to ensure
                       ensure there is no duplication or overlap in your lead assignment options.`}
        confirmLabel="Close"
        onConfirm={() => setSystemRegionsInfoOpen(false)}
        hideCancel={true}
      >
        <div className="pt-2">
          {excludedSystemRegions.length > 0 ? (
            <div className="mb-3 mt-4" data-testid="excluded-regions-section">
              <h5 className="text-xs uppercase text-muted-foreground font-medium mb-1">
                Currently Excluded System Regions
              </h5>
              <p className="text-xs text-muted-foreground mb-2">
                These system regions are not included in your configuration
                because your custom regions already cover them:
              </p>
              {excludedSystemRegions.includes(
                TEMPLATE_REGION_TYPES.USA_ALL_STATES,
              ) && (
                <div
                  className="p-3 border border-dashed rounded-md bg-muted/10 border-muted mb-2"
                  data-testid="excluded-region-usa-all-states"
                >
                  <div className="font-medium flex items-center justify-between text-muted-foreground">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-muted-foreground mr-2" />
                      <span>USA (All States)</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Not Included
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 ml-6">
                    Not included because you have defined regions with US
                    states.
                  </div>
                </div>
              )}
              {excludedSystemRegions.includes(
                TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES,
              ) && (
                <div
                  className="p-3 border border-dashed rounded-md bg-muted/10 border-muted mb-2"
                  data-testid="excluded-region-all-non-us-countries"
                >
                  <div className="font-medium flex items-center justify-between text-muted-foreground">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-muted-foreground mr-2" />
                      <span>All Non-US Countries</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Not Included
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 ml-6">
                    Not included because you have defined regions with non-US
                    countries.
                  </div>
                </div>
              )}
              {excludedSystemRegions.includes(
                SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
              ) && (
                <div
                  className="p-3 border border-dashed rounded-md bg-muted/10 border-muted mb-2"
                  data-testid="excluded-region-usa-all-other-states"
                >
                  <div className="font-medium flex items-center justify-between text-muted-foreground">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-muted-foreground mr-2" />
                      <span>All Other US States/Territories</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Not Included
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 ml-6">
                    Not included because either all US states are covered by
                    another region, or no US regions with specific states are
                    defined yet.
                    <br />
                    When you add a region with specific US states, this
                    complementary region will appear automatically.
                  </div>
                </div>
              )}
              {excludedSystemRegions.includes(
                SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
              ) && (
                <div
                  className="p-3 border border-dashed rounded-md bg-muted/10 border-muted mb-2"
                  data-testid="excluded-region-all-other-countries"
                >
                  <div className="font-medium flex items-center justify-between text-muted-foreground">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-muted-foreground mr-2" />
                      <span>All Other Countries/Regions</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Not Included
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 ml-6">
                    Not included because all countries are covered by your
                    defined regions.
                  </div>
                </div>
              )}
            </div>
          ) : (
            <></>
          )}
        </div>
      </ConfirmationDialog>

      {/* Main Confirmation Dialog for deletions and other actions */}
      <ConfirmationDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        title={confirmDialogTitle}
        description={confirmDialogMessage}
        onConfirm={() => {
          confirmDialogAction();
          setConfirmDialogOpen(false);
        }}
      />

      {/* Deletion Confirmation Dialog */}
      <ConfirmationDialog
        open={deletionConfirmOpen}
        onOpenChange={setDeletionConfirmOpen}
        title="Delete Region"
        description={deletionConfirmMessage}
        onConfirm={() => {
          confirmDeleteRegion();
          setDeletionConfirmOpen(false);
        }}
      />
    </SlideInPanel>
  );
}
