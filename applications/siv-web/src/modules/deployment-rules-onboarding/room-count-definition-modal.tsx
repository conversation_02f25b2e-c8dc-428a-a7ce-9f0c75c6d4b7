"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import type { RoomCountRange } from "./types/lead-assignment";
import { ROOM_COUNT_CONDITION_TYPES } from "@/modules/deployment-rules-onboarding/room-count-constants";
import { ConfirmationDialog } from "./components/confirmation-dialog";

interface RoomCountDefinitionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ranges: RoomCountRange[];
  onSave: (ranges: RoomCountRange[]) => void;
  // Optional callback to check if a range is in use
  isRangeInUse?: (rangeId: string) => boolean;
  // Optional callback when definitions change to clean up invalid values
  onDefinitionsChanged?: (validRangeIds: string[]) => void;
}

export default function RoomCountDefinitionModal({
  open,
  onOpenChange,
  ranges,
  onSave,
  isRangeInUse,
  onDefinitionsChanged,
}: RoomCountDefinitionModalProps) {
  const [currentRanges, setCurrentRanges] = useState<RoomCountRange[]>(ranges);
  const [selectedBucket, setSelectedBucket] = useState<RoomCountRange | null>(
    null,
  );
  const [bucketName, setBucketName] = useState("");
  const [conditionType, setConditionType] = useState<
    "less" | "greater" | "range"
  >("less");
  const [minValue, setMinValue] = useState<number | undefined>(undefined);
  const [maxValue, setMaxValue] = useState<number | undefined>(undefined);
  const [validationError, setValidationError] = useState<string | null>(null);

  // State for deletion confirmation dialog
  const [deletionConfirmOpen, setDeletionConfirmOpen] = useState(false);
  const [deletionConfirmMessage, setDeletionConfirmMessage] = useState("");

  const validateRoomCountRange = (): boolean => {
    // Check for name
    if (!bucketName.trim()) {
      setValidationError("Bucket name is required");
      return false;
    }

    // Validate values based on condition type
    if (conditionType === "less" && (maxValue === undefined || maxValue <= 0)) {
      setValidationError("Maximum value must be greater than 0");
      return false;
    }

    if (
      conditionType === "greater" &&
      (minValue === undefined || minValue <= 0)
    ) {
      setValidationError("Minimum value must be greater than 0");
      return false;
    }

    if (conditionType === "range") {
      if (minValue === undefined || minValue <= 0) {
        setValidationError("Minimum value must be greater than 0");
        return false;
      }

      if (maxValue === undefined || maxValue <= minValue) {
        setValidationError("Maximum value must be greater than minimum value");
        return false;
      }
    }

    // Check for overlaps with other buckets
    const currentBucketId = selectedBucket?.id;
    const otherBuckets = currentRanges.filter(
      (bucket) => bucket.id !== currentBucketId,
    );

    for (const bucket of otherBuckets) {
      let hasOverlap = false;

      // Check for overlaps based on condition types
      if (conditionType === "less" && bucket.condition === "less") {
        // Both are "less than" - check if they overlap
        hasOverlap =
          maxValue !== undefined &&
          bucket.maxValue !== undefined &&
          (maxValue === bucket.maxValue ||
            (maxValue > 0 && bucket.maxValue > 0));
      } else if (
        conditionType === "greater" &&
        bucket.condition === "greater"
      ) {
        // Both are "greater than" - check if they overlap
        hasOverlap =
          minValue !== undefined &&
          bucket.minValue !== undefined &&
          (minValue === bucket.minValue ||
            (minValue > 0 && bucket.minValue > 0));
      } else if (conditionType === "range" && bucket.condition === "range") {
        // Both are ranges - check for overlap
        hasOverlap =
          minValue !== undefined &&
          maxValue !== undefined &&
          bucket.minValue !== undefined &&
          bucket.maxValue !== undefined &&
          ((minValue <= bucket.maxValue && maxValue >= bucket.minValue) ||
            (bucket.minValue <= maxValue && bucket.maxValue >= minValue));
      } else if (conditionType === "less" && bucket.condition === "range") {
        // This is "less than" and other is range - check if they overlap
        hasOverlap =
          maxValue !== undefined &&
          bucket.minValue !== undefined &&
          maxValue > bucket.minValue;
      } else if (conditionType === "range" && bucket.condition === "less") {
        // This is range and other is "less than" - check if they overlap
        hasOverlap =
          minValue !== undefined &&
          bucket.maxValue !== undefined &&
          minValue < bucket.maxValue;
      } else if (conditionType === "greater" && bucket.condition === "range") {
        // This is "greater than" and other is range - check if they overlap
        hasOverlap =
          minValue !== undefined &&
          bucket.maxValue !== undefined &&
          minValue < bucket.maxValue;
      } else if (conditionType === "range" && bucket.condition === "greater") {
        // This is range and other is "greater than" - check if they overlap
        hasOverlap =
          maxValue !== undefined &&
          bucket.minValue !== undefined &&
          maxValue > bucket.minValue;
      } else if (conditionType === "less" && bucket.condition === "greater") {
        // This is "less than" and other is "greater than" - check if they overlap
        hasOverlap =
          maxValue !== undefined &&
          bucket.minValue !== undefined &&
          maxValue > bucket.minValue;
      } else if (conditionType === "greater" && bucket.condition === "less") {
        // This is "greater than" and other is "less than" - check if they overlap
        hasOverlap =
          minValue !== undefined &&
          bucket.maxValue !== undefined &&
          minValue < bucket.maxValue;
      }

      if (hasOverlap) {
        setValidationError(
          `Overlaps with "${bucket.name}" bucket. Room count buckets must not overlap.`,
        );
        return false;
      }
    }

    return true;
  };

  // Check for coverage gaps across all buckets
  const checkForCoverageGaps = (): string | null => {
    const allBuckets = [...currentRanges];

    // If we're editing a bucket, add the current state
    if (selectedBucket) {
      const editedBucket: RoomCountRange = {
        id: selectedBucket.id,
        name: bucketName,
        condition: conditionType,
        minValue,
        maxValue,
      };

      // Remove the original and add the edited version
      const bucketIndex = allBuckets.findIndex(
        (b) => b.id === selectedBucket.id,
      );
      if (bucketIndex !== -1) {
        allBuckets.splice(bucketIndex, 1, editedBucket);
      }
    } else if (bucketName) {
      // If creating a new bucket, add it to the list
      allBuckets.push({
        id: crypto.randomUUID(),
        name: bucketName,
        condition: conditionType,
        minValue,
        maxValue,
      });
    }

    // Check for gaps in number line coverage
    if (allBuckets.length === 0) {
      return "No room count buckets defined";
    }

    // Sort buckets by their minimum values (for ranges and greater than)
    // or maximum values (for less than)
    allBuckets.sort((a, b) => {
      const aValue = a.condition === "less" ? a.maxValue || 0 : a.minValue || 0;
      const bValue = b.condition === "less" ? b.maxValue || 0 : b.minValue || 0;
      return aValue - bValue;
    });

    // Check if values from 0 to infinity are covered
    const coveredRanges: [number, number | null][] = [];

    allBuckets.forEach((bucket) => {
      if (bucket.condition === "less" && bucket.maxValue !== undefined) {
        coveredRanges.push([0, bucket.maxValue]);
      } else if (
        bucket.condition === "greater" &&
        bucket.minValue !== undefined
      ) {
        coveredRanges.push([bucket.minValue, null]); // null indicates infinity
      } else if (
        bucket.condition === "range" &&
        bucket.minValue !== undefined &&
        bucket.maxValue !== undefined
      ) {
        coveredRanges.push([bucket.minValue, bucket.maxValue]);
      }
    });

    // Sort by start value
    coveredRanges.sort((a, b) => a[0] - b[0]);

    // Check for gaps
    const gaps: string[] = [];

    // Check if 0 is covered
    if (coveredRanges.length > 0 && coveredRanges[0][0] > 0) {
      gaps.push(`Gap: 0 to ${coveredRanges[0][0]}`);
    }

    // Check for gaps between ranges
    for (let i = 0; i < coveredRanges.length - 1; i++) {
      const currentEnd = coveredRanges[i][1];
      const nextStart = coveredRanges[i + 1][0];

      if (currentEnd !== null && nextStart > currentEnd) {
        gaps.push(`Gap: ${currentEnd} to ${nextStart}`);
      }
    }

    // Check if infinity is covered
    const lastRange = coveredRanges[coveredRanges.length - 1];
    if (lastRange && lastRange[1] !== null) {
      gaps.push(`Gap: ${lastRange[1]} to infinity`);
    }

    return gaps.length > 0 ? `Coverage gaps found: ${gaps.join(", ")}` : null;
  };

  const handleSelectBucket = (bucket: RoomCountRange) => {
    setSelectedBucket(bucket);
    setBucketName(bucket.name);
    setConditionType(bucket.condition);
    setMinValue(bucket.minValue);
    setMaxValue(bucket.maxValue);
    setValidationError(null);
  };

  const handleClearForm = () => {
    setSelectedBucket(null);
    setBucketName("");
    setConditionType("less");
    setMinValue(undefined);
    setMaxValue(undefined);
    setValidationError(null);
  };

  const handleSaveBucket = () => {
    if (!validateRoomCountRange()) return;

    const bucketData: RoomCountRange = {
      id: selectedBucket?.id || crypto.randomUUID(),
      name: bucketName,
      condition: conditionType,
      minValue: conditionType === "less" ? undefined : minValue,
      maxValue: conditionType === "greater" ? undefined : maxValue,
    };

    if (selectedBucket) {
      setCurrentRanges(
        currentRanges.map((r) => (r.id === bucketData.id ? bucketData : r)),
      );
    } else {
      setCurrentRanges([...currentRanges, bucketData]);
    }

    handleClearForm();
  };

  const handleDeleteBucket = () => {
    if (selectedBucket) {
      // Check if this range is in use
      if (isRangeInUse && isRangeInUse(selectedBucket.id)) {
        // Show custom confirmation dialog instead of window.confirm
        const confirmMessage = `Warning: The range "${selectedBucket.name}" is currently in use by assignment rules. 
        Deleting this range will remove it from any assignment rules using it.`;

        setDeletionConfirmMessage(confirmMessage);
        setDeletionConfirmOpen(true);
        return;
      }

      // If the range is not in use, delete it without confirmation
      confirmDeleteBucket();
    }
  };

  // Actual deletion logic after confirmation
  const confirmDeleteBucket = () => {
    if (selectedBucket) {
      setCurrentRanges(currentRanges.filter((r) => r.id !== selectedBucket.id));
      handleClearForm();
      setDeletionConfirmOpen(false);
    }
  };

  const handleSaveAll = () => {
    // Create a list of all valid range IDs
    const validRangeIds = currentRanges.map((range) => range.id);

    // Call onDefinitionsChanged if provided to clean up invalid entries
    if (onDefinitionsChanged) {
      onDefinitionsChanged(validRangeIds);
    }

    onSave(currentRanges);
    onOpenChange(false);
  };

  const coverageGap = checkForCoverageGaps();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[800px]"
        aria-describedby="room-count-definition-desc"
      >
        <DialogHeader>
          <DialogTitle>Define Room Count Buckets</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p
            className="text-sm text-muted-foreground mb-4"
            id="room-count-definition-desc"
          >
            Define room count buckets for lead assignment (e.g., small events,
            large events)
          </p>

          {/* Display coverage warning */}
          {coverageGap && (
            <Alert className="mb-4">
              <AlertTitle>Coverage Warning</AlertTitle>
              <AlertDescription>{coverageGap}</AlertDescription>
            </Alert>
          )}

          {/* Display validation error */}
          {validationError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left side: Current buckets */}
            <div>
              <h3 className="font-medium mb-3">Current Room Count Buckets</h3>

              {currentRanges.length === 0 ? (
                <div className="border rounded-md p-4 text-center text-muted-foreground">
                  No room count buckets defined yet. Create your first one.
                </div>
              ) : (
                <div className="space-y-2">
                  {currentRanges.map((bucket) => (
                    <div
                      key={bucket.id}
                      className={`p-3 border rounded-md cursor-pointer hover:bg-muted/50 ${
                        selectedBucket?.id === bucket.id
                          ? "bg-primary/10 border-primary/30"
                          : ""
                      }`}
                      onClick={() => handleSelectBucket(bucket)}
                    >
                      <div className="font-medium">{bucket.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {bucket.condition === "less" &&
                          `Less than ${bucket.maxValue} rooms`}
                        {bucket.condition === "greater" &&
                          `Greater than ${bucket.minValue} rooms`}
                        {bucket.condition === "range" &&
                          `${bucket.minValue} to ${bucket.maxValue} rooms`}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Right side: Edit form */}
            <div className="border rounded-md p-4">
              <div className="flex items-center gap-2 mb-3">
                <h3 className="font-medium">
                  {selectedBucket ? "Edit Bucket" : "New Bucket"}
                </h3>
              </div>

              <div className="space-y-4">
                <div>
                  <Label
                    htmlFor="bucket-name"
                    className="block text-sm font-medium mb-1"
                  >
                    Bucket Name
                  </Label>
                  <Input
                    id="bucket-name"
                    data-testid="bucket-name-input"
                    placeholder="e.g., Small Event"
                    value={bucketName}
                    onChange={(e) => setBucketName(e.target.value)}
                  />
                </div>

                <div>
                  <Label
                    htmlFor="condition-type"
                    className="block text-sm font-medium mb-1"
                  >
                    Condition Type
                  </Label>
                  <Select
                    value={conditionType}
                    onValueChange={(value) => setConditionType(value as any)}
                  >
                    <SelectTrigger
                      data-testid="condition-type"
                      id="condition-type"
                    >
                      <SelectValue placeholder="Select condition type" />
                    </SelectTrigger>
                    <SelectContent>
                      {ROOM_COUNT_CONDITION_TYPES.map((type) => (
                        <SelectItem
                          data-testid={`${type.value}`}
                          key={type.value}
                          value={type.value}
                        >
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {(conditionType === "greater" || conditionType === "range") && (
                  <div>
                    <Label
                      htmlFor="min-value"
                      className="block text-sm font-medium mb-1"
                    >
                      Minimum Value
                    </Label>
                    <Input
                      id="min-value"
                      data-testid="min-value-input"
                      type="number"
                      min="0"
                      value={minValue !== undefined ? minValue : ""}
                      onChange={(e) =>
                        setMinValue(
                          e.target.value
                            ? Number.parseInt(e.target.value)
                            : undefined,
                        )
                      }
                      placeholder="e.g., 100"
                    />
                  </div>
                )}

                {(conditionType === "less" || conditionType === "range") && (
                  <div>
                    <Label
                      htmlFor="max-value"
                      className="block text-sm font-medium mb-1"
                    >
                      Maximum Value
                    </Label>
                    <Input
                      id="max-value"
                      data-testid="max-value-input"
                      type="number"
                      min={
                        conditionType === "range" && minValue ? minValue + 1 : 1
                      }
                      value={maxValue !== undefined ? maxValue : ""}
                      onChange={(e) =>
                        setMaxValue(
                          e.target.value
                            ? Number.parseInt(e.target.value)
                            : undefined,
                        )
                      }
                      placeholder="e.g., 500"
                    />
                  </div>
                )}

                <div className="flex justify-end space-x-2 pt-4">
                  {selectedBucket && (
                    <Button variant="destructive" onClick={handleDeleteBucket}>
                      Delete
                    </Button>
                  )}
                  <Button variant="outline" onClick={handleClearForm}>
                    Cancel
                  </Button>
                  <Button
                    data-testid="save-bucket"
                    onClick={handleSaveBucket}
                    disabled={!bucketName.trim()}
                  >
                    {selectedBucket ? "Update" : "Save"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            data-testid="save-all-roomcount-buckets"
            onClick={handleSaveAll}
          >
            Save All Buckets
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog for Bucket Deletion */}
      <ConfirmationDialog
        open={deletionConfirmOpen}
        onOpenChange={setDeletionConfirmOpen}
        title="Delete Bucket"
        description={deletionConfirmMessage}
        confirmLabel="Delete"
        onConfirm={confirmDeleteBucket}
        variant="destructive"
      />
    </Dialog>
  );
}
