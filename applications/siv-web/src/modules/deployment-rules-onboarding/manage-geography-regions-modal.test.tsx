import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import ManageGeographyRegionsModal from "./manage-geography-regions-modal";
import { SYSTEM_REGION_TYPES } from "./utils/geography-region-utils";
import type { GeographyRegion } from "./types/lead-assignment";

// Mock confirmation dialog for testing
// We don't need to mock window.confirm anymore since we use custom dialog now

// Mock crypto.randomUUID
vi.mock("crypto", () => ({
  randomUUID: () => "mocked-uuid",
}));

describe("ManageGeographyRegionsModal", () => {
  console.log("Starting ManageGeographyRegionsModal tests...");

  const mockOnOpenChange = vi.fn();
  const mockOnSave = vi.fn();
  const mockIsRegionInUse = vi.fn();

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
    geographyRegions: [] as GeographyRegion[],
    onSave: mockOnSave,
  };

  // Reset all mocks before each test
  beforeEach(() => {
    console.log("Running a new test...");
    mockOnOpenChange.mockReset();
    mockOnSave.mockReset();
    mockIsRegionInUse.mockReset();
  });

  afterEach(() => {
    console.log("Test completed");
  });

  it("should have templates available for USA All States and All Non-US Countries", async () => {
    // Render with no regions
    render(<ManageGeographyRegionsModal {...defaultProps} />);

    // Verify USA All States template is directly available
    const usaTemplate = screen.getByTestId(
      "quick-add-template-usa-(all-states)",
    );
    expect(usaTemplate).toBeInTheDocument();

    // Verify All Non-US Countries template is available
    const nonUsTemplate = screen.getByTestId(
      "quick-add-template-all-non-us-countries",
    );
    expect(nonUsTemplate).toBeInTheDocument();
  });

  it("should immediately show 'All Other US States/Territories' when adding a region with specific US states", async () => {
    // Setup user event
    const user = userEvent.setup({ delay: 50 }); // Add a small delay to make interactions more reliable

    // Render the modal with no regions
    render(<ManageGeographyRegionsModal {...defaultProps} />);

    // Make sure the modal is fully rendered
    await waitFor(() =>
      expect(screen.getByTestId("slide-in-panel")).toBeInTheDocument(),
    );

    // Initially, "All Other US States/Territories" should not be in the included regions
    expect(
      screen.queryByTestId("included-region-usa-all-other-states"),
    ).not.toBeInTheDocument();

    // Find the US - Midwest button directly by its test ID, as it's set in the component on line 819
    const midwestButton = screen.getByTestId("quick-add-template-us---midwest");
    expect(midwestButton).toBeInTheDocument();

    // Click the Midwest button to add the template region
    await user.click(midwestButton);

    // Wait for the UI to update
    await waitFor(
      () => {
        expect(
          screen.getByTestId("included-region-usa-all-other-states"),
        ).toBeInTheDocument();
      },
      { timeout: 5000 },
    );

    // Also verify the name is correct
    const nameElement = screen.getByTestId(
      "included-region-name-usa-all-other-states",
    );
    expect(nameElement).toHaveTextContent("All Other US States/Territories");
  });

  // Tests for new warning functionality with isRegionInUse
  describe("Region in use warnings", () => {
    // Mock regions for testing
    const mockRegions: GeographyRegion[] = [
      {
        id: "region1",
        name: "North America",
        countries: ["US", "CA"],
        isPredefined: false,
      },
      {
        id: "region2",
        name: "Europe",
        countries: ["GB", "FR", "DE"],
        isPredefined: false,
      },
      {
        id: "system-region1",
        name: "All Other Countries/Regions",
        countries: [],
        isPredefined: true,
        isAllOthers: true,
        predefinedType: SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
      },
    ];

    it("should warn when deleting a region that is in use", async () => {
      // Mock isRegionInUse to return that region1 is in use
      mockIsRegionInUse.mockImplementation(
        (regionId: string) => regionId === "region1",
      );

      render(
        <ManageGeographyRegionsModal
          {...defaultProps}
          geographyRegions={mockRegions}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Select the first region (North America) which is in use
      // Using the data-testid attribute to find the region card
      const regionCard = screen.getByTestId("region-card-region1");
      fireEvent.click(regionCard);

      // Click delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Check if confirmation dialog was shown with warning message
      await waitFor(() => {
        // Check for dialog using data-testid
        const confirmationDialog = screen.getByTestId("confirmation-dialog");
        expect(confirmationDialog).toBeInTheDocument();

        // Check for warning message content
        const dialogMessage = screen.getByText((content) => {
          return (
            content.includes("Warning") && content.includes("North America")
          );
        });
        expect(dialogMessage).toBeInTheDocument();
      });
    });

    it("doesn't warn when deleting a region that is not in use", async () => {
      // Mock isRegionInUse to return that only region1 is in use (not region2)
      mockIsRegionInUse.mockImplementation(
        (regionId: string) => regionId === "region1",
      );

      render(
        <ManageGeographyRegionsModal
          {...defaultProps}
          geographyRegions={mockRegions}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Select the second region (Europe) which is not in use
      const regionCard = screen.getByTestId("region-card-region2");
      fireEvent.click(regionCard);

      // Click delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Confirmation dialog should not be shown for regions not in use
      // Let's verify that by checking if the dialog isn't in the document
      const confirmationDialog = screen.queryByTestId("confirmation-dialog");
      expect(confirmationDialog).not.toBeInTheDocument();
    });

    it("warns when deleting a region that would affect regions in use", async () => {
      // Mock isRegionInUse to indicate a region is in use
      mockIsRegionInUse.mockImplementation(
        (regionId: string) => regionId === "region1",
      );

      // Start with full set of regions
      render(
        <ManageGeographyRegionsModal
          {...defaultProps}
          geographyRegions={mockRegions}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Select region1 (North America) which is in use
      const regionCard = screen.getByTestId("region-card-region1");
      fireEvent.click(regionCard);

      // Click delete button to delete it
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Check if confirmation dialog was shown with warning message
      await waitFor(() => {
        // Check for dialog using data-testid
        const confirmationDialog = screen.getByTestId("confirmation-dialog");
        expect(confirmationDialog).toBeInTheDocument();

        // Check for warning message content
        const dialogMessage = screen.getByText((content) => {
          return content.includes("Warning");
        });
        expect(dialogMessage).toBeInTheDocument();
      });
    });

    it("user declining the warning cancels the save operation", async () => {
      // Mock isRegionInUse to indicate a region is in use
      mockIsRegionInUse.mockImplementation(
        (regionId: string) => regionId === "region1",
      );

      render(
        <ManageGeographyRegionsModal
          {...defaultProps}
          geographyRegions={mockRegions}
          isRegionInUse={mockIsRegionInUse}
          onSave={mockOnSave}
        />,
      );

      // Select the first region (North America) which is in use
      // Using the data-testid attribute to find the region card
      const regionCard = screen.getByTestId("region-card-region1");
      fireEvent.click(regionCard);

      // Click delete button
      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Check confirmation dialog appears
      await waitFor(() => {
        const confirmationDialog = screen.getByTestId("confirmation-dialog");
        expect(confirmationDialog).toBeInTheDocument();
      });

      // Click cancel button in the dialog - use a more specific approach for the confirmation dialog
      // Find the alert dialog that contains our confirmation
      const alertDialog = screen.getByRole("alertdialog");
      // Find the cancel button within that dialog
      const cancelButton = within(alertDialog).getByText("Cancel");
      fireEvent.click(cancelButton);

      // Since user declined, onSave should not have been called
      expect(mockOnSave).not.toHaveBeenCalled();

      // Dialog should be closed
      await waitFor(() => {
        const dialogTitle = screen.queryByRole("heading", {
          name: "Delete Region",
        });
        expect(dialogTitle).not.toBeInTheDocument();
      });
    });

    it("properly calls isRegionInUse with correct region IDs", async () => {
      // Mock isRegionInUse
      mockIsRegionInUse.mockImplementation((regionId: string) => false);

      render(
        <ManageGeographyRegionsModal
          {...defaultProps}
          geographyRegions={mockRegions}
          isRegionInUse={mockIsRegionInUse}
        />,
      );

      // Select a region and delete it
      const regionCard = screen.getByTestId("region-card-region1");
      fireEvent.click(regionCard);

      const deleteButton = screen.getByTestId("delete-region-button");
      fireEvent.click(deleteButton);

      // Check if isRegionInUse was called with the correct ID
      expect(mockIsRegionInUse).toHaveBeenCalledWith("region1");
    });
  });

  it("should reset form fields to original region values when Reset is clicked", async () => {
    // Setup initial region
    const initialRegion: GeographyRegion = {
      id: "region-reset-test",
      name: "Test Region",
      countries: ["US"],
      usStates: ["CA", "NY"],
      isPredefined: false,
    };
    render(
      <ManageGeographyRegionsModal
        {...defaultProps}
        geographyRegions={[initialRegion]}
      />,
    );
    // Select the region for editing
    const regionCard = screen.getByTestId("region-card-region-reset-test");
    fireEvent.click(regionCard);
    // Change the region name and countries
    const nameInput = screen.getByTestId("region-name-input");
    fireEvent.change(nameInput, { target: { value: "Changed Name" } });
    // Remove US from selected countries by clicking the badge's X button
    const usBadge = screen.getByText("United States");
    const removeBtn = usBadge.parentElement?.querySelector("button");
    if (removeBtn) {
      fireEvent.click(removeBtn);
    }
    // Click Reset
    const resetBtn = screen.getByTestId("reset-form-to-original-button");
    fireEvent.click(resetBtn);
    // The form fields should revert to the original region values
    expect(screen.getByTestId("region-name-input")).toHaveValue("Test Region");
    // US should be selected again as a badge
    expect(screen.getByText("United States")).toBeInTheDocument();
    // US states should be restored as badges
    expect(screen.getByText("California")).toBeInTheDocument();
    expect(screen.getByText("New York")).toBeInTheDocument();
  });

  it("should show Cancel button with correct test id and label", async () => {
    const initialRegion: GeographyRegion = {
      id: "region-cancel-test",
      name: "Cancel Test Region",
      countries: ["US"],
      isPredefined: false,
    };
    render(
      <ManageGeographyRegionsModal
        {...defaultProps}
        geographyRegions={[initialRegion]}
      />,
    );
    // Select the region for editing
    const regionCard = screen.getByTestId("region-card-region-cancel-test");
    fireEvent.click(regionCard);
    // The Cancel button should be present with the new test id and label
    const cancelBtn = screen.getByTestId("cancel-edit-button");
    expect(cancelBtn).toBeInTheDocument();
    expect(cancelBtn).toHaveTextContent("Cancel");
  });
});
