"use client";

import { Settings } from "lucide-react";
import { TableH<PERSON>, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { CriteriaTypeString } from "../types/lead-assignment";
import { getCriteriaDisplayName } from "../utils/criteria-helpers";

interface TableHeaderProps {
  /**
   * Active criteria types to display as columns
   */
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;

  /**
   * Called when the Geography settings button is clicked
   */
  onConfigureGeography?: () => void;

  /**
   * Called when the Room Count settings button is clicked
   */
  onConfigureRoomCount?: () => void;
}

/**
 * Header row for the assignment table
 */
export function AssignmentTableHeader({
  activeCriteria,
  onConfigureGeography,
  onConfigureRoomCount,
}: TableHeaderProps) {
  // Get active criteria types
  const activeCriteriaTypes = Object.keys(activeCriteria)
    .filter((key) => activeCriteria[key as CriteriaTypeString])
    .map((key) => key as CriteriaTypeString);

  /**
   * Returns true if this criteria type has a settings button
   */
  const hasSettingsButton = (criteriaType: CriteriaTypeString): boolean => {
    return criteriaType === "geography" || criteriaType === "roomCount";
  };

  /**
   * Handles a click on the settings button
   */
  const handleSettingsClick = (
    e: React.MouseEvent,
    criteriaType: CriteriaTypeString,
  ) => {
    e.stopPropagation();

    if (criteriaType === "geography" && onConfigureGeography) {
      onConfigureGeography();
    } else if (criteriaType === "roomCount" && onConfigureRoomCount) {
      onConfigureRoomCount();
    }
  };

  return (
    <TableHeader>
      <TableRow>
        <TableHead className="w-[250px]">Name</TableHead>

        {activeCriteriaTypes.map((criteriaType) => (
          <TableHead key={criteriaType} data-testid={`${criteriaType}-header`}>
            <div className="flex items-center gap-1">
              {getCriteriaDisplayName(criteriaType)}

              {hasSettingsButton(criteriaType) && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 ml-1"
                        onClick={(e) => handleSettingsClick(e, criteriaType)}
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Configure {getCriteriaDisplayName(criteriaType)}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </TableHead>
        ))}

        <TableHead className="w-[100px]">Actions</TableHead>
      </TableRow>
    </TableHeader>
  );
}
