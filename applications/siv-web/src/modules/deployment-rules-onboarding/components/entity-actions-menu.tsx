"use client";

import { Ch<PERSON><PERSON>Down, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import type { Individual, Team, Exception } from "../types/lead-assignment";

interface EntityActionsMenuProps {
  /**
   * The entity (Team or Individual) this menu is for
   */
  entity: Team | Individual;

  /**
   * The exceptions for this entity
   */
  exceptions: Exception[];

  /**
   * Called when the exception menu item is clicked
   */
  onExceptionClick: () => void;

  /**
   * Called when the delete menu item is clicked
   */
  onDelete: () => void;
}

/**
 * Dropdown menu for entity actions including exceptions and delete
 */
export function EntityActionsMenu({
  entity,
  exceptions = [],
  onExceptionClick,
  onDelete,
}: EntityActionsMenuProps) {
  const exceptionCount = exceptions.length;
  const hasExceptions = exceptionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-24 relative"
          aria-label={`Actions for ${entity.name}`}
          data-testid="entity-actions-trigger"
        >
          Actions
          <ChevronDown className="h-4 w-4" />
          {hasExceptions && (
            <Badge
              variant="secondary"
              className="absolute -top-1 -right-1 h-5 min-w-[20px] px-1 text-xs"
            >
              {exceptionCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={onExceptionClick}
          data-testid="exceptions-menu-item"
        >
          {hasExceptions
            ? `Manage Exceptions (${exceptionCount})`
            : "Add Exception"}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={onDelete}
          className="text-destructive focus:text-destructive"
          data-testid="delete-menu-item"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
