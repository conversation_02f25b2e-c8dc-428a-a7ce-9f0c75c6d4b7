import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import React from "react";

export interface CriteriaCheckboxProps {
  id: string;
  label: string;
  description: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  // Additional props passed to the container div (like data-testid)
  "data-testid"?: string;
}

export function CriteriaCheckbox({
  id,
  label,
  description,
  checked,
  onCheckedChange,
  ...props
}: CriteriaCheckboxProps & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className="flex items-start space-x-2" {...props}>
      <Checkbox
        id={id}
        checked={checked}
        onCheckedChange={onCheckedChange}
        data-testid={`${id}-checkbox`}
      />
      <div className="grid gap-1.5 leading-none">
        <Label htmlFor={id} className="font-medium">
          {label}
        </Label>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  );
}
