"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Info } from "lucide-react";
import type {
  Individual,
  Team,
  CriteriaTypeString,
  GeographyRegion,
  RoomCountRange,
} from "../types/lead-assignment";

export interface CriteriaDetail {
  type: CriteriaTypeString;
  rule1Values: string[];
  rule2Values: string[];
  isOverlapping: boolean;
  isExceptedOut?: boolean;
  relevantExceptions?: {
    entityId: string;
    exceptionIds: string[];
    explanation: string;
  }[];
  explanation?: string;
}

interface SingleOverlapDetailDisplayProps {
  entity1: Individual | Team;
  entity2: Individual | Team;
  overlapDetails: CriteriaDetail[];
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;
  geographyRegions?: GeographyRegion[];
  roomCountRanges?: RoomCountRange[];
}

export function SingleOverlapDetailDisplay({
  entity1,
  entity2,
  overlapDeta<PERSON>,
  activeCriteria,
  geographyRegions = [],
  roomCountRanges = [],
}: SingleOverlapDetailDisplayProps) {
  const getCriteriaLabel = (criteriaType: string) => {
    switch (criteriaType) {
      case "geography":
        return "Geography";
      case "roomCount":
        return "Room Count";
      case "eventype":
        return "Event Type";
      case "eventType":
        return "Event Type";
      case "industry":
        return "Industry";
      case "eventNeeds":
        return "Event Needs";
      case "dayOfMonth":
        return "Day of Month";
      default:
        return criteriaType;
    }
  };

  const formatValue = (criteriaType: string, value: string) => {
    if (criteriaType === "geography" && geographyRegions.length > 0) {
      const region = geographyRegions.find((r) => r.id === value);
      return region ? region.name : value;
    }

    if (criteriaType === "roomCount" && roomCountRanges.length > 0) {
      const range = roomCountRanges.find((r) => r.id === value);
      return range ? range.name : value;
    }

    return value;
  };

  const getOverlapExplanation = (
    criteriaType: string,
    rule1Values: string[],
    rule2Values: string[],
  ) => {
    // If either rule has "Any" values (empty array), explanation is simple
    if (rule1Values.length === 0 && rule2Values.length === 0) {
      return `Both rules accept any ${getCriteriaLabel(criteriaType).toLowerCase()}.`;
    }

    if (rule1Values.length === 0) {
      return `First rule accepts any ${getCriteriaLabel(criteriaType).toLowerCase()}, including the values from the second rule.`;
    }

    if (rule2Values.length === 0) {
      return `Second rule accepts any ${getCriteriaLabel(criteriaType).toLowerCase()}, including the values from the first rule.`;
    }

    // Find common values
    const commonValues = rule1Values.filter((value) =>
      rule2Values.includes(value),
    );
    if (commonValues.length > 0) {
      const formattedValues = commonValues
        .map((v) => formatValue(criteriaType, v))
        .join(", ");
      return `Both rules share these values: ${formattedValues}`;
    }

    return "There's no overlap for this criteria, but other criteria allow the rules to overlap.";
  };

  return (
    <div className="space-y-4 pt-2" data-testid="single-overlap-detail-display">
      {Object.keys(activeCriteria)
        .filter((key) => activeCriteria[key as CriteriaTypeString])
        .map((criteriaType, i) => {
          const detail = overlapDetails.find((d) => d.type === criteriaType);

          // Skip details that aren't overlapping or are excepted out
          if (!detail || !detail.isOverlapping || detail.isExceptedOut)
            return null;

          return (
            <Card
              key={i}
              data-testid={`overlap-detail-${detail.type}`}
              className={
                detail.isOverlapping
                  ? "border-destructive/20 bg-destructive/5"
                  : ""
              }
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  {getCriteriaLabel(criteriaType)}
                  {detail.isOverlapping && (
                    <Badge variant="destructive" className="ml-2">
                      Overlaps
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">
                        {entity1.name}
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {detail.rule1Values.length === 0 ? (
                          <span className="text-sm text-muted-foreground">
                            Any
                          </span>
                        ) : (
                          detail.rule1Values.map((value, i) => (
                            <Badge
                              key={i}
                              variant="outline"
                              data-testid={`entity1-${criteriaType}-value-${i}`}
                            >
                              {formatValue(criteriaType, value)}
                            </Badge>
                          ))
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">
                        {entity2.name}
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {detail.rule2Values.length === 0 ? (
                          <span className="text-sm text-muted-foreground">
                            Any
                          </span>
                        ) : (
                          detail.rule2Values.map((value, i) => (
                            <Badge
                              key={i}
                              variant="outline"
                              data-testid={`entity2-${criteriaType}-value-${i}`}
                            >
                              {formatValue(criteriaType, value)}
                            </Badge>
                          ))
                        )}
                      </div>
                    </div>
                  </div>

                  {detail.isOverlapping && (
                    <div className="pt-2">
                      <div
                        className="flex items-start gap-2 p-2 border rounded-md border-destructive/10 bg-destructive/5"
                        data-testid={`overlap-explanation-box-${criteriaType}`}
                      >
                        <Info className="h-4 w-4 mt-1 flex-shrink-0 text-destructive" />
                        <div className="text-sm">
                          <span data-testid="overlap-explanation">
                            {detail.explanation ||
                              getOverlapExplanation(
                                criteriaType,
                                detail.rule1Values,
                                detail.rule2Values,
                              )}
                          </span>

                          {detail.relevantExceptions &&
                            detail.relevantExceptions.length > 0 && (
                              <div className="mt-2">
                                <div className="font-medium mb-1">
                                  Relevant exceptions:
                                </div>
                                <ul className="list-disc pl-5 space-y-1">
                                  {detail.relevantExceptions.map((exc, i) => (
                                    <li
                                      key={i}
                                      className="text-xs"
                                      data-testid={`exception-${i}`}
                                    >
                                      {exc.explanation}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
    </div>
  );
}
