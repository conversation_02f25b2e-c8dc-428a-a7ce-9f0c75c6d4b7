"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>ci<PERSON>, Plus } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Too<PERSON>ipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { CriteriaValueDisplay } from "./criteria-value-display";
import type { CriteriaTypeString, Team } from "../types/lead-assignment";
import type { EntityWithCriteria } from "../utils/criteria-helpers";
import * as CriteriaService from "../utils/criteria-helpers";
import { toEntityWithCriteria } from "../utils/criteria-helpers";

export interface CriteriaCellProps {
  /**
   * The entity (individual or team) that owns this criteria
   */
  entity: EntityWithCriteria;

  /**
   * The type of criteria being displayed
   */
  criteriaType: CriteriaTypeString;

  /**
   * Optional geographic regions for display name resolution
   */
  geographyRegions?: any[];

  /**
   * Optional room count ranges for display name resolution
   */
  roomCountRanges?: any[];

  /**
   * Called when the edit button is clicked
   */
  onEdit: () => void;

  /**
   * If true, shows this criterion as inherited from a team
   */
  isTeamInherited?: boolean;

  /**
   * The team this criterion is inherited from (if isTeamInherited is true)
   */
  team?: Team;

  /**
   * Whether this entity is a team (vs individual)
   */
  isTeam?: boolean;

  /**
   * Data test ID for targeting in tests
   */
  "data-testid"?: string;
}

/**
 * A reusable cell for displaying criteria values with edit functionality.
 * Handles both regular criteria display and team inheritance scenarios.
 */
export function CriteriaCell({
  entity,
  criteriaType,
  geographyRegions = [],
  roomCountRanges = [],
  onEdit,
  isTeamInherited = false,
  team,
  isTeam = false,
  "data-testid": dataTestId,
}: CriteriaCellProps) {
  // Get the criterion directly to check if it's "Any" or null
  const criterion = CriteriaService.getCriterion(entity, criteriaType);
  const isAnyCriterion = criterion?.type === "Any";
  const isNull = criterion === null;

  // Get the values for this criteria type
  const values = CriteriaService.getCriteriaValues(entity, criteriaType);
  const isEmpty = values.length === 0 || isNull;

  // For inherited team criteria - only show if team has a non-null criterion
  if (isTeamInherited && team) {
    const teamCriterion = CriteriaService.getCriterion(
      toEntityWithCriteria(team),
      criteriaType,
    );
    // If team has non-null criterion, show as inherited
    if (teamCriterion !== null) {
      const teamIsAnyCriterion = teamCriterion?.type === "Any";
      const teamValues = CriteriaService.getCriteriaValues(
        toEntityWithCriteria(team),
        criteriaType,
      );

      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger
              className="text-muted-foreground italic w-full text-left"
              data-testid={dataTestId}
            >
              Inherited from {team.name}
            </TooltipTrigger>
            <TooltipContent>
              <p>This criteria is defined at the team level</p>
              <div className="mt-1">
                <CriteriaValueDisplay
                  criteriaType={criteriaType}
                  values={teamValues}
                  geographyRegions={geographyRegions}
                  roomCountRanges={roomCountRanges}
                  className="text-xs"
                  isAnyCriterion={teamIsAnyCriterion}
                />
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
  }

  // Determine display text and styling for null criteria
  let emptyText = "Any";
  let cellClassName = "";

  if (isNull) {
    if (isTeam) {
      emptyText = "No team rule";
    } else {
      emptyText = "Not Set";
      cellClassName = "bg-yellow-50 border border-yellow-200 rounded px-2";
    }
  }

  // For regular criteria
  return (
    <div
      className={`flex items-center justify-between ${cellClassName}`}
      data-testid={dataTestId}
    >
      <CriteriaValueDisplay
        criteriaType={criteriaType}
        values={values}
        geographyRegions={geographyRegions}
        roomCountRanges={roomCountRanges}
        emptyText={emptyText}
        isAnyCriterion={isAnyCriterion}
        isBlank={isNull}
      />

      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        onClick={onEdit}
        data-testid={`${dataTestId}-edit-button`}
      >
        {isEmpty ? (
          <Plus className="h-4 w-4" />
        ) : (
          <Pencil className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
