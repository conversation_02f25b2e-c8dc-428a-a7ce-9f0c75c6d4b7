"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SlideInPanel } from "./slide-in-panel";
import { AlertCircle } from "lucide-react";
import type {
  Individual,
  Team,
  CriteriaTypeString,
} from "../types/lead-assignment";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  SingleOverlapDetailDisplay,
  type CriteriaDetail,
} from "./single-overlap-detail-display";

interface OverlapDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entity: Individual | Team;
  overlappingEntities: Array<{
    entity: Individual | Team;
    overlapDetails: CriteriaDetail[];
  }>;
  geographyRegions?: any[];
  roomCountRanges?: any[];
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;
}

export function OverlapDetailsPanel({
  open,
  onOpenChange,
  entity,
  overlappingEntities,
  geographyRegions = [],
  roomCountRanges = [],
  activeCriteria,
}: OverlapDetailsProps) {
  if (!entity) return null;

  const isTeam = !("title" in entity);
  const entityType = isTeam ? "team" : "individual";
  const entityName = entity.name;

  return (
    <SlideInPanel
      open={open}
      onOpenChange={onOpenChange}
      title={`Overlapping Rules for ${entityName}`}
      data-testid="overlap-details-panel"
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Rule Overlap Details
            </CardTitle>
            <CardDescription>
              {`This ${entityType} has overlapping rules with ${overlappingEntities.length} other ${entityType}${overlappingEntities.length !== 1 ? "s" : ""}.`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Overlapping rules mean that when a lead matches both rules, the
              system won't know which {entityType} should handle it. This can
              lead to confusion or inconsistent lead assignment.
            </p>
          </CardContent>
        </Card>

        <div className="space-y-4 pt-4">
          <h3 className="text-lg font-medium mb-2">
            Detailed Overlap Analysis
          </h3>

          <Card className="mb-4">
            <CardContent className="pt-4">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className="bg-destructive/10 w-6 h-6 flex items-center justify-center p-1"
                  >
                    {overlappingEntities.length}
                  </Badge>
                  <span>Overlaps that need to be resolved</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Accordion type="single" collapsible className="w-full">
            {overlappingEntities.map((item, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger
                  data-index={index}
                  data-testid={`overlap-entity-${index}`}
                >
                  <div className="flex items-center gap-2">
                    <span>{item.entity.name}</span>
                    <Badge variant="outline" className="ml-2 bg-destructive/10">
                      {
                        item.overlapDetails.filter(
                          (d) => d.isOverlapping && !d.isExceptedOut,
                        ).length
                      }{" "}
                      overlapping criteria
                    </Badge>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div
                    data-testid={`entity-${index}-overlap-detail-display-contents`}
                  >
                    <SingleOverlapDetailDisplay
                      entity1={entity}
                      entity2={item.entity}
                      overlapDetails={item.overlapDetails}
                      activeCriteria={activeCriteria}
                      geographyRegions={geographyRegions}
                      roomCountRanges={roomCountRanges}
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        <div className="flex justify-end mt-8">
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </div>
      </div>
    </SlideInPanel>
  );
}
