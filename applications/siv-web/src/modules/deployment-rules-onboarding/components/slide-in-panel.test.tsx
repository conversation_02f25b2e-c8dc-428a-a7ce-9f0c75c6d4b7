import { render, screen, fireEvent } from "@testing-library/react";
import { vi, beforeEach, it, expect, describe } from "vitest";
import { SlideInPanel } from "./slide-in-panel";

describe("SlideInPanel", () => {
  const mockOnOpenChange = vi.fn();
  const mockOnBack = vi.fn();

  beforeEach(() => {
    mockOnOpenChange.mockReset();
    mockOnBack.mockReset();
  });

  it("renders when open is true", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
      >
        <div data-testid="panel-content">Panel Content</div>
      </SlideInPanel>,
    );

    expect(screen.getByTestId("panel-content")).toBeInTheDocument();
    expect(screen.getByText("Test Panel")).toBeInTheDocument();
  });

  it("does not display content when open is false", () => {
    const { container } = render(
      <SlideInPanel
        open={false}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
      >
        <div data-testid="panel-content">Panel Content</div>
      </SlideInPanel>,
    );

    // The component still renders but with display: none
    const panel = container.firstChild as HTMLElement;
    expect(panel).toHaveStyle("display: none");
  });

  it("calls onOpenChange when back button is clicked", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    // Find the back button by its aria label
    const backButton = screen.getByRole("button", { name: /back/i });
    fireEvent.click(backButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it("calls onBack instead of onOpenChange when provided", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        onBack={mockOnBack}
        title="Test Panel"
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    const backButton = screen.getByRole("button", { name: /back/i });
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
    expect(mockOnOpenChange).not.toHaveBeenCalled();
  });

  it("renders header actions when provided", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
        headerActions={<button>Header Action</button>}
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    expect(
      screen.getByRole("button", { name: "Header Action" }),
    ).toBeInTheDocument();
  });

  it("renders footer content when provided", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
        footerContent={<button>Footer Action</button>}
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    expect(
      screen.getByRole("button", { name: "Footer Action" }),
    ).toBeInTheDocument();
  });

  it("applies animation classes based on open state", () => {
    const { rerender, container } = render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    const panel = container.firstChild as HTMLElement;
    expect(panel.className).toContain("animate-in slide-in-from-right");

    // Re-render with open=false
    rerender(
      <SlideInPanel
        open={false}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
      >
        <div>Content</div>
      </SlideInPanel>,
    );

    expect(panel.className).toContain("animate-out slide-out-to-right");
  });

  it("uses custom maxContentWidth when provided", () => {
    render(
      <SlideInPanel
        open={true}
        onOpenChange={mockOnOpenChange}
        title="Test Panel"
        maxContentWidth="md"
      >
        <div data-testid="panel-content">Content</div>
      </SlideInPanel>,
    );

    // Find content wrapper divs
    const contentWrappers = screen.getAllByRole("generic");

    // Check if any have the max-w-md class
    const hasMaxWidthClass = contentWrappers.some((wrapper) =>
      wrapper.className.includes("max-w-md"),
    );

    expect(hasMaxWidthClass).toBe(true);
  });
});
