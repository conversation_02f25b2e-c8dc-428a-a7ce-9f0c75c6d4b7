"use client";

import { Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { Individual, Team, Exception } from "../types/lead-assignment";

interface EntityActionsProps {
  /**
   * The entity (Team or Individual) this menu is for
   */
  entity: Team | Individual;

  /**
   * The exceptions for this entity
   */
  exceptions: Exception[];

  /**
   * Called when the exception link is clicked
   */
  onExceptionClick: () => void;

  /**
   * Called when the delete button is clicked
   */
  onDelete: () => void;
}

/**
 * Direct action buttons for entity actions including exceptions and delete
 */
export function EntityActions({
  entity,
  exceptions = [],
  onExceptionClick,
  onDelete,
}: EntityActionsProps) {
  const exceptionCount = exceptions.length;
  const hasExceptions = exceptionCount > 0;

  return (
    <div className="flex items-center gap-2">
      {/* Exception link/button - auto width */}
      <button
        onClick={onExceptionClick}
        className="text-sm font-medium text-primary hover:underline focus:outline-none focus-visible:ring-2 focus-visible:ring-primary rounded px-1"
        data-testid="exception-action"
      >
        <span className="flex items-center gap-1">
          {hasExceptions ? "Manage Exceptions" : "Add Exception"}
          {hasExceptions && (
            <Badge
              variant="secondary"
              className="h-5 min-w-[20px] px-1 text-xs flex-shrink-0"
            >
              {exceptionCount}
            </Badge>
          )}
        </span>
      </button>

      {/* Delete button - fixed position */}
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8 text-muted-foreground hover:text-destructive flex-shrink-0"
        onClick={onDelete}
        aria-label={`Delete ${entity.name}`}
        data-testid="delete-action"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}
