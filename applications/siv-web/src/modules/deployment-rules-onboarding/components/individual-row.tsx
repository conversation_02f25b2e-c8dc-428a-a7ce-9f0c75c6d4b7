"use client";

import { Fragment, useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Trash2, Edit } from "lucide-react";
import { CriteriaValueDisplay } from "./criteria-value-display";
import { OverlapIndicator } from "./overlap-indicator";
import type {
  CriteriaTypeString,
  Individual,
  Team,
  AssignmentRule,
  CriterionValue,
} from "../types/lead-assignment";

interface IndividualRowV2Props {
  /**
   * The individual to display
   */
  individual: Individual;

  /**
   * Whether this individual has conflicts
   */
  hasConflict: boolean;

  /**
   * Whether this individual has redundancies only
   */
  hasRedundancyOnly: boolean;

  /**
   * Active criteria types to display
   */
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;

  /**
   * Optional geography regions for display name resolution
   */
  geographyRegions?: any[];

  /**
   * Optional room count ranges for display name resolution
   */
  roomCountRanges?: any[];

  /**
   * Called when the user wants to add a new rule
   */
  onAddRule: (individual: Individual) => void;

  /**
   * Called when the user wants to add a rule and immediately edit a specific criteria
   */
  onAddRuleAndEditCriteria?: (individual: Individual, criteriaType: CriteriaTypeString) => void;

  /**
   * Called when the user wants to edit a rule
   */
  onEditRule: (individual: Individual, ruleId: string, criteriaType?: CriteriaTypeString) => void;

  /**
   * Called when the user wants to delete a rule
   */
  onDeleteRule: (individual: Individual, ruleId: string) => void;

  /**
   * Called when the user wants to edit the individual
   */
  onEditIndividual: (individual: Individual) => void;

  /**
   * Called when the user wants to delete the individual
   */
  onDeleteIndividual: (individual: Individual) => void;

  /**
   * Called when the user clicks the overlap indicator
   */
  onOverlapClick: (individual: Individual) => void;

  /**
   * Optional team the individual belongs to (for display)
   */
  team?: Team;

  /**
   * Optional flag to indicate if this is in a team section (affects styling)
   */
  inTeamSection?: boolean;
}

/**
 * A row in the assignment table v2 that displays individual with multiple rules
 */
export function IndividualRowV2({
  individual,
  hasConflict,
  hasRedundancyOnly,
  activeCriteria,
  geographyRegions = [],
  roomCountRanges = [],
  onAddRule,
  onAddRuleAndEditCriteria,
  onEditRule,
  onDeleteRule,
  onEditIndividual,
  onDeleteIndividual,
  onOverlapClick,
  team,
  inTeamSection = false,
}: IndividualRowV2Props) {
  
  // Get active criteria types
  const activeCriteriaTypes = Object.keys(activeCriteria)
    .filter((key) => activeCriteria[key as CriteriaTypeString])
    .map((key) => key as CriteriaTypeString);

  // Determine border color based on conflict/redundancy
  const borderColorClass = hasConflict 
    ? "border-l-destructive" 
    : hasRedundancyOnly 
    ? "border-l-yellow-500" 
    : "";
    
  const bgColorClass = hasConflict 
    ? "bg-destructive/5" 
    : hasRedundancyOnly 
    ? "bg-yellow-50/50" 
    : "";

  // Check if individual has any rules
  const hasRules = individual.rules && individual.rules.length > 0;
  const hasOverlap = hasConflict || hasRedundancyOnly;

  return (
    <>
      {/* Main Individual Row */}
      <TableRow
        className={`${hasOverlap ? `border-l-4 ${borderColorClass}` : ""}`}
        data-row-type="individual"
        data-testid={`individual-row-${individual.id}`}
      >
        {/* Individual name cell */}
        <TableCell
          className={`${inTeamSection ? "pl-8" : ""} ${bgColorClass}`}
        >
          <div className="flex items-center gap-2">
            <div className="w-6" />
            
            <div className="flex-1">
              <button
                type="button"
                className="font-medium underline cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary rounded"
                onClick={() => onEditIndividual(individual)}
                tabIndex={0}
                aria-label={`Edit details for ${individual.name}`}
              >
                <span data-testid={`individual-name-${individual.id}`}>
                  {individual.name}
                </span>
              </button>
              {individual.title && (
                <div
                  className="text-xs text-muted-foreground"
                  data-testid={`individual-title-${individual.id}`}
                >
                  {individual.title}
                </div>
              )}
              {hasOverlap && (
                <div className="flex items-center gap-2 mt-1">
                  <OverlapIndicator
                    hasOverlap={hasOverlap}
                    onClick={() => onOverlapClick(individual)}
                    className="text-xs"
                  />
                </div>
              )}
            </div>
          </div>
        </TableCell>

        {/* Criteria cells */}
        {!hasRules ? (
          <>
            {activeCriteriaTypes.map((criteriaType) => {
              // Check if team has rules for this criteria when in team section
              const teamHasRuleForCriteria = team?.rules?.some(rule => 
                rule.criteria[criteriaType] !== null && rule.criteria[criteriaType] !== undefined
              );
              
              return (
                <TableCell
                  key={criteriaType}
                  className={`p-2 border ${bgColorClass}`}
                  data-column={criteriaType}
                >
                  {teamHasRuleForCriteria ? (
                    <div className="text-sm text-muted-foreground italic px-2">
                      Inherited from team
                    </div>
                  ) : (
                    <button
                      type="button"
                      className="w-full min-h-[2.5rem] text-left bg-yellow-50 border border-yellow-200 rounded px-3 py-2 transition-colors cursor-pointer hover:bg-yellow-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                      onClick={() => {
                        // If we have a handler for adding and editing in one go, use it
                        if (onAddRuleAndEditCriteria) {
                          onAddRuleAndEditCriteria(individual, criteriaType);
                        } else {
                          // Otherwise fall back to just adding
                          onAddRule(individual);
                        }
                      }}
                      aria-label={`Add ${criteriaType} rule for ${individual.name}`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Not Set</span>
                        <Plus className="h-3 w-3 text-yellow-700" />
                      </div>
                    </button>
                  )}
                </TableCell>
              );
            })}
          </>
        ) : individual.rules.length === 1 ? (
          // Single rule - show criteria values inline
          <>
            {activeCriteriaTypes.map((criteriaType) => {
              // Check if team has a rule for this criteria FIRST
              const teamHasRuleForCriteria = team?.rules?.some(rule => 
                rule.criteria[criteriaType] !== null && rule.criteria[criteriaType] !== undefined
              );
              
              // If team has a rule, always show inherited, regardless of individual rules
              if (teamHasRuleForCriteria) {
                return (
                  <TableCell
                    key={criteriaType}
                    className={`p-2 border ${bgColorClass}`}
                    data-column={criteriaType}
                  >
                    <div className="text-sm text-muted-foreground italic px-2">
                      Inherited from team
                    </div>
                  </TableCell>
                );
              }
              
              // Otherwise, show individual's rule or Not Set
              const criteriaValue = individual.rules[0].criteria[criteriaType];
              
              return (
                <TableCell
                  key={criteriaType}
                  className={`p-2 border ${bgColorClass}`}
                  data-column={criteriaType}
                >
                  {criteriaValue === null || criteriaValue === undefined ? (
                    <button
                      type="button"
                      className="w-full min-h-[2.5rem] text-left bg-yellow-50 border border-yellow-200 rounded px-3 py-2 transition-colors cursor-pointer hover:bg-yellow-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                      onClick={() => onEditRule(individual, individual.rules[0].id, criteriaType)}
                      aria-label={`Edit ${criteriaType} for ${individual.name}`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Not Set</span>
                        <Plus className="h-3 w-3 text-yellow-700" />
                      </div>
                    </button>
                  ) : (
                    <button
                      type="button"
                      className="w-full min-h-[2.5rem] text-left border border-transparent hover:bg-gray-50 hover:border-gray-200 rounded px-3 py-2 transition-all cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                      onClick={() => onEditRule(individual, individual.rules[0].id, criteriaType)}
                      aria-label={`Edit ${criteriaType} for ${individual.name}`}
                    >
                      <div className="flex items-center justify-between">
                        <CriteriaValueDisplay
                          criteriaType={criteriaType}
                          values={criteriaValue.type === "Specific" ? criteriaValue.values : []}
                          isAnyCriterion={criteriaValue.type === "Any"}
                          geographyRegions={geographyRegions}
                          roomCountRanges={roomCountRanges}
                        />
                        <Edit className="h-3 w-3 text-gray-400 ml-2" />
                      </div>
                    </button>
                  )}
                </TableCell>
              );
            })}
          </>
        ) : (
          // Multiple rules - show summary
          <>
            {activeCriteriaTypes.map((criteriaType) => {
              // Check if team has a rule for this criteria
              const teamHasRuleForCriteria = team?.rules?.some(rule => 
                rule.criteria[criteriaType] !== null && rule.criteria[criteriaType] !== undefined
              );
              
              return (
                <TableCell
                  key={criteriaType}
                  className={`p-2 border ${bgColorClass}`}
                  data-column={criteriaType}
                >
                  {teamHasRuleForCriteria ? (
                    <div className="text-sm text-muted-foreground italic px-2">
                      Inherited from team
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground px-2">
                      {individual.rules.length} rules
                    </div>
                  )}
                </TableCell>
              );
            })}
          </>
        )}

        {/* Actions cell */}
        <TableCell className={bgColorClass}>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAddRule(individual)}
              className="text-xs"
              data-testid={`add-rule-individual-${individual.id}`}
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Rule
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteIndividual(individual)}
              className="text-destructive hover:text-destructive"
              data-testid={`delete-individual-${individual.id}`}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>

      {/* Additional Rules (only show if more than 1 rule) */}
      {hasRules && individual.rules.length > 1 && individual.rules.map((rule, index) => (
        <TableRow
          key={rule.id}
          className={`${hasOverlap ? `border-l-4 ${borderColorClass}` : ""}`}
          data-row-type="rule"
          data-testid={`rule-row-${rule.id}`}
        >
          {/* Rule number */}
          <TableCell className={`pl-16 ${bgColorClass}`}>
            <div className="text-sm text-muted-foreground">
              Rule {index + 1}
            </div>
          </TableCell>

          {/* Criteria values for this rule */}
          {activeCriteriaTypes.map((criteriaType) => {
            // Check if team has a rule for this criteria
            const teamHasRuleForCriteria = team?.rules?.some(teamRule => 
              teamRule.criteria[criteriaType] !== null && teamRule.criteria[criteriaType] !== undefined
            );
            
            // If team has a rule, show inherited
            if (teamHasRuleForCriteria) {
              return (
                <TableCell
                  key={criteriaType}
                  className={`p-2 border ${bgColorClass}`}
                  data-column={criteriaType}
                >
                  <div className="text-sm text-muted-foreground italic px-2">
                    Inherited from team
                  </div>
                </TableCell>
              );
            }
            
            const criteriaValue = rule.criteria[criteriaType];
            
            return (
              <TableCell
                key={criteriaType}
                className={`p-2 border ${bgColorClass}`}
                data-column={criteriaType}
              >
                {criteriaValue === null || criteriaValue === undefined ? (
                  <button
                    type="button"
                    className="w-full min-h-[2.5rem] text-left bg-yellow-50 border border-yellow-200 rounded px-3 py-2 transition-colors cursor-pointer hover:bg-yellow-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                    onClick={() => onEditRule(individual, rule.id, criteriaType)}
                    aria-label={`Edit ${criteriaType} for rule ${index + 1}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Not Set</span>
                      <Plus className="h-3 w-3 text-yellow-700" />
                    </div>
                  </button>
                ) : (
                  <button
                    type="button"
                    className="w-full min-h-[2.5rem] text-left border border-transparent hover:bg-gray-50 hover:border-gray-200 rounded px-3 py-2 transition-all cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
                    onClick={() => onEditRule(individual, rule.id, criteriaType)}
                    aria-label={`Edit ${criteriaType} for rule ${index + 1}`}
                  >
                    <div className="flex items-center justify-between">
                      <CriteriaValueDisplay
                        criteriaType={criteriaType}
                        values={criteriaValue.type === "Specific" ? criteriaValue.values : []}
                        isAnyCriterion={criteriaValue.type === "Any"}
                        geographyRegions={geographyRegions}
                        roomCountRanges={roomCountRanges}
                      />
                      <Edit className="h-3 w-3 text-gray-400 ml-2" />
                    </div>
                  </button>
                )}
              </TableCell>
            );
          })}

          {/* Rule actions */}
          <TableCell className={bgColorClass}>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEditRule(individual, rule.id)}
                className="text-xs"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteRule(individual, rule.id)}
                className="text-destructive hover:text-destructive text-xs"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}