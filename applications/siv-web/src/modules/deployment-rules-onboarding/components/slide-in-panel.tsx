"use client";

import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface SlideInPanelProps {
  /**
   * Whether the panel is open
   */
  open: boolean;

  /**
   * Callback for when the open state changes
   */
  onOpenChange: (open: boolean) => void;

  /**
   * The title to display in the header
   */
  title: string;

  /**
   * Children to render in the main content area
   */
  children: ReactNode;

  /**
   * Optional content to render in the footer action area (typically buttons)
   */
  footerContent?: ReactNode;

  /**
   * Optional action buttons to render in the header
   */
  headerActions?: ReactNode;

  /**
   * Optional callback for when the back button is clicked
   * If not provided, onOpenChange(false) will be called instead
   */
  onBack?: () => void;

  /**
   * Optional maximum width for the content area
   * @default screen-xl
   */
  maxContentWidth?: string;

  /**
   * Optional data-testid for testing
   */
  "data-testid"?: string;
}

/**
 * A full-screen slide-in panel component with a header, content area, and footer
 * Used for modal workflows that require more screen space
 */
export function SlideInPanel({
  open,
  onOpenChange,
  title,
  children,
  footerContent,
  headerActions,
  onBack,
  maxContentWidth = "screen-xl",
  "data-testid": dataTestId,
}: SlideInPanelProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 z-50 bg-background flex flex-col",
        open
          ? "animate-in slide-in-from-right duration-300"
          : "animate-out slide-out-to-right duration-300",
      )}
      style={{ display: open ? "flex" : "none" }}
      data-testid={dataTestId || "slide-in-panel"}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => (onBack ? onBack() : onOpenChange(false))}
            className="rounded-full"
            aria-label="Back"
          >
            <ChevronDown className="h-5 w-5 rotate-90" />
            <span className="sr-only">Back</span>
          </Button>
          <h2 className="text-lg font-semibold">{title}</h2>
        </div>

        {headerActions && (
          <div className="flex items-center gap-2" data-testid="header-actions">
            {headerActions}
          </div>
        )}
      </div>

      {/* Content area */}
      <div className="flex-grow overflow-auto p-6">
        <div
          className={cn(`max-w-${maxContentWidth} mx-auto`)}
          data-testid="panel-content-wrapper"
        >
          {children}
        </div>
      </div>

      {/* Footer */}
      {footerContent && (
        <div
          className="flex-shrink-0 sticky bottom-0 bg-background py-4 px-6 border-t shadow-sm"
          data-testid="footer-content"
        >
          <div
            className={cn(
              `max-w-${maxContentWidth} mx-auto flex justify-end gap-3`,
            )}
          >
            {footerContent}
          </div>
        </div>
      )}
    </div>
  );
}
