import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { CoverageGapTable } from "./coverage-gap-table";
import type { CoverageGap } from "../types/lead-assignment";

describe("CoverageGapTable", () => {
  it("renders table with all coverage gaps", () => {
    const coverageGaps: CoverageGap[] = [
      {
        id: "gap-1",
        description: "USA, Meeting, Technology",
        missingCombination: [
          { criteriaType: "geography", value: "usa-id", displayValue: "USA" },
          {
            criteriaType: "eventType",
            value: "Meeting",
            displayValue: "Meeting",
          },
          {
            criteriaType: "industry",
            value: "Technology",
            displayValue: "Technology",
          },
        ],
      },
      {
        id: "gap-2",
        description: "Canada, Conference, Healthcare",
        missingCombination: [
          {
            criteriaType: "geography",
            value: "canada-id",
            displayValue: "Canada",
          },
          {
            criteriaType: "eventType",
            value: "Conference",
            displayValue: "Conference",
          },
          {
            criteriaType: "industry",
            value: "Healthcare",
            displayValue: "Healthcare",
          },
        ],
      },
    ];

    const activeCriteria = {
      geography: true,
      eventType: true,
      industry: true,
      roomCount: false,
      eventNeeds: false,
      date: false,
    };

    const registries = {
      geographyRegions: [
        { id: "usa-id", name: "USA", countries: ["US"] },
        { id: "canada-id", name: "Canada", countries: ["CA"] },
      ],
    };

    render(
      <CoverageGapTable
        coverageGaps={coverageGaps}
        activeCriteria={activeCriteria}
        registries={registries}
      />,
    );

    // Check that table headers are rendered for active criteria
    expect(
      screen.getByTestId("coverage-gap-table-header-geography"),
    ).toHaveTextContent("Geography");
    expect(
      screen.getByTestId("coverage-gap-table-header-eventType"),
    ).toHaveTextContent("Event Type");
    expect(
      screen.getByTestId("coverage-gap-table-header-industry"),
    ).toHaveTextContent("Industry");

    // Check that gaps are rendered as rows with full criteria in testid
    expect(
      screen.getByTestId(
        "coverage-gap-row-eventType-meeting_geography-usa_industry-technology",
      ),
    ).toBeInTheDocument();
    expect(
      screen.getByTestId(
        "coverage-gap-row-eventType-conference_geography-canada_industry-healthcare",
      ),
    ).toBeInTheDocument();

    // Check that values are displayed correctly
    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-meeting_geography-usa_industry-technology-geography",
      ),
    ).toHaveTextContent("USA");
    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-meeting_geography-usa_industry-technology-eventType",
      ),
    ).toHaveTextContent("Meeting");
    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-meeting_geography-usa_industry-technology-industry",
      ),
    ).toHaveTextContent("Technology");

    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-conference_geography-canada_industry-healthcare-geography",
      ),
    ).toHaveTextContent("Canada");
    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-conference_geography-canada_industry-healthcare-eventType",
      ),
    ).toHaveTextContent("Conference");
    expect(
      screen.getByTestId(
        "coverage-gap-cell-eventType-conference_geography-canada_industry-healthcare-industry",
      ),
    ).toHaveTextContent("Healthcare");

    // Check filter buttons are rendered in headers
    const geographyHeader = screen.getByTestId(
      "coverage-gap-table-header-geography",
    );
    expect(geographyHeader.querySelector("button")).toBeInTheDocument();

    const eventTypeHeader = screen.getByTestId(
      "coverage-gap-table-header-eventType",
    );
    expect(eventTypeHeader.querySelector("button")).toBeInTheDocument();

    const industryHeader = screen.getByTestId(
      "coverage-gap-table-header-industry",
    );
    expect(industryHeader.querySelector("button")).toBeInTheDocument();

    // Check results summary
    expect(
      screen.getByText("Showing 2 of 2 coverage gaps"),
    ).toBeInTheDocument();

    // Check summary message
    expect(screen.getByText("2 coverage gaps identified")).toBeInTheDocument();
  });

  it("shows empty state when no gaps", () => {
    const activeCriteria = {
      geography: true,
      eventType: true,
      industry: false,
      roomCount: false,
      eventNeeds: false,
      date: false,
    };

    render(
      <CoverageGapTable
        coverageGaps={[]}
        activeCriteria={activeCriteria}
        registries={{}}
      />,
    );

    expect(screen.getByText("No coverage gaps found")).toBeInTheDocument();
  });

  it("handles gaps with partial criteria (some criteria not included in gap)", () => {
    const coverageGaps: CoverageGap[] = [
      {
        id: "gap-1",
        description: "USA, Meeting",
        missingCombination: [
          { criteriaType: "geography", value: "usa-id", displayValue: "USA" },
          {
            criteriaType: "eventType",
            value: "Meeting",
            displayValue: "Meeting",
          },
          // Note: no industry criteria in this gap
        ],
      },
    ];

    const activeCriteria = {
      geography: true,
      eventType: true,
      industry: true, // Active but not part of the gap
      roomCount: false,
      eventNeeds: false,
      date: false,
    };

    const registries = {
      geographyRegions: [{ id: "usa-id", name: "USA", countries: ["US"] }],
    };

    render(
      <CoverageGapTable
        coverageGaps={coverageGaps}
        activeCriteria={activeCriteria}
        registries={registries}
      />,
    );

    // Check that the industry cell shows a dash for missing criteria
    // Note: Component normalizes display values to lowercase and replaces spaces with hyphens
    const row = screen.getByTestId(
      "coverage-gap-row-eventType-meeting_geography-usa",
    );
    expect(row).toBeInTheDocument();

    // Industry column should show dash since it's not part of this gap
    const cells = row.querySelectorAll("td");
    expect(cells[3]).toHaveTextContent("-"); // Industry is the 4th cell (after #, geography, eventType)
  });
});
