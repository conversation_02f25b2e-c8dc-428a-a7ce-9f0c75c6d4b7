/**
 * CriteriaValueDisplay - A reusable component for displaying criteria values
 *
 * This component provides a consistent way to display criteria values throughout
 * the application, ensuring that proper display names are used and handling
 * empty values appropriately.
 */

import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import * as CriteriaService from "../utils/criteria-helpers";
import type {
  CriteriaTypeString,
  GeographyRegion,
  RoomCountRange,
} from "../types/lead-assignment";

export interface CriteriaValueDisplayProps {
  criteriaType: CriteriaTypeString;
  values: string[];
  maxDisplay?: number;
  geographyRegions?: GeographyRegion[];
  roomCountRanges?: RoomCountRange[];
  emptyText?: string;
  className?: string;
  isAnyCriterion?: boolean;
  isBlank?: boolean;
}

export function CriteriaValueDisplay({
  criteriaType,
  values,
  maxDisplay = 2,
  geographyRegions = [],
  roomCountRanges = [],
  emptyText = "Any",
  className = "",
  isAnyCriterion = false,
  isBlank = false,
}: CriteriaValueDisplayProps) {
  const registries = { geographyRegions, roomCountRanges };

  // Display precedence: Any > Blank > Empty values
  if (isAnyCriterion) {
    return <span className="text-muted-foreground">Any</span>;
  }

  if (isBlank) {
    return <span className="text-muted-foreground">{emptyText}</span>;
  }

  if (!values || values.length === 0) {
    return <span className="text-muted-foreground">{emptyText}</span>;
  }

  // The values to display immediately
  const displayValues = values.slice(0, maxDisplay);

  // Any overflow values
  const hasMore = values.length > maxDisplay;
  const overflowValues = hasMore ? values.slice(maxDisplay) : [];

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {displayValues.map((value, index) => {
        const displayName = CriteriaService.getCriteriaValueDisplayName(
          criteriaType,
          value,
          registries,
        );

        return (
          <Badge key={index} variant="outline">
            {displayName}
          </Badge>
        );
      })}

      {hasMore && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline">
                +{values.length - maxDisplay} more
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                {overflowValues.map((value, index) => {
                  const displayName =
                    CriteriaService.getCriteriaValueDisplayName(
                      criteriaType,
                      value,
                      registries,
                    );

                  return <div key={index}>{displayName}</div>;
                })}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
