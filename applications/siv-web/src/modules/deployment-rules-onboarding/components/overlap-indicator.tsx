"use client";

import { AlertTriangle, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface OverlapIndicatorProps {
  /**
   * Whether this entity has overlapping rules
   */
  hasOverlap: boolean;

  /**
   * Called when the user clicks on the overlap badge
   */
  onClick: () => void;

  /**
   * Optional additional class name
   */
  className?: string;

  /**
   * Optional label text (defaults to "Overlapping rules")
   */
  label?: string;
}

/**
 * A component that displays an overlap warning badge when rules overlap
 */
export function OverlapIndicator({
  hasOverlap,
  onClick,
  className = "",
  label = "View Overlap Details",
}: OverlapIndicatorProps) {
  if (!hasOverlap) return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            className={`flex items-center gap-1 cursor-pointer bg-red-600 text-white hover:bg-red-700 transition-colors ${className}`}
            onClick={onClick}
            data-testid="overlap-indicator"
            aria-label="View overlap details"
          >
            <AlertTriangle className="h-3 w-3" />
            <span>{label}</span>
            <Info className="h-3 w-3 opacity-70" />
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to see detailed overlap information</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
