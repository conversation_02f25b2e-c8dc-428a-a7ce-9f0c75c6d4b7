"use client";

import { useState, useCallback } from "react";
import type { Individual, Team } from "../types/lead-assignment";

// Define all possible modal types
export type ModalType =
  | "criteria"
  | "geography"
  | "exception"
  | "individualDetails"
  | "teamConfig"
  | "deleteConfirmation"
  | "overlapDetails";

// Context information for the current modal
export interface ModalContext {
  entityType: "individual" | "team" | null;
  entityId: string | null;
  criteriaType: string | null;
  isEditing: boolean;
  individual: Individual | null;
  team: Team | null;
  deleteEntityName: string;
}

/**
 * A hook to manage modal state in a centralized way
 */
export function useModalState() {
  // Track which modal is visible
  const [activeModal, setActiveModal] = useState<ModalType | null>(null);

  // Track context information for the modal
  const [context, setContext] = useState<ModalContext>({
    entityType: null,
    entityId: null,
    criteriaType: null,
    isEditing: false,
    individual: null,
    team: null,
    deleteEntityName: "",
  });

  // Open a modal with context
  const openModal = useCallback(
    (modal: ModalType, newContext: Partial<ModalContext> = {}) => {
      setActiveModal(modal);
      setContext((prev) => ({
        ...prev,
        ...newContext,
      }));
    },
    [],
  );

  // Close the active modal
  const closeModal = useCallback(() => {
    setActiveModal(null);
  }, []);

  // Check if a specific modal is open
  const isModalOpen = useCallback(
    (modal: ModalType) => {
      return activeModal === modal;
    },
    [activeModal],
  );

  return {
    context,
    activeModal,
    isModalOpen,
    openModal,
    closeModal,
    setContext,
  };
}
