import {
  CRITERION_ANY,
  type CriteriaTypeString,
  type CriterionValue,
} from "@/modules/deployment-rules-onboarding/types/lead-assignment";

/**
 * Creates a complete criteria object with all required fields set to default values
 * @param overrides - Partial criteria to override defaults
 * @returns Complete criteria object
 */
export function createDefaultCriteria(
  overrides: Partial<Record<CriteriaTypeString, CriterionValue>> = {},
): Record<CriteriaTypeString, CriterionValue> {
  const defaults: Record<CriteriaTypeString, CriterionValue> = {
    geography: CRITERION_ANY,
    roomCount: CRITERION_ANY,
    eventType: CRITERION_ANY,
    industry: CRITERION_ANY,
    eventNeeds: CRITERION_ANY,
    dayOfMonth: CRITERION_ANY,
  };

  return {
    ...defaults,
    ...overrides,
  };
}

/**
 * Creates a specific criterion value
 * @param values - Array of string values for the criterion
 * @returns CriterionValue object with type "Specific"
 */
export function createSpecificCriterion(values: string[]): CriterionValue {
  return { type: "Specific", values };
}
