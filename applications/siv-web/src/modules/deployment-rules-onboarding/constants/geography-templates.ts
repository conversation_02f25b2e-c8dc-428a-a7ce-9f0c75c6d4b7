// Preconfigured region templates for quick add functionality in geography management
export interface GeographyTemplate {
  name: string;
  countries: string[];
  usStates?: string[];
  dynamicTemplate?: "ALL_NON_US_COUNTRIES" | "USA_ALL_STATES";
}

export const PRECONFIGURED_REGIONS: GeographyTemplate[] = [
  {
    name: "USA (All States)",
    countries: ["US"],
    usStates: [],
    dynamicTemplate: "USA_ALL_STATES",
  },
  {
    name: "All Non-US Countries",
    countries: [], // Will be dynamically populated with all non-US countries
    usStates: [],
    dynamicTemplate: "ALL_NON_US_COUNTRIES",
  },
  {
    name: "APAC",
    countries: ["AU", "CN", "IN", "JP", "KR", "SG"], // ISO country codes
    usStates: [],
  },
  {
    name: "Western Europe",
    countries: ["GB", "FR", "DE", "IT", "ES", "NL", "SE"],
    usStates: [],
  },
  {
    name: "North America",
    countries: ["CA", "MX", "US"],
    usStates: [],
  },
  {
    name: "US - West Coast",
    countries: ["US"],
    usStates: ["CA", "OR", "WA"],
  },
  {
    name: "US - East Coast",
    countries: ["US"],
    usStates: [
      "NY",
      "NJ",
      "MA",
      "CT",
      "PA",
      "MD",
      "VA",
      "NC",
      "SC",
      "GA",
      "FL",
    ],
  },
  {
    name: "US - Midwest",
    countries: ["US"],
    usStates: [
      "IL",
      "IN",
      "IA",
      "KS",
      "MI",
      "MN",
      "MO",
      "NE",
      "ND",
      "OH",
      "SD",
      "WI",
    ],
  },
];
