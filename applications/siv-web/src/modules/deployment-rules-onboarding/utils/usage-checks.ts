import type {
  Individual,
  Team,
  CriteriaTypeString,
  CriterionValue,
} from "../types/lead-assignment";

/**
 * Generic function to check if a criteria value is being used in any assignment rules
 *
 * @param criteriaType The type of criteria to check (e.g., 'geography', 'roomCount')
 * @param valueId The ID of the value to check
 * @param individuals The array of individuals to check against
 * @param teams The array of teams to check against
 * @returns true if the value is in use, false otherwise
 */
export function isCriteriaValueInUse(
  criteriaType: CriteriaTypeString,
  valueId: string,
  individuals: Individual[],
  teams: Team[],
): boolean {
  // Check if any individual is using this value in their criteria
  const individualUsingValue = individuals.some((individual) => {
    const criterion = individual.criteria[criteriaType];
    if (!criterion) return false;
    // "Any" criteria don't use specific values
    if (criterion.type === "Any") return false;
    // Check if the specific values include this valueId
    return criterion.values.includes(valueId);
  });

  if (individualUsingValue) {
    return true;
  }

  // Check if any team is using this value in their criteria
  const teamUsingValue = teams.some((team) => {
    const criterion = team.criteria[criteriaType];
    if (!criterion) return false;
    // "Any" criteria don't use specific values
    if (criterion.type === "Any") return false;
    // Check if the specific values include this valueId
    return criterion.values.includes(valueId);
  });

  return teamUsingValue;
}

/**
 * Removes invalid criteria values from all individuals and teams.
 * This function should be called whenever the available criteria values for a type change.
 *
 * @param criteriaType The type of criteria being updated (e.g., 'geography', 'roomCount')
 * @param validValues Array of valid IDs for this criteria type
 * @param individuals The array of individuals to update
 * @param teams The array of teams to update
 * @returns An object containing updated individuals and teams arrays
 */
export function cleanupInvalidCriteriaValues(
  criteriaType: CriteriaTypeString,
  validValues: string[],
  individuals: Individual[],
  teams: Team[],
): { individuals: Individual[]; teams: Team[] } {
  // Create a set of valid values for faster lookups
  const validValueSet = new Set(validValues);

  // Update individuals
  const updatedIndividuals = individuals.map((individual) => {
    // Get the current criterion for this type
    const currentCriterion = individual.criteria[criteriaType];

    // If no criterion or it's "Any", no need to clean up
    if (!currentCriterion || currentCriterion.type === "Any") {
      return individual;
    }

    // Filter out invalid values from specific values
    const filteredValues = currentCriterion.values.filter((value) =>
      validValueSet.has(value),
    );

    // Only create a new object if values changed
    if (filteredValues.length !== currentCriterion.values.length) {
      const newCriterion: CriterionValue | null =
        filteredValues.length === 0
          ? null // If no values remain, set to null
          : { type: "Specific", values: filteredValues };

      return {
        ...individual,
        criteria: {
          ...individual.criteria,
          [criteriaType]: newCriterion,
        },
      };
    }

    // Return the original individual if no changes
    return individual;
  });

  // Update teams
  const updatedTeams = teams.map((team) => {
    // Get the current criterion for this type
    const currentCriterion = team.criteria[criteriaType];

    // If no criterion or it's "Any", no need to clean up
    if (!currentCriterion || currentCriterion.type === "Any") {
      return team;
    }

    // Filter out invalid values from specific values
    const filteredValues = currentCriterion.values.filter((value) =>
      validValueSet.has(value),
    );

    // Only create a new object if values changed
    if (filteredValues.length !== currentCriterion.values.length) {
      const newCriterion: CriterionValue | null =
        filteredValues.length === 0
          ? null // If no values remain, set to null
          : { type: "Specific", values: filteredValues };

      return {
        ...team,
        criteria: {
          ...team.criteria,
          [criteriaType]: newCriterion,
        },
      };
    }

    // Return the original team if no changes
    return team;
  });

  return { individuals: updatedIndividuals, teams: updatedTeams };
}
