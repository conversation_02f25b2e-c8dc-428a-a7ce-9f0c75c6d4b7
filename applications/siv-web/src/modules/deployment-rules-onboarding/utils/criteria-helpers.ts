/**
 * Criteria Service - Unified interface for handling criteria operations
 *
 * This module provides a clean abstraction for working with criteria types,
 * their values, and display representation across the lead assignment system.
 * It centralizes knowledge about different criteria types and provides
 * consistent interfaces for accessing, updating, and displaying them.
 */

import type {
  CriteriaTypeString,
  CriterionValue,
  GeographyRegion,
  RoomCountRange,
} from "../types/lead-assignment";

/**
 * EntityWithCriteria - Common interface for any entity that has criteria
 */
export interface EntityWithCriteria {
  id: string;
  name: string;
  criteria: Record<CriteriaTypeString, CriterionValue | null>;
}

/**
 * CriteriaOption - Interface for criteria options with value/label pattern
 */
export interface CriteriaOption {
  value: string;
  label: string;
}

/**
 * CriteriaTypeDefinition - Full definition of a criteria type's metadata and behavior
 */
export interface CriteriaTypeDefinition {
  key: CriteriaTypeString;
  displayName: string;
  storageKey: string; // Allows for mappings like 'date' -> 'dayOfMonth'
  getLookupId: (entity: EntityWithCriteria) => CriterionValue | null;
  getDisplayName: (id: string, registry?: any[]) => string;
  getDefaultOptions: () => string[] | CriteriaOption[];
}

/**
 * The master registry of all criteria types with their metadata
 */
export const CRITERIA_REGISTRY: Record<
  CriteriaTypeString,
  CriteriaTypeDefinition
> = {
  geography: {
    key: "geography",
    displayName: "Geography",
    storageKey: "geography",
    getLookupId: (entity) => entity.criteria?.geography ?? null,
    getDisplayName: (id, regions) => {
      if (!regions?.length) return id;
      const region = regions.find((r: GeographyRegion) => r.id === id);
      return region?.name || id;
    },
    getDefaultOptions: () => [
      "North America",
      "Europe",
      "Asia",
      "Australia",
      "South America",
      "Africa",
    ],
  },
  roomCount: {
    key: "roomCount",
    displayName: "Room Count",
    storageKey: "roomCount",
    getLookupId: (entity) => entity.criteria?.roomCount ?? null,
    getDisplayName: (id, ranges) => {
      if (!ranges?.length) return id;
      const range = ranges.find((r: RoomCountRange) => r.id === id);
      return range?.name || id;
    },
    getDefaultOptions: () => ["1-10", "11-50", "51-100", "101+"],
  },
  eventType: {
    key: "eventType",
    displayName: "Event Type",
    storageKey: "eventType",
    getLookupId: (entity) => entity.criteria?.eventType ?? null,
    getDisplayName: (id) => id,
    getDefaultOptions: () => [
      "Meeting",
      "Conference",
      "Wedding",
      "Social Event",
      "Corporate Event",
    ],
  },
  industry: {
    key: "industry",
    displayName: "Industry",
    storageKey: "industry",
    getLookupId: (entity) => entity.criteria?.industry ?? null,
    getDisplayName: (id) => id,
    getDefaultOptions: () => [
      "Government",
      "Technology",
      "Healthcare",
      "Finance",
      "Education",
      "Manufacturing",
      "Retail",
      "Other",
    ],
  },
  eventNeeds: {
    key: "eventNeeds",
    displayName: "Event Needs",
    storageKey: "eventNeeds",
    getLookupId: (entity) => entity.criteria?.eventNeeds ?? null,
    getDisplayName: (id) => id,
    getDefaultOptions: () => [
      "Catering",
      "AV Equipment",
      "Meeting Space",
      "Accommodations",
      "Transportation",
    ],
  },
  dayOfMonth: {
    key: "dayOfMonth",
    displayName: "Day of Month",
    storageKey: "dayOfMonth",
    getLookupId: (entity) => entity.criteria?.dayOfMonth ?? null,
    getDisplayName: (id) => id,
    getDefaultOptions: () => ["Odd Days", "Even Days"],
  },
};

/**
 * Type guard to check if a string is a valid criteria type
 */
export function isCriteriaType(key: string): key is CriteriaTypeString {
  return key in CRITERIA_REGISTRY;
}

/**
 * Ensure type completeness at compile time
 * This creates a type error if any CriteriaTypeString values are missing from CRITERIA_REGISTRY
 */
type CheckTypeCompleteness = {
  [K in CriteriaTypeString]: K extends keyof typeof CRITERIA_REGISTRY
    ? true
    : never;
};
// This line will cause a type error if any keys are missing
const _typeCheck: CheckTypeCompleteness = {} as any;

/**
 * Get the human-readable display name for a criteria type
 */
export function getCriteriaDisplayName(
  criteriaType: CriteriaTypeString,
): string {
  return CRITERIA_REGISTRY[criteriaType]?.displayName || criteriaType;
}

/**
 * Get the storage key for a criteria type (important for date/dayOfMonth mapping)
 */
export function getCriteriaStorageKey(
  criteriaType: CriteriaTypeString,
): string {
  return CRITERIA_REGISTRY[criteriaType]?.storageKey || criteriaType;
}

/**
 * Registry type for criteria reference data
 */
export interface CriteriaRegistries {
  geographyRegions?: GeographyRegion[];
  roomCountRanges?: RoomCountRange[];
}

/**
 * Get the human-readable display name for a criteria value
 */
export function getCriteriaValueDisplayName(
  criteriaType: CriteriaTypeString,
  itemId: string | null,
  registries: CriteriaRegistries = {},
): string {
  // Handle null/empty values
  if (!itemId) {
    return "Not set";
  }

  const definition = CRITERIA_REGISTRY[criteriaType];

  switch (criteriaType) {
    case "geography":
      return definition.getDisplayName(itemId, registries.geographyRegions);
    case "roomCount":
      return definition.getDisplayName(itemId, registries.roomCountRanges);
    default:
      return definition.getDisplayName(itemId);
  }
}

/**
 * Get the criterion value object for an entity and criteria type
 */
export function getCriterion(
  entity: EntityWithCriteria,
  criteriaType: CriteriaTypeString,
): CriterionValue | null | undefined {
  return entity.criteria?.[criteriaType];
}

/**
 * Get the criteria values for an entity and criteria type
 * For "Any" criteria, returns all possible values for that criteria type
 * For "Specific" criteria, returns the selected values
 * For null or undefined criteria, returns empty array
 */
export function getCriteriaValues(
  entity: EntityWithCriteria,
  criteriaType: CriteriaTypeString,
  registries: CriteriaRegistries = {},
): string[] {
  const criterion = entity.criteria?.[criteriaType];

  // If no criterion is defined or is null, return empty array
  if (!criterion || criterion === null) {
    return [];
  }

  // If criterion is "Any", return all possible values
  if (criterion.type === "Any") {
    const options = getCriteriaOptions(criteriaType, registries);
    return options.map((option) => option.value);
  }

  // Otherwise return the specific values
  return criterion.values;
}

/**
 * Get criteria options for selection UI
 * Returns a unified interface of options with value/label pairs
 */
export function getCriteriaOptions(
  criteriaType: CriteriaTypeString,
  registries: CriteriaRegistries = {},
): CriteriaOption[] {
  const definition = CRITERIA_REGISTRY[criteriaType];

  switch (criteriaType) {
    case "geography":
      if (
        registries.geographyRegions &&
        registries.geographyRegions.length > 0
      ) {
        return registries.geographyRegions.map((region) => ({
          value: region.id,
          label: region.name,
        }));
      }
      break;

    case "roomCount":
      if (registries.roomCountRanges && registries.roomCountRanges.length > 0) {
        return registries.roomCountRanges.map((range) => ({
          value: range.id,
          label: range.name,
        }));
      }
      break;
  }

  // For default options or non-specialized criteria types
  const defaultOptions = definition.getDefaultOptions();

  // Convert string[] to CriteriaOption[] if needed
  if (typeof defaultOptions[0] === "string") {
    return (defaultOptions as string[]).map((option) => ({
      value: option,
      label: option,
    }));
  }

  return defaultOptions as CriteriaOption[];
}

/**
 * Create an empty criteria object with all supported criteria types initialized to null
 */
export function createEmptyCriteria(): Record<
  CriteriaTypeString,
  CriterionValue | null
> {
  return Object.keys(CRITERIA_REGISTRY).reduce(
    (acc, criteriaType) => {
      acc[criteriaType as CriteriaTypeString] = null;
      return acc;
    },
    {} as Record<CriteriaTypeString, CriterionValue | null>,
  );
}

/**
 * Convert an entity with partial criteria to one with complete criteria
 * Missing criteria fields are filled with null
 */
export function toEntityWithCriteria<
  T extends {
    id: string;
    name: string;
    criteria: Partial<Record<CriteriaTypeString, CriterionValue | null>>;
  },
>(entity: T): EntityWithCriteria {
  const completeCriteria = createEmptyCriteria();

  // Override with any defined criteria from the entity
  if (entity.criteria) {
    Object.entries(entity.criteria).forEach(([key, value]) => {
      if (value !== undefined) {
        completeCriteria[key as CriteriaTypeString] = value;
      }
    });
  }

  return {
    id: entity.id,
    name: entity.name,
    criteria: completeCriteria,
  };
}
