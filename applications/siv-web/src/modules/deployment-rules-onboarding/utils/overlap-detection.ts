import type {
  AssignmentRule,
  CriterionValue,
  CriteriaTypeString,
  GeographyRegion,
  Individual,
  RoomCountRange,
  Team,
} from "../types/lead-assignment";

/**
 * Check if a rule is complete (has non-null values for all active criteria)
 *
 * @param rule The rule to check
 * @param activeCriteria Record of which criteria types are active
 * @returns true if the rule is complete, false if it has null values for any active criteria
 */
const isRuleComplete = (
  rule: AssignmentRule,
  activeCriteria?: Record<string, boolean>
): boolean => {
  if (!activeCriteria) {
    return true; // If no active criteria specified, consider rule complete
  }

  for (const [criteriaType, isActive] of Object.entries(activeCriteria)) {
    if (isActive) {
      const criterion = rule.criteria[criteriaType as CriteriaTypeString];
      if (criterion === null || criterion === undefined) {
        return false; // Rule is incomplete - has null or undefined for an active criterion
      }
    }
  }

  return true;
};

/**
 * Simplified version of doRulesOverlap that works with AssignmentRule objects
 * without any exception logic
 * 
 * @param rule1 First rule to compare
 * @param rule2 Second rule to compare
 * @returns true if the rules overlap, false otherwise
 */
export const doRulesOverlap = (
  rule1: AssignmentRule,
  rule2: AssignmentRule,
  activeCriteria?: Record<string, boolean>
): boolean => {
  // If either rule is incomplete (has null for any active criterion), they cannot overlap
  // This prevents incomplete rules from overlapping with any other rules
  if (!isRuleComplete(rule1, activeCriteria) || !isRuleComplete(rule2, activeCriteria)) {
    return false;
  }

  const criteriaTypes = new Set([
    ...Object.keys(rule1.criteria),
    ...Object.keys(rule2.criteria),
  ]) as Set<CriteriaTypeString>;

  // Track if we found at least one valid criterion to compare
  let hasValidCriterion = false;

  // Track which criteria are set (non-null) in each rule
  const rule1SetCriteria = new Set<CriteriaTypeString>();
  const rule2SetCriteria = new Set<CriteriaTypeString>();

  // First pass: identify which criteria are set in each rule
  for (const criteriaType of criteriaTypes) {
    const criterion1 = rule1.criteria[criteriaType];
    const criterion2 = rule2.criteria[criteriaType];

    if (criterion1 !== undefined && criterion1 !== null) {
      rule1SetCriteria.add(criteriaType);
    }
    if (criterion2 !== undefined && criterion2 !== null) {
      rule2SetCriteria.add(criteriaType);
    }
  }

  // Check if one rule has criteria that the other doesn't
  for (const criteriaType of rule1SetCriteria) {
    if (!rule2SetCriteria.has(criteriaType)) {
      // Rule1 has a criterion that Rule2 doesn't have
      return false;
    }
  }
  for (const criteriaType of rule2SetCriteria) {
    if (!rule1SetCriteria.has(criteriaType)) {
      // Rule2 has a criterion that Rule1 doesn't have
      return false;
    }
  }

  // If both rules have no valid criteria to compare, they don't overlap
  if (rule1SetCriteria.size === 0 && rule2SetCriteria.size === 0) {
    return false;
  }

  // For each criteria type, check if the rules overlap
  for (const criteriaType of criteriaTypes) {
    const criterion1 = rule1.criteria[criteriaType];
    const criterion2 = rule2.criteria[criteriaType];

    // If either rule doesn't specify this criteria, skip it
    // (undefined means not set, which is different from null or "Any")
    if (criterion1 === undefined || criterion2 === undefined) {
      continue;
    }

    // Handle null values - null means "not set" so skip this criterion
    if (criterion1 === null || criterion2 === null) {
      continue;
    }

    // At this point we have two non-null criteria to compare
    hasValidCriterion = true;

    // If either is "Any", they overlap on this criteria
    if (criterion1.type === "Any" || criterion2.type === "Any") {
      continue;
    }

    // Both are "Specific" - check if they have any common values
    const hasCommonValues = criterion1.values.some((value) =>
      criterion2.values.includes(value)
    );

    // If no common values for this criteria type, rules don't overlap
    if (!hasCommonValues) {
      return false;
    }
  }

  // If we never found any valid criteria to compare, rules don't overlap
  if (!hasValidCriterion) {
    return false;
  }

  // If we made it through all criteria types without finding a mismatch,
  // the rules overlap
  return true;
};

export type OverlapType = "conflict" | "redundancy";

export interface OverlapDetail {
  type: OverlapType;
  involvedEntityIds: string[];
  overlappingRuleIds: [string, string];
  entity1Name: string;
  entity2Name: string;
  rule1Index?: number;
  rule2Index?: number;
  summary: string;
}

/**
 * Find all overlapping assignments among individuals and teams
 * Distinguishes between conflicts (inter-entity) and redundancies (intra-entity)
 *
 * Uses team-based logic that handles all scenarios:
 * - If no teams exist: works like individual assignment
 * - If individual not on team: works like individual assignment
 * - If individual on team: inherits missing criteria from team
 *
 * @param individuals Array of individuals with rules
 * @param teams Array of teams with rules
 * @param activeCriteria Optional criteria filter
 * @returns Array of overlap details
 */
export const findOverlappingAssignments = (
  individuals: Individual[],
  teams: Team[],
  activeCriteria?: Record<string, boolean>
): {
  allOverlaps: OverlapDetail[];
  entityIdsWithConflicts: Set<string>;
  entityIdsWithRedundanciesOnly: Set<string>;
} => {
  const allOverlaps: OverlapDetail[] = [];
  const entityIdsWithConflicts = new Set<string>();
  const entityIdsWithRedundanciesOnly = new Set<string>();

  // Helper to create a flattened list of all rules with their entity info
  interface RuleWithEntity {
    rule: AssignmentRule;
    entityId: string;
    entityName: string;
    entityType: "team" | "individual";
    ruleIndex: number;
  }

  const allRules: RuleWithEntity[] = [];

  // Add team rules (always include teams if they exist)
  for (const team of teams) {
    if (!team.rules) continue;
    team.rules.forEach((rule, index) => {
      // Check if the team rule is complete
      if (!isRuleComplete(rule, activeCriteria)) {
        return; // Skip incomplete team rules
      }

      allRules.push({
        rule,
        entityId: team.id,
        entityName: team.name,
        entityType: "team",
        ruleIndex: index,
      });
    });
  }

  // Add individual rules with team inheritance
  for (const individual of individuals) {
    // If individual has no rules but belongs to a team, create rules from team
    if (individual.teamId && (!individual.rules || individual.rules.length === 0)) {
      const team = teams.find(t => t.id === individual.teamId);
      if (team && team.rules && team.rules.length > 0) {
        // Create individual rules from team rules
        team.rules.forEach((teamRule, index) => {
          // Check if the team rule is complete before using it for the individual
          if (!isRuleComplete(teamRule, activeCriteria)) {
            return; // Skip incomplete team rules
          }

          allRules.push({
            rule: teamRule, // Use team rule directly since individual has no rules
            entityId: individual.id,
            entityName: individual.name,
            entityType: "individual",
            ruleIndex: index,
          });
        });
      }
      continue; // Skip the normal rule processing for this individual
    }

    if (!individual.rules) continue;
    
    individual.rules.forEach((rule, index) => {
      // Apply team rule inheritance if individual belongs to a team
      let effectiveRule = rule;

      if (individual.teamId) {
        const team = teams.find(t => t.id === individual.teamId);
        if (team && team.rules && team.rules.length > 0) {
          // Create a merged rule where team values override individual values for each criterion
          const mergedCriteria = { ...rule.criteria };

          // For each team rule, apply overrides
          team.rules.forEach(teamRule => {
            Object.keys(teamRule.criteria).forEach(criteriaType => {
              const teamValue = teamRule.criteria[criteriaType as CriteriaTypeString];

              // Team value overrides individual value if team has a value for this criterion
              if (teamValue !== null && teamValue !== undefined) {
                mergedCriteria[criteriaType as CriteriaTypeString] = teamValue;
              }
            });
          });

          effectiveRule = {
            ...rule,
            criteria: mergedCriteria,
          };

        }
      }

      // Check if the effective rule (after inheritance) is complete
      // If the rule is still incomplete after inheritance, skip it
      if (!isRuleComplete(effectiveRule, activeCriteria)) {
        return;
      }

      allRules.push({
        rule: effectiveRule,
        entityId: individual.id,
        entityName: individual.name,
        entityType: "individual",
        ruleIndex: index,
      });
    });
  }

  // Debug: log all rules (commented out for production)
  // console.log("All rules for overlap detection:", JSON.stringify(allRules.map(r => ({
  //   entityName: r.entityName,
  //   entityType: r.entityType,
  //   rule: r.rule
  // })), null, 2));

  // Compare all rules pairwise
  for (let i = 0; i < allRules.length; i++) {
    for (let j = i + 1; j < allRules.length; j++) {
      const ruleWithEntity1 = allRules[i];
      const ruleWithEntity2 = allRules[j];

      // Skip if rules don't overlap
      if (!doRulesOverlap(ruleWithEntity1.rule, ruleWithEntity2.rule, activeCriteria)) {
        continue;
      }

      // Skip conflicts between a team and its own members
      const isTeamVsOwnMember =
        (ruleWithEntity1.entityType === "team" && ruleWithEntity2.entityType === "individual" &&
         individuals.find(ind => ind.id === ruleWithEntity2.entityId)?.teamId === ruleWithEntity1.entityId) ||
        (ruleWithEntity2.entityType === "team" && ruleWithEntity1.entityType === "individual" &&
         individuals.find(ind => ind.id === ruleWithEntity1.entityId)?.teamId === ruleWithEntity2.entityId);

      if (isTeamVsOwnMember) {
        continue;
      }

      // Determine if this is a conflict or redundancy
      const isSameEntity = ruleWithEntity1.entityId === ruleWithEntity2.entityId;
      const overlapType: OverlapType = isSameEntity ? "redundancy" : "conflict";

      // For individuals from different teams, create conflicts
      if (
        ruleWithEntity1.entityType === "individual" &&
        ruleWithEntity2.entityType === "individual"
      ) {
        const individual1 = individuals.find(
          (ind) => ind.id === ruleWithEntity1.entityId
        );
        const individual2 = individuals.find(
          (ind) => ind.id === ruleWithEntity2.entityId
        );

        // Only create overlap if they're from different teams (or one/both have no team)
        if (individual1?.teamId !== individual2?.teamId) {
          const overlap: OverlapDetail = {
            type: "conflict",
            involvedEntityIds: [ruleWithEntity1.entityId, ruleWithEntity2.entityId],
            overlappingRuleIds: [ruleWithEntity1.rule.id, ruleWithEntity2.rule.id],
            entity1Name: ruleWithEntity1.entityName,
            entity2Name: ruleWithEntity2.entityName,
            rule1Index: ruleWithEntity1.ruleIndex,
            rule2Index: ruleWithEntity2.ruleIndex,
            summary: `${ruleWithEntity1.entityName} (Rule ${ruleWithEntity1.ruleIndex + 1}) conflicts with ${ruleWithEntity2.entityName} (Rule ${ruleWithEntity2.ruleIndex + 1})`,
          };
          allOverlaps.push(overlap);
          entityIdsWithConflicts.add(ruleWithEntity1.entityId);
          entityIdsWithConflicts.add(ruleWithEntity2.entityId);
        }
      } else {
        // Standard overlap handling
        const overlap: OverlapDetail = {
          type: overlapType,
          involvedEntityIds: isSameEntity
            ? [ruleWithEntity1.entityId]
            : [ruleWithEntity1.entityId, ruleWithEntity2.entityId],
          overlappingRuleIds: [ruleWithEntity1.rule.id, ruleWithEntity2.rule.id],
          entity1Name: ruleWithEntity1.entityName,
          entity2Name: ruleWithEntity2.entityName,
          rule1Index: ruleWithEntity1.ruleIndex,
          rule2Index: ruleWithEntity2.ruleIndex,
          summary: isSameEntity
            ? `${ruleWithEntity1.entityName} has redundant rules: Rule ${ruleWithEntity1.ruleIndex + 1} and Rule ${ruleWithEntity2.ruleIndex + 1}`
            : `${ruleWithEntity1.entityName} (Rule ${ruleWithEntity1.ruleIndex + 1}) conflicts with ${ruleWithEntity2.entityName} (Rule ${ruleWithEntity2.ruleIndex + 1})`,
        };
        allOverlaps.push(overlap);

        if (overlapType === "conflict") {
          entityIdsWithConflicts.add(ruleWithEntity1.entityId);
          entityIdsWithConflicts.add(ruleWithEntity2.entityId);
          // Remove from redundancies if they were there
          entityIdsWithRedundanciesOnly.delete(ruleWithEntity1.entityId);
          entityIdsWithRedundanciesOnly.delete(ruleWithEntity2.entityId);
        } else {
          // Only add to redundancies if not already in conflicts
          if (!entityIdsWithConflicts.has(ruleWithEntity1.entityId)) {
            entityIdsWithRedundanciesOnly.add(ruleWithEntity1.entityId);
          }
        }
      }
    }
  }

  return {
    allOverlaps,
    entityIdsWithConflicts,
    entityIdsWithRedundanciesOnly,
  };
};