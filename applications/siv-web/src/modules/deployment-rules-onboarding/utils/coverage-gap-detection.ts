import type {
  AssignmentRule,
  CoverageGap,
  CriteriaTypeString,
  Individual,
  Team,
  GeographyRegion,
} from "../types/lead-assignment";

/**
 * Helper function to generate Cartesian product of arrays
 * Uses an iterative approach to avoid recursion depth issues
 */
const generateCartesianProduct = <T>(arrays: T[][]): T[][] => {
  if (arrays.length === 0) return [];
  if (arrays.length === 1) return arrays[0].map((item) => [item]);

  let result = arrays[0].map((item) => [item]);

  for (let i = 1; i < arrays.length; i++) {
    const currentArray = arrays[i];
    const tempResult: T[][] = [];

    for (const item of currentArray) {
      for (const existingResult of result) {
        tempResult.push([...existingResult, item]);
      }
    }

    result = tempResult;
  }

  return result;
};

/**
 * Check if a scenario matches a rule
 * 
 * @param scenario The lead scenario to check
 * @param rule The rule to check against
 * @param geographyRegions Optional geography regions for RoW handling
 * @returns true if the scenario matches the rule
 */
const doesScenarioMatchRule = (
  scenario: Record<CriteriaTypeString, string>,
  rule: AssignmentRule,
  geographyRegions?: GeographyRegion[]
): boolean => {
  for (const [criteriaType, scenarioValue] of Object.entries(scenario)) {
    const ruleCriterion = rule.criteria[criteriaType as CriteriaTypeString];
    
    // If rule doesn't specify this criteria, skip it
    if (ruleCriterion === undefined) {
      continue;
    }
    
    // If rule has null for this criteria, it doesn't match
    if (ruleCriterion === null) {
      return false;
    }
    
    // If rule has "Any" for this criteria, it matches
    if (ruleCriterion.type === "Any") {
      continue;
    }
    
    // Special handling for Rest of World geography
    if (criteriaType === "geography" && geographyRegions) {
      // Check if scenario is Rest of World
      const isRestOfWorldScenario = geographyRegions.some(
        (r) => r.id === scenarioValue && (r.predefinedType === "ALL_OTHER_COUNTRIES" || r.isAllOthers)
      );
      
      if (isRestOfWorldScenario) {
        // Check if rule includes RoW
        const ruleHasRestOfWorld = ruleCriterion.values.some((value) =>
          geographyRegions.some(
            (r) => r.id === value && (r.predefinedType === "ALL_OTHER_COUNTRIES" || r.isAllOthers)
          )
        );
        
        if (!ruleHasRestOfWorld) {
          return false;
        }
        continue;
      }
    }
    
    // Rule has "Specific" values - check if scenario value is included
    if (!ruleCriterion.values.includes(scenarioValue)) {
      return false;
    }
  }
  
  // Scenario matches all criteria in the rule
  return true;
};

/**
 * Collect all distinct values for a criteria type from all entities
 */
const collectDistinctValues = (
  entities: Array<{ rules: AssignmentRule[] }>,
  criteriaType: CriteriaTypeString
): Set<string> => {
  const values = new Set<string>();
  
  for (const entity of entities) {
    if (!entity.rules) continue;
    
    for (const rule of entity.rules) {
      const criterion = rule.criteria[criteriaType];
      if (criterion && criterion.type === "Specific") {
        criterion.values.forEach(value => values.add(value));
      }
    }
  }
  
  return values;
};

/**
 * Detect coverage gaps in the assignment configuration
 * 
 * @param individuals Array of individuals with rules
 * @param teams Array of teams with rules
 * @param activeCriteria Record of which criteria types are active
 * @param allPossibleValues Optional predefined values for each criteria type
 * @returns Array of coverage gaps
 */
export const detectCoverageGaps = (
  individuals: Individual[],
  teams: Team[],
  activeCriteria: Record<CriteriaTypeString, boolean>,
  allPossibleValues?: Partial<Record<CriteriaTypeString, Set<string>>>,
  geographyRegions?: GeographyRegion[]
): CoverageGap[] => {
  // Get active criteria types
  const activeCriteriaTypes = Object.entries(activeCriteria)
    .filter(([_, isActive]) => isActive)
    .map(([type, _]) => type as CriteriaTypeString);
  
  if (activeCriteriaTypes.length === 0) {
    return [];
  }
  
  // Collect all rules from individuals and teams
  const allRules: AssignmentRule[] = [];
  
  for (const individual of individuals) {
    if (individual.rules) {
      allRules.push(...individual.rules);
    }
  }
  
  for (const team of teams) {
    if (team.rules) {
      allRules.push(...team.rules);
    }
  }
  
  // If no rules defined, everything is a gap
  if (allRules.length === 0) {
    return [{
      id: "gap-no-rules",
      description: "No assignment rules defined",
      missingCombination: activeCriteriaTypes.map(type => ({
        criteriaType: type,
        value: "Any",
        displayValue: "Any value",
      })),
    }];
  }
  
  // Build the map of all possible values for each criteria type
  const possibleValuesMap: Record<CriteriaTypeString, string[]> = {} as any;
  
  for (const criteriaType of activeCriteriaTypes) {
    if (allPossibleValues?.[criteriaType]) {
      possibleValuesMap[criteriaType] = Array.from(allPossibleValues[criteriaType]!);
    } else {
      // Collect values from all rules
      const allEntities = [...individuals, ...teams];
      const values = collectDistinctValues(allEntities, criteriaType);
      
      // Special handling for dayOfMonth
      if (criteriaType === "dayOfMonth") {
        possibleValuesMap[criteriaType] = ["Odd Days", "Even Days"];
      } else if (values.size > 0) {
        possibleValuesMap[criteriaType] = Array.from(values);
      } else {
        // No values found for this criteria type - skip it
        continue;
      }
    }
  }
  
  // Prepare arrays for cartesian product
  const valueArrays: string[][] = [];
  const criteriaTypesForProduct: CriteriaTypeString[] = [];
  
  for (const criteriaType of activeCriteriaTypes) {
    if (possibleValuesMap[criteriaType]?.length > 0) {
      valueArrays.push(possibleValuesMap[criteriaType]);
      criteriaTypesForProduct.push(criteriaType);
    }
  }
  
  if (valueArrays.length === 0) {
    return [];
  }
  
  // Generate all possible combinations
  const combinations = generateCartesianProduct(valueArrays);
  const gaps: CoverageGap[] = [];
  
  // Check each combination
  for (const combination of combinations) {
    // Create scenario object
    const scenario: Record<CriteriaTypeString, string> = {};
    for (let i = 0; i < criteriaTypesForProduct.length; i++) {
      scenario[criteriaTypesForProduct[i]] = combination[i];
    }
    
    // Check if any rule matches this scenario
    const isCovered = allRules.some(rule => doesScenarioMatchRule(scenario, rule, geographyRegions));
    
    if (!isCovered) {
      gaps.push({
        id: `gap-${gaps.length + 1}`,
        description: Object.entries(scenario)
          .map(([type, value]) => `${type}: ${value}`)
          .join(", "),
        missingCombination: Object.entries(scenario).map(([type, value]) => ({
          criteriaType: type,
          value,
          displayValue: value,
        })),
      });
    }
  }
  
  return gaps;
};