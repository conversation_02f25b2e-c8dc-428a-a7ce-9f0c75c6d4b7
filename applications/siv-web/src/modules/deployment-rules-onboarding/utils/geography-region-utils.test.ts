import { describe, expect, it } from "vitest";
import {
  ALL_STATES,
  areScopesIdentical,
  calculateDynamicSystemRegions,
  checkScopesOverlap,
  createComplementaryNonUSCountriesScope,
  createComplementaryUSStatesScope,
  createTemplateRegions,
  findOverlappingRegion,
  type GeographicScope,
  getEffectiveScope,
  getRegionDescription,
  isLocationCoveredByScope,
  SYSTEM_REGION_TYPES,
  TEMPLATE_REGION_TYPES,
} from "./geography-region-utils";
import type { GeographyRegion } from "../types/lead-assignment";

// Using real libraries instead of mocks for more realistic tests

describe("getEffectiveScope", () => {
  it("should handle a single non-US country", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "France Only",
      countries: ["FR"],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { FR: true },
      usStates: undefined,
    });
  });

  it("should handle multiple non-US countries", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "Europe",
      countries: ["FR", "DE", "IT", "ES", "GB"],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { FR: true, DE: true, IT: true, ES: true, GB: true },
      usStates: undefined,
    });
  });

  it("should treat US with no usStates as ALL_STATES", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "All US",
      countries: ["US"],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { US: true },
      usStates: ALL_STATES,
    });
  });

  it("should treat US with empty usStates array as ALL_STATES", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "All US",
      countries: ["US"],
      usStates: [],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { US: true },
      usStates: ALL_STATES,
    });
  });

  it("should handle US with specific states", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "West Coast",
      countries: ["US"],
      usStates: ["CA", "OR", "WA"],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    });
  });

  it("should handle mixed US and non-US countries", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "North America",
      countries: ["US", "CA"],
      usStates: ["CA", "NY", "TX"],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { US: true, CA: true },
      usStates: new Set(["CA", "NY", "TX"]),
    });
  });

  it("should handle USA_ALL_OTHER_STATES system region", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "USA (All States)",
      countries: ["US"],
      usStates: [],
      isPredefined: true,
      predefinedType: SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: { US: true },
      usStates: ALL_STATES,
    });
  });

  it("should handle ALL_OTHER_COUNTRIES system region", () => {
    const nonUSCountries = [
      "CA",
      "FR",
      "DE",
      "IT",
      "ES",
      "GB",
      "JP",
      "CN",
      "AU",
    ];
    const region: GeographyRegion = {
      id: "1",
      name: "All Non-US Countries",
      countries: nonUSCountries,
      isPredefined: true,
      predefinedType: SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    };

    const scope = getEffectiveScope(region);

    const expected: GeographicScope = {
      countries: {},
      usStates: undefined,
    };
    nonUSCountries.forEach((c) => (expected.countries[c] = true));

    expect(scope).toEqual(expected);
  });

  it("should handle empty countries array", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "Empty Region",
      countries: [],
      isPredefined: false,
    };

    const scope = getEffectiveScope(region);

    expect(scope).toEqual({
      countries: {},
      usStates: undefined,
    });
  });
});

describe("areScopesIdentical", () => {
  it("should return true for the exact same countries", () => {
    const scopeA: GeographicScope = {
      countries: { FR: true, DE: true },
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { FR: true, DE: true },
      usStates: undefined,
    };

    expect(areScopesIdentical(scopeA, scopeB)).toBe(true);
  });

  it("should return false for different countries", () => {
    const scopeA: GeographicScope = {
      countries: { FR: true, DE: true },
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { JP: true, CN: true },
      usStates: undefined,
    };

    expect(areScopesIdentical(scopeA, scopeB)).toBe(false);
  });

  it("should return true for identical US states", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    expect(areScopesIdentical(scopeA, scopeB)).toBe(true);
  });

  it("should return false for different US states", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["NY", "NJ", "FL"]),
    };

    expect(areScopesIdentical(scopeA, scopeB)).toBe(false);
  });
});

describe("checkScopesOverlap", () => {
  it("should return false for disjoint countries", () => {
    const scopeA: GeographicScope = {
      countries: { FR: true, DE: true },
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { JP: true, CN: true },
      usStates: undefined,
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(false);
  });

  it("should return false for US with disjoint states", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["NY", "NJ", "FL"]),
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(false);
  });

  it("should return true for identical non-US country", () => {
    const scopeA: GeographicScope = {
      countries: { FR: true },
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { FR: true },
      usStates: undefined,
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should return true when one scope includes a country that the other has", () => {
    const scopeA: GeographicScope = {
      countries: { FR: true, DE: true, IT: true },
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { FR: true, ES: true, GB: true },
      usStates: undefined,
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should return true when both have US with ALL_STATES", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: ALL_STATES,
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: ALL_STATES,
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should return true when one has US with ALL_STATES and the other has specific states", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: ALL_STATES,
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "NY", "TX"]),
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should return true when US has intersecting state sets", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "NY", "TX", "FL"]),
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["NY", "PA", "OH", "FL"]),
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should return true for identical US specific state sets", () => {
    const scopeA: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "NY", "TX"]),
    };

    const scopeB: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "NY", "TX"]),
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(true);
  });

  it("should handle empty scopes", () => {
    const scopeA: GeographicScope = {
      countries: {},
      usStates: undefined,
    };

    const scopeB: GeographicScope = {
      countries: { FR: true },
      usStates: undefined,
    };

    expect(checkScopesOverlap(scopeA, scopeB)).toBe(false);
    expect(checkScopesOverlap(scopeB, scopeA)).toBe(false);

    const emptyB: GeographicScope = {
      countries: {},
      usStates: undefined,
    };

    expect(checkScopesOverlap(scopeA, emptyB)).toBe(false);
  });
});

describe("isLocationCoveredByScope", () => {
  it("should return true for a country in the scope", () => {
    const scope: GeographicScope = {
      countries: { FR: true, DE: true, US: true },
      usStates: undefined,
    };

    expect(isLocationCoveredByScope(scope, "FR")).toBe(true);
  });

  it("should return false for a country not in the scope", () => {
    const scope: GeographicScope = {
      countries: { FR: true, DE: true },
      usStates: undefined,
    };

    expect(isLocationCoveredByScope(scope, "US")).toBe(false);
  });

  it("should return true for a US state in the scope", () => {
    const scope: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    expect(isLocationCoveredByScope(scope, "US", "CA")).toBe(true);
  });

  it("should return false for a US state not in the scope", () => {
    const scope: GeographicScope = {
      countries: { US: true },
      usStates: new Set(["CA", "OR", "WA"]),
    };

    expect(isLocationCoveredByScope(scope, "US", "NY")).toBe(false);
  });

  it("should return true for any US state when ALL_STATES is set", () => {
    const scope: GeographicScope = {
      countries: { US: true },
      usStates: ALL_STATES,
    };

    expect(isLocationCoveredByScope(scope, "US", "TX")).toBe(true);
  });
});

describe("createComplementaryUSStatesScope", () => {
  it("should return a scope with uncovered US states", () => {
    const scopes: GeographicScope[] = [
      {
        countries: { US: true },
        usStates: new Set(["CA", "OR", "WA"]),
      },
      {
        countries: { US: true },
        usStates: new Set(["TX", "FL"]),
      },
    ];

    const complementaryScope = createComplementaryUSStatesScope(scopes);

    expect(complementaryScope).not.toBeNull();
    expect(complementaryScope?.countries).toEqual({ US: true });
    expect(complementaryScope?.usStates).toBeInstanceOf(Set);

    // The uncovered states should include some well-known US states
    // that aren't in our explicitly covered lists
    if (complementaryScope?.usStates instanceof Set) {
      // Verify that we don't have states we excluded
      expect(complementaryScope.usStates.has("CA")).toBe(false);
      expect(complementaryScope.usStates.has("TX")).toBe(false);

      // Verify that the set has some states (the actual states will depend
      // on the country-state-city library data)
      expect(complementaryScope.usStates.size).toBeGreaterThan(0);
    }
  });

  it("should return null when all US states are covered", () => {
    const scopes: GeographicScope[] = [
      {
        countries: { US: true },
        usStates: ALL_STATES,
      },
    ];

    const complementaryScope = createComplementaryUSStatesScope(scopes);

    expect(complementaryScope).toBeNull();
  });
});

describe("createComplementaryNonUSCountriesScope", () => {
  it("should return a scope with uncovered non-US countries", () => {
    const scopes: GeographicScope[] = [
      {
        countries: { FR: true, DE: true },
        usStates: undefined,
      },
    ];

    const complementaryScope = createComplementaryNonUSCountriesScope(scopes);

    expect(complementaryScope).not.toBeNull();

    // The excluded countries shouldn't be in the result
    expect(complementaryScope?.countries).not.toHaveProperty("FR");
    expect(complementaryScope?.countries).not.toHaveProperty("DE");

    // Should have some countries (the actual countries will depend
    // on the country-state-city library data)
    const countryCount = Object.keys(
      complementaryScope?.countries || {},
    ).length;
    expect(countryCount).toBeGreaterThan(0);
  });

  it("should return countries when not all countries are covered", () => {
    // In this test we're just testing a subset of countries, so with the real library
    // we expect to get back countries not in our list
    const someCountries: Record<string, true> = {};
    for (const country of [
      "CA",
      "FR",
      "DE",
      "IT",
      "ES",
      "GB",
      "JP",
      "CN",
      "AU",
    ]) {
      someCountries[country] = true;
    }

    const scopes: GeographicScope[] = [
      {
        countries: someCountries,
        usStates: undefined,
      },
    ];

    const complementaryScope = createComplementaryNonUSCountriesScope(scopes);

    // With the real library, we should get back countries not in our list
    expect(complementaryScope).not.toBeNull();
    expect(
      Object.keys(complementaryScope?.countries || {}).length,
    ).toBeGreaterThan(0);
  });
});

describe("calculateDynamicSystemRegions", () => {
  it("should generate system regions when no user regions exist", () => {
    const userRegions: GeographyRegion[] = [];

    const result = calculateDynamicSystemRegions(userRegions);

    // With no user regions, the function should return system regions
    // for all countries (which should be all of them)
    expect(result.length).toBeGreaterThan(0);

    // Should only generate the ALL_OTHER_COUNTRIES region
    expect(result.length).toBe(1);
    expect(result[0].predefinedType).toBe(
      SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(result[0].isAllOthers).toBe(true);

    // System regions should be marked as isPredefined
    expect(result[0].isPredefined).toBe(true);
  });

  it("should generate USA_ALL_OTHER_STATES and ALL_OTHER_COUNTRIES when user regions cover some but not all US states", () => {
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "West Coast",
        countries: ["US"],
        usStates: ["CA", "OR", "WA"],
        isPredefined: false,
      },
      {
        id: "2",
        name: "East Coast",
        countries: ["US"],
        usStates: ["NY", "NJ", "FL"],
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should generate both system regions
    expect(result.length).toBe(2);

    // Should have USA_ALL_OTHER_STATES
    const usaOtherStatesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
    );
    expect(usaOtherStatesRegion).toBeDefined();
    expect(usaOtherStatesRegion?.isPredefined).toBe(true);
    expect(usaOtherStatesRegion?.countries).toEqual(["US"]);
    expect(usaOtherStatesRegion?.usStates).toBeDefined();

    // Should contain states not in West or East Coast
    expect(usaOtherStatesRegion?.usStates).toContain("TX");
    expect(usaOtherStatesRegion?.usStates).toContain("IL");
    expect(usaOtherStatesRegion?.usStates).not.toContain("CA");
    expect(usaOtherStatesRegion?.usStates).not.toContain("NY");

    // Should also have ALL_OTHER_COUNTRIES for non-US countries
    const allOtherCountriesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(allOtherCountriesRegion).toBeDefined();
    expect(allOtherCountriesRegion?.isPredefined).toBe(true);
    expect(allOtherCountriesRegion?.isAllOthers).toBe(true);
    expect(allOtherCountriesRegion?.countries).not.toContain("US");
    expect(allOtherCountriesRegion?.countries.length).toBeGreaterThan(0);
  });

  it("should not generate USA_ALL_OTHER_STATES when a user region covers all US", () => {
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "All USA",
        countries: ["US"],
        usStates: [],
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should not have USA_ALL_OTHER_STATES since user defined region covers all US
    expect(
      result.some(
        (r) => r.predefinedType === SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
      ),
    ).toBe(false);

    // Should only have ALL_OTHER_COUNTRIES
    expect(result.length).toBe(1);
    expect(result[0].predefinedType).toBe(
      SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(result[0].isAllOthers).toBe(true);
  });

  it("should generate ALL_OTHER_COUNTRIES when user regions cover some but not all non-US countries", () => {
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "Europe",
        countries: ["FR", "DE", "IT"],
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should only generate the ALL_OTHER_COUNTRIES region
    expect(result.length).toBe(1);

    // Find the ALL_OTHER_COUNTRIES region in the result
    const otherCountriesRegion = result[0];
    expect(otherCountriesRegion.predefinedType).toBe(
      SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(otherCountriesRegion.isPredefined).toBe(true);
    expect(otherCountriesRegion.isAllOthers).toBe(true);

    // Should contain countries not in Europe
    expect(otherCountriesRegion.countries).toContain("CA");
    expect(otherCountriesRegion.countries).toContain("JP");
    expect(otherCountriesRegion.countries).not.toContain("FR");
    expect(otherCountriesRegion.countries).not.toContain("DE");
  });

  it("should still generate ALL_OTHER_COUNTRIES when not all countries are covered", () => {
    // With a subset of countries and the real library data
    const someNonUSCountries = [
      "CA",
      "FR",
      "DE",
      "IT",
      "ES",
      "GB",
      "JP",
      "CN",
      "AU",
    ];
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "Some Countries",
        countries: someNonUSCountries,
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should only have ALL_OTHER_COUNTRIES since our specific list doesn't cover all countries
    expect(result.length).toBe(1);
    expect(result[0].predefinedType).toBe(
      SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(result[0].isAllOthers).toBe(true);

    // Should not contain any of the countries in our user region
    for (const country of someNonUSCountries) {
      expect(result[0].countries).not.toContain(country);
    }
  });

  it("should handle mixed scenarios correctly with some US states and some non-US countries", () => {
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "West Coast",
        countries: ["US"],
        usStates: ["CA", "OR", "WA"],
        isPredefined: false,
      },
      {
        id: "2",
        name: "Europe",
        countries: ["FR", "DE", "IT"],
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should have two dynamic regions
    expect(result.length).toBe(2);

    // Should have USA_ALL_OTHER_STATES
    const usStatesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
    );
    expect(usStatesRegion).toBeDefined();
    expect(usStatesRegion?.isPredefined).toBe(true);
    expect(usStatesRegion?.countries).toEqual(["US"]);
    expect(usStatesRegion?.usStates?.length).toBeGreaterThan(0);
    expect(usStatesRegion?.usStates).not.toContain("CA");
    expect(usStatesRegion?.usStates).not.toContain("OR");
    expect(usStatesRegion?.usStates).not.toContain("WA");

    // Should have ALL_OTHER_COUNTRIES
    const otherCountriesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(otherCountriesRegion).toBeDefined();
    expect(otherCountriesRegion?.isPredefined).toBe(true);
    expect(otherCountriesRegion?.isAllOthers).toBe(true);
    expect(otherCountriesRegion?.countries.length).toBeGreaterThan(0);
    expect(otherCountriesRegion?.countries).not.toContain("FR");
    expect(otherCountriesRegion?.countries).not.toContain("DE");
    expect(otherCountriesRegion?.countries).not.toContain("IT");
    expect(otherCountriesRegion?.countries).not.toContain("US");
  });

  it("should generate USA_ALL_OTHER_STATES even when many (but not all) US states are selected", () => {
    // Create a region with a large number of US states (12 as seen in the screenshot)
    const manyStates = [
      "AL",
      "AK",
      "AZ",
      "AR",
      "CA",
      "CO",
      "CT",
      "DE",
      "FL",
      "GA",
      "HI",
      "ID",
    ];
    const userRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "US - Midwest",
        countries: ["US"],
        usStates: manyStates,
        isPredefined: false,
      },
    ];

    const result = calculateDynamicSystemRegions(userRegions);

    // Should still generate USA_ALL_OTHER_STATES even with many states selected
    expect(result.length).toBe(2);

    // Should have USA_ALL_OTHER_STATES
    const usStatesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
    );
    expect(usStatesRegion).toBeDefined();
    expect(usStatesRegion?.isPredefined).toBe(true);
    expect(usStatesRegion?.countries).toEqual(["US"]);

    // Should have remaining states not in the list
    expect(usStatesRegion?.usStates).toBeDefined();
    // Should NOT contain any of the states in our user region
    for (const state of manyStates) {
      expect(usStatesRegion?.usStates).not.toContain(state);
    }
    // Should contain some other states not in our list
    expect(usStatesRegion?.usStates).toContain("NY"); // New York should be included
    expect(usStatesRegion?.usStates).toContain("TX"); // Texas should be included

    // Should also have ALL_OTHER_COUNTRIES
    const allOtherCountriesRegion = result.find(
      (r) => r.predefinedType === SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
    );
    expect(allOtherCountriesRegion).toBeDefined();
  });
});

describe("findOverlappingRegion", () => {
  it("should return null when there is no overlap", () => {
    const regionToCheck: GeographyRegion = {
      id: "1",
      name: "West Coast",
      countries: ["US"],
      usStates: ["CA", "OR", "WA"],
      isPredefined: false,
    };

    const existingRegions: GeographyRegion[] = [
      {
        id: "2",
        name: "East Coast",
        countries: ["US"],
        usStates: ["NY", "NJ", "FL"],
        isPredefined: false,
      },
      {
        id: "3",
        name: "Europe",
        countries: ["FR", "DE", "IT"],
        isPredefined: false,
      },
    ];

    expect(findOverlappingRegion(regionToCheck, existingRegions)).toBeNull();
  });

  it("should find a region with overlapping US states", () => {
    const regionToCheck: GeographyRegion = {
      id: "1",
      name: "Pacific Coast",
      countries: ["US"],
      usStates: ["CA", "OR", "WA"],
      isPredefined: false,
    };

    const existingRegions: GeographyRegion[] = [
      {
        id: "2",
        name: "California & Nevada",
        countries: ["US"],
        usStates: ["CA", "NV"],
        isPredefined: false,
      },
    ];

    const overlappingRegion = findOverlappingRegion(
      regionToCheck,
      existingRegions,
    );
    expect(overlappingRegion).not.toBeNull();
    expect(overlappingRegion?.name).toBe("California & Nevada");
  });

  it("should find a region with overlapping countries", () => {
    const regionToCheck: GeographyRegion = {
      id: "1",
      name: "Western Europe",
      countries: ["FR", "DE", "ES"],
      isPredefined: false,
    };

    const existingRegions: GeographyRegion[] = [
      {
        id: "2",
        name: "France and Italy",
        countries: ["FR", "IT"],
        isPredefined: false,
      },
    ];

    const overlappingRegion = findOverlappingRegion(
      regionToCheck,
      existingRegions,
    );
    expect(overlappingRegion).not.toBeNull();
    expect(overlappingRegion?.name).toBe("France and Italy");
  });

  it("should not find overlap with itself", () => {
    const regionToCheck: GeographyRegion = {
      id: "1",
      name: "Western Europe",
      countries: ["FR", "DE", "ES"],
      isPredefined: false,
    };

    const existingRegions: GeographyRegion[] = [
      {
        id: "1", // Same ID
        name: "Western Europe",
        countries: ["FR", "DE", "ES"],
        isPredefined: false,
      },
    ];

    expect(findOverlappingRegion(regionToCheck, existingRegions)).toBeNull();
  });

  it("should return null for empty existing regions", () => {
    const regionToCheck: GeographyRegion = {
      id: "1",
      name: "Western Europe",
      countries: ["FR", "DE", "ES"],
      isPredefined: false,
    };

    expect(findOverlappingRegion(regionToCheck, [])).toBeNull();
  });
});

describe("getRegionDescription", () => {
  it("should format USA_ALL_STATES template region", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "USA (All States)",
      countries: ["US"],
      usStates: [],
      isPredefined: false,
      predefinedType: TEMPLATE_REGION_TYPES.USA_ALL_STATES,
    };

    const description = getRegionDescription(region);

    expect(description).toBe(
      "All United States (all 50 states and territories)",
    );
  });

  it("should format ALL_NON_US_COUNTRIES template region", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "All Non-US Countries",
      countries: ["CA", "FR", "DE", "IT"], // Mock countries
      isPredefined: false,
      predefinedType: TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES,
    };

    const description = getRegionDescription(region);

    expect(description).toBe(
      "All countries worldwide except the United States",
    );
  });

  it("should format dynamic USA_ALL_OTHER_STATES system region", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "All Other US States/Territories",
      countries: ["US"],
      usStates: ["TX", "FL", "NY"],
      isPredefined: true,
      predefinedType: SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
    };

    const description = getRegionDescription(region);

    // Should format like a regular US states region
    expect(description).toBe("US: Texas, Florida, New York");
  });

  it("should format ALL_OTHER_COUNTRIES system region", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "All Other Countries/Regions",
      countries: ["CA", "FR", "DE", "IT"], // Sample countries
      isPredefined: true,
      predefinedType: SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
      isAllOthers: true,
    };

    const description = getRegionDescription(region);

    // Should show a list of countries
    expect(description).toBe("Canada, France, Germany, Italy");
  });

  it("should format a US-only region with few states", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "US West Coast",
      countries: ["US"],
      usStates: ["CA", "OR", "WA"],
      isPredefined: false,
    };

    const description = getRegionDescription(region);

    expect(description).toBe("US: California, Oregon, Washington");
  });

  it("should format a region with few countries", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "Europe",
      countries: ["FR", "DE", "IT"],
      isPredefined: false,
    };

    const description = getRegionDescription(region);

    expect(description).toBe("France, Germany, Italy");
  });

  it("should format a region with many countries", () => {
    const region: GeographyRegion = {
      id: "1",
      name: "Global",
      countries: ["CA", "FR", "DE", "IT", "ES", "GB"],
      isPredefined: false,
    };

    const description = getRegionDescription(region);

    // Should show first 3 countries and indicate there are 3 more
    expect(description).toContain("and 3 more");
  });
});

describe("Validation Logic", () => {
  it("should detect when a user tries to create a region that overlaps with an existing one", () => {
    const existingRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "France Only",
        countries: ["FR"],
        isPredefined: false,
      },
      {
        id: "2",
        name: "West Coast",
        countries: ["US"],
        usStates: ["CA", "OR", "WA"],
        isPredefined: false,
      },
    ];

    // Try to create a Europe region that contains France
    const newRegion: GeographyRegion = {
      id: "3",
      name: "Europe",
      countries: ["FR", "DE", "IT", "ES"],
      isPredefined: false,
    };

    const overlapping = findOverlappingRegion(newRegion, existingRegions);
    expect(overlapping).not.toBeNull();
    expect(overlapping?.name).toBe("France Only");
  });

  it("should allow creating a region that doesn't overlap with existing ones", () => {
    const existingRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "France Only",
        countries: ["FR"],
        isPredefined: false,
      },
      {
        id: "2",
        name: "West Coast",
        countries: ["US"],
        usStates: ["CA", "OR", "WA"],
        isPredefined: false,
      },
    ];

    // Try to create a non-overlapping region
    const newRegion: GeographyRegion = {
      id: "3",
      name: "Japan Only",
      countries: ["JP"],
      isPredefined: false,
    };

    expect(findOverlappingRegion(newRegion, existingRegions)).toBeNull();
  });

  it("should detect when a user tries to edit a region to include states that overlap with another region", () => {
    const existingRegions: GeographyRegion[] = [
      {
        id: "1",
        name: "East Coast",
        countries: ["US"],
        usStates: ["NY", "NJ", "FL"],
        isPredefined: false,
      },
      {
        id: "2",
        name: "West Coast",
        countries: ["US"],
        usStates: ["CA", "OR", "WA"],
        isPredefined: false,
      },
    ];

    // Try to edit East Coast to include California
    const editedRegion: GeographyRegion = {
      id: "1", // Same ID as East Coast
      name: "East Coast Plus",
      countries: ["US"],
      usStates: ["NY", "NJ", "FL", "CA"], // Added CA which is in West Coast
      isPredefined: false,
    };

    // Filter out the region being edited from the check
    const otherRegions = existingRegions.filter(
      (r) => r.id !== editedRegion.id,
    );

    const overlapping = findOverlappingRegion(editedRegion, otherRegions);
    expect(overlapping).not.toBeNull();
    expect(overlapping?.name).toBe("West Coast");
  });
});
