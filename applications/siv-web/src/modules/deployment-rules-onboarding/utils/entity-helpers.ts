/**
 * Pure functions for working with entities (individuals and teams)
 */

import type {
  Individual,
  Team,
  Exception,
  CriteriaTypeString,
} from "../types/lead-assignment";
import { getCriteriaStorageKey } from "./criteria-helpers";

/**
 * Updates an individual with new criteria values
 */
export function updateIndividualCriteria(
  individual: Individual,
  criteriaType: CriteriaTypeString,
  values: string[],
): Individual {
  const storageKey = getCriteriaStorageKey(criteriaType);

  return {
    ...individual,
    criteria: {
      ...(individual.criteria || {}),
      [storageKey]: values,
    },
  };
}

/**
 * Updates a team with new criteria values
 */
export function updateTeamCriteria(
  team: Team,
  criteriaType: CriteriaTypeString,
  values: string[],
): Team {
  const storageKey = getCriteriaStorageKey(criteriaType);

  return {
    ...team,
    criteria: {
      ...(team.criteria || {}),
      [storageKey]: values,
    },
  };
}

/**
 * Updates an individual with new exceptions
 */
export function updateIndividualExceptions(
  individual: Individual,
  exceptions: Exception[],
): Individual {
  return {
    ...individual,
    exceptions,
  };
}

/**
 * Updates a team with new exceptions
 */
export function updateTeamExceptions(
  team: Team,
  exceptions: Exception[],
): Team {
  return {
    ...team,
    exceptions,
  };
}

/**
 * Adds a new exception to an entity (individual or team)
 */
export function addException<T extends Individual | Team>(
  entity: T,
  exception: Exception,
): T {
  const existingExceptions = entity.exceptions || [];

  // Check if exception already exists
  const existingIndex = existingExceptions.findIndex(
    (e) => e.id === exception.id,
  );

  // If it exists, update it, otherwise add it
  const updatedExceptions =
    existingIndex >= 0
      ? existingExceptions.map((e, i) => (i === existingIndex ? exception : e))
      : [...existingExceptions, exception];

  return {
    ...entity,
    exceptions: updatedExceptions,
  };
}

/**
 * Updates or adds an exception in an entity's list of exceptions
 */
export function updateException<T extends Individual | Team>(
  entity: T,
  exception: Exception,
): T {
  // If the exception is marked for deletion, filter it out
  if (exception._toBeDeleted) {
    return {
      ...entity,
      exceptions: entity.exceptions.filter((e) => e.id !== exception.id),
    };
  }

  return addException(entity, exception);
}

/**
 * Removes an exception from an entity
 */
export function removeException<T extends Individual | Team>(
  entity: T,
  exceptionId: string,
): T {
  return {
    ...entity,
    exceptions: entity.exceptions.filter((e) => e.id !== exceptionId),
  };
}

/**
 * Returns individuals that belong to a specific team
 */
export function getTeamMembers(
  teamId: string,
  individuals: Individual[],
): Individual[] {
  return individuals.filter((individual) => individual.teamId === teamId);
}

/**
 * Returns individuals that don't belong to any team
 */
export function getStandaloneIndividuals(
  individuals: Individual[],
): Individual[] {
  return individuals.filter((individual) => !individual.teamId);
}
