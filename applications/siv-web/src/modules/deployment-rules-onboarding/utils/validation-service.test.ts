import { describe, it, expect } from "vitest";
import {
  validateAssignmentConfiguration,
  getValidationSummary,
  type ValidationConfig,
  type RuleWithUnsetCriteria,
} from "./validation-service";
import type { Individual, Team } from "../types/lead-assignment";

describe("validateAssignmentConfiguration v2", () => {
  const baseConfig: ValidationConfig = {
    individuals: [],
    teams: [],
    activeCriteria: { geography: true, eventType: true },
    assignmentStrategy: "team",
    geographyRegions: [
      { id: "usa", name: "United States", countries: ["US"] },
      { id: "canada", name: "Canada", countries: ["CA"] },
    ],
    roomCountRanges: [
      { id: "small", name: "1-50", condition: "range", minValue: 1, maxValue: 50 },
      { id: "large", name: "51+", condition: "greater", minValue: 51 },
    ],
    gapsAcknowledged: false,
  };

  describe("conflict detection", () => {
    it("should detect conflicts between teams", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding", "Corporate"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasConflicts).toBe(true);
      expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
      expect(result.entityIdsWithConflicts.has("team2")).toBe(true);
      expect(result.allOverlaps).toHaveLength(1);
      expect(result.allOverlaps[0].type).toBe("conflict");
    });

    it("should mark configuration as invalid when conflicts exist", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          teamId: "team1",
          rules: [
            {
              id: "rule1",
              criteria: { geography: { type: "Any" } },
            },
          ],
        },
        {
          id: "ind2",
          name: "Jane",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          teamId: "team2",
          rules: [
            {
              id: "rule2",
              criteria: { geography: { type: "Specific", values: ["usa"] } },
            },
          ],
        },
      ];

      const config = { ...baseConfig, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasConflicts).toBe(true);
    });
  });

  describe("redundancy detection", () => {
    it("should detect redundancies within the same entity", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams, gapsAcknowledged: true };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true); // Redundancies are not blocking
      expect(result.hasRedundancies).toBe(true);
      expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(true);
      expect(result.allOverlaps).toHaveLength(1);
      expect(result.allOverlaps[0].type).toBe("redundancy");
    });

    it("should not mark redundancies as invalid", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "rule1",
              criteria: { 
                geography: { type: "Any" },
                eventType: { type: "Any" }
              },
            },
            {
              id: "rule2",
              criteria: { 
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] }
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, individuals, gapsAcknowledged: true, assignmentStrategy: "individual" as const };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasRedundancies).toBe(true);
    });
  });

  describe("coverage gap detection", () => {
    it("should detect coverage gaps", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(false);
      expect(result.hasCoverageGaps).toBe(true);
      expect(result.coverageGaps.length).toBeGreaterThan(0);
    });

    it("should allow acknowledged gaps", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Specific", values: ["Wedding"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams, gapsAcknowledged: true };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasCoverageGaps).toBe(true);
    });

    it("should enrich gaps with display values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      const canadaGap = result.coverageGaps.find(gap =>
        gap.missingCombination.some(c => c.value === "canada")
      );

      expect(canadaGap).toBeDefined();
      expect(canadaGap?.missingCombination.find(c => c.criteriaType === "geography")?.displayValue).toBe("Canada");
    });
  });

  describe("inactive entity detection", () => {
    it("should detect entities with no rules", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [],
        },
      ];

      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John",
          title: "Sales",
          email: "<EMAIL>",
          phone: "************",
          rules: [],
        },
      ];

      const config = { ...baseConfig, teams, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasInactiveEntities).toBe(true);
      expect(result.inactiveEntityIds).toContain("team1");
      expect(result.inactiveEntityIds).toContain("ind1");
    });

    it("should not affect validity for inactive entities", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.isValid).toBe(true);
      expect(result.hasInactiveEntities).toBe(true);
    });
  });

  describe("validation summary", () => {
    it("should generate appropriate summary for valid configuration", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      expect(summary).toBe("Configuration is valid");
    });

    it("should list all issues in summary", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: { 
                geography: { type: "Any" },
                eventType: { type: "Any" }
              },
            },
            {
              id: "rule2",
              criteria: { 
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" }
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule3",
              criteria: { 
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" }
              },
            },
          ],
        },
        {
          id: "team3",
          name: "Team C",
          rules: [],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      // Since team1 has both conflicts and redundancies, it only shows up in conflicts
      // The summary shows "2 conflicts detected" (team1 rule1 vs team2, team1 rule2 vs team2)
      expect(summary).toContain("conflict");
      expect(summary).toContain("inactive");
      
      // Check that all types of issues were detected
      expect(result.allOverlaps.some(o => o.type === "redundancy")).toBe(true);
      expect(result.hasConflicts).toBe(true);
      expect(result.hasInactiveEntities).toBe(true);
    });
  });

  describe("complex scenarios", () => {
    it("should correctly prioritize conflicts over redundancies", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: { 
                geography: { type: "Specific", values: ["usa"] },
                eventType: { type: "Any" }
              },
            },
            {
              id: "rule2",
              criteria: { 
                geography: { type: "Any" },
                eventType: { type: "Any" }
              },
            },
          ],
        },
        {
          id: "team2",
          name: "Team B",
          rules: [
            {
              id: "rule3",
              criteria: { 
                geography: { type: "Specific", values: ["usa", "canada"] },
                eventType: { type: "Any" }
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
      expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(false);
    });
  });

  describe("unset criteria detection", () => {
    it("should detect rules with null criteria values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Specific", values: ["usa"] },
                eventType: null, // Unset criteria
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(1);
      expect(result.rulesWithUnsetCriteria[0]).toEqual({
        entityId: "team1",
        entityName: "Team A",
        entityType: "team",
        ruleId: "rule1",
        unsetCriteria: ["eventType"],
      });
      expect(result.isValid).toBe(false); // Should be invalid due to unset criteria
    });

    it("should detect multiple unset criteria in a single rule", () => {
      const individuals: Individual[] = [
        {
          id: "ind1",
          name: "John Doe",
          title: "Sales Rep",
          email: "<EMAIL>",
          phone: "************",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: null, // Unset
                eventType: null, // Unset
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, individuals };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(true);
      expect(result.rulesWithUnsetCriteria).toHaveLength(1);
      expect(result.rulesWithUnsetCriteria[0].unsetCriteria).toEqual(["geography", "eventType"]);
      expect(result.isValid).toBe(false);
    });

    it("should not flag undefined criteria as unset", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                // eventType is undefined (not specified), which is OK
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
    });

    it("should check only active criteria for null values", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" },
                roomCount: null, // Null but roomCount is not active
              },
            },
          ],
        },
      ];

      const config = { 
        ...baseConfig, 
        teams,
        activeCriteria: { geography: true, eventType: true, roomCount: false },
      };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.isValid).toBe(true);
    });

    it("should include unset criteria in validation summary", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: null,
                eventType: { type: "Any" },
              },
            },
            {
              id: "rule2",
              criteria: {
                geography: { type: "Any" },
                eventType: null,
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);
      const summary = getValidationSummary(result);

      expect(result.rulesWithUnsetCriteria).toHaveLength(2);
      expect(summary).toContain("2 rules with unset criteria");
    });

    it("should validate correctly when all criteria are set", () => {
      const teams: Team[] = [
        {
          id: "team1",
          name: "Team A",
          rules: [
            {
              id: "rule1",
              criteria: {
                geography: { type: "Any" },
                eventType: { type: "Any" }, // Changed to Any to cover all cases
              },
            },
          ],
        },
      ];

      const config = { ...baseConfig, teams };
      const result = validateAssignmentConfiguration(config);

      expect(result.hasUnsetCriteria).toBe(false);
      expect(result.rulesWithUnsetCriteria).toHaveLength(0);
      expect(result.isValid).toBe(true);
    });
  });
});