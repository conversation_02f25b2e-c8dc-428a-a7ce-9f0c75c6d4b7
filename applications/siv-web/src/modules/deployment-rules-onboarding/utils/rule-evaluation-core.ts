// Standard criteria type mapping for consistency
import type {
  CriteriaTypeString,
  CriterionValue,
  Exception,
  ExceptionCondition,
  GeographyRegion,
  Individual,
  RoomCountRange,
  Team,
} from "@/modules/deployment-rules-onboarding/types/lead-assignment";
import { normalizeOperator } from "@/modules/deployment-rules-onboarding/utils/overlap-detection";

export const CRITERIA_TYPES = {
  GEOGRAPHY: "geography",
  ROOM_COUNT: "roomCount",
  EVENT_TYPE: "eventType",
  INDUSTRY: "industry",
  EVENT_NEEDS: "eventNeeds",
  DAY_OF_MONTH: "dayOfMonth",
} as const;
// Re-export the CriteriaType type from the values in CRITERIA_TYPES
export type CriteriaType = CriteriaTypeString;
// Type for criteria values record
export type CriteriaValues = Record<CriteriaType, CriterionValue | null>;
// Type for active criteria configuration
// Allow partial records for ActiveCriteria to support backward compatibility
export type ActiveCriteria = Partial<Record<CriteriaType, boolean>>;

/**
 * Type guard to check if a string is a valid CriteriaType
 * @param key String to check
 * @returns true if the key is a valid CriteriaType
 */
export function isCriteriaType(key: string): key is CriteriaType {
  return Object.values(CRITERIA_TYPES).includes(key as any);
}

/**
 * Helper function to safely get active criteria types
 * @param activeCriteria The criteria configuration
 * @returns Array of criteria types that are active
 */
function getActiveCriteriaTypes(
  activeCriteria: ActiveCriteria,
): CriteriaType[] {
  return Object.keys(activeCriteria)
    .filter((key) => {
      // Check if the key exists in ActiveCriteria and is true
      const criteriaValue = activeCriteria[key as keyof typeof activeCriteria];
      return criteriaValue === true && isCriteriaType(key);
    })
    .map((key) => key as CriteriaType);
}

/**
 * Helper function to safely access criteria values with proper type narrowing
 * @param criteria Criteria record
 * @param key Criteria key
 * @returns The CriterionValue for the given key, or null if not found
 */
export function getCriterionValue(
  criteria: Partial<CriteriaValues>,
  key: CriteriaType,
): CriterionValue | null {
  const value = criteria[key];
  return value !== undefined ? value : null;
}

/**
 * Helper function to get the actual string array values from criteria
 * @param criteria Criteria record
 * @param key Criteria key
 * @returns Array of criteria values or empty array if "Any", null, or not found
 *
 * IMPORTANT: Returns empty array for "Any" and null to maintain compatibility with overlap detection logic
 * where empty array is conventionally treated as "matches all values" or "no values"
 */
function getCriteriaValues(
  criteria: Partial<CriteriaValues>,
  key: CriteriaType,
): string[] {
  const criterion = getCriterionValue(criteria, key);
  if (!criterion || criterion.type === "Any") {
    return [];
  }
  return criterion.values;
}

/**
 * Helper function to get the effective criteria for an individual, taking into account team inheritance
 * @param individual The individual to get effective criteria for
 * @param team Optional team the individual belongs to
 * @returns The effective criteria combining individual and team criteria
 *
 * Logic:
 * - For each criteria type, if the individual has a non-null value, use that
 * - Otherwise, if they're part of a team and the team has a non-null value, use the team's value
 * - Otherwise, use null
 */
function getEffectiveCriteria(
  individual: Individual,
  team?: Team,
): Partial<CriteriaValues> {
  const effectiveCriteria: Partial<CriteriaValues> = {};

  // Get all possible criteria types from CRITERIA_TYPES
  const allCriteriaTypes = Object.values(CRITERIA_TYPES) as CriteriaType[];

  for (const criteriaType of allCriteriaTypes) {
    // First check if individual has a value for this criteria type
    const individualCriterion = individual.criteria?.[criteriaType];

    if (individualCriterion !== null && individualCriterion !== undefined) {
      // Individual has a value (could be "Any" or "Specific"), use it
      effectiveCriteria[criteriaType] = individualCriterion;
    } else if (
      team &&
      team.criteria?.[criteriaType] !== null &&
      team.criteria?.[criteriaType] !== undefined
    ) {
      // Individual doesn't have a value, but team does, use team's value
      effectiveCriteria[criteriaType] = team.criteria[criteriaType];
    } else {
      // Neither individual nor team has a value, use null
      effectiveCriteria[criteriaType] = null;
    }
  }

  return effectiveCriteria;
}

// Special symbol to represent "other value" in the context of "Any" criteria
export const SYMBOLIC_OTHER_PREFIX = "__OTHER_FOR_ANY__";
/**
 * Evaluates if a lead scenario passes a rule's criteria
 *
 * @param leadScenario Record mapping criteria types to specific values
 * @param ruleCriteriaValues Rule's criteria values by type
 * @param activeCriteriaKeys Active criteria types
 * @returns true if the lead matches the criteria, false otherwise
 */
export const evaluateLeadAgainstCriteria = (
  leadScenario: Partial<Record<CriteriaType, string>>,
  ruleCriteriaValues: Partial<CriteriaValues>,
  activeCriteriaKeys: CriteriaType[],
): boolean => {
  // Check each active criteria type
  for (const critType of activeCriteriaKeys) {
    const ruleCriterion = getCriterionValue(ruleCriteriaValues, critType);
    const scenarioValueForCrit = leadScenario[critType];

    // If rule has null for this criterion, it doesn't match any lead
    if (ruleCriterion === null) {
      return false;
    }

    // If the rule accepts any value for this criterion, it matches
    if (ruleCriterion.type === "Any") {
      continue; // "Any" always matches
    }

    // Rule has specific values - we already checked it's not "Any" above
    // TypeScript needs explicit type narrowing here
    if (ruleCriterion.type !== "Specific") {
      throw new Error("Invalid criterion type");
    }
    const ruleValues = ruleCriterion.values;

    // Special handling for symbolic "other" value
    // Handle missing values in the scenario
    if (scenarioValueForCrit === undefined) {
      // If scenario is missing this criterion but rule requires specific values, it doesn't match
      return false;
    }
    // Check for symbolic "other" value
    else if (scenarioValueForCrit.startsWith(SYMBOLIC_OTHER_PREFIX)) {
      // An "OTHER" scenario value does NOT match a specific set of rule values
      return false;
    }
    // Otherwise check if the value is included in the rule's values
    else if (!ruleValues.includes(scenarioValueForCrit)) {
      // Specific scenario value not found in rule's specific values
      return false;
    }
  }

  // The lead matches all criteria
  return true;
};
export { getEffectiveCriteria };
export { getCriteriaValues };
export { getActiveCriteriaTypes };
/**
 * Evaluates if a lead scenario matches exception conditions
 *
 * @param leadScenario Record mapping criteria types to specific values
 * @param conditions Exception conditions to evaluate
 * @param geographyRegions Optional array of geography regions for UUID/name mapping
 * @returns true if the lead matches the conditions, false otherwise
 */
const evaluateLeadAgainstExceptionConditions = (
  leadScenario: Partial<Record<CriteriaType, string>>,
  conditions: ExceptionCondition[],
  geographyRegions?: GeographyRegion[],
  roomCountRanges?: RoomCountRange[],
): boolean => {
  // No conditions means the exception isn't triggered
  if (!conditions || conditions.length === 0) {
    return false;
  }

  // Check if all conditions match
  for (const condition of conditions) {
    const scenarioValue = leadScenario[condition.criteriaType];
    let conditionTargetValue = condition.value;
    const isScenarioValueSymbolicOther =
      scenarioValue !== undefined &&
      scenarioValue.startsWith(SYMBOLIC_OTHER_PREFIX);

    let conditionMet = false;

    // Convert operator to uppercase for normalized comparison
    const operator = normalizeOperator(condition.operator);

    // Special handling for geography criteria - map region names to IDs
    let effectiveScenarioValue = scenarioValue;
    if (
      condition.criteriaType === CRITERIA_TYPES.GEOGRAPHY &&
      geographyRegions &&
      scenarioValue
    ) {
      // The scenario value is likely a UUID, but the condition value is likely a name
      // First check if the condition value is a region name that needs to be mapped to an ID
      const regionByName = geographyRegions.find(
        (r) => r.name === conditionTargetValue,
      );
      if (regionByName) {
        // Map the condition's region name to its ID for comparison
        conditionTargetValue = regionByName.id;
      }

      // Also handle the case where scenario has a name instead of ID
      const regionByScenarioName = geographyRegions.find(
        (r) => r.name === scenarioValue,
      );
      if (regionByScenarioName) {
        effectiveScenarioValue = regionByScenarioName.id;
      }
    }

    // Special handling for room count criteria - map range names to IDs
    if (
      condition.criteriaType === CRITERIA_TYPES.ROOM_COUNT &&
      roomCountRanges &&
      scenarioValue
    ) {
      // The scenario value is likely a UUID, but the condition value is likely a name
      // First check if the condition value is a range name that needs to be mapped to an ID
      const rangeByName = roomCountRanges.find(
        (r) => r.name === conditionTargetValue,
      );
      if (rangeByName) {
        // Map the condition's range name to its ID for comparison
        conditionTargetValue = rangeByName.id;
      }

      // Also handle the case where scenario has a name instead of ID
      const rangeByScenarioName = roomCountRanges.find(
        (r) => r.name === scenarioValue,
      );
      if (rangeByScenarioName) {
        effectiveScenarioValue = rangeByScenarioName.id;
      }
    }

    switch (operator) {
      case "EQUALS":
        if (effectiveScenarioValue === undefined) {
          // If the scenario value is undefined, it can't equal anything
          conditionMet = false;
        } else if (isScenarioValueSymbolicOther) {
          conditionMet = conditionTargetValue === effectiveScenarioValue;
        } else {
          conditionMet = effectiveScenarioValue === conditionTargetValue;
        }
        break;
      case "NOT_EQUALS":
        if (effectiveScenarioValue === undefined) {
          // If the scenario value is undefined, we'll consider it different from anything
          conditionMet = true;
        } else if (isScenarioValueSymbolicOther) {
          conditionMet = conditionTargetValue !== effectiveScenarioValue;
        } else {
          conditionMet = effectiveScenarioValue !== conditionTargetValue;
        }
        break;
      case "IN":
        if (effectiveScenarioValue === undefined) {
          // If the scenario value is undefined, it can't be in any list
          conditionMet = false;
        } else if (isScenarioValueSymbolicOther) {
          conditionMet = false;
        } else {
          const inValues = conditionTargetValue.split(",").map((v) => v.trim());
          // For IN operator with geography, also map the values
          if (
            condition.criteriaType === CRITERIA_TYPES.GEOGRAPHY &&
            geographyRegions
          ) {
            const mappedInValues = inValues.map((val) => {
              const region = geographyRegions.find((r) => r.name === val);
              return region ? region.id : val;
            });
            conditionMet = mappedInValues.includes(effectiveScenarioValue);
            // For IN operator with room count, also map the values
          } else if (
            condition.criteriaType === CRITERIA_TYPES.ROOM_COUNT &&
            roomCountRanges
          ) {
            const mappedInValues = inValues.map((val) => {
              const range = roomCountRanges.find((r) => r.name === val);
              return range ? range.id : val;
            });
            conditionMet = mappedInValues.includes(effectiveScenarioValue);
          } else {
            conditionMet = inValues.includes(effectiveScenarioValue);
          }
        }
        break;
      case "NOT_IN":
        if (effectiveScenarioValue === undefined) {
          // If the scenario value is undefined, we consider it not in any list
          conditionMet = true;
        } else if (isScenarioValueSymbolicOther) {
          conditionMet = true;
        } else {
          const notInValues = conditionTargetValue
            .split(",")
            .map((v) => v.trim());
          // For NOT_IN operator with geography, also map the values
          if (
            condition.criteriaType === CRITERIA_TYPES.GEOGRAPHY &&
            geographyRegions
          ) {
            const mappedNotInValues = notInValues.map((val) => {
              const region = geographyRegions.find((r) => r.name === val);
              return region ? region.id : val;
            });
            conditionMet = !mappedNotInValues.includes(effectiveScenarioValue);
            // For NOT_IN operator with room count, also map the values
          } else if (
            condition.criteriaType === CRITERIA_TYPES.ROOM_COUNT &&
            roomCountRanges
          ) {
            const mappedNotInValues = notInValues.map((val) => {
              const range = roomCountRanges.find((r) => r.name === val);
              return range ? range.id : val;
            });
            conditionMet = !mappedNotInValues.includes(effectiveScenarioValue);
          } else {
            conditionMet = !notInValues.includes(effectiveScenarioValue);
          }
        }
        break;
      default:
        // Unknown operator, assume condition not met
        conditionMet = false;
    }

    if (!conditionMet) {
      return false; // This condition not met, so the AND-conjunction of conditions fails
    }
  }

  // All conditions met
  return true;
};
/**
 * Evaluates if a lead scenario would be processed by a rule after considering exceptions
 *
 * @param leadScenario Record mapping criteria types to specific values
 * @param exceptions Rule's exceptions
 * @param geographyRegions Optional array of geography regions for UUID/name mapping
 * @returns true if the lead is "excepted out" (skipped or redirected) by this rule, false if it applies
 */
export const evaluateRuleExceptions = (
  leadScenario: Partial<Record<CriteriaType, string>>,
  exceptions: Exception[],
  geographyRegions?: GeographyRegion[],
  roomCountRanges?: RoomCountRange[],
): boolean => {
  // No exceptions means the rule always applies
  if (!exceptions || exceptions.length === 0) {
    return false;
  }

  // Check each exception in order
  for (const exception of exceptions) {
    // See if all conditions in this exception match the lead
    if (
      evaluateLeadAgainstExceptionConditions(
        leadScenario,
        exception.conditions,
        geographyRegions,
        roomCountRanges,
      )
    ) {
      // For overlap detection, any triggered exception (skip/redirect) means the rule wouldn't apply
      return true; // Lead is "excepted out" by this rule
    }
  }

  // Lead is not "excepted out" by any exception of this rule
  return false;
};
