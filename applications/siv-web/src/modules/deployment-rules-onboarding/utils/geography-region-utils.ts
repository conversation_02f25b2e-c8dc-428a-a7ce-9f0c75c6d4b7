/**
 * Utility functions for geography regions, specifically for detecting overlaps and
 * ensuring comprehensive global coverage.
 */

import { Country, State } from "country-state-city";
import type { GeographyRegion } from "../types/lead-assignment";

// Special constants for system-managed regions
// Keep two system-managed region types
// - ALL_OTHER_COUNTRIES: For all countries not covered by user regions
// - USA_ALL_OTHER_STATES: For remaining US states when user defines some specific US states
export const SYSTEM_REGION_TYPES = {
  ALL_OTHER_COUNTRIES: "ALL_OTHER_COUNTRIES",
  USA_ALL_OTHER_STATES: "USA_ALL_OTHER_STATES",
} as const;

// Types that are now just templates for quick-add
export const TEMPLATE_REGION_TYPES = {
  USA_ALL_STATES: "USA_ALL_STATES",
  ALL_NON_US_COUNTRIES: "ALL_NON_US_COUNTRIES",
} as const;

// Special constant for "ALL_STATES" condition for a country
export const ALL_STATES = Symbol("ALL_STATES");

/**
 * Type representing a standardized geographic footprint
 * For non-US countries: country ISO code -> true
 * For the US specifically: "US" -> Set of state ISO codes or ALL_STATES
 */
export type GeographicScope = {
  countries: Record<string, true>;
  usStates: Set<string> | typeof ALL_STATES | undefined;
};

/**
 * Determines if a country/state is covered by a given scope
 * @param scope The geographic scope
 * @param country Country ISO code
 * @param state Optional state ISO code (only used if country is 'US')
 * @returns true if the location is covered by the scope
 */
export function isLocationCoveredByScope(
  scope: GeographicScope,
  country: string,
  state?: string,
): boolean {
  // Check if the country is included in the scope
  if (!scope.countries[country]) {
    return false;
  }

  // Special handling for US
  if (country === "US" && state) {
    if (!scope.usStates) {
      // No US states defined means all US (empty array in the original data)
      return true;
    }

    // ALL_STATES symbol means all US states are included
    if (scope.usStates === ALL_STATES) {
      return true;
    }

    // Check if the state is in the set of states
    return scope.usStates.has(state);
  }

  // For non-US countries or US without state specified
  return true;
}

/**
 * Converts a GeographyRegion object into a standardized GeographicScope
 * for easier comparison and overlap detection
 *
 * @param region The geography region to convert
 * @returns A standardized geographic scope representation
 */
export function getEffectiveScope(region: GeographyRegion): GeographicScope {
  const scope: GeographicScope = {
    countries: {},
    usStates: undefined,
  };

  // Process each country in the region
  for (const countryCode of region.countries) {
    scope.countries[countryCode] = true;

    // Special handling for US
    if (countryCode === "US") {
      if (!region.usStates || region.usStates.length === 0) {
        // Empty/undefined usStates for US means ALL states
        scope.usStates = ALL_STATES;
      } else {
        // Create a set of specific US states
        scope.usStates = new Set(region.usStates);
      }
    }
  }

  return scope;
}

/**
 * Checks if two geographic scopes overlap
 *
 * @param scopeA First geographic scope
 * @param scopeB Second geographic scope
 * @returns true if there's any geographic overlap
 */
export function checkScopesOverlap(
  scopeA: GeographicScope,
  scopeB: GeographicScope,
): boolean {
  // Get overlapping countries
  const overlappingCountries = Object.keys(scopeA.countries).filter(
    (country) => scopeB.countries[country],
  );

  // If no countries overlap, there's no overlap at all
  if (overlappingCountries.length === 0) {
    return false;
  }

  // Check for US state overlap if US is in both scopes
  if (
    overlappingCountries.includes("US") &&
    scopeA.usStates !== undefined &&
    scopeB.usStates !== undefined
  ) {
    // If either scope includes ALL_STATES, there's definitely an overlap
    if (scopeA.usStates === ALL_STATES || scopeB.usStates === ALL_STATES) {
      return true;
    }

    // Check if there's any common state between the two sets
    const statesA = scopeA.usStates as Set<string>;
    const statesB = scopeB.usStates as Set<string>;

    // Convert one set to array and check for intersection
    for (const state of statesA) {
      if (statesB.has(state)) {
        return true;
      }
    }

    // US is in both scopes but no states overlap
    return false;
  }

  // There are overlapping countries (and either no US or US with undefined states)
  return true;
}

/**
 * Checks if one scope is effectively identical to another
 *
 * @param scopeA First geographic scope
 * @param scopeB Second geographic scope
 * @returns true if the scopes are functionally identical
 */
export function areScopesIdentical(
  scopeA: GeographicScope,
  scopeB: GeographicScope,
): boolean {
  // Check if both scopes have the same countries
  const countriesA = Object.keys(scopeA.countries).sort();
  const countriesB = Object.keys(scopeB.countries).sort();

  if (countriesA.length !== countriesB.length) {
    return false;
  }

  for (let i = 0; i < countriesA.length; i++) {
    if (countriesA[i] !== countriesB[i]) {
      return false;
    }
  }

  // Special handling for US states
  if (countriesA.includes("US")) {
    // If both have ALL_STATES or both have undefined, they're the same
    if (scopeA.usStates === ALL_STATES && scopeB.usStates === ALL_STATES) {
      return true;
    }

    if (scopeA.usStates instanceof Set && scopeB.usStates instanceof Set) {
      // Compare the two sets
      if (scopeA.usStates.size !== scopeB.usStates.size) {
        return false;
      }

      for (const state of scopeA.usStates) {
        if (!scopeB.usStates.has(state)) {
          return false;
        }
      }

      // All states match
      return true;
    }

    // One has ALL_STATES and the other has specific states, or one is undefined
    return false;
  }

  // Same countries, no US or US with matching state handling
  return true;
}

/**
 * Creates a scope that represents all US states that aren't covered by the given scopes
 *
 * @param scopes Array of geographic scopes to exclude
 * @returns A scope that covers only US states not in any of the input scopes
 */
export function createComplementaryUSStatesScope(
  scopes: GeographicScope[],
): GeographicScope | null {
  // Get all US states from the country-state-city library
  const allUSStates = State.getStatesOfCountry("US");
  const allStateISOCodes = new Set(allUSStates.map((state) => state.isoCode));

  // Find all US states that are covered by the input scopes
  const coveredStates = new Set<string>();

  for (const scope of scopes) {
    // If this scope doesn't include US, skip it
    if (!scope.countries["US"]) {
      continue;
    }

    // If this scope includes ALL US states, all states are covered
    if (scope.usStates === ALL_STATES) {
      return null; // No complementary US states available
    }

    // Add all specific states from this scope to the covered set
    if (scope.usStates instanceof Set) {
      for (const state of scope.usStates) {
        coveredStates.add(state);
      }
    }
  }

  // If all states are covered, there's no complementary scope to create
  if (coveredStates.size >= allStateISOCodes.size) {
    return null;
  }

  // Get the uncovered states
  const uncoveredStates = new Set<string>();

  for (const state of allStateISOCodes) {
    if (!coveredStates.has(state)) {
      uncoveredStates.add(state);
    }
  }

  // Create a scope for the uncovered states
  return {
    countries: { US: true },
    usStates: uncoveredStates,
  };
}

/**
 * Creates a scope that represents all non-US countries that aren't covered by the given scopes
 *
 * @param scopes Array of geographic scopes to exclude
 * @returns A scope that covers only non-US countries not in any of the input scopes
 */
export function createComplementaryNonUSCountriesScope(
  scopes: GeographicScope[],
): GeographicScope | null {
  // Get all countries from the country-state-city library
  const allCountries = Country.getAllCountries();
  const nonUSCountries = allCountries
    .filter((country) => country.isoCode !== "US")
    .map((country) => country.isoCode);

  // Find all non-US countries that are covered by the input scopes
  const coveredCountries = new Set<string>();

  for (const scope of scopes) {
    // Add all countries (except US) from this scope to the covered set
    for (const country of Object.keys(scope.countries)) {
      if (country !== "US") {
        coveredCountries.add(country);
      }
    }
  }

  // If all non-US countries are covered, there's no complementary scope to create
  if (coveredCountries.size >= nonUSCountries.length) {
    return null;
  }

  // Create a scope for the uncovered countries
  const uncoveredCountries: Record<string, true> = {};

  for (const country of nonUSCountries) {
    if (!coveredCountries.has(country)) {
      uncoveredCountries[country] = true;
    }
  }

  return {
    countries: uncoveredCountries,
    usStates: undefined,
  };
}

/**
 * Calculates the dynamic system-managed regions needed for complete global coverage
 * based on the current set of user-defined regions
 *
 * @param userDefinedRegions Array of user-defined regions
 * @returns Array of system-managed regions needed to ensure global coverage
 */
export function calculateDynamicSystemRegions(
  userDefinedRegions: GeographyRegion[],
): GeographyRegion[] {
  // Convert user regions to standardized scopes for easier analysis
  const userScopes = userDefinedRegions.map(getEffectiveScope);

  const systemRegions: GeographyRegion[] = [];

  // Check if any user-defined regions have specific US states defined
  const hasSpecificUSStates = userDefinedRegions.some(
    (region) =>
      region.countries.includes("US") &&
      region.usStates &&
      region.usStates.length > 0,
  );

  // Check if any user-defined region contains the entire US (no states specified)
  const hasEntireUS = userDefinedRegions.some(
    (region) =>
      region.countries.includes("US") &&
      (!region.usStates || region.usStates.length === 0),
  );

  // Only calculate "All Other US States" when we have specific US states defined
  // We should skip this only if we don't have any specific US states OR we have the entire US
  if (hasSpecificUSStates && !hasEntireUS) {
    // Calculate "All Other US States/Territories" region
    const otherUSStatesScope = createComplementaryUSStatesScope(userScopes);

    // Always create the region when we have specific US states and not the entire US
    // This ensures the region appears even when many states are selected
    if (
      otherUSStatesScope !== null &&
      otherUSStatesScope.usStates instanceof Set
    ) {
      systemRegions.push({
        id: crypto.randomUUID(),
        name: "All Other US States/Territories",
        countries: ["US"],
        usStates: Array.from(otherUSStatesScope.usStates),
        isPredefined: true,
        predefinedType: SYSTEM_REGION_TYPES.USA_ALL_OTHER_STATES,
      });
    }
  }

  // Always calculate the "All Other Countries/Regions" to ensure global coverage
  // This collects both:
  // 1. All non-US countries not covered by user regions
  // 2. The US itself if not covered by any user region
  const allCountries = Country.getAllCountries().map((c) => c.isoCode);
  const coveredCountries = new Set<string>();

  // Add all countries from user-defined regions to covered set
  for (const region of userDefinedRegions) {
    for (const country of region.countries) {
      coveredCountries.add(country);
    }
  }

  // Get all countries not covered by any user region
  const uncoveredCountries = allCountries.filter(
    (country) => !coveredCountries.has(country),
  );

  // Only add the system region if there are uncovered countries
  if (uncoveredCountries.length > 0) {
    systemRegions.push({
      id: crypto.randomUUID(),
      name: "All Other Countries/Regions",
      countries: uncoveredCountries,
      isPredefined: true,
      predefinedType: SYSTEM_REGION_TYPES.ALL_OTHER_COUNTRIES,
      isAllOthers: true,
    });
  }

  return systemRegions;
}

/**
 * Creates all template regions for quick-add functionality
 *
 * @returns Array containing template regions like "USA (All States)" and "All Non-US Countries"
 */
export function createTemplateRegions(): GeographyRegion[] {
  const templateRegions: GeographyRegion[] = [];

  // Create "USA (All States)" template
  templateRegions.push({
    id: crypto.randomUUID(),
    name: "USA (All States)",
    countries: ["US"],
    usStates: [],
    isPredefined: false, // This is now a template, not system-managed
    predefinedType: TEMPLATE_REGION_TYPES.USA_ALL_STATES,
  });

  // Create "All Non-US Countries" template
  const allCountries = Country.getAllCountries();
  const nonUSCountries = allCountries
    .filter((country) => country.isoCode !== "US")
    .map((country) => country.isoCode);

  templateRegions.push({
    id: crypto.randomUUID(),
    name: "All Non-US Countries",
    countries: nonUSCountries,
    isPredefined: false, // This is now a template, not system-managed
    predefinedType: TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES,
  });

  return templateRegions;
}

/**
 * Checks if a region would overlap with any region in the given array
 * Returns the first overlapping region, or null if no overlap
 *
 * @param regionToCheck The region to check for overlaps
 * @param existingRegions Existing regions to check against
 * @returns The first overlapping region, or null if no overlap
 */
export function findOverlappingRegion(
  regionToCheck: GeographyRegion,
  existingRegions: GeographyRegion[],
): GeographyRegion | null {
  const scopeToCheck = getEffectiveScope(regionToCheck);

  for (const existingRegion of existingRegions) {
    // Skip if comparing with itself
    if (regionToCheck.id === existingRegion.id) {
      continue;
    }

    const existingScope = getEffectiveScope(existingRegion);

    if (checkScopesOverlap(scopeToCheck, existingScope)) {
      return existingRegion;
    }
  }

  return null;
}

/**
 * Format a geographic region for display
 *
 * @param region The region to format
 * @returns A display-friendly description of the region's contents
 */
export function getRegionDescription(region: GeographyRegion): string {
  if (region.predefinedType === TEMPLATE_REGION_TYPES.USA_ALL_STATES) {
    return "All United States (all 50 states and territories)";
  }

  if (region.predefinedType === TEMPLATE_REGION_TYPES.ALL_NON_US_COUNTRIES) {
    return "All countries worldwide except the United States";
  }

  let description = "";

  // If this is just the US with specific states
  if (
    region.countries.length === 1 &&
    region.countries[0] === "US" &&
    region.usStates &&
    region.usStates.length > 0
  ) {
    const stateNames = region.usStates.map((stateCode) => {
      const state = State.getStateByCodeAndCountry(stateCode, "US");
      return state?.name || stateCode;
    });

    if (stateNames.length <= 5) {
      description = `US: ${stateNames.join(", ")}`;
    } else {
      description = `US: ${stateNames.slice(0, 3).join(", ")} and ${stateNames.length - 3} more`;
    }
    return description;
  }

  // For multiple countries or non-US countries
  const countryNames = region.countries.map((countryCode) => {
    const country = Country.getCountryByCode(countryCode);
    return country?.name || countryCode;
  });

  if (countryNames.length <= 5) {
    description = countryNames.join(", ");
  } else {
    description = `${countryNames.slice(0, 3).join(", ")} and ${countryNames.length - 3} more countries`;
  }

  return description;
}
