import { render, screen } from "@testing-library/react";
import AssignmentSummary from "./assignment-summary";
import type {
  Individual,
  GeographyRegion,
  RoomCountRange,
} from "./types/lead-assignment";
import { describe, expect, it } from "vitest";
import { createDefaultCriteria } from "./test-helpers/criteria-test-helpers";

describe("AssignmentSummary", () => {
  const mockIndividuals: Individual[] = [
    {
      id: "1",
      name: "<PERSON>",
      title: "Sales Manager",
      email: "<EMAIL>",
      phone: "555-1234",
      criteria: createDefaultCriteria({
        geography: { type: "Specific", values: ["region-1", "region-2"] },
        roomCount: { type: "Specific", values: ["range-1"] },
      }),
      exceptions: [],
    },
  ];

  const mockGeographyRegions: GeographyRegion[] = [
    {
      id: "region-1",
      name: "North America",
      countries: ["US", "CA"],
      isPredefined: false,
      predefinedType: undefined,
      isAllOthers: false,
    },
    {
      id: "region-2",
      name: "Europe",
      countries: ["UK", "DE", "FR"],
      isPredefined: false,
      predefinedType: undefined,
      isAllOthers: false,
    },
  ];

  const mockRoomCountRanges: RoomCountRange[] = [
    {
      id: "range-1",
      name: "1-10 rooms",
      condition: "range",
      minValue: 1,
      maxValue: 10,
    },
  ];

  it("displays human-readable geography region names instead of IDs", () => {
    render(
      <AssignmentSummary
        individuals={mockIndividuals}
        registries={{
          geographyRegions: mockGeographyRegions,
          roomCountRanges: mockRoomCountRanges,
        }}
        activeCriteria={{
          geography: true,
          roomCount: true,
        }}
      />,
    );

    // Should show "North America" and "Europe" instead of "region-1" and "region-2"
    expect(screen.getByText("North America")).toBeInTheDocument();
    expect(screen.getByText("Europe")).toBeInTheDocument();
    expect(screen.queryByText("region-1")).not.toBeInTheDocument();
    expect(screen.queryByText("region-2")).not.toBeInTheDocument();
  });

  it("displays human-readable room count range names instead of IDs", () => {
    render(
      <AssignmentSummary
        individuals={mockIndividuals}
        registries={{
          geographyRegions: mockGeographyRegions,
          roomCountRanges: mockRoomCountRanges,
        }}
        activeCriteria={{
          geography: true,
          roomCount: true,
        }}
      />,
    );

    // Should show "1-10 rooms" instead of "range-1"
    expect(screen.getByText("1-10 rooms")).toBeInTheDocument();
    expect(screen.queryByText("range-1")).not.toBeInTheDocument();
  });

  it("falls back to displaying IDs when no registries are provided", () => {
    render(
      <AssignmentSummary
        individuals={mockIndividuals}
        registries={{}}
        activeCriteria={{
          geography: true,
          roomCount: true,
        }}
      />,
    );

    // Should fall back to displaying the raw IDs
    expect(screen.getByText("region-1")).toBeInTheDocument();
    expect(screen.getByText("region-2")).toBeInTheDocument();
    expect(screen.getByText("range-1")).toBeInTheDocument();
  });

  it("only displays active criteria", () => {
    const individualsWithMultipleCriteria: Individual[] = [
      {
        id: "1",
        name: "John Doe",
        title: "Sales Manager",
        email: "<EMAIL>",
        phone: "555-1234",
        criteria: createDefaultCriteria({
          geography: { type: "Specific", values: ["region-1"] },
          roomCount: { type: "Specific", values: ["range-1"] },
          eventType: { type: "Specific", values: ["Meeting"] },
          industry: { type: "Specific", values: ["Technology"] },
        }),
        exceptions: [],
      },
    ];

    render(
      <AssignmentSummary
        individuals={individualsWithMultipleCriteria}
        registries={{
          geographyRegions: mockGeographyRegions,
          roomCountRanges: mockRoomCountRanges,
        }}
        activeCriteria={{
          geography: true,
          roomCount: false, // Not active
          eventType: false, // Not active
          industry: true,
        }}
      />,
    );

    // Should show geography and industry (active criteria)
    expect(screen.getByText("Geography:")).toBeInTheDocument();
    expect(screen.getByText("Industry:")).toBeInTheDocument();
    expect(screen.getByText("North America")).toBeInTheDocument();
    expect(screen.getByText("Technology")).toBeInTheDocument();

    // Should NOT show room count or event type (inactive criteria)
    expect(screen.queryByText("Room Count:")).not.toBeInTheDocument();
    expect(screen.queryByText("Event Type:")).not.toBeInTheDocument();
    expect(screen.queryByText("1-10 rooms")).not.toBeInTheDocument();
    expect(screen.queryByText("Meeting")).not.toBeInTheDocument();
  });
});
