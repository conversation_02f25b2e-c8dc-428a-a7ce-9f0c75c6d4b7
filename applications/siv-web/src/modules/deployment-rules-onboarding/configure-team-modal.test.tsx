import { render, screen } from "@testing-library/react";
import ConfigureTeamModal from "./configure-team-modal";
import type { Team, CriteriaState } from "./types/lead-assignment";
import { describe, expect, it, vi } from "vitest";
import { createDefaultCriteria } from "./test-helpers/criteria-test-helpers";

describe("ConfigureTeamModal", () => {
  const mockCriteria: CriteriaState = {
    geography: { active: false, regions: [] },
    roomCount: { active: false, ranges: [] },
    eventType: { active: false, options: [] },
    industry: { active: false, options: [] },
    eventNeeds: { active: false, options: [] },
    dayOfMonth: { active: false, options: [] },
  };

  const mockTeam: Team = {
    id: "team-1",
    name: "Sales Team",
    criteria: createDefaultCriteria(),
    exceptions: [],
  };

  const defaultProps = {
    open: true,
    onOpenChange: vi.fn(),
    onSave: vi.fn(),
    criteria: mockCriteria,
    isCreating: false,
  };

  describe("Team creation guidance", () => {
    it("shows helpful message when creating a new team", () => {
      render(
        <ConfigureTeamModal {...defaultProps} isCreating={true} team={null} />,
      );

      // Check that the guidance message is displayed
      expect(
        screen.getByText(/Creating teams is optional/),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          /Teams are useful when multiple individuals share common assignment rules/,
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          /such as an entire team that only handles west coast leads or events with specific room count ranges/,
        ),
      ).toBeInTheDocument();
    });

    it("does not show guidance message when editing an existing team", () => {
      render(
        <ConfigureTeamModal
          {...defaultProps}
          isCreating={false}
          team={mockTeam}
        />,
      );

      // Check that the guidance message is NOT displayed
      expect(
        screen.queryByText(/Creating teams is optional/),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText(
          /Teams are useful when multiple individuals share common assignment rules/,
        ),
      ).not.toBeInTheDocument();
    });

    it("shows correct dialog title when creating a team", () => {
      render(
        <ConfigureTeamModal {...defaultProps} isCreating={true} team={null} />,
      );

      expect(screen.getByText("Add New Team")).toBeInTheDocument();
    });

    it("shows correct dialog title when editing a team", () => {
      render(
        <ConfigureTeamModal
          {...defaultProps}
          isCreating={false}
          team={mockTeam}
        />,
      );

      expect(screen.getByText("Edit Team")).toBeInTheDocument();
    });
  });
});
