import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type {
  CriteriaTypeString,
  Exception,
  Individual,
} from "./types/lead-assignment";
import {
  type CriteriaRegistries,
  getCriteriaDisplayName,
  getCriteriaValueDisplayName,
} from "./utils/criteria-helpers";

interface AssignmentSummaryProps {
  individuals: Individual[];
  registries: CriteriaRegistries;
  activeCriteria: Partial<Record<CriteriaTypeString, boolean>>;
}

export default function AssignmentSummary({
  individuals,
  registries,
  activeCriteria,
}: AssignmentSummaryProps) {
  const formatExceptionCondition = (condition: {
    criteriaType: string;
    operator: string;
    value: string;
  }) => {
    const operatorMap: Record<string, string> = {
      EQUALS: "equals",
      NOT_EQUALS: "does not equal",
      CONTAINS: "contains",
      IS_EMPTY: "is empty",
    };

    // Get the friendly display name for the criteria type
    const criteriaDisplayName = getCriteriaDisplayName(
      condition.criteriaType as CriteriaTypeString,
    );

    return `${criteriaDisplayName} ${operatorMap[condition.operator] || condition.operator.toLowerCase()} ${condition.value}`;
  };

  const getRedirectTarget = (
    exception: Exception,
    individuals: Individual[],
  ) => {
    if (exception.action !== "redirect" || !exception.redirectToId)
      return "Unknown";
    const target = individuals.find((ind) => ind.id === exception.redirectToId);
    return target ? target.name : "Unknown";
  };

  // Helper function to get the display name for a criteria type
  const getCriteriaFriendlyName = (criteriaType: string): string => {
    return getCriteriaDisplayName(criteriaType as CriteriaTypeString);
  };

  return (
    <div className="space-y-6">
      <p className="text-muted-foreground">
        This is a summary of your lead assignment configuration. Review it
        carefully before activating.
      </p>

      <div className="space-y-6">
        {individuals.map((individual) => (
          <Card key={individual.id}>
            <CardHeader>
              <CardTitle>{individual.name}</CardTitle>
              <div className="text-sm text-muted-foreground">
                {individual.title}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Display v2 rules if available */}
              {individual.rules && individual.rules.length > 0 ? (
                <div className="space-y-4">
                  {individual.rules.map((rule, ruleIndex) => (
                    <div key={rule.id} className="space-y-2">
                      <h3 className="font-medium">Rule {ruleIndex + 1}:</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Object.entries(rule.criteria)
                          .filter(
                            ([criteriaType]) =>
                              activeCriteria[criteriaType as CriteriaTypeString] ===
                              true,
                          )
                          .map(([criteriaType, criterion]) => (
                            <div
                              key={criteriaType}
                              className="flex items-start gap-2"
                            >
                              <div className="font-medium min-w-[120px]">
                                {getCriteriaFriendlyName(criteriaType)}:
                              </div>
                              <div>
                                {!criterion ? (
                                  <span className="text-muted-foreground">
                                    Not set
                                  </span>
                                ) : criterion.type === "Any" ? (
                                  <span className="text-muted-foreground">Any</span>
                                ) : (
                                  <div className="flex flex-wrap gap-1">
                                    {criterion.values.map(
                                      (value: string, index: number) => (
                                        <Badge key={index} variant="outline">
                                          {getCriteriaValueDisplayName(
                                            criteriaType as CriteriaTypeString,
                                            value,
                                            registries,
                                          )}
                                        </Badge>
                                      ),
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : individual.criteria ? (
                /* Fallback to v1 format for backward compatibility */
                <div className="space-y-2">
                  <h3 className="font-medium">Assignment Criteria:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(individual.criteria)
                      .filter(
                        ([criteriaType]) =>
                          activeCriteria[criteriaType as CriteriaTypeString] ===
                          true,
                      )
                      .map(([criteriaType, criterion]) => (
                        <div
                          key={criteriaType}
                          className="flex items-start gap-2"
                        >
                          <div className="font-medium min-w-[120px]">
                            {getCriteriaFriendlyName(criteriaType)}:
                          </div>
                          <div>
                            {!criterion ? (
                              <span className="text-muted-foreground">
                                Not set
                              </span>
                            ) : criterion.type === "Any" ? (
                              <span className="text-muted-foreground">Any</span>
                            ) : (
                              <div className="flex flex-wrap gap-1">
                                {criterion.values.map(
                                  (value: string, index: number) => (
                                    <Badge key={index} variant="outline">
                                      {getCriteriaValueDisplayName(
                                        criteriaType as CriteriaTypeString,
                                        value,
                                        registries,
                                      )}
                                    </Badge>
                                  ),
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ) : (
                <div className="text-muted-foreground">No rules configured</div>
              )}

              {individual.exceptions && individual.exceptions.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium">Exceptions:</h3>
                  <ul className="space-y-2">
                    {individual.exceptions.map((exception) => (
                      <li key={exception.id} className="border rounded-md p-3">
                        <div className="space-y-2">
                          <div>
                            <span className="font-medium">When:</span>
                            <ul className="list-disc list-inside ml-2">
                              {exception.conditions.map((condition, index) => (
                                <li key={index} className="text-sm">
                                  {formatExceptionCondition(condition)}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <span className="font-medium">Action:</span>{" "}
                            <span className="text-sm">
                              {exception.action === "skip"
                                ? "Skip assignment (send to general inbox)"
                                : `Redirect to ${getRedirectTarget(exception, individuals)}`}
                            </span>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
