"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Settings } from "lucide-react";
import type { GeographyRegion, CriterionValue } from "./types/lead-assignment";
import { CRITERION_ANY } from "./types/lead-assignment";

interface GeographyDefinitionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCriterion?: CriterionValue | null;
  onSave: (criterion: CriterionValue | null) => void;
  geographyRegions?: GeographyRegion[];
  onManageDefinitions?: () => void;
}

export function GeographyDefinitionModal({
  // Class name kept the same for compatibility
  open,
  onOpenChange,
  selectedCriterion,
  onSave,
  geographyRegions = [],
  onManageDefinitions,
}: GeographyDefinitionModalProps) {
  console.log(
    "GeographyDefinitionModal rendered with regions:",
    geographyRegions.map((r) => ({
      name: r.name,
      isPredefined: r.isPredefined,
    })),
  );
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [isAnySelected, setIsAnySelected] = useState(false);

  // Default options if no geography regions provided - these should never be used
  // since the system should always provide system-managed regions
  const defaultOptions = [
    "North America",
    "Europe",
    "Asia",
    "Australia",
    "South America",
    "Africa",
  ];

  // Reset state when modal opens with new props
  useEffect(() => {
    if (open) {
      if (selectedCriterion === null) {
        // If null, nothing is selected
        setIsAnySelected(false);
        setSelectedOptions([]);
      } else if (selectedCriterion?.type === "Any") {
        setIsAnySelected(true);
        setSelectedOptions([]);
      } else if (selectedCriterion?.type === "Specific") {
        setIsAnySelected(false);
        setSelectedOptions([...selectedCriterion.values]);
      } else {
        // Default to null (no selection)
        setIsAnySelected(false);
        setSelectedOptions([]);
      }

      // Debug the geography regions available
      console.log("Geography regions in modal:", geographyRegions);

      // Log any system regions we're receiving
      const systemRegions = geographyRegions.filter((r) => r.isPredefined);
      console.log(
        "System regions in modal:",
        systemRegions.map((r) => r.name),
      );

      // Log detailed information about regions
      console.log(
        "Geography regions detailed:",
        geographyRegions.map((r) => ({
          name: r.name,
          id: r.id,
          isPredefined: r.isPredefined,
          predefinedType: r.predefinedType,
        })),
      );
    }
  }, [open, selectedCriterion, geographyRegions]);

  // Check if two regions would overlap based on their definitions
  const wouldRegionsOverlap = (
    region1Id: string,
    region2Id: string,
  ): boolean => {
    const region1 = geographyRegions.find((r) => r.id === region1Id);
    const region2 = geographyRegions.find((r) => r.id === region2Id);

    if (!region1 || !region2) return false;

    // Check countries overlap
    const countriesOverlap = region1.countries.some((country) =>
      region2.countries.includes(country),
    );

    if (!countriesOverlap) return false;

    // Special handling for US states
    if (region1.countries.includes("US") && region2.countries.includes("US")) {
      // If either has no states specified, it means "all states" which overlaps with anything
      if (!region1.usStates?.length || !region2.usStates?.length) {
        return true;
      }

      // Check if there's any common state
      return region1.usStates.some((state) =>
        region2.usStates?.includes(state),
      );
    }

    // Countries overlap but neither is US with states
    return true;
  };

  const handleOptionToggle = (option: string) => {
    // If "Assign Any Geography" is selected, uncheck it first
    if (isAnySelected) {
      setIsAnySelected(false);
    }

    // Then toggle the option
    if (selectedOptions.includes(option)) {
      // Removing an option - just filter it out
      setSelectedOptions(selectedOptions.filter((item) => item !== option));
    } else {
      // Adding an option - check for overlaps with existing selections
      const wouldOverlap = selectedOptions.some((selectedOption) =>
        wouldRegionsOverlap(option, selectedOption),
      );

      if (wouldOverlap) {
        // Alert the user about the overlap
        alert(
          "Warning: This region overlaps with one of your already selected regions. You should choose mutually exclusive regions to avoid routing confusion.",
        );
      }

      // Add the option anyway, even with warning
      setSelectedOptions([...selectedOptions, option]);
    }
  };

  const handleAnyToggle = (checked: boolean) => {
    setIsAnySelected(checked);
    if (checked) {
      setSelectedOptions([]);
    } else {
      // When unchecking "Any", clear selections (set to null)
      setSelectedOptions([]);
    }
  };

  const handleSave = () => {
    let criterion: CriterionValue | null;
    if (isAnySelected) {
      criterion = CRITERION_ANY;
    } else if (selectedOptions.length > 0) {
      criterion = { type: "Specific", values: selectedOptions };
    } else {
      criterion = null;
    }
    onSave(criterion);
    onOpenChange(false);
  };

  // Determine which options to display
  // Trust that the provided geographyRegions already include system regions as needed
  const optionsToDisplay =
    geographyRegions && geographyRegions.length > 0
      ? geographyRegions
      : defaultOptions.map((name) => ({ id: name, name, countries: [] }));

  // Safety check - log if we're missing system regions
  useEffect(() => {
    const systemRegionCount = geographyRegions.filter(
      (r) => r.isPredefined,
    ).length;
    if (geographyRegions.length > 0 && systemRegionCount === 0) {
      console.warn(
        "WARNING: No system-managed regions found in the geography modal. This may indicate a data flow issue.",
      );
    }
  }, [geographyRegions]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        aria-describedby="geography-definition-desc"
        onKeyDown={(e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSave();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Edit Geography Rule Criteria</span>
            {onManageDefinitions && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={(e) => {
                  e.preventDefault();
                  onManageDefinitions();
                }}
              >
                <Settings className="h-4 w-4 mr-1" />
                Manage Definitions
              </Button>
            )}
          </DialogTitle>
          <p
            id="geography-definition-desc"
            className="text-sm text-muted-foreground mt-2"
          >
            Select geographic regions for lead assignment.
          </p>
        </DialogHeader>
        <div className="py-4">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="assign-any-geography"
                checked={isAnySelected}
                onCheckedChange={handleAnyToggle}
              />
              <Label htmlFor="assign-any-geography" className="font-medium">
                Assign Any Geography
                <span className="block text-xs text-muted-foreground mt-1">
                  Lead will be assigned regardless of geography
                </span>
              </Label>
            </div>

            <div className="text-sm text-muted-foreground mb-2">
              Or select specific regions:
            </div>
            <ScrollArea className="h-[200px] pr-4 border rounded-sm">
              <div className="pt-2 space-y-2 p-2">
                {optionsToDisplay.length === 0 ? (
                  <div className="text-center text-muted-foreground p-4">
                    No geography regions defined yet. Click "Manage Definitions"
                    to create your first regions.
                  </div>
                ) : (
                  optionsToDisplay.map((option) => {
                    return (
                      <div
                        key={option.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`geography-${option.id}`}
                          data-testid={`geography: ${option.name}`}
                          checked={selectedOptions.includes(option.id)}
                          onCheckedChange={() => handleOptionToggle(option.id)}
                        />
                        <Label htmlFor={`geography-${option.id}`}>
                          {option.name}
                        </Label>
                      </div>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            data-testid="save-geography-criteria-selection"
            onClick={handleSave}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
