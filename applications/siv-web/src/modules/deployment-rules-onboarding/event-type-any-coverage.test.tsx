import { describe, expect, test } from "vitest";

/**
 * Test case focusing on correctly handling "Any" event type in coverage gap detection
 *
 * This test verifies that when an individual or team has an empty array for eventType criteria,
 * it's correctly treated as accepting ALL event types, and no coverage gaps are detected.
 */
describe("Event Type Any Coverage Tests", () => {
  // The helper function that mimics the criteria evaluation in lead-assignment-configuration.tsx
  function evaluateLeadAgainstCriteria(
    leadScenario: Record<string, string>,
    ruleCriteriaValues: Record<string, string[]>,
    activeCriteriaTypes: string[],
  ): boolean {
    // Check each active criteria type
    for (const critType of activeCriteriaTypes) {
      const ruleValuesForCritArray = ruleCriteriaValues[critType] || [];
      const scenarioValueForCrit = leadScenario[critType];

      // If the rule accepts any value for this criterion (empty array), it matches
      if (ruleValuesForCritArray.length === 0) {
        continue; // "Any" always matches - this criteria type is satisfied
      }

      // Handle missing values in the scenario
      if (scenarioValueForCrit === undefined) {
        // If scenario is missing this criterion but rule requires specific values, it doesn't match
        return false;
      }
      // Otherwise check if the value is included in the rule's values
      else if (!ruleValuesForCritArray.includes(scenarioValueForCrit)) {
        // Specific scenario value not found in rule's specific values
        return false;
      }
    }

    // The lead matches all criteria
    return true;
  }

  test("empty event type array should match any event type", () => {
    // Individual with empty eventType array = Any event type
    const individual = {
      criteria: {
        geography: ["region1"],
        roomCount: ["range1"],
        eventType: [], // Empty = Any event type
      },
    };

    // Scenarios with different event types
    const meetingScenario = {
      geography: "region1",
      roomCount: "range1",
      eventType: "Meeting",
    };
    const conferenceScenario = {
      geography: "region1",
      roomCount: "range1",
      eventType: "Conference",
    };
    const weddingScenario = {
      geography: "region1",
      roomCount: "range1",
      eventType: "Wedding",
    };

    // Active criteria types
    const activeCriteriaTypes = ["geography", "roomCount", "eventType"];

    // All scenarios should be covered with the empty eventType array
    expect(
      evaluateLeadAgainstCriteria(
        meetingScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        conferenceScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        weddingScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
  });

  test("empty geography array should match any geography", () => {
    // Individual with empty geography array = Any geography
    const individual = {
      criteria: {
        geography: [], // Empty = Any geography
        roomCount: ["range1"],
        eventType: ["Meeting"],
      },
    };

    // Scenarios with different geographies
    const usScenario = {
      geography: "US",
      roomCount: "range1",
      eventType: "Meeting",
    };
    const canadaScenario = {
      geography: "Canada",
      roomCount: "range1",
      eventType: "Meeting",
    };
    const ukScenario = {
      geography: "UK",
      roomCount: "range1",
      eventType: "Meeting",
    };

    // Active criteria types
    const activeCriteriaTypes = ["geography", "roomCount", "eventType"];

    // All scenarios should be covered with the empty geography array
    expect(
      evaluateLeadAgainstCriteria(
        usScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        canadaScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        ukScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
  });

  test("all empty arrays should match any combination", () => {
    // Individual with all empty arrays = Match any combination
    const individual = {
      criteria: {
        geography: [], // Empty = Any geography
        roomCount: [], // Empty = Any room count
        eventType: [], // Empty = Any event type
      },
    };

    // Various scenarios with different combinations
    const scenario1 = {
      geography: "US",
      roomCount: "small",
      eventType: "Meeting",
    };
    const scenario2 = {
      geography: "Canada",
      roomCount: "large",
      eventType: "Conference",
    };
    const scenario3 = {
      geography: "UK",
      roomCount: "medium",
      eventType: "Wedding",
    };

    // Active criteria types
    const activeCriteriaTypes = ["geography", "roomCount", "eventType"];

    // All scenarios should be covered with all empty arrays
    expect(
      evaluateLeadAgainstCriteria(
        scenario1,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        scenario2,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        scenario3,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
  });

  test("specific values should only match those exact values", () => {
    // Individual with specific values for all criteria
    const individual = {
      criteria: {
        geography: ["US"], // Only US
        roomCount: ["small"], // Only small
        eventType: ["Meeting"], // Only Meeting
      },
    };

    // Various scenarios
    const matchingScenario = {
      geography: "US",
      roomCount: "small",
      eventType: "Meeting",
    };
    const wrongGeography = {
      geography: "Canada",
      roomCount: "small",
      eventType: "Meeting",
    };
    const wrongRoomCount = {
      geography: "US",
      roomCount: "large",
      eventType: "Meeting",
    };
    const wrongEventType = {
      geography: "US",
      roomCount: "small",
      eventType: "Conference",
    };

    // Active criteria types
    const activeCriteriaTypes = ["geography", "roomCount", "eventType"];

    // Only the exact matching scenario should be covered
    expect(
      evaluateLeadAgainstCriteria(
        matchingScenario,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        wrongGeography,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(false);
    expect(
      evaluateLeadAgainstCriteria(
        wrongRoomCount,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(false);
    expect(
      evaluateLeadAgainstCriteria(
        wrongEventType,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(false);
  });

  test("mixed empty and specific arrays should behave correctly", () => {
    // Individual with mix of Any and specific criteria
    const individual = {
      criteria: {
        geography: ["US"], // Only US
        roomCount: [], // Any room count
        eventType: ["Meeting"], // Only Meeting
      },
    };

    // Various scenarios
    const matchingScenario1 = {
      geography: "US",
      roomCount: "small",
      eventType: "Meeting",
    };
    const matchingScenario2 = {
      geography: "US",
      roomCount: "large",
      eventType: "Meeting",
    };
    const wrongGeography = {
      geography: "Canada",
      roomCount: "small",
      eventType: "Meeting",
    };
    const wrongEventType = {
      geography: "US",
      roomCount: "small",
      eventType: "Conference",
    };

    // Active criteria types
    const activeCriteriaTypes = ["geography", "roomCount", "eventType"];

    // Scenarios that match the specific values and Any should be covered
    expect(
      evaluateLeadAgainstCriteria(
        matchingScenario1,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        matchingScenario2,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(true);
    expect(
      evaluateLeadAgainstCriteria(
        wrongGeography,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(false);
    expect(
      evaluateLeadAgainstCriteria(
        wrongEventType,
        individual.criteria,
        activeCriteriaTypes,
      ),
    ).toBe(false);
  });
});
