import { Factory } from "fishery";
import { randomUUID } from "crypto";
import { InferInsertModel } from "drizzle-orm";
import { db } from "@/db/dbclient";
import { stsIntegrationSettings } from "@/drizzle/schema";

export const stsIntegrationSettingsFactory = Factory.define<
  InferInsertModel<typeof stsIntegrationSettings>
>(({ onCreate }) => {
  onCreate(
    async (settings) =>
      (await db.insert(stsIntegrationSettings).values(settings).returning())[0],
  );

  return {
    propertyId: randomUUID(),
    stsUsername: `test-sts-username-${randomUUID()}`,
    apiKey: `test-api-key-${randomUUID()}`,
    rfpFormId: `rfp-form-id-${randomUUID()}`,
  };
});
