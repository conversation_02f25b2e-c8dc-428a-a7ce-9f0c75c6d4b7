import { Hono } from "hono";
import { createHighLevelRoutes } from "./highlevel/lead-assignment/highlevel-lead-assignment-webhook-routes";

import { LeadAssignedEventPublisher } from "@/modules/leads/domain/events";

interface Dependencies {
  leadAssignedEventPublisher: LeadAssignedEventPublisher;
}

export function createIntegrationRoutes(deps: Dependencies) {
  const routes = new Hono();

  // Mount HighLevel webhook routes
  const highlevelWebhookRoutes = createHighLevelRoutes({
    leadAssignedEventPublisher: deps.leadAssignedEventPublisher,
  });
  routes.route("/highlevel", highlevelWebhookRoutes);

  return routes;
}

export type IntegrationRoutes = ReturnType<typeof createIntegrationRoutes>;
