import { MessageConsumerPact } from "@pact-foundation/pact";
import { HighlevelSyncApplicationService } from "./HighlevelSyncApplicationService";
import { createHighLevelLeadSyncWorkflow } from "./high-level-lead-sync-workflow";
import { createInngestClient } from "@/inngest-client";
import { mock } from "vitest-mock-extended";
import { LeadCreatedEvent } from "@/modules/leads/domain/events";
import { InngestTestEngine } from "@inngest/test";
import { beforeEach, describe, expect, it } from "vitest";
import { ok } from "neverthrow";
import {
  createMessagePactProvider,
  pactMatchers,
  PARTICIPANT_NAMES,
} from "@siv/pact-helpers";

// Import PACT matching helpers
const { like } = pactMatchers.message;

// PACT setup constants
const CONSUMER_NAME = PARTICIPANT_NAMES.HIGH_LEVEL_LEAD_SYNC_WORKFLOW;
const PROVIDER_NAME = PARTICIPANT_NAMES.SIV_WEB_EVENT_PROVIDER;

describe("HighLevel Lead Sync Workflow - Event Contract Tests", () => {
  let messagePact: MessageConsumerPact;
  let appService: ReturnType<typeof mock<HighlevelSyncApplicationService>>;
  let testEngine: InngestTestEngine;

  beforeEach(() => {
    messagePact = createMessagePactProvider({
      consumerName: CONSUMER_NAME,
      providerName: PROVIDER_NAME,
    });

    // Mock the application service
    appService = mock<HighlevelSyncApplicationService>();
    const contactId = "test-contact-456";
    const opportunityId = "test-opportunity-789";

    appService.createContactInHighLevelIfNotExists.mockResolvedValue(
      ok({ highLevelContactId: contactId }),
    );

    appService.createOpportunityInHighlevelIfNotExists.mockResolvedValue(
      ok({
        highLevelOpportunityId: opportunityId,
        highLevelAuthToken: "test-auth-token",
        highLevelLocationId: "test-location-id",
      }),
    );

    // Create the test engine
    const inngest = createInngestClient();
    const fn = createHighLevelLeadSyncWorkflow(inngest, appService);
    testEngine = new InngestTestEngine({ function: fn });
  });

  const leadCreatedEventName: LeadCreatedEvent["name"] = "lead.created";

  it("should process a valid lead.created event", () => {
    // Create type-safe example data with a valid UUID
    const leadId = "123e4567-e89b-12d3-a456-************"; // Valid UUID format
    const propertyId = "prop-123e4567-e89b-12d3-a456-************"; // Valid property ID

    const payload: LeadCreatedEvent["data"] = {
      leadId,
      propertyId,
    };

    // Use PACT matchers to enforce type validation while allowing dynamic values
    return messagePact
      .expectsToReceive("lead.created")
      .withMetadata({
        name: leadCreatedEventName,
      })
      .withContent({
        leadId: like(leadId),
        propertyId: like(propertyId),
      })
      .verify(async (message) => {
        const metadata = message.metadata ?? {};
        if (typeof metadata.name !== "string") {
          throw new Error(
            "Event name must be provided in metadata and must be a string",
          );
        }
        const eventName = metadata.name;
        const eventData = message.contents;

        // For Inngest, we need to pass the event in the format it expects
        const result = await testEngine.execute({
          events: [
            {
              name: eventName,
              data: eventData,
            },
          ],
        });

        // Verify the function processed the event correctly
        expect(result.result).toEqual({ success: true });

        // Verify service calls were called with the correct parameters
        expect(
          appService.createContactInHighLevelIfNotExists,
        ).toHaveBeenCalledWith({
          leadId: leadId,
        });

        expect(
          appService.createOpportunityInHighlevelIfNotExists,
        ).toHaveBeenCalledWith({
          leadId: leadId,
        });
      });
  });
});
