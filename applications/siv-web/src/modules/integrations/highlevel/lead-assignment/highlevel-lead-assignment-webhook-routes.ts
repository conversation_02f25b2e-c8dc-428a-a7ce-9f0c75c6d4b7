import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/db/dbclient";
import {
  lead,
  highlevelWebhookIntegrationSettings,
  leadFile,
} from "@/drizzle/schema";
import { HTTPException } from "hono/http-exception";
import { eq } from "drizzle-orm";
import logger from "@/logger";
import { Variables } from "@/auth";

import { LeadAssignedEventPublisher } from "@/modules/leads/domain/events";
import { toDomain } from "@/modules/leads/infrastructure/lead-db-data-mapper";

const HighLevelLeadWebhookSchema = z.object({
  user: z.object({
    firstName: z.string().min(1, "User first name is required"),
    lastName: z.string().min(1, "User last name is required"),
    email: z.string().email("Invalid user email format"),
  }),
  contact_id: z.string().min(1, "Contact ID is required"),
  siv_lead_id: z.string().trim().min(1, "siv_lead_id is required"),
});

export type HighLevelLeadWebhook = z.infer<typeof HighLevelLeadWebhookSchema>;

const querySchema = z.object({
  api_key: z.string(),
});

export function createHighLevelRoutes(deps: {
  leadAssignedEventPublisher: LeadAssignedEventPublisher;
}) {
  const app = new Hono<{ Variables: Variables }>().post(
    "/webhook",
    zValidator("query", querySchema),
    zValidator("json", HighLevelLeadWebhookSchema),
    async (c) => {
      const apiKey = c.req.valid("query").api_key;
      const payload = c.req.valid("json");

      // Verify API key
      const [channelSettings] = await db
        .select()
        .from(highlevelWebhookIntegrationSettings)
        .where(eq(highlevelWebhookIntegrationSettings.webhookSecret, apiKey));

      if (!channelSettings) {
        throw new HTTPException(403, { message: "Invalid API key" });
      }

      try {
        // 1. Find and update the lead with assignment details
        const [updatedLead] = await db
          .update(lead)
          .set({
            assignedSalesRepEmail: payload.user.email,
            assignedAt: new Date().toISOString(),
          })
          .where(eq(lead.id, payload.siv_lead_id))
          .returning();

        if (!updatedLead) {
          logger.error("Lead not found for HighLevel assignment", {
            leadId: payload.siv_lead_id,
            contactId: payload.contact_id,
            userFromHighLevel: payload.user,
          });
          return c.json({ error: "Lead not found" }, 404);
        }

        // Fetch the lead files metadata
        const leadFiles = await db
          .select()
          .from(leadFile)
          .where(eq(leadFile.leadId, updatedLead.id));

        // Transform dates from ISO string format to Date objects
        const leadData = { ...toDomain(updatedLead), files: leadFiles };

        await deps.leadAssignedEventPublisher.publish({
          name: "lead.assigned",
          data: {
            leadId: updatedLead.id,
            leadData,
          },
          id: `lead-assigned-${updatedLead.id}`,
        });

        logger.info("Successfully processed lead assignment", {
          leadId: updatedLead.id,
          assignedTo: payload.user.email,
        });

        return c.json({ success: true });
      } catch (error) {
        logger.error("Error processing lead assignment webhook", {
          error: error instanceof Error ? error.message : "Unknown error",
          payload,
        });
        return c.json({ error: "Internal server error" }, 500);
      }
    },
  );

  return app;
}

export type HighLevelRoutes = ReturnType<typeof createHighLevelRoutes>;
