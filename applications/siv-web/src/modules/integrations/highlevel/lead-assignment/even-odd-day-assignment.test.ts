import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
import {
  isEvenOrOddDay,
  determineEvenOddAssignmentDate,
} from "./even-odd-day-assignment";
import type { Lead } from "@/modules/leads/domain/types";

// Helper function to create date in UTC with specific day in LA time
const createDateWithPacificDay = (day: number) => {
  // Los Angeles is UTC-8 in standard time, UTC-7 during daylight savings
  // We'll use January dates which are in standard time (UTC-8)
  // For a January date, 8am UTC = 12am Pacific
  return new Date(Date.UTC(2023, 0, day, 8, 0, 0));
};

// Helper function to create a mock lead with a specific creation date
const createLeadWithCreationDate = (date: Date): Lead => {
  return {
    id: "123",
    propertyId: "456",
    firstName: "Test",
    lastName: "User",
    email: "<EMAIL>",
    createdAt: date,
  } as Lead;
};

describe("isEvenOrOddDay", () => {
  it('should return "odd" for days that are odd numbers in Pacific Time', () => {
    // Test with odd days (1st, 3rd, 31st)
    const testDates = [
      createDateWithPacificDay(1),
      createDateWithPacificDay(3),
      createDateWithPacificDay(31),
    ];

    testDates.forEach((date) => {
      expect(isEvenOrOddDay(date)).toBe("odd");
    });
  });

  it('should return "even" for days that are even numbers in Pacific Time', () => {
    // Test with even days (2nd, 4th, 30th)
    const testDates = [
      createDateWithPacificDay(2),
      createDateWithPacificDay(4),
      createDateWithPacificDay(30),
    ];

    testDates.forEach((date) => {
      expect(isEvenOrOddDay(date)).toBe("even");
    });
  });

  it("should handle date objects correctly when Pacific Time differs from UTC date", () => {
    // 3:00 UTC Jan 2 = 19:00 PST Jan 1 (odd day in Pacific)
    const crossoverDate = new Date(Date.UTC(2023, 0, 2, 3, 0, 0));
    expect(isEvenOrOddDay(crossoverDate)).toBe("odd");

    // 9:00 UTC Jan 2 = 1:00 PST Jan 2 (even day in Pacific)
    const sameDate = new Date(Date.UTC(2023, 0, 2, 9, 0, 0));
    expect(isEvenOrOddDay(sameDate)).toBe("even");
  });
});

describe("determineEvenOddAssignmentDate", () => {
  beforeEach(() => {
    // Use fake timers for each test
    vi.useFakeTimers();
  });

  afterEach(() => {
    // Restore real timers after each test
    vi.useRealTimers();
  });

  it("should use the lead creation date to determine even/odd day", () => {
    // Create leads with even and odd creation dates
    const evenDayLead = createLeadWithCreationDate(createDateWithPacificDay(2));
    const oddDayLead = createLeadWithCreationDate(createDateWithPacificDay(3));

    expect(determineEvenOddAssignmentDate(evenDayLead)).toBe("even");
    expect(determineEvenOddAssignmentDate(oddDayLead)).toBe("odd");
  });

  it("should handle leads with timezone-crossing creation dates", () => {
    // 3:00 UTC Jan 2 = 19:00 PST Jan 1 (odd day in Pacific)
    const crossoverDateLead = createLeadWithCreationDate(
      new Date(Date.UTC(2023, 0, 2, 3, 0, 0)),
    );
    expect(determineEvenOddAssignmentDate(crossoverDateLead)).toBe("odd");
  });

  it("should fall back to current date if lead creation date is undefined", () => {
    // Create a lead without a creation date
    const leadWithoutDate = { id: "123", propertyId: "456" } as Lead;

    // Mock the current date
    vi.useFakeTimers();
    vi.setSystemTime(createDateWithPacificDay(2)); // Set to January 2nd (even day)

    expect(determineEvenOddAssignmentDate(leadWithoutDate)).toBe("even");

    vi.useRealTimers();
  });
});
