import { Factory } from "fishery";
import { randomUUID } from "crypto";
import { InferInsertModel } from "drizzle-orm";
import { db } from "@/db/dbclient";
import { highlevelWebhookIntegrationSettings } from "@/drizzle/schema";

export const highlevelInboundWebhookSettingsFactory = Factory.define<
  InferInsertModel<typeof highlevelWebhookIntegrationSettings>
>(({ onCreate }) => {
  onCreate(
    async (settings) =>
      (
        await db
          .insert(highlevelWebhookIntegrationSettings)
          .values(settings)
          .returning()
      )[0],
  );

  return {
    propertyId: randomUUID(),
    webhookSecret: "test-webhook-secret",
  };
});
