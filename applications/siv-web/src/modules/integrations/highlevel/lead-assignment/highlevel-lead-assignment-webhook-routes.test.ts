import { beforeEach, describe, expect, it } from "vitest";
import {
  createHighLevelRoutes,
  HighLevelRoutes,
} from "./highlevel-lead-assignment-webhook-routes";
import { Hono } from "hono";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import {
  lead,
  leadFile,
  highlevelWebhookIntegrationSettings,
} from "@/drizzle/schema";
import { mockAuthMiddleware, testRenderer } from "@/test/setup";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { highlevelInboundWebhookSettingsFactory } from "@/modules/integrations/highlevel/lead-assignment/highlevel-inbound-webhook-settings-test-factories";
import { leadFactory } from "@/modules/leads/lead-test-factories";
import { eq } from "drizzle-orm";
import { Lead } from "@/modules/leads/domain/types";
import { InMemoryEventPublisher } from "@/modules/leads/infrastructure/InMemoryEventPublisher";
import { LeadAssignedEvent } from "@/modules/leads/domain/events";
import { toDomain } from "@/modules/leads/infrastructure/lead-db-data-mapper";

function aValidLeadAssignedWebhookPayload(testLead: Lead) {
  return {
    siv_lead_id: testLead.id,
    user: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
    },
    email: "<EMAIL>",
    full_name: "John Doe",
    first_name: "John",
    last_name: "Doe",
    phone: "+12345678901",
    contact_id: "hl_123456",
    city: "Test City",
    state: "CA",
    postal_code: "12345",
    company_name: "Test Company",
    "Preferred Start Date": "2025-03-01",
    "Preferred End Date": "2025-03-05",
    "Guestrooms Needed": "50",
    "Tell Us More": "Test event details",
    "Event Type": "Corporate Meeting",
  };
}

describe("HighLevel Routes", () => {
  let eventPublisher: InMemoryEventPublisher<LeadAssignedEvent>;
  let routes: HighLevelRoutes;
  let app: Hono<{ Variables: Variables }, {}, "/">;

  withTransactionalTests();
  let testLead: Lead;

  beforeEach(async () => {
    app = new Hono<{ Variables: Variables }>();
    eventPublisher = new InMemoryEventPublisher<LeadAssignedEvent>();

    // Add auth middleware and mount routes
    app.use("*", mockAuthMiddleware);
    app.use("*", testRenderer);
    routes = createHighLevelRoutes({
      leadAssignedEventPublisher: eventPublisher,
    });
    app.route("/", routes);
    testLead = await leadFactory.create({
      highlevelContactId: null,
      highlevelOpportunityId: null,
      propertyId: (await propertyFactory.create()).id,
    });
  });

  describe("POST /webhook", () => {
    let validWebhookPayload: ReturnType<
      typeof aValidLeadAssignedWebhookPayload
    >;

    beforeEach(() => {
      validWebhookPayload = aValidLeadAssignedWebhookPayload(testLead);
    });

    it("should return 403 with invalid API key", async () => {
      const res = await app.request("/webhook?api_key=invalid-key", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(validWebhookPayload),
      });

      expect(res.status).toBe(403);
      expect(eventPublisher.getEvents()).toHaveLength(0);
    });

    it("should validate webhook payload schema", async () => {
      const property = await propertyFactory.create();
      const settings = await highlevelInboundWebhookSettingsFactory.create({
        propertyId: property.id,
      });

      const invalidPayload = {
        ...validWebhookPayload,
        siv_lead_id: null, // missing siv_lead_id
      };

      const res = await app.request(
        `/webhook?api_key=${settings.webhookSecret}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(invalidPayload),
        },
      );

      expect(res.status).toBe(400);
      expect(eventPublisher.getEvents()).toHaveLength(0);
    });

    it("should process lead assignment and publish LeadAssigned event", async () => {
      const settings = await highlevelInboundWebhookSettingsFactory.create({
        propertyId: testLead.propertyId,
      });

      // Create a mock lead file
      await db.insert(leadFile).values({
        id: testLead.id,
        leadId: testLead.id,
        fileName: "test-file.pdf",
        s3Key: "submitted/test-file.pdf",
        s3KeyWithoutStatusPrefix: "test-file.pdf",
        contentType: "application/pdf",
      });

      const res = await app.request(
        `/webhook?api_key=${settings.webhookSecret}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(validWebhookPayload),
        },
      );

      expect(res.status).toBe(200);

      // Verify lead was updated
      const [updatedLead] = await db
        .select()
        .from(lead)
        .where(eq(lead.id, testLead.id));

      expect(updatedLead.assignedSalesRepEmail).toBe(
        validWebhookPayload.user.email,
      );
      expect(updatedLead.assignedAt).not.toBeNull();

      // Verify event was published
      expect(eventPublisher.getEvents()).toHaveLength(1);
      const expected: LeadAssignedEvent = {
        data: {
          leadId: testLead.id,
          leadData: {
            ...toDomain(updatedLead),
            files: [
              expect.objectContaining({
                s3Key: "submitted/test-file.pdf",
                fileName: "test-file.pdf",
                contentType: "application/pdf",
              }),
            ],
          },
        },
        id: `lead-assigned-${testLead.id}`,
        name: "lead.assigned",
      };
      expect(eventPublisher.getEvents()[0]).toEqual(expected);
    });

    // Additional test case for lead without files
    it("should process lead assignment and publish LeadAssigned event with empty files array", async () => {
      const settings = await highlevelInboundWebhookSettingsFactory.create({
        propertyId: testLead.propertyId,
      });

      const res = await app.request(
        `/webhook?api_key=${settings.webhookSecret}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(validWebhookPayload),
        },
      );

      expect(res.status).toBe(200);

      // Verify lead was updated
      const [updatedLead] = await db
        .select()
        .from(lead)
        .where(eq(lead.id, testLead.id));

      expect(updatedLead.assignedSalesRepEmail).toBe(
        validWebhookPayload.user.email,
      );
      expect(updatedLead.assignedAt).not.toBeNull();

      // Verify event was published
      expect(eventPublisher.getEvents()).toHaveLength(1);
      const expected: LeadAssignedEvent = {
        data: {
          leadId: testLead.id,
          leadData: { ...toDomain(updatedLead), files: [] },
        },
        id: `lead-assigned-${testLead.id}`,
        name: "lead.assigned",
      };
      expect(eventPublisher.getEvents()[0]).toEqual(expected);
    });

    it("should return 404 when lead not found", async () => {
      const property = await propertyFactory.create();
      const settings = await highlevelInboundWebhookSettingsFactory.create({
        propertyId: property.id,
      });

      const notAValidLeadId = "00000000-0000-0000-0000-000000000000";
      const res = await app.request(
        `/webhook?api_key=${settings.webhookSecret}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...validWebhookPayload,
            siv_lead_id: notAValidLeadId,
          }),
        },
      );

      expect(res.status).toBe(404);
      expect(eventPublisher.getEvents()).toHaveLength(0);
    });
  });
});
