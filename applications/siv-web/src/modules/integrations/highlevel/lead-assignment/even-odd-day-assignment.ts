import type { Lead } from "@/modules/leads/domain/types";
type EvenOrOdd = "even" | "odd";

/**
 * Determines if a calendar day is even or odd in Pacific Time
 *
 * @param date The date to check
 * @returns "even" or "odd" based on the calendar day
 */
export function isEvenOrOddDay(date: Date): EvenOrOdd {
  // Use Pacific Time (America/Los_Angeles for Oregon)
  const pacificTime = date.toLocaleString("en-US", {
    timeZone: "America/Los_Angeles",
  });
  const pacificDate = new Date(pacificTime);
  const day = pacificDate.getDate();
  return day % 2 === 0 ? "even" : "odd";
}

/**
 * Determines the even/odd assignment value for a lead based on its creation date
 * Used for rotating lead assignments at Paradise Point (and potentially other
 * properties in the future)
 *
 * @param lead The lead to determine assignment for
 * @returns "even" or "odd" based on the calendar day when the lead was created
 */
export function determineEvenOddAssignmentDate(lead: Lead): EvenOrOdd {
  // Use the lead's creation date, or fall back to current date if unavailable
  const leadDate = lead.createdAt || new Date();
  return isEvenOrOddDay(leadDate);
}
