import { leadFile } from "@/drizzle/schema";
import logger from "@/logger";
import { SivInngestClient } from "@/inngest-client";
import { HighlevelSyncApplicationService } from "@/modules/integrations/highlevel/HighlevelSyncApplicationService";
import { db } from "@/db/dbclient";
import { eq } from "drizzle-orm";
import { LeadCreatedEvent } from "@/modules/leads/domain/events";

// Type-safe helper to ensure propertyId exists in the event at compile time
const createConcurrencyKey = <T extends LeadCreatedEvent>(
  eventPath: T["data"] extends { propertyId: string } ? "event.data.propertyId" : never
): string => eventPath;

export const createHighLevelLeadSyncWorkflow = (
  inngest: SivInngestClient,
  highlevelSyncService: HighlevelSyncApplicationService,
) =>
  inngest.createFunction(
    {
      id: "lead-sync-highlevel-workflow",
      retries: 3,
      concurrency: [
        {
          key: createConcurrencyKey("event.data.propertyId"),
          limit: 0,
        },
      ],
      throttle: {
        key: createConcurrencyKey("event.data.propertyId"),
        limit: 10,
        period: "1s",
        burst: 1,
      },
    },
    { event: "lead.created" },
    async ({ event, step }) => {
      try {
        const highLevelContactSyncResult = await step.run(
          "sync-contact-to-highlevel",
          async () => {
            const result =
              await highlevelSyncService.createContactInHighLevelIfNotExists({
                leadId: event.data.leadId,
              });
            return result.match(
              (result) => {
                return result;
              },
              (error) => {
                throw new Error(
                  `Failing highlevel contact sync step: ${error.message}`,
                );
              },
            );
          },
        );

        const highlevelOpportunitySyncResult = await step.run(
          "sync-to-highlevel-opportunity",
          async () => {
            const result =
              await highlevelSyncService.createOpportunityInHighlevelIfNotExists(
                { leadId: event.data.leadId },
              );
            return result.match(
              (result) => {
                return result;
              },
              (error) => {
                throw new Error(
                  `Failing highlevel contact sync step: ${error.message}`,
                );
              },
            );
          },
        );

        // Get all file uploads for this lead
        const fileUploads = await step.run("get-file-uploads", async () => {
          return db
            .select()
            .from(leadFile)
            .where(eq(leadFile.leadId, event.data.leadId));
        });

        // Emit the lead-synced-to-highlevel event with HighLevel IDs and file locations
        await step.sendEvent("emit-lead-synced-event", {
          name: "lead.synced-to-highlevel", // Use type assertion to bypass type checking
          id: `lead-synced-${event.data.leadId}`,
          data: {
            leadId: event.data.leadId,
            highLevelContactId: highLevelContactSyncResult.highLevelContactId,
            highLevelOpportunityId:
              highlevelOpportunitySyncResult.highLevelOpportunityId,
            highLevelAuthToken:
              highlevelOpportunitySyncResult.highLevelAuthToken,
            highLevelLocationId:
              highlevelOpportunitySyncResult.highLevelLocationId,
            fileUploads: fileUploads.map((file) => ({
              fileId: file.id,
              s3Key: file.s3Key,
              keyWithoutStatusPrefix: file.s3KeyWithoutStatusPrefix,
              fileName: file.fileName,
              contentType: file.contentType,
            })),
          },
        });

        logger.info(
          {
            leadId: event.data.leadId,
            highLevelContactId: highLevelContactSyncResult.highLevelContactId,
            highLevelOpportunityId:
              highlevelOpportunitySyncResult.highLevelOpportunityId,
            fileCount: fileUploads.length,
          },
          "Emitted lead.synced-to-highlevel event",
        );

        return { success: true };
      } catch (error) {
        logger.error(
          {
            leadId: event.data.leadId,
            error: error instanceof Error ? error.message : "Unknown error",
          },
          "Lead sync workflow failed",
        );

        throw error;
      }
    },
  );
