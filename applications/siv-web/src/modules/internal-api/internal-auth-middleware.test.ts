import { beforeEach, describe, expect, it } from "vitest";
import { createInternalApiRoutes } from "./internal-routes";
import { env } from "@/config/env";

// We do not need database setup for pure auth middleware tests

describe("Internal Auth Middleware (via /test-auth-middleware endpoint)", () => {
  let app: ReturnType<typeof createInternalApiRoutes>;

  beforeEach(() => {
    app = createInternalApiRoutes(false, true);
  });

  it("should return 401 if X-Api-Key header is missing", async () => {
    const res = await app.request("/test-auth-middleware", {
      method: "GET",
    });
    expect(res.status).toBe(401);
    const body = await res.json();
    expect(body.errorType).toBe("authentication_error");
  });

  it("should return 403 if X-Api-Key header is invalid", async () => {
    const res = await app.request("/test-auth-middleware", {
      method: "GET",
      headers: {
        "X-Api-Key": "invalid-secret",
      },
    });
    expect(res.status).toBe(403);
    const body = await res.json();
    expect(body.errorType).toBe("authentication_error");
  });

  it("should return 200 and success if X-Api-Key header is valid", async () => {
    const res = await app.request("/test-auth-middleware", {
      method: "GET",
      headers: {
        "X-Api-Key": env.INTERNAL_API_SECRET,
      },
    });
    expect(res.status).toBe(200);
    const body = await res.json();
    expect(body).toEqual({ success: true, message: "Auth middleware passed" });
  });
});
