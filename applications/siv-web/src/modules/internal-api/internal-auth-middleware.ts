import { Context, Next } from "hono";
import { env } from "@/config/env";
import logger from "@/logger";

export function createInternalAuthMiddleware(
  disableAuthorizationForContractTests = false,
) {
  /**
   * Middleware to authenticate internal API requests using a shared secret
   * Validates the X-Api-Key header against the INTERNAL_API_SECRET environment variable
   */
  const internalAuthMiddleware = async (c: Context, next: Next) => {
    const secret = c.req.header("X-Api-Key");

    if (!secret) {
      logger.warn("Internal API request missing X-Api-Key header");
      return c.json(
        {
          errorType: "authentication_error",
          message: "Authentication failed: missing internal secret",
        },
        401,
      );
    }

    // Skip validation for contract tests if flag is set
    if (disableAuthorizationForContractTests) {
      logger.info("Skipping API key validation for contract tests");
      await next();
      return;
    }

    // Regular validation
    if (secret !== env.INTERNAL_API_SECRET) {
      logger.warn("Internal API request with invalid X-Api-Key");
      return c.json(
        {
          errorType: "authentication_error",
          message: "Authentication failed: invalid internal secret",
        },
        403,
      );
    }

    await next();
  };

  return internalAuthMiddleware;
}
