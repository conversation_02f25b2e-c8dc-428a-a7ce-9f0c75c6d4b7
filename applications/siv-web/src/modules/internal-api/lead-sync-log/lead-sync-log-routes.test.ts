import { beforeAll, beforeEach, describe, expect, it } from "vitest";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { env } from "@/config/env";
import { db } from "@/db/dbclient";
import { createLeadSyncLogRoutes } from "@/modules/internal-api/lead-sync-log/lead-sync-log-routes";
import { randomUUID } from "crypto";
import { integrationLeadSyncLog } from "@/drizzle/schema";
import { leadFactory } from "@/modules/leads/lead-test-factories";
import { stsIntegrationSettingsFactory } from "@/modules/integrations/sts/sts-test-factories";

describe("Lead Sync Log Routes", () => {
  withTransactionalTests();

  let app: ReturnType<typeof createLeadSyncLogRoutes>;
  let testPropertyId: string;

  beforeAll(async () => {
    const property = await propertyFactory.create();
    testPropertyId = property.id as string;
  });

  beforeEach(async () => {
    app = createLeadSyncLogRoutes();
  });

  describe("GET /leads/:leadId/sync-log", () => {
    it("should return 404 if no lead is found with that id", async () => {
      const notARealLeadId = randomUUID();
      const res = await app.request(
        `/leads/${notARealLeadId}/sync-log?integrationType=sts`,
        {
          method: "GET",
          headers: {
            "X-Api-Key": env.INTERNAL_API_SECRET,
          },
        },
      );

      expect(res.status).toBe(404);
    });

    it("should return lead sync log if found", async () => {
      // Create a test lead and STS settings
      const testLead = await leadFactory.create({ propertyId: testPropertyId });
      const stsSettings = await stsIntegrationSettingsFactory.create({
        propertyId: testPropertyId,
      });

      // Insert a sync log for the lead
      const leadSyncLog = await db
        .insert(integrationLeadSyncLog)
        .values({
          leadId: testLead.id,
          integrationType: "sts",
          integrationLeadId: "12345",
          syncedAt: new Date().toISOString(),
        })
        .returning();

      // Make the request
      const res = await app.request(
        `/leads/${testLead.id}/sync-log?integrationType=sts`,
        {
          method: "GET",
          headers: {
            "X-Api-Key": env.INTERNAL_API_SECRET,
          },
        },
      );

      expect(res.status).toBe(200);
      const body = await res.json();
      expect(body).toEqual({ data: leadSyncLog });
    });

    it("should return 400 if the propertyId is not a valid UUID", async () => {
      const res = await app.request(
        "/leads/invalid-uuid/sync-log?integrationType=sts",
        {
          method: "GET",
          headers: {
            "X-Api-Key": env.INTERNAL_API_SECRET,
          },
        },
      );

      expect(res.status).toBe(400);
    });
  });
});
