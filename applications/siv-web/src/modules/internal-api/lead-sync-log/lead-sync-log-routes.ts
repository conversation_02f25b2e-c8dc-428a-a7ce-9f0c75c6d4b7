import { Hono } from "hono";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import {
  integrationLeadSyncLog,
  integrationType,
  lead,
  stsIntegrationSettings,
} from "@/drizzle/schema";
import { and, desc, eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import logger from "@/logger";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

const paramsSchema = z.object({
  leadId: z.string().uuid(),
});

const querySchema = z.object({
  integrationType: z.enum(integrationType.enumValues),
});

const createSyncLogSchema = z.object({
  integrationType: z.enum(integrationType.enumValues),
  integrationLeadId: z.string(),
});

// Create routes
export function createLeadSyncLogRoutes() {
  const app = new Hono<{ Variables: Variables }>();

  app.get(
    "/leads/:leadId/sync-log",
    zValidator("param", paramsSchema),
    zValidator("query", querySchema),
    async (c) => {
      const { leadId } = c.req.valid("param");
      const { integrationType } = c.req.valid("query");

      console.log(
        "[GET /leads/:leadId/sync-log] leadId:",
        leadId,
        "integrationType:",
        integrationType,
      );
      const leadRecord = await db
        .select()
        .from(lead)
        .where(eq(lead.id, leadId));
      console.log("[GET /leads/:leadId/sync-log] leadRecord:", leadRecord);

      if (leadRecord.length === 0) {
        console.log(
          "[GET /leads/:leadId/sync-log] Lead not found, returning 404",
        );
        return c.json(
          {
            errorType: "not_found_error",
            message: `Lead with id ${leadId} not found`,
          },
          404,
        );
      }
      const syncLogResults = await db
        .select()
        .from(integrationLeadSyncLog)
        .where(
          and(
            eq(integrationLeadSyncLog.leadId, leadId),
            eq(integrationLeadSyncLog.integrationType, integrationType),
          ),
        )
        .orderBy(desc(integrationLeadSyncLog.syncedAt));
      console.log(
        "[GET /leads/:leadId/sync-log] syncLogResults:",
        syncLogResults,
      );

      return c.json({ data: syncLogResults });
    },
  );

  // POST endpoint for creating a lead sync log entry - POST /leads/:leadId/sync-log
  app.post(
    "/leads/:leadId/sync-log",
    zValidator("param", paramsSchema),
    zValidator("json", createSyncLogSchema),
    async (c) => {
      const { leadId } = c.req.valid("param");
      const body = c.req.valid("json");

      console.log(
        "[POST /leads/:leadId/sync-log] leadId:",
        leadId,
        "body:",
        body,
      );
      try {
        // Verify lead exists
        const leadRecord = await db
          .select()
          .from(lead)
          .where(eq(lead.id, leadId));
        console.log("[POST /leads/:leadId/sync-log] leadRecord:", leadRecord);

        if (leadRecord.length === 0) {
          console.log(
            "[POST /leads/:leadId/sync-log] Lead not found, returning 404",
          );
          return c.json(
            {
              errorType: "not_found_error",
              message: `Lead with id ${leadId} not found`,
            },
            404,
          );
        }

        // Create the sync log
        const syncLog = await db
          .insert(integrationLeadSyncLog)
          .values({
            leadId: leadId,
            integrationType: body.integrationType,
            integrationLeadId: body.integrationLeadId,
          })
          .returning();
        console.log("[POST /leads/:leadId/sync-log] Created syncLog:", syncLog);

        logger.info("Created lead sync log", {
          leadId,
          integrationType: body.integrationType,
          integrationLeadId: body.integrationLeadId,
        });

        return c.json(syncLog[0], 201);
      } catch (error) {
        logger.error("Error creating lead sync log", {
          leadId,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        console.log("[POST /leads/:leadId/sync-log] Error:", error);
        throw new HTTPException(500, {
          message: "Failed to create lead sync log",
        });
      }
    },
  );

  return app;
}
