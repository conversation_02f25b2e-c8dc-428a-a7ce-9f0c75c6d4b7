import { Hono } from "hono";
import { Variables } from "@/auth";
import { createInternalStsSettingsRoutes } from "./sts-settings/sts-settings-routes";
import { createLeadSyncLogRoutes } from "./lead-sync-log/lead-sync-log-routes";
import { createInternalAuthMiddleware } from "@/modules/internal-api/internal-auth-middleware";

export function createInternalApiRoutes(
  disableAuthorizationForContractTests = false,
  addTestRoute = false,
) {
  const app = new Hono<{ Variables: Variables }>();
  app.use(
    "*",
    createInternalAuthMiddleware(disableAuthorizationForContractTests),
  );

  if (addTestRoute) {
    app.get("/test-auth-middleware", (c) => {
      return c.json({ success: true, message: "Auth middleware passed" });
    });
  }

  // Mount STS settings routes
  app.route("", createInternalStsSettingsRoutes());

  // Mount lead sync log routes
  app.route("", createLeadSyncLogRoutes());

  return app;
}
