import { Hono } from "hono";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import { stsIntegrationSettings } from "@/drizzle/schema";
import { eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import logger from "@/logger";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

// Define schemas
const paramsSchema = z.object({
  propertyId: z.string().uuid(),
});

// Create routes
export function createInternalStsSettingsRoutes() {
  const app = new Hono<{ Variables: Variables }>();

  // GET endpoint for STS settings by propertyId
  app.get(
    "/properties/:propertyId/sts-settings",
    zValidator("param", paramsSchema),
    async (c) => {
      const { propertyId } = c.req.valid("param");

      try {
        // Query database for STS settings
        const results = await db
          .select({
            stsUsername: stsIntegrationSettings.stsUsername,
            apiKey: stsIntegrationSettings.apiKey,
            rfpFormId: stsIntegrationSettings.rfpFormId,
          })
          .from(stsIntegrationSettings)
          .where(eq(stsIntegrationSettings.propertyId, propertyId));

        // Check if settings exist
        if (results.length === 0) {
          logger.info(
            `No STS integration settings found for property ${propertyId}`,
          );
          return c.json(
            {
              errorType: "not_found_error",
              message: `No STS integration settings found for property ${propertyId}`,
            },
            404,
          );
        }

        return c.json(results[0]);
      } catch (error) {
        logger.error("Error fetching STS settings", { error, propertyId });
        throw new HTTPException(500, {
          message: "Failed to fetch STS settings",
        });
      }
    },
  );

  return app;
}
