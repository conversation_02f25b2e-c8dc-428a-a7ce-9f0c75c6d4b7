import { afterEach, beforeAll, beforeEach, describe, expect, it } from "vitest";
import { createInternalStsSettingsRoutes } from "./sts-settings-routes";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { env } from "@/config/env";
import { stsIntegrationSettingsFactory } from "@/modules/integrations/sts/sts-test-factories";

describe("STS Settings Routes", () => {
  withTransactionalTests();

  let app: ReturnType<typeof createInternalStsSettingsRoutes>;
  let testPropertyId: string;

  beforeAll(async () => {
    const property = await propertyFactory.create();
    testPropertyId = property.id as string;
  });

  beforeEach(async () => {
    app = createInternalStsSettingsRoutes();
  });

  afterEach(async () => {});

  describe("GET /properties/:propertyId/sts-settings", () => {
    it("should return 404 if no STS settings are found", async () => {
      const res = await app.request(
        `/properties/${testPropertyId}/sts-settings`,
        {
          method: "GET",
          headers: {
            "X-Api-Key": env.INTERNAL_API_SECRET,
          },
        },
      );

      expect(res.status).toBe(404);
      const body = await res.json();
      expect(body.errorType).toBe("not_found_error");
    });

    it("should return STS settings if found", async () => {
      // Create STS settings using factory
      const stsSettings = await stsIntegrationSettingsFactory.create({
        propertyId: testPropertyId,
        stsUsername: "test-username",
        apiKey: "test-api-key",
        rfpFormId: "test-rfp-form-id",
      });

      const res = await app.request(
        `/properties/${testPropertyId}/sts-settings`,
        {
          method: "GET",
          headers: {
            "X-Api-Key": env.INTERNAL_API_SECRET,
          },
        },
      );

      expect(res.status).toBe(200);
      const body = await res.json();
      expect(body).toEqual({
        stsUsername: stsSettings.stsUsername,
        apiKey: stsSettings.apiKey,
        rfpFormId: stsSettings.rfpFormId,
      });
    });

    it("should return 400 if the propertyId is not a valid UUID", async () => {
      const res = await app.request("/properties/invalid-uuid/sts-settings", {
        method: "GET",
        headers: {
          "X-Api-Key": env.INTERNAL_API_SECRET,
        },
      });

      expect(res.status).toBe(400);
    });
  });
});
