import { <PERSON><PERSON>, Middle<PERSON>Handler } from "hono";
import { clerkMiddleware, getAuth } from "@hono/clerk-auth";
import { User } from "@/user";
import logger from "./logger";
import { env } from "@/config/env";

// Define Hono variable types
export type Variables = {
  user: User | null;
  clerkAuth: ReturnType<typeof getAuth> | null;
};

export const createAuthMiddleware = (app: Hono<{ Variables: Variables }>) => {
  const clerkSecretKey = env.SIV_ADMIN_CLERK_SECRET_KEY;
  const disableAuthenticationForTesting = env.DISABLE_AUTH_FOR_TESTING;
  // For server-side, we need to use process.env, not import.meta.env
  const publishableKey = env.SIV_ADMIN_CLERK_PUBLISHABLE_KEY;

  logger.info(
    {
      hasClerkSecretKey: !!clerkSecretKey,
      clerkSecretKeyPrefix: clerkSecretKey?.substring(0, 7),
      disableAuthenticationForTesting,
      hasPublishableKey: !!publishableKey,
      publishableKeyPrefix: publishableKey?.substring(0, 7),
    },
    "Auth middleware configuration",
  );

  if (!disableAuthenticationForTesting) {
    if (!clerkSecretKey) {
      throw new Error(
        "SIV_ADMIN_CLERK_SECRET_KEY environment variable is required",
      );
    }

    if (!publishableKey) {
      throw new Error(
        "SIV_ADMIN_CLERK_PUBLISHABLE_KEY environment variable is required",
      );
    }

    // Apply Clerk middleware to the app - this is crucial!
    logger.info("Applying Clerk middleware to app");
    app.use(
      "*",
      clerkMiddleware({
        secretKey: clerkSecretKey,
        publishableKey: publishableKey,
      }),
    );
  } else {
    logger.warn(
      {
        disableAuthenticationForTesting,
      },
      "Clerk middleware NOT applied due to test auth settings",
    );
  }

  const protect: MiddlewareHandler<{ Variables: Variables }> = async (
    c,
    next,
  ) => {
    const currentPath = new URL(c.req.url).pathname;

    // Skip authentication for auth routes and Clerk routes to prevent redirect loops
    const publicPaths = [
      "/auth/",
      "/sign-in",
      "/sign-up",
      "/sign-out",
      "/clerk/",
      "/health",
      "/api/inngest",
      "/static/",
      "/forms/", // Public form routes
      "/api/integrations/", // Webhook routes
      "/internal/api/", // Internal API has its own auth
    ];

    if (publicPaths.some((path) => currentPath.startsWith(path))) {
      logger.info(
        { path: currentPath },
        "[protect] Skipping auth check for public route",
      );
      await next();
      return;
    }

    let user: User | null = null;
    let clerkAuth = null;

    if (!disableAuthenticationForTesting) {
      // Get Clerk authentication info
      clerkAuth = getAuth(c);

      logger.debug(
        {
          path: currentPath,
          clerkAuth: {
            userId: clerkAuth?.userId,
            sessionId: clerkAuth?.sessionId,
            claims: clerkAuth?.sessionClaims,
          },
        },
        "[protect] Clerk auth state",
      );

      if (clerkAuth?.userId) {
        // Transform Clerk user data into our generic User type
        // Note: For more user details, you would need to call Clerk's API
        user = {
          id: clerkAuth.userId,
          provider: "clerk", // Using Clerk authentication
        };
        logger.info(
          { userId: user.id, path: currentPath },
          "[protect] User authenticated",
        );
      } else {
        logger.info({ path: currentPath }, "[protect] No Clerk userId found");
      }
    } else {
      // Testing mode
      user = { id: "test-user-id", provider: "email" };
      logger.info({ path: currentPath }, "[protect] Using test auth mode");
    }

    if (!user) {
      const currentPath = new URL(c.req.url).pathname;

      logger.info(
        { path: currentPath },
        "[protect] Redirecting unauthenticated request from path",
      );

      return c.redirect("/auth/login");
    }

    c.set("user", user);
    c.set("clerkAuth", clerkAuth);
    await next();
  };

  return protect;
};
