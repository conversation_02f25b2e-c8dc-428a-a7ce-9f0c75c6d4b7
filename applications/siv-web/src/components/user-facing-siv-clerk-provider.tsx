import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/clerk-react";

// Extend the Window interface to include our custom global variable
declare global {
  interface Window {
    USER_FACING_SIV_CLERK_PUBLISHABLE_KEY?: string;
  }
}

/**
 * Gets the Clerk publishable key from the appropriate source:
 * - Server-side: from process.env
 * - Client-side: from window object (injected by server renderer)
 */
function getPublishableKey(): string {
  // Check if we're on the server
  if (typeof window === "undefined") {
    // Server-side: get from process.env
    const key = process.env.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY;
    if (!key) {
      throw new Error(
        "USER_FACING_SIV_CLERK_PUBLISHABLE_KEY is required but not found in process.env",
      );
    }
    return key;
  } else {
    // Client-side: get from window object
    const key = window.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY;
    if (!key) {
      throw new Error(
        "USER_FACING_SIV_CLERK_PUBLISHABLE_KEY is required but not found on window object. " +
          "This should be injected by the server-side renderer.",
      );
    }
    return key;
  }
}

/**
 * A specialized Clerk provider for the user-facing Siv application.
 *
 * This component automatically gets the Clerk publishable key from:
 * - process.env during SSR (server-side rendering)
 * - window.USER_FACING_SIV_CLERK_PUBLISHABLE_KEY on the client (injected by renderer)
 *
 * This allows the component to work in both environments without
 * requiring the key to be passed as a prop.
 */
export const UserFacingSivClerkProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const publishableKey = getPublishableKey();

  return (
    <ClerkProvider publishableKey={publishableKey}>{children}</ClerkProvider>
  );
};
