import { useEffect, useState } from "react";

export const useIsClient = () => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  return isClient;
};

export const ClientOnly = (params: {
  serverRender?: React.ReactNode;
  children: React.ReactNode;
}) => {
  let serverRender = params.serverRender;
  if (!params.serverRender) {
    serverRender = <></>;
  }
  const isClient = useIsClient();
  return isClient ? params.children : serverRender;
};
