import dotenvFlow from 'dotenv-flow';

dotenvFlow.config();

import {defineConfig} from 'drizzle-kit';
import {env} from "@/config/env";

export default defineConfig({
    out: './src/drizzle',
    schema: './src/db/schema.ts',
    dialect: 'postgresql',
    dbCredentials: {
        user: env.DATABASE_USER,
        database: env.DATABASE_NAME,
        host: env.DATABASE_HOST,
        port: parseInt(env.DATABASE_PORT),
        password: env.DATABASE_PASSWORD,
        ssl: false
    },
});

