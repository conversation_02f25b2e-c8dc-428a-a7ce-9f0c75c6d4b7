import type { Preview, Decorator } from '@storybook/react'
import '../src/app.css'; // Import application CSS
import 'intl-tel-input/build/css/intlTelInput.css'; // Import intl-tel-input CSS

// Simple preview configuration
const preview: Preview = {
  decorators: [],
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    }
  },
};

console.log('Storybook preview initialized with styles and decorators');

export default preview;
