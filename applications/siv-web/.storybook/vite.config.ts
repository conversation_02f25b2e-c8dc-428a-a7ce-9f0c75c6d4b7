import { defineConfig, Plugin } from 'vite';
import * as path from 'path';
import react from '@vitejs/plugin-react';

// Simple utility plugin for intl-tel-input
function intlTelInputPlugin(): Plugin {
  return {
    name: 'intl-tel-input-utils',
    resolveId(id) {
      if (id === 'intl-tel-input/build/js/utils.js' || id === 'intl-tel-input/utils') {
        return '\0intl-tel-input:utils';
      }
    },
    load(id) {
      if (id === '\0intl-tel-input:utils') {
        return `
          // ESM-compatible mock of intl-tel-input utils
          export default {
            formatNumber: () => '',
            getCountryCode: () => '',
            getExtension: () => '',
            getNumber: () => '',
            getNumberType: () => -99,
            getSelectedCountryData: () => ({}),
            isValidNumber: () => true,
            setCountry: () => {},
            setNumber: () => {},
            setPlaceholderNumberType: () => {}
          };
        `;
      }
    }
  };
}

// Simplified Vite config for Storybook - client-side only
export default defineConfig({
  // Use client-side only rendering
  build: {
    ssr: false,
    commonjsOptions: {
      transformMixedEsModules: true
    }
  },
  
  // Essential plugins
  plugins: [
    // React plugin with minimal configuration
    react({
      jsxImportSource: 'react',
      jsxRuntime: 'automatic',
    }),
    intlTelInputPlugin()
  ],
  
  // Ensure React is properly bundled
  optimizeDeps: {
    include: [
      'react', 
      'react-dom',
      'intl-tel-input'
    ],
    force: true
  },
  
  // Simple resolve settings
  resolve: {
    dedupe: ['react', 'react-dom']
  },
  
  // Basic environment settings
  define: {
    'global': 'window',
  }
}); 
