import { StorybookConfig } from "@storybook/react-vite";
import { mergeConfig } from 'vite';
import * as path from 'path';

// Simple, minimal Storybook configuration
const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@storybook/addon-essentials",
    "@storybook/addon-onboarding",
    "@chromatic-com/storybook",
    "@storybook/experimental-addon-test"
  ],
  framework: {
    name: "@storybook/react-vite",
    options: {
      strictMode: false
    }
  },
  
  // Simplified Vite configuration
  async viteFinal(config, { configType }) {
    return mergeConfig(config, {
      // Ensure client-only mode
      appType: 'spa',
      
      // Ensure build works
      build: {
        ssr: false,
        commonjsOptions: {
          transformMixedEsModules: true
        }
      },
      // Minimal environment settings
      define: {
        'global': 'window',
        'process.env.NODE_ENV': JSON.stringify(configType === 'PRODUCTION' ? 'production' : 'development'),
      },
      // Use dedupe to prevent multiple instances of React
      resolve: {
        dedupe: ['react', 'react-dom']
      },
      // Ensure React is pre-bundled correctly
      optimizeDeps: {
        include: ['react', 'react-dom'],
        force: true
      },
      // DISABLE THE ERROR OVERLAY
      server: {
        hmr: {
          overlay: false
        },
        // Allow all files to be served
        fs: {
          strict: false,
          allow: [
            // Allow the entire project and node_modules
            path.resolve(__dirname, '../')
          ]
        }
      }
    });
  }
};

export default config;