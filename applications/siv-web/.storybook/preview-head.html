<script>
  // Minimal environment for Storybook with React 19
  // Only setting globals that won't conflict with ESM modules
  if (typeof window !== 'undefined') {
    // Ensure global object for compatibility
    window.global = window;
    
    // Basic environment settings
    window.process = window.process || { 
      env: { NODE_ENV: 'development' }
    };
    
    // Add minimal exports object for compatibility
    // This only provides the minimal exports object used by some files
    // without introducing the full CommonJS/ESM compatibility layer
    if (typeof exports === 'undefined') {
      window.exports = {};
    }
    
    console.log('Basic environment globals set for Storybook');
  }
</script> 