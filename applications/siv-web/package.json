{"type": "module", "scripts": {"dev": "vite", "dev:e2e": "NODE_ENV=e2e vite", "build": "rimraf dist && pnpm run build:client && pnpm run build:server && pnpm run postbuild", "build:client": "vite build --mode client", "build:server": "vite build", "build:local": "rimraf dist && pnpm run build:client && pnpm run build:server && pnpm run postbuild && rimraf dist/server/drizzle && echo 'Note: ln -s needs a cross-platform solution like symlink-cli for Windows' && cp -r applications/siv-web/drizzle dist/server/drizzle", "postbuild": "mkdirp dist/server/client && cpy dist/client/.vite/manifest.json dist/server/client", "start": "NODE_PATH=./node_modules node --experimental-specifier-resolution=node --enable-source-maps --loader ./loader.js dist/server/index.js", "start:e2e": "NODE_ENV=e2e NODE_PATH=./node_modules DOTENV_FLOW_PATH=../siv-e2e-tests DOTENV_FLOW_FILES=.env,.env.e2e node -r dotenv-flow/config --experimental-specifier-resolution=node --enable-source-maps --loader ./loader.js dist/server/index.js", "start:e2e:container": "node e2e-container.js", "start:e2e:all": "pnpm run build:local && echo 'Build complete. Starting e2e container...' && pnpm run start:e2e:container", "healthcheck": "node ./src/healthcheck.js", "test": "vitest run", "test:unit": "vitest --config vitest.unit.config.ts", "test:routes": "vitest --config vitest.routes.config.ts", "test:all": "pnpm run test:unit && pnpm run test:routes", "test:pact": "USE_PACT_BROKER=true vitest run --testTimeout=60000 src/pact-tests/internal-api.pact.provider.test.ts", "pact:test": "vitest run $(pnpm dlx glob-cli '**/*.pact.test.ts')", "pact:test:consumer": "vitest run $(pnpm dlx glob-cli '**/*.consumer.pact.test.ts')", "pact:test:provider": "vitest run $(pnpm dlx glob-cli '**/*.provider.pact.test.ts')", "pact:verify": "USE_PACT_BROKER=true vitest run --testTimeout=60000 src/pact-tests/internal-api.pact.provider.test.ts", "pact:can-i-deploy-api": "node ./node_modules/@siv/pact-helpers/dist/bin/track-deployment.js check --env staging --participant siv-web", "pact:can-i-deploy-events": "node ./node_modules/@siv/pact-helpers/dist/bin/track-deployment.js check --env staging --participant siv-web-event-provider", "pact:can-i-deploy": "pnpm pact:can-i-deploy-api && pnpm pact:can-i-deploy-events", "pact:record-deployment-api": "tsx record-latest-deployment.ts siv-web staging", "pact:record-deployment-events": "tsx record-latest-deployment.ts siv-web-event-provider staging", "pact:record-deployment": "pnpm pact:record-deployment-api && pnpm pact:record-deployment-events", "pact:publish-highlevel-lead-sync-workflow": "pnpm dlx tsx ./node_modules/@siv/pact-helpers/src/bin/publish-pacts.ts high-level-lead-sync-workflow", "pact:publish": "pnpm pact:publish-highlevel-lead-sync-workflow", "datadog-ci": "datadog-ci", "upload-source-maps": "node scripts/upload-source-maps.cjs", "drizzle-kit": "NODE_ENV=development drizzle-kit", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx}\"", "typecheck": "tsc --noEmit", "build:all": "pnpm build:client && pnpm build:server", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "pnpm dlx chromatic --project-token=chpt_6b8f3fe9faa7335", "check-types": "tsc --noEmit", "track-deps": "tsx ../../scripts/track-deps.ts", "invoke-lead-api": "tsx scripts/invoke-lead-api.ts"}, "name": "siv-web", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.777.0", "@aws-sdk/client-sqs": "^3.744.0", "@aws-sdk/s3-presigned-post": "^3.777.0", "@aws-sdk/s3-request-presigner": "^3.777.0", "@clerk/backend": "^2.0.0", "@clerk/clerk-react": "^5.31.9", "@datadog/datadog-api-client": "^1.32.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hono/clerk-auth": "^2.0.0", "@hono/node-server": "^1.13.8", "@hono/oauth-providers": "^0.6.2", "@hono/react-renderer": "^0.2.1", "@hono/zod-openapi": "^0.19.4", "@hono/zod-validator": "^0.4.3", "@hookform/resolvers": "^3.10.0", "@iframe-resizer/child": "^5.3.3", "@leeoniya/ufuzzy": "^1.0.18", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.8", "@scalar/hono-api-reference": "^0.8.2", "@sindresorhus/slugify": "^2.2.1", "@siv/highlevel-client": "workspace:*", "@siv/lead-sync-field-helpers": "workspace:*", "@siv/pact-helpers": "workspace:*", "@types/intl-tel-input": "^18.1.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "countries-list": "^3.1.1", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "dd-trace": "^5.39.0", "dotenv": "^16.4.7", "dotenv-flow": "^4.1.0", "drizzle-orm": "^0.41.0", "framer-motion": "^12.0.6", "fuse.js": "^7.1.0", "fuzzysort": "^3.1.0", "hono": "^4.7.1", "hono-rate-limiter": "^0.4.2", "hono-sessions": "^0.7.1", "inngest": "^3.31.7", "intl-tel-input": "25.3.0", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.20", "lucide-react": "^0.474.0", "motion": "^12.0.6", "neverthrow": "8.2.0", "next-themes": "^0.4.4", "pg": "^8.14.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.54.2", "react-joyride-react-19": "^2.9.2", "react-share": "^5.1.2", "reactflow": "^11.11.4", "recharts": "^2.15.1", "remarkable": "^2.0.1", "sonner": "^1.7.4", "survey-core": "^1.12.22", "survey-react-ui": "^1.12.22", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "ua-parser-js": "^2.0.2", "usehooks-ts": "^3.1.0", "uuid": "^11.1.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/plugin-transform-react-display-name": "^7.25.9", "@chromatic-com/storybook": "^3.2.5", "@datadog/datadog-ci": "^2.48.0", "@datadog/vite-plugin": "^2.4.1", "@faker-js/faker": "^9.4.0", "@hono/vite-build": "^1.3.0", "@hono/vite-dev-server": "^0.18.1", "@inngest/test": "^0.1.5", "@pact-foundation/pact": "^15.0.1", "@pact-foundation/pact-cli": "16.0.7", "@pact-foundation/pact-node": "^10.18.0", "@rollup/plugin-commonjs": "^28.0.3", "@storybook/addon-essentials": "^8.6.4", "@storybook/addon-onboarding": "^8.6.4", "@storybook/blocks": "^8.6.4", "@storybook/experimental-addon-test": "^8.6.4", "@storybook/react": "^8.6.4", "@storybook/react-vite": "^8.6.4", "@storybook/test": "^8.6.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/iframe-resizer": "^4.0.0", "@types/node": "^20.17.19", "@types/pg": "^8.11.11", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/remarkable": "^2.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/browser": "3.1.2", "@vitest/coverage-v8": "3.1.2", "@vitest/ui": "3.1.2", "autoprefixer": "^10.4.20", "chromatic": "^11.27.0", "cpy-cli": "^5.0.0", "drizzle-kit": "^0.30.6", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-storybook": "^0.11.4", "fishery": "^2.2.3", "glob": "^11.0.1", "glob-cli": "^1.0.0", "happy-dom": "^17.3.0", "mkdirp": "^3.0.1", "msw": "^2.7.0", "nock": "^14.0.0", "pg-transactional-tests": "^1.2.0", "playwright": "^1.51.0", "postcss": "^8.5.1", "prettier": "^3.5.1", "resolve-tspaths": "^0.8.23", "rimraf": "^6.0.1", "rollup-preserve-directives": "^1.1.3", "storybook": "^8.6.4", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5.8.3", "vite": "^6.0.11", "vite-plugin-markdown": "^2.2.0", "vite-plugin-node-polyfills": "^0.23.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2", "vitest-mock-extended": "^2.0.2"}}