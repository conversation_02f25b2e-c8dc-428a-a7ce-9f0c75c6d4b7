import iframeResize from '@iframe-resizer/parent';

/**
 * SIV Form Embed Library - Parent Script
 * 
 * This script adds iframe-resizer functionality and handles redirect messages 
 * from embedded SIV forms. It performs the actual page navigation when an 
 * embedded form sends a redirect signal.
 */

// Enhanced debug logger with stringification for objects
const debug = {
  log: (message: string, ...data: any[]) => {
    // Process any objects for better visibility
    const processedData = data.map(item => 
      typeof item === 'object' && item !== null ? JSON.stringify(item, null, 2) : item
    );
    console.log(`%c[SIV-PARENT]%c ${message}`, 'background:#0077ff;color:#fff;font-weight:bold;', '', ...processedData);
  },
  
  warn: (message: string, ...data: any[]) => {
    // Process any objects for better visibility
    const processedData = data.map(item => 
      typeof item === 'object' && item !== null ? JSON.stringify(item, null, 2) : item
    );
    console.warn(`%c[SIV-PARENT]%c ${message}`, 'background:#ff9900;color:#fff;font-weight:bold;', '', ...processedData);
  },
  
  error: (message: string, ...data: any[]) => {
    // Process any objects for better visibility
    const processedData = data.map(item => 
      typeof item === 'object' && item !== null ? JSON.stringify(item, null, 2) : item
    );
    console.error(`%c[SIV-PARENT]%c ${message}`, 'background:#ff0000;color:#fff;font-weight:bold;', '', ...processedData);
  },
  
  important: (message: string, ...data: any[]) => {
    // Process any objects for better visibility
    const processedData = data.map(item => 
      typeof item === 'object' && item !== null ? JSON.stringify(item, null, 2) : item
    );
    console.log(`%c[SIV-PARENT]%c ${message}`, 'background:#ff00ff;color:#fff;font-weight:bold;', '', ...processedData);
  }
};

// Track whether we've received a redirect that we're processing to avoid handling duplicates
let isRedirectInProgress = false;

/**
 * Perform the actual redirect in the parent page
 */
function redirectToUrl(url: string): void {
  if (!url) {
    debug.warn('Redirect attempt with empty URL');
    return;
  }
  
  // Prevent multiple redirects
  if (isRedirectInProgress) {
    debug.warn('Redirect already in progress, ignoring additional redirect request');
    return;
  }
  
  isRedirectInProgress = true;
  debug.important(`🚨 REDIRECTING TO: ${url}`);
  debug.log('Current window location is:', window.location.href);
  
  try {
    // Simply navigate to the URL
    window.location.href = url;
    debug.log('Navigation initiated');
  } catch (error) {
    debug.error('Error redirecting:', error);
    isRedirectInProgress = false; // Reset flag if redirect fails
  }
}

/**
 * Process a message that might contain redirect instructions
 * Using standardized format: { type: 'redirect', url: 'https://...' }
 */
function processMessage(message: any): void {
  debug.log('Processing message:', message);
  
  // Check if this is a redirect message
  if (!message) return;
  
  // Direct format: { type: 'redirect', url: 'https://...' }
  if (typeof message === 'object' && message.type === 'redirect' && message.url) {
    debug.important(`🔄 Redirect message detected with URL: ${message.url}`);
    redirectToUrl(message.url);
    return;
  }
  
  // Check within message.message if it exists (iframe-resizer wraps this way)
  if (typeof message === 'object' && message.message) {
    debug.log('Message contains nested message property, checking inside:', message.message);
    
    // Nested format: { message: { type: 'redirect', url: 'https://...' } }
    if (typeof message.message === 'object' && message.message.type === 'redirect' && message.message.url) {
      debug.important(`🔄 Nested redirect message detected with URL: ${message.message.url}`);
      redirectToUrl(message.message.url);
      return;
    }
    
    // Handle stringified JSON messages
    if (typeof message.message === 'string') {
      try {
        const parsedMessage = JSON.parse(message.message);
        if (parsedMessage && typeof parsedMessage === 'object' && 
            parsedMessage.type === 'redirect' && parsedMessage.url) {
          debug.important(`🔄 Parsed JSON redirect message with URL: ${parsedMessage.url}`);
          redirectToUrl(parsedMessage.url);
          return;
        }
      } catch (e) {
        debug.log('Not valid JSON or no redirect info in parsed message');
      }
    }
  }
  
  debug.log('No redirect information found in message');
}

/**
 * Initialize iframe-resizer for SIV form iframes
 */
export function init(): void {
  debug.log('🚀 Initializing form embed library');
  debug.log('Window location:', window.location.href);
  
  // Find all SIV form iframes
  const forms = document.querySelectorAll<HTMLIFrameElement>('iframe[data-siv-form-id]');

  if (forms.length === 0) {
    debug.warn('No SIV form iframes found on the page');
    return; // Exit early if no forms found
  }
  
  // Set up iframe-resizer for each form
  forms.forEach((iframe, index) => {
    const formId = iframe.getAttribute('data-siv-form-id');
    try {
      iframeResize({
        log: false,
        checkOrigin: false, // TODO: Add proper origin checking for security
        
        // Handle messages from the iframe
        onMessage: (messageData) => {
          debug.important(`📨 Received message from iframe ${formId}:`, messageData);
          processMessage(messageData);
        },
        license: '1jy4dww5qzv-s54r73oxcn-v59f4kfgfz', // Required by iframe-resizer
        scrolling: true
      }, iframe);
      
    } catch (error) {
      debug.error(`Error initializing iframe-resizer for form ${formId}:`, error);
    }
  });
  
  // Also listen for postMessage events (backup communication method)
  debug.log('Setting up global postMessage event listener');
  
  window.addEventListener('message', (event) => {
    debug.important(`📨 Received window message from ${event.origin}:`, event.data);
    processMessage(event.data);
  });
  
  debug.log('✅ Form embed library initialization complete');
}

// Run initialization when DOM is ready
if (document.readyState === 'loading') {
  debug.log('Document still loading, waiting for DOMContentLoaded event');
  document.addEventListener('DOMContentLoaded', () => {
    init();
  });
} else {
  init();
} 
