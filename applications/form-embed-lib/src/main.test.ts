import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

const iframeResizeMock = vi.hoisted(() => vi.fn());

// Mock iframe-resizer before any imports
vi.mock('@iframe-resizer/parent', async () => {
  return {
    default: iframeResizeMock
  };
});

describe('Form embed initialization', () => {
  let mainModule: typeof import('./main');
  let originalLocation: Location;

  beforeEach(async () => {
    // Store original location
    originalLocation = window.location;
    
    // Mock window.location
    // @ts-ignore
    window.location = {
      ...originalLocation,
      href: 'http://localhost:3000/'
    } as Location;
    
    // Clear the DOM and reset mocks
    document.body.innerHTML = '';
    vi.clearAllMocks();
    
    // Reset module cache
    vi.resetModules();
    
    // Import the module fresh for each test
    mainModule = await import('./main');
  });

  afterEach(() => {
    // Restore original location
    // @ts-ignore
    window.location = originalLocation;
  });

  it('initializes iframe-resizer on forms with data-siv-form-id', async () => {
    // Add test iframes
    document.body.innerHTML = `
      <iframe data-siv-form-id="form1" height="500"></iframe>
      <iframe data-siv-form-id="form2" height="600"></iframe>
      <iframe></iframe>
    `;

    // Call init directly since we've already imported the module
    mainModule.init();

    // Should find two forms
    const forms = document.querySelectorAll<HTMLIFrameElement>('iframe[data-siv-form-id]');
    expect(forms.length).toBe(2);

    // Verify iframe-resizer was called for each form
    expect(iframeResizeMock).toHaveBeenCalledTimes(2);
    
    // Verify it was called with correct options
    expect(iframeResizeMock).toHaveBeenCalledWith(
      {
        log: false,
        checkOrigin: false,
        onMessage: expect.any(Function),
        license: expect.any(String),
        scrolling: true
      },
      expect.any(HTMLIFrameElement)
    );
  });

  it('does not affect iframes without data-siv-form-id', () => {
    document.body.innerHTML = `
      <iframe height="500"></iframe>
      <iframe src="something" height="600"></iframe>
    `;

    mainModule.init();

    // Should not call iframe-resizer for non-form iframes
    expect(iframeResizeMock).not.toHaveBeenCalled();
  });

  it('handles empty page with no iframes', () => {
    mainModule.init();
    const iframes = document.querySelectorAll('iframe');
    expect(iframes.length).toBe(0);
    expect(iframeResizeMock).not.toHaveBeenCalled();
  });

  it('initializes iframe-resizer with correct options', () => {
    // Add test iframe
    document.body.innerHTML = `
      <iframe data-siv-form-id="form1" height="500"></iframe>
    `;

    mainModule.init();

    // Verify iframe-resizer was called with correct options
    expect(iframeResizeMock).toHaveBeenCalledWith(
      {
        log: false,
        checkOrigin: false,
        onMessage: expect.any(Function),
        license: expect.any(String),// need to purchase a license before going to prod
        scrolling: true
      },
      expect.any(HTMLIFrameElement)
    );
  });

  // Tests for redirect functionality via onMessage
  it('handles redirect messages through the onMessage callback', () => {
    // Add test iframe
    document.body.innerHTML = `
      <iframe data-siv-form-id="form1" height="500"></iframe>
    `;

    // Initialize the module
    mainModule.init();

    // Get the onMessage callback from the iframe-resizer call
    const onMessageCallback = iframeResizeMock.mock.calls[0][0].onMessage;
    expect(typeof onMessageCallback).toBe('function');
    
    // Call the onMessage callback with a redirect message
    const redirectUrl = 'https://example.com/thank-you';
    onMessageCallback({
      message: {
        type: 'redirect',
        url: redirectUrl
      }
    });

    // Verify that window.location.href was set to the redirect URL
    expect(window.location.href).toBe(redirectUrl);
  });

  it('handles direct message objects (not wrapped by iframe-resizer)', () => {
    // Add test iframe
    document.body.innerHTML = `
      <iframe data-siv-form-id="form1" height="500"></iframe>
    `;
    
    // Initialize the module
    mainModule.init();

    // Get the onMessage callback
    const onMessageCallback = iframeResizeMock.mock.calls[0][0].onMessage;
    
    // Call with direct message object (no wrapper)
    const redirectUrl = 'https://example.com/thank-you';
    onMessageCallback({
      type: 'redirect',
      url: redirectUrl
    });
    
    // Verify redirect happened
    expect(window.location.href).toBe(redirectUrl);
  });

  it('ignores messages without valid redirect URLs', () => {
    // Add test iframe
    document.body.innerHTML = `
      <iframe data-siv-form-id="form1" height="500"></iframe>
    `;
    
    // Initialize the module
    mainModule.init();

    // Get the onMessage callback
    const onMessageCallback = iframeResizeMock.mock.calls[0][0].onMessage;
    
    // Call with invalid message
    onMessageCallback({
      message: { type: 'redirect' } // No URL
    });
    expect(window.location.href).toBe('http://localhost:3000/');

    // Call with wrong message type
    onMessageCallback({
      message: { type: 'other', url: 'https://example.com' }
    });
    expect(window.location.href).toBe('http://localhost:3000/');
    
    // Call with empty message
    onMessageCallback({});
    expect(window.location.href).toBe('http://localhost:3000/');
    
    // Call with null message
    onMessageCallback(null);
    expect(window.location.href).toBe('http://localhost:3000/');
  });
}); 
