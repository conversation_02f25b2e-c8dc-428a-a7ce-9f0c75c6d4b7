# SIV Form Embed

A lightweight JavaScript library for embedding SIV forms into any website. This library handles iframe resizing automatically and provides a simple API for form interaction.

## Installation

```html
<!-- Add this to your HTML -->
<script src="https://cdn.siv.dev/form-embed/latest/siv-form-embed.js"></script>
```

Or install via npm:

```bash
npm install @siv/form-embed
```

## Usage

### Basic Usage

```html
<div id="form-container"></div>

<script>
  const embed = SivFormEmbed.createFormEmbed({
    target: '#form-container',
    formId: 'your-form-id'
  });
</script>
```

### With All Options

```javascript
import { createFormEmbed } from '@siv/form-embed';

const embed = createFormEmbed({
  // Required options
  target: '#form-container', // CSS selector or HTMLElement
  formId: 'your-form-id',    // The ID of your form

  // Optional options
  baseUrl: 'https://custom.domain.com', // Default: https://app.siv.dev
  styles: {                            // Custom iframe styles
    border: '1px solid #ccc',
    borderRadius: '4px'
  },

  // Event callbacks
  onLoad: () => {
    console.log('Form loaded');
  },
  onSubmit: (data) => {
    console.log('Form submitted:', data);
  },
  onError: (error) => {
    console.error('Form error:', error);
  }
});
```

### Cleanup

When you're done with the form, you can remove it and clean up event listeners:

```javascript
embed.destroy();
```

## API Reference

### createFormEmbed(options)

Creates a new form embed instance.

#### Options

- `target` (string | HTMLElement): The element to mount the form into
- `formId` (string): The ID of the form to load
- `baseUrl` (string, optional): Custom base URL for the form service
- `styles` (object, optional): Custom styles to apply to the iframe
- `onLoad` (function, optional): Called when the form is loaded
- `onSubmit` (function, optional): Called when the form is submitted
- `onError` (function, optional): Called when an error occurs

#### Returns

Returns an object with:

- `iframe`: The iframe HTMLElement
- `iframeResizer`: The iframe-resizer instance
- `destroy()`: Function to clean up the embed

## Browser Support

This library supports all modern browsers and IE11+.

## Development

```bash
# Install dependencies
pnpm install

# Run development server
pnpm dev

# Build for production
turbo build

# Run tests
turbo test 
```

## License

MIT 
