/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
// noinspection ES6UnusedImports
import type {UserConfigExport} from 'vitest/config'

export default defineConfig({
  build: {
    lib: {
      entry: 'src/main.ts',
      name: 'SivFormEmbed',
      fileName: (format) => `siv-form-embed.${format}.js`,
      formats: ['iife', 'es'],
    },
    rollupOptions: {
      output: {
        globals: {
          '@iframe-resizer/parent': 'iframeResizer'
        },
        extend: true,
      },
    },
    sourcemap: true,
    minify: 'terser' as const,
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default', ['junit', {
      outputFile: './test-results/junit.xml'
    }], ['html', {
      outputFile: './test-results/html'
    }]],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/test/',
      ]
    }
  },
}); 
