{"name": "@siv/form-embed", "private": true, "version": "1.0.0", "files": ["dist"], "main": "./dist/siv-form-embed.umd.js", "module": "./dist/siv-form-embed.js", "exports": {".": {"import": "./dist/siv-form-embed.js", "require": "./dist/siv-form-embed.umd.js"}}, "scripts": {"dev": "vite", "build": "tsc && vite build && pnpm run copy-to-example", "preview": "vite preview", "test": "vitest run", "check-types": "tsc --noEmit", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "serve:example": "vite --config vite.example.config.ts", "copy-to-example": "mkdirp example/public && cpy \"dist/siv-form-embed.*\" example/public/"}, "dependencies": {"@iframe-resizer/parent": "^5.3.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@types/iframe-resizer": "^4.0.0", "@vitest/coverage-v8": "^3.0.6", "@vitest/ui": "3.0.6", "cpy-cli": "^5.0.0", "jsdom": "^22.1.0", "mkdirp": "^3.0.1", "terser": "^5.39.0", "typescript": "^5.8.3", "vite": "^5.0.8", "vitest": "^3.0.6"}}