version: '3.8'

services:
  db:
    image: "postgres:16.6-alpine"
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secret-postgres-password
      POSTGRES_DB: sivdev
  db-test:
    image: "postgres:16.6-alpine"
    ports:
      - 5433:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secret-test-postgres-password
      POSTGRES_DB: sivtest
  valkey:
    image: valkey/valkey:8.0
    ports:
      - 6379:6379
  minio:
    image: "minio/minio:RELEASE.2025-03-12T18-04-18Z-cpuv1"
    entrypoint: /bootstrap/bootstrap-local-minio.sh
    ports:
      - 9000:9000
      - 9001:9001
    volumes:
      - /tmp/data:/data
      - ./bootstrap-local-minio:/bootstrap
  inngest:
    image: inngest/inngest:latest
    ports:
      - "8288:8288"
    command: 'inngest dev -u http://host.docker.internal:5173/api/inngest -u http://host.docker.internal:3000/api/inngest -u http://host.docker.internal:3001/api/inngest'
  inngest-test:
    image: inngest/inngest:latest
    ports:
      - "8289:8288"
    command: 'inngest dev -u http://host.docker.internal:5174/api/inngest -u http://host.docker.internal:3000/api/innges -u http://host.docker.internal:3001/api/inngest'
