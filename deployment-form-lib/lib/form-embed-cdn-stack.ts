import { Construct } from 'constructs';
import { Stack, StackProps, RemovalPolicy, Duration } from 'aws-cdk-lib';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import { aws_cloudfront as cloudfront } from 'aws-cdk-lib';
import { aws_cloudfront_origins as origins } from 'aws-cdk-lib';
import { aws_s3_deployment as s3deploy } from 'aws-cdk-lib';
import { aws_route53 as route53 } from 'aws-cdk-lib';
import { aws_route53_targets as targets } from 'aws-cdk-lib';
import { aws_certificatemanager as acm } from 'aws-cdk-lib';

export interface FormEmbedCdnStackProps extends StackProps {
  /**
   * Whether to retain the bucket when the stack is destroyed.
   * Defaults to true for safety.
   */
  retainBucket?: boolean;

  /**
   * Certificate for the CloudFront distribution.
   * Must be in us-east-1 region.
   */
  certificate: acm.ICertificate;
}

export class FormEmbedCdnStack extends Stack {
  constructor(scope: Construct, id: string, props: FormEmbedCdnStackProps) {
    super(scope, id, {
      ...props,
      crossRegionReferences: true, // Enable cross-region references for CloudFront certificate
    });

    // Look up existing hosted zone
    const zone = route53.HostedZone.fromLookup(this, 'Zone', {
      domainName: 'sivconverts.com',
    });

    // Create an S3 bucket to store the form embed library
    const bucket = new s3.Bucket(this, 'FormEmbedBucket', {
      publicReadAccess: false,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: props.retainBucket !== false ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      versioned: true,
    });

    // Create Origin Access Identity for CloudFront
    const originAccessIdentity = new cloudfront.OriginAccessIdentity(this, 'FormEmbedOAI', {
      comment: `OAI for ${id}`,
    });
    bucket.grantRead(originAccessIdentity);

    // Create shared response headers policy
    const responseHeadersPolicy = new cloudfront.ResponseHeadersPolicy(this, 'FormEmbedSecurityHeaders', {
      securityHeadersBehavior: {
        contentSecurityPolicy: {
          contentSecurityPolicy: "default-src * 'unsafe-inline' 'unsafe-eval'; frame-ancestors *;",
          override: true,
        },
        strictTransportSecurity: {
          accessControlMaxAge: Duration.days(365),
          includeSubdomains: true,
          preload: true,
          override: true,
        },
        contentTypeOptions: {
          override: true
        }
      },
      corsBehavior: {
        accessControlAllowOrigins: ['*'],
        accessControlAllowMethods: ['GET'],
        accessControlAllowHeaders: [
          'Accept',
          'Accept-Encoding',
          'Origin',
          'Cache-Control',
          'If-Match',
          'If-None-Match',
          'If-Modified-Since',
          'If-Unmodified-Since'
        ],
        accessControlMaxAge: Duration.seconds(3600),
        originOverride: true,
        accessControlAllowCredentials: false,
      },
    });

    // Create the CloudFront distribution
    const distribution = new cloudfront.Distribution(this, 'FormEmbedDistribution', {
      defaultBehavior: {
        origin: origins.S3BucketOrigin.withOriginAccessIdentity(bucket, { originAccessIdentity }),
        compress: true,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
        cachePolicy: new cloudfront.CachePolicy(this, 'FormEmbedVersionedCachePolicy', {
          defaultTtl: Duration.days(365),
          maxTtl: Duration.days(365),
          minTtl: Duration.days(365),
          enableAcceptEncodingGzip: true,
          enableAcceptEncodingBrotli: true,
          headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
            'Accept',
            'Origin'
          ),
          queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
          cookieBehavior: cloudfront.CacheCookieBehavior.none(),
        }),
        responseHeadersPolicy,
      },
      additionalBehaviors: {
        '/latest/*': {
          origin: origins.S3BucketOrigin.withOriginAccessIdentity(bucket, { originAccessIdentity }),
          compress: true,
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
          originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
          cachePolicy: new cloudfront.CachePolicy(this, 'FormEmbedLatestCachePolicy', {
            defaultTtl: Duration.minutes(5),
            maxTtl: Duration.hours(1),
            minTtl: Duration.minutes(1),
            enableAcceptEncodingGzip: true,
            enableAcceptEncodingBrotli: true,
            headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
              'Accept',
              'Origin'
            ),
            queryStringBehavior: cloudfront.CacheQueryStringBehavior.none(),
            cookieBehavior: cloudfront.CacheCookieBehavior.none(),
          }),
          responseHeadersPolicy,
        },
      },
      domainNames: ['sivform-cdn.sivconverts.com'],
      certificate: props.certificate,
      priceClass: cloudfront.PriceClass.PRICE_CLASS_100,
      minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
      httpVersion: cloudfront.HttpVersion.HTTP2_AND_3,
      comment: 'CDN for SIV Form Embed Library',
      enableLogging: false,
    });

    // Create DNS record
    new route53.ARecord(this, 'FormEmbedAliasRecord', {
      zone,
      recordName: 'sivform-cdn',
      target: route53.RecordTarget.fromAlias(new targets.CloudFrontTarget(distribution)),
    });

    // Deploy the built files to S3
    new s3deploy.BucketDeployment(this, 'FormEmbedDeployment', {
      sources: [s3deploy.Source.asset('../applications/form-embed-lib/dist')],
      destinationBucket: bucket,
      distribution,
      distributionPaths: ['/v1/*'],
      destinationKeyPrefix: 'v1',
    });

    // Deploy the same files to the latest path
    new s3deploy.BucketDeployment(this, 'FormEmbedLatestDeployment', {
      sources: [s3deploy.Source.asset('../applications/form-embed-lib/dist')],
      destinationBucket: bucket,
      distribution,
      distributionPaths: ['/latest/*'],
      destinationKeyPrefix: 'latest',
    });
  }
} 
