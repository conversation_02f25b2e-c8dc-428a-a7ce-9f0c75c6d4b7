import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_route53 as route53 } from 'aws-cdk-lib';
import { aws_certificatemanager as acm } from 'aws-cdk-lib';

export class CertificateStack extends Stack {
  public readonly certificate: acm.ICertificate;

  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, {
      ...props,
      crossRegionReferences: true, // Enable cross-region references
    });

    // Look up existing hosted zone
    const zone = route53.HostedZone.fromLookup(this, 'Zone', {
      domainName: 'sivconverts.com',
    });

    // Create certificate for CloudFront
    this.certificate = new acm.Certificate(this, 'FormEmbedCertificate', {
      domainName: 'sivform-cdn.sivconverts.com',
      validation: acm.CertificateValidation.fromDns(zone),
    });
  }
} 