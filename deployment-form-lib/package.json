{"name": "deployment-form-lib", "version": "0.1.0", "bin": {"deployment-form-lib": "bin/app.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest --passWithNoTests", "cdk": "cdk", "deploy": "cdk deploy", "diff": "cdk diff", "synth": "cdk synth"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.13.4", "aws-cdk": "2.1000.2", "aws-cdk-lib": "2.179.0", "constructs": "^10.4.2", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"source-map-support": "^0.5.21"}}