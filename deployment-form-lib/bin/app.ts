#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { FormEmbedCdnStack } from '../lib/form-embed-cdn-stack';
import { CertificateStack } from '../lib/certificate-stack';
import { Tags } from "aws-cdk-lib";

function addStack<T extends cdk.Stack>(stackCreationFn: () => T): T {
    const stack = stackCreationFn();
    Tags.of(stack).add("cdk-stack", stack.stackName);
    return stack;
}

const app = new cdk.App();

// Create certificate in us-east-1
const certStack = addStack(() =>
    new CertificateStack(app, 'FormEmbedCertificateStack', {
        env: { 
            account: process.env.CDK_DEFAULT_ACCOUNT,
            region: 'us-east-1'
        },
        description: 'ACM Certificate for the SIV Form Embed CDN',
        tags: {
            Project: 'SIV Form Embed',
            Environment: 'prod',
        },
    })
);

// Create CDN stack in default region
addStack(() =>
    new FormEmbedCdnStack(app, 'FormEmbedCdnStack', {
        env: {
            account: process.env.CDK_DEFAULT_ACCOUNT,
            region: process.env.CDK_DEFAULT_REGION || 'us-east-1',
        },
        certificate: certStack.certificate,
        retainBucket: true,
        description: 'CDN infrastructure for the SIV Form Embed Library',
        tags: {
            Project: 'SIV Form Embed',
            Environment: 'prod',
        },
    })
);
